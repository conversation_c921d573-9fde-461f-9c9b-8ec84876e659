<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss-v3/master/en/semiconductor/product/dl/device-family-pack</url>
  <name>TXZ4Aplus-M4K1_DFP</name>
  <description>Toshiba TXZ4A+ Series TMPM4K(1) Group Device Support</description>
  <releases>
    <release version="1.0.0" date="2025-01-21">
      First Release version of TXZ4A+ Series Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4K</keyword>
    <keyword>TXZ4A+</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4A+ Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
The TXZ4A+ microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>
      <!-- ************************  Subfamily 'TMPM4Kx'  **************************** -->
      <subFamily DsubFamily="M4K(1)">
        <!-- ***********************  Device 'TMPM4K4FYBUG'  ************************* -->
        <device Dname="TMPM4K4FYBUG">
          <processor Dclock="120000000"/>
          <compile header="Device/Include/TMPM4K4B.h" define="TMPM4K4"/>
          <debug svd="SVD/M4K4B.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          <feature type="IOs"           n="52"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="16"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="11"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="4"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="13"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="13"     m="12"               name="Analog to Digital Converter B (ADC)"/>

          <feature type="CoreOther"     n="2"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                          name="P-LQFP64-1010-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->
        <!-- ***********************  Device 'TMPM4K4FWBUG'  ************************* -->
        <device Dname="TMPM4K4FWBUG">
          <processor Dclock="120000000"/>
          <compile header="Device/Include/TMPM4K4B.h" define="TMPM4K4"/>
          <debug svd="SVD/M4K4B.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          <feature type="IOs"           n="52"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="16"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="11"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="4"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="13"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="13"     m="12"               name="Analog to Digital Converter B (ADC)"/>

          <feature type="CoreOther"     n="2"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                          name="P-LQFP64-1010-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- ***********************  Device 'TMPM4K2FYBUG'  ************************* -->
        <device Dname="TMPM4K2FYBUG">
          <processor Dclock="120000000"/>
          <compile header="Device/Include/TMPM4K2B.h" define="TMPM4K2"/>
          <debug svd="SVD/M4K2B.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          <feature type="IOs"           n="38"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="12"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="10"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="26"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter B (ADC)"/>

          <feature type="CoreOther"     n="2"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="48"                          name="P-LQFP48-0707-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->
        <!-- ***********************  Device 'TMPM4K2FWBUG'  ************************* -->
        <device Dname="TMPM4K2FWBUG">
          <processor Dclock="120000000"/>
          <compile header="Device/Include/TMPM4K2B.h" define="TMPM4K2"/>
          <debug svd="SVD/M4K2B.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          <feature type="IOs"           n="38"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="12"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="10"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="26"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter B (ADC)"/>

          <feature type="CoreOther"     n="2"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="48"                          name="P-LQFP48-0707-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- ***********************  Device 'TMPM4K1FYBUG'  ************************* -->
        <device Dname="TMPM4K1FYBUG">
          <processor Dclock="120000000"/>
          <compile header="Device/Include/TMPM4K1B.h" define="TMPM4K1"/>
          <debug svd="SVD/M4K1B.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          <feature type="IOs"           n="34"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="11"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="9"                           name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="24"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="2"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="10"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="10"     m="12"               name="Analog to Digital Converter B (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="44"                          name="P-LQFP44-1010-0.80-003"/>
        </device>
        <!-- ********************************************************************************************* -->
        <!-- ***********************  Device 'TMPM4K1FWBUG'  ************************* -->
        <device Dname="TMPM4K1FWBUG">
          <processor Dclock="120000000"/>
          <compile header="Device/Include/TMPM4K1B.h" define="TMPM4K1"/>
          <debug svd="SVD/M4K1B.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          <feature type="IOs"           n="34"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="11"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="9"                           name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="24"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="2"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="1"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="10"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="10"     m="12"               name="Analog to Digital Converter B (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="44"                          name="P-LQFP44-1010-0.80-003"/>
        </device>
        <!-- ********************************************************************************************* -->
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="TMPM4K Compiler">
      <accept condition="Compiler ARMCC"/>
      <accept condition="Compiler IAR"/>
    </condition>
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="TMPM4K4x CMSIS">
      <description>Toshiba TMPM4K4x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K4*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4K2x CMSIS">
      <description>Toshiba TMPM4K2x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K2*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4K1x CMSIS">
      <description>Toshiba TMPM4K1x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K1*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <!-- Startup TMPM4K4x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4K4x CMSIS">
      <description>System Startup for Toshiba TMPM4K4x Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4K Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4K4B.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4K4FYBUG.scat"  attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/IAR/startup_TMPM4K4B.s" attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="linkerScript" name="Device/Source/IAR/TMPM4K4FYBUG.icf"   attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="source"       name="Device/Source/system_TMPM4KxB.c"      attr="config" version="1.0.0" condition="TMPM4K Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4K2x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4K2x CMSIS">
      <description>System Startup for Toshiba TMPM4K2x Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4K Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4K2B.s"  attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4K2FYBDUG.scat"  attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/IAR/startup_TMPM4K2B.s"  attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="linkerScript" name="Device/Source/IAR/TMPM4K2FYBDUG.icf"   attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="source"       name="Device/Source/system_TMPM4KxB.c"       attr="config" version="1.0.0" condition="TMPM4K Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4K1x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4K1x CMSIS">
      <description>System Startup for Toshiba TMPM4K1x Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4K Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4K1B.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4K1FYBUG.scat"  attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/IAR/startup_TMPM4K1B.s" attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="linkerScript" name="Device/Source/IAR/TMPM4K1FYBUG.icf"   attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="source"       name="Device/Source/system_TMPM4KxB.c"      attr="config" version="1.0.0" condition="TMPM4K Compiler"/>
      </files>
    </component>
  </components>

</package>
