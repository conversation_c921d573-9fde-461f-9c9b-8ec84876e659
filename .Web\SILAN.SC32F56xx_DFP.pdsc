<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
	<vendor>SILAN</vendor>
	<name>SC32F56xx_DFP</name>
	<description>Silan SC32F56xx Device Family Pack</description>
	<url>https://github.com/Silanpack/Pack/raw/main/</url>
	<releases>
		<release version="1.1.5" date="2023-10-17">
			Update the IAP_Type of SC32F5632xxG and SC32F5664xxG header files
		</release>
	
		<release version="1.1.4" date="2023-07-24">
			Update Device Name
		</release>
	
		<release version="1.1.3" date="2023-06-30">
			Update Flash algorith
		</release>
	
		<release version="1.1.2" date="2023-03-29">
			Add SC32F56A
		</release>
		
		<release version="1.1.1" date="2021-07-29">
			Update svd
		</release>	
	
		<release version="1.1.0" date="2020-12-02">
			Initial Version
		</release>	
	</releases>
	
	<keywords>
		<!-- keywords for indexing -->
		<keyword>SC32F56xx</keyword>
		<keyword>SC32F5632</keyword>
		<keyword>SC32F5664</keyword>
		<keyword>SC32F56128</keyword>
	</keywords>
	<!-- devices section (mandatory for Device Family Packs) -->
	<devices>
		<family Dfamily="SC32F56 Series" Dvendor="SILAN:164">
			<!-- ************************  Subfamily 'Silan_SC32F56xx'  **************************** -->
			<subFamily DsubFamily="SC32F56xx">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="64000000"/>
				<feature type="VCC"				n="2.00"	m="5.50"/>
				<feature type="Temp"			n="-40"		m="85"/>
				<feature type="UART"			n="2"/>
				<feature type="SPI"				n="1"/>
				<feature type="Timer"			n="1"       m="16"		name="ETIMER T0"/>
				<feature type="Timer"			n="2"       m="32"		name="General Purpose Timer TIM6"/>
				<feature type="WDT"    			n="2"/>
				<feature type="PWM"    			n="8"       m="16"/>
				<feature type="ADC"    	n="16"      m="12"/>
				
				<!-- *************************  Device 'SC32F5632RE1G'  ***************************** -->
				<device Dname="SC32F5632RE1G">
					<compile header="Device/Include/SC32F5632xxG.h"/>
					<debug svd="SVD/SC32F5632xxG.svd"/>
					<memory id="IROM1" start="0x00000000" size="0x8000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1000" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F56xxG.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5632 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 64MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5632 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-32 kB on-chip FLASH programming memory, data retention >10 years. 
-4 kB SRAM, with parity check 
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LF1G'  ***************************** -->
				<device Dname="SC32F5664LF1G">
					<compile header="Device/Include/SC32F5664xxG.h"/>
					<debug svd="SVD/SC32F5664xxG.svd"/>
					<memory id="IROM1" start="0x00000000" size="0x10000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1800" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F56xxG.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 64MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5664 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-64 kB on-chip FLASH programming memory, data retention >10 years. 
-6 kB SRAM, with parity check 
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LL1G'  ***************************** -->
				<device Dname="SC32F5664LL1G">
					<compile header="Device/Include/SC32F5664xxG.h"/>
					<debug svd="SVD/SC32F5664xxG.svd"/>
					<memory id="IROM1" start="0x00000000" size="0x10000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1800" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F56xxG.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 64MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5664 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-64 kB on-chip FLASH programming memory, data retention >10 years. 
-6 kB SRAM, with parity check 
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5632RE1G_BOOT'  ***************************** -->
				<device Dname="SC32F5632RE1G_BOOT">
					<compile header="Device/Include/SC32F5632xxG.h"/>
					<debug svd="SVD/SC32F5632xxG.svd"/>
					<memory id="IROM1" start="0x00000000" size="0x8000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1000" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F56xxG_BOOT.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5632 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 64MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5632 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-32 kB on-chip FLASH programming memory, data retention >10 years. 
-4 kB SRAM, with parity check 
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LF1G_BOOT'  ***************************** -->
				<device Dname="SC32F5664LF1G_BOOT">
					<compile header="Device/Include/SC32F5664xxG.h"/>
					<debug svd="SVD/SC32F5664xxG.svd"/>
					<memory id="IROM1" start="0x00000000" size="0x10000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1800" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F56xxG_BOOT.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 64MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5664 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-64 kB on-chip FLASH programming memory, data retention >10 years. 
-6 kB SRAM, with parity check 
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LL1G_BOOT'  ***************************** -->
				<device Dname="SC32F5664LL1G_BOOT">
					<compile header="Device/Include/SC32F5664xxG.h"/>
					<debug svd="SVD/SC32F5664xxG.svd"/>
					<memory id="IROM1" start="0x00000000" size="0x10000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1800" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F56xxG_BOOT.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 64MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5664 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-64 kB on-chip FLASH programming memory, data retention >10 years. 
-6 kB SRAM, with parity check 
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5632RE1H'  ***************************** -->
				<device Dname="SC32F5632RE1H">
					<compile header="Device/Include/SC32F5632xxH.h"/>   
					<debug svd="SVD/SC32F5632xxH.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x8000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F5632xxH.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664LF1H
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LF1H'  ***************************** -->
				<device Dname="SC32F5664LF1H">
					<compile header="Device/Include/SC32F5664xxH.h"/>   
					<debug svd="SVD/SC32F5664xxH.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F5664xxH.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664LF1H
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LL1H'  ***************************** -->
				<device Dname="SC32F5664LL1H">
					<compile header="Device/Include/SC32F5664xxH.h"/>   
					<debug svd="SVD/SC32F5664xxH.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F5664xxH.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664LF1H
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128NL1G'  ***************************** -->
				<device Dname="SC32F56128NL1G">
					<compile header="Device/Include/SC32F56128xxG.h"/>
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128NL1G
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128LF1G'  ***************************** -->
				<device Dname="SC32F56128LF1G">
					<compile header="Device/Include/SC32F56128xxG.h"/>
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128LF1G
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128LA1G'  ***************************** -->
				<device Dname="SC32F56128LA1G">
					<compile header="Device/Include/SC32F56128xxG.h"/>
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128LA1G
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128LL1G'  ***************************** -->
				<device Dname="SC32F56128LL1G">
					<compile header="Device/Include/SC32F56128xxG.h"/>
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128LL1G
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LF1H_BOOT'  ***************************** -->
				<device Dname="SC32F5664LF1H_BOOT">
					<compile header="Device/Include/SC32F5664xxH.h"/> 
					<debug svd="SVD/SC32F5664xxH.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F5664xxH_BOOT.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664LF1H_BOOT
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5664LL1H_BOOT'  ***************************** -->
				<device Dname="SC32F5664LL1H_BOOT">
					<compile header="Device/Include/SC32F5664xxH.h"/> 
					<debug svd="SVD/SC32F5664xxH.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F5664xxH_BOOT.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F5664LL1H_BOOT
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128NL1G_BOOT'  ***************************** -->
				<device Dname="SC32F56128NL1G_BOOT">
					<compile header="Device/Include/SC32F56128xxG.h"/>  
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG_BOOT.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128NL1G_BOOT
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128LF1G_BOOT'  ***************************** -->
				<device Dname="SC32F56128LF1G_BOOT">
					<compile header="Device/Include/SC32F56128xxG.h"/>  
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG_BOOT.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128LF1G_BOOT
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128LA1G_BOOT'  ***************************** -->
				<device Dname="SC32F56128LA1G_BOOT">
					<compile header="Device/Include/SC32F56128xxG.h"/>  
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG_BOOT.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128LA1G_BOOT
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F56128LL1G_BOOT'  ***************************** -->
				<device Dname="SC32F56128LL1G_BOOT">
					<compile header="Device/Include/SC32F56128xxG.h"/>  
					<debug svd="SVD/SC32F56128xxG.svd"/>
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x3000"     init   ="0"   default="1"/>
					<algorithm name="Flash/SC32F56128xxG_BOOT.FLM" start="0x00000000" size="0x20000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<description>
SC32F56128LL1G_BOOT
					</description>
				</device>						
			</subFamily>
		</family>
	</devices>
	<!-- examples section (optional for all Software Packs)-->
	<!--
  <examples>
  </examples>
  -->
	<!-- conditions section (optional for all Software Packs)-->
	<conditions>
		<!-- Compiler Conditions -->
		<condition id="Compiler ARMCC">
			<require Tcompiler="ARMCC"/>
		</condition>
		
		<condition id="SC32F5632RE1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5632RE1G"/>
		</condition>
		
		<condition id="SC32F5664LF1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LF1G"/>
		</condition>
		
		<condition id="SC32F5664LL1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LL1G"/>
		</condition>
		
		<condition id="SC32F5632RE1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5632RE1G_BOOT"/>
		</condition>
		
		<condition id="SC32F5664LF1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LF1G_BOOT"/>
		</condition>
		
		<condition id="SC32F5664LL1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LL1G_BOOT"/>
		</condition>
		
		<condition id="SC32F5632RE1H">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5632RE1H"/>
		</condition>
		
		<condition id="SC32F5664LF1H">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LF1H"/>
		</condition>
		
		<condition id="SC32F5664LL1H">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LL1H"/>
		</condition>
		
		<condition id="SC32F56128NL1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128NL1G"/>
		</condition>
		
		<condition id="SC32F56128LF1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128LF1G"/>
		</condition>
		
		<condition id="SC32F56128LA1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128LA1G"/>
		</condition>
		
		<condition id="SC32F56128LL1G">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128LL1G"/>
		</condition>
		
		<condition id="SC32F5664LF1H_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LF1H_BOOT"/>
		</condition>
		
		<condition id="SC32F5664LL1H_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5664LL1H_BOOT"/>
		</condition>
		
		<condition id="SC32F56128NL1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128NL1G_BOOT"/>
		</condition>
		
		<condition id="SC32F56128LF1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128LF1G_BOOT"/>
		</condition>
		
		<condition id="SC32F56128LA1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128LA1G_BOOT"/>
		</condition>
		
		<condition id="SC32F56128LL1G_BOOT">
			<description>Slian SC32F56xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F56128LL1G_BOOT"/>
		</condition>		
		
		<condition id="Startup ARM">
			<description>Startupassembler file for ARMCC</description>
			<require Tcompiler="ARMCC"/>
		</condition>
		
		<condition id="Startup GCC">
			<description>Startupassembler file for GCC</description>
			<require Tcompiler="GCC"/>
		</condition>
		
		<condition id="Startup IAR">
			<description>Startupassembler file for IAR</description>
			<require Tcompiler="IAR"/>
		</condition>	
	</conditions>
	
	<!-- component section (optional for all Software Packs)-->
	<components>
		<!-- Startup SC32F5632RE1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5632RE1G">
			<description>System Startup for Silan SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5632xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5632xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5632xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5632xxG.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		
		<!-- Startup SC32F5664LF1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LF1G">
			<description>System Startup for Silan SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5664xxG.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		
		<!-- Startup SC32F5664LL1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LL1G">
			<description>System Startup for Silan SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5664xxG.c" attr="config" version="1.0.0"/>
			</files>
		</component>		
		
		<!-- Startup SC32F5632RE1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5632RE1G_BOOT">
			<description>System Startup for Silan SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5632xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5632xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5632xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5632xxG.c" attr="config" version="1.0.0"/>
			</files>
		</component>

		<!-- Startup SC32F5664LF1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LF1G_BOOT">
			<description>System Startup for Silan SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5664xxG.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		
		<!-- Startup SC32F5664LL1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LL1G_BOOT">
			<description>System Startup for Silan SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5664xxG.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		
		<!-- Startup SC32F5632RE1H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5632RE1H">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5632xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5632xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5632xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5632xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5664LF1H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LF1H">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5664xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5664LL1H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LL1H">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5664xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128NL1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128NL1G">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128LF1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128LF1G">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128LA1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128LA1G">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128LL1G -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128LL1G">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5664LF1H_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LF1H_BOOT">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5664xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5664LL1H_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5664LL1H_BOOT">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5664xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5664xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5664xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128NL1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128NL1G_BOOT">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128LF1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128LF1G_BOOT">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128LA1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128LA1G_BOOT">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F56128LL1G_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F56128LL1G_BOOT">
			<description>System Startup for Slian SC32F56 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F56128xxG.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F56128xxG.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F56128xxG.c" attr="config" version="1.0.0" />
			</files>
		</component>		
	</components>
</package>
