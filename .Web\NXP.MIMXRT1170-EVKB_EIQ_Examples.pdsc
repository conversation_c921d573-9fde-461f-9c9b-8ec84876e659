<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1170-EVKB_EIQ_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware eiq Examples Pack for MIMXRT1170-EVKB</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1170-EVKB_BSP" vendor="NXP" version="19.0.0"/>
      <package name="EIQ" vendor="NXP" version="2.0.0"/>
      <package name="MIMXRT1176_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="LWIP" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="mpp_camera_mobilenet_view_tflm_cm7" folder="boards/evkbmimxrt1170/eiq_examples/mpp_camera_mobilenet_view_tflm" doc="doc/README.md">
      <description>Image Classification with TensorFlow Lite Micro Example</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="mpp_camera_mobilenet_view_tflm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpp_camera_persondetect_view_tflm_cm7" folder="boards/evkbmimxrt1170/eiq_examples/mpp_camera_persondetect_view_tflm" doc="doc/README.md">
      <description>Person detection with TensorFlow Lite Micro Example</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="mpp_camera_persondetect_view_tflm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpp_camera_ultraface_view_tflm_cm7" folder="boards/evkbmimxrt1170/eiq_examples/mpp_camera_ultraface_view_tflm" doc="doc/README.md">
      <description>Face detection with TensorFlow Lite Micro Example</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="mpp_camera_ultraface_view_tflm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpp_camera_view_cm7" folder="boards/evkbmimxrt1170/eiq_examples/mpp_camera_view" doc="doc/README.md">
      <description>MPP Camera View Example</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="mpp_camera_view_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpp_static_image_nanodet_view_tflm_cm7" folder="boards/evkbmimxrt1170/eiq_examples/mpp_static_image_nanodet_view_tflm" doc="doc/README.md">
      <description>Object Detection with TensorFlow Lite Micro Example</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="mpp_static_image_nanodet_view_tflm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tflm_cifar10_cm7" folder="boards/evkbmimxrt1170/eiq_examples/tflm_cifar10" doc="readme.md">
      <description>CIFAR-10 example for TensorFlow Lite Micro</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tflm_cifar10_cm7.uvprojx"/>
        <environment name="csolution" load="tflm_cifar10_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tflm_label_image_cm7" folder="boards/evkbmimxrt1170/eiq_examples/tflm_label_image" doc="readme.md">
      <description>Label image example for TensorFlow Lite Micro</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tflm_label_image_cm7.uvprojx"/>
        <environment name="csolution" load="tflm_label_image_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tflm_modelrunner_cm7" folder="boards/evkbmimxrt1170/eiq_examples/tflm_modelrunner/cm7" doc="readme.md">
      <description>ModelRunner for TFlite</description>
      <board name="MIMXRT1170-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tflm_modelrunner_cm7.uvprojx"/>
        <environment name="csolution" load="tflm_modelrunner_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
