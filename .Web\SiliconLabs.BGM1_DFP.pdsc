<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>BGM1_DFP</name>
  <description>Silicon Labs BGM1 Blue Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_BGM1_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_BGM1_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>BGM1</keyword>
    <keyword>BGM1</keyword>
    <keyword>Blue Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="BGM1 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="38400000"/>
      <book name="Documents/cortex_m4_dgug.pdf"      title="Cortex-M4 Generic User Guide"/>
      <book name="Documents/efr32xg1-rm.pdf"  title="BGM1 Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M4 core with 40 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in several footprint compatible QFN packages&#xD;&#xA;- 12-channel Peripheral Reflex System enabling autonomous interaction of MCU peripherals&#xD;&#xA;- Autonomous Hardware Crypto Accelerator and Random Number Generator&#xD;&#xA;- Integrated 2.4 GHz balun and PA with up to 19.5 dBm transmit power&#xD;&#xA;- Integrated DC-DC with RF noise mitigation&#xD;&#xA;&#xD;&#xA;The Wireless Gecko portfolio of SoCs (EFR32) include Mighty Gecko (EFR32MG1), Blue Gecko (EFR32BG1), and Flex Gecko (EFR32FG1) families. With support for Bluetooth Smart (BLE), ZigBee, Thread and proprietary protocols, the Wireless Gecko portfolio is ideal for enabling energy-friendly wireless networking for IoT devices. The single-die solution provides industry-leading energy efficiency, ultra-fast wakeup times, a scalable high-power amplifier, an integrated balun and no-compromise MCU features.
      </description>

      <subFamily DsubFamily="BGM111">
        <book         name="Documents/bgm111_datasheet.pdf"      title="BGM111 Data Sheet"/>
        <book         name="Documents/bgm111_datashort.pdf"      title="BGM111 Data Short"/>
        <!-- *************************  Device 'BGM111A256V2'  ***************************** -->
        <device Dname="BGM111A256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM111A256V2"/>
          <debug      svd="SVD/BGM1/BGM111A256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="BGM111E">
        <book         name="Documents/bgm111e-datasheet.pdf"      title="BGM111E Data Sheet"/>
        <!-- *************************  Device 'BGM111E256V2'  ***************************** -->
        <device Dname="BGM111E256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM111E256V2"/>
          <debug      svd="SVD/BGM1/BGM111E256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="BGM113">
        <book         name="Documents/bgm113_datasheet.pdf"      title="BGM113 Data Sheet"/>
        <!-- *************************  Device 'BGM113A256V2'  ***************************** -->
        <device Dname="BGM113A256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM113A256V2"/>
          <debug      svd="SVD/BGM1/BGM113A256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="BGM11S">
        <book         name="Documents/bgm11s-datasheet.pdf"      title="BGM11S Data Sheet"/>
        <!-- *************************  Device 'BGM11S12F256GA'  ***************************** -->
        <device Dname="BGM11S12F256GA">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM11S12F256GA"/>
          <debug      svd="SVD/BGM1/BGM11S12F256GA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'BGM11S22F256GA'  ***************************** -->
        <device Dname="BGM11S22F256GA">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM11S22F256GA"/>
          <debug      svd="SVD/BGM1/BGM11S22F256GA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="BGM12X">
        <book         name="Documents/bgm12x-datasheet.pdf"      title="BGM12X Data Sheet"/>
        <book         name="Documents/bgm12x-datashort.pdf"      title="BGM12X Data Short"/>
        <!-- *************************  Device 'BGM121A256V2'  ***************************** -->
        <device Dname="BGM121A256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM121A256V2"/>
          <debug      svd="SVD/BGM1/BGM121A256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'BGM121N256V2'  ***************************** -->
        <device Dname="BGM121N256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM121N256V2"/>
          <debug      svd="SVD/BGM1/BGM121N256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'BGM123A256V2'  ***************************** -->
        <device Dname="BGM123A256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM123A256V2"/>
          <debug      svd="SVD/BGM1/BGM123A256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'BGM123N256V2'  ***************************** -->
        <device Dname="BGM123N256V2">
          <compile header="Device/SiliconLabs/BGM1/Include/em_device.h"  define="BGM123N256V2"/>
          <debug      svd="SVD/BGM1/BGM123N256V2.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="BGM1">
      <description>Silicon Labs BGM1 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="BGM1*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="BGM1">
      <description>System Startup for Silicon Labs BGM1 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/BGM1/Include/"/>
        <file category="header"  name="Device/SiliconLabs/BGM1/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/BGM1/Source/ARM/startup_bgm1.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/BGM1/Source/GCC/startup_bgm1.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/BGM1/Source/GCC/bgm1.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/BGM1/Source/system_bgm1.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
