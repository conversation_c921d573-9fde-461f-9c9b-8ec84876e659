<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: Ethernet</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('driver_eth.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Ethernet </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="driver_eth_devices"></a>
Driver Implementations</h1>
<p >The <a class="el" href="index.html#driver_pack_content">Pack Content</a> provides implementations of <a href="https://arm-software.github.io/CMSIS_6/latest/Driver/group__eth__interface__gr.html" target="_blank"><b>CMSIS-Ethernet drivers</b></a> for the following devices:</p>
<table class="cmtable" summary="Ethernet Drivers">
<tr>
<th>Driver </th><th>Description  <a class="anchor" id="driver_KSZ8851SNL"></a> </th></tr>
<tr>
<td><b>KSZ8851SNL/SNLI</b>  </td><td>Ethernet PHY and MAC interfaces for the Microchip <a href="https://www.microchip.com/KSZ8851" target="_blank"><b>KSZ8851</b></a>.   <a class="anchor" id="driver_LAN9220"></a> </td></tr>
<tr>
<td><b>LAN9220</b>  </td><td>Ethernet PHY and MAC interfaces for the Microchip <a href="https://www.microchip.com/LAN9220" target="_blank"><b>LAN9220</b></a>.   <a class="anchor" id="driver_DP83848C"></a> </td></tr>
<tr>
<td><b>DP83848C</b>  </td><td>Ethernet PHY interface for the Texas Instruments <a href="http://www.ti.com/product/DP83848C" target="_blank"><b>DP83848C</b></a>.   <a class="anchor" id="driver_KSZ8061RNB"></a> </td></tr>
<tr>
<td><b>KSZ8061RNB</b>  </td><td>Ethernet PHY interface for the Microchip <a href="https://www.microchip.com/KSZ8061" target="_blank"><b>KSZ8061</b></a>.   <a class="anchor" id="driver_KSZ8081RNA"></a> </td></tr>
<tr>
<td><b>KSZ8081RNA</b>  </td><td>Ethernet PHY interface for the Microchip <a href="https://www.microchip.com/KSZ8081" target="_blank"><b>KSZ8081</b></a>.   <a class="anchor" id="driver_LAN8710A"></a> </td></tr>
<tr>
<td><b>LAN8710A</b>  </td><td>Ethernet PHY interface for the Microchip <a href="https://www.microchip.com/LAN8710A" target="_blank"><b>LAN8710A</b></a>.   <a class="anchor" id="driver_LAN8720"></a> </td></tr>
<tr>
<td><b>LAN8720</b>  </td><td>Ethernet PHY interface for the Microchip <a href="https://www.microchip.com/LAN8720A" target="_blank"><b>LAN8720</b></a>.   <a class="anchor" id="driver_LAN8740A"></a> </td></tr>
<tr>
<td><b>LAN8740A</b>  </td><td>Ethernet PHY interface for the Microchip <a href="https://www.microchip.com/LAN8740A" target="_blank"><b>LAN8740A</b></a>.   <a class="anchor" id="driver_LAN8742A"></a> </td></tr>
<tr>
<td><b>LAN8742A</b>  </td><td>Ethernet PHY interface for the Microchip <a href="https://www.microchip.com/LAN8742A" target="_blank"><b>LAN8742A</b></a>.   <a class="anchor" id="driver_ST802RT1"></a> </td></tr>
<tr>
<td><b>ST802RT1</b>  </td><td>Ethernet PHY interface for the STMicroelectronics <a href="http://www.st.com/content/ccc/resource/technical/document/data_brief/37/8e/14/c7/84/39/4d/61/**********.pdf/files/**********.pdf/jcr:content/translations/en.**********.pdf" target="_blank"><b>ST802RT1</b></a>.   </td></tr>
</table>
<h1><a class="anchor" id="driver_eth_multiple"></a>
Multiple Driver Instances</h1>
<p >CMSIS-Driver API supports multiple driver instances. The Ethernet drivers are implemented within a single C module and several driver instances of the same type can be used in a project as follows: </p><ul>
<li>
Add the first driver instance to the project. In IDEs with CMSIS-pack management support this can be done from the Run-Time Environment (RTE). </li>
<li>
Create a copy of the driver's .c file with a different file name and add it to the project. This will be the second driver instance. For example, copy <code>ETH_LAN9220.c</code> file as <code>ETH2_LAN9220.c</code>. </li>
<li>
Copy the driver's .h file to the project or add the driver's folder to the compiler include search path. </li>
<li>
Specify the driver parameters for the second instance. For example, in <code>ETH2_LAN9220.c</code> new values to the following parameters are needed instead of default ones: <div class="fragment"><div class="line"><span class="preprocessor">#define ETH_MAC_NUM             1</span></div>
<div class="line"><span class="preprocessor">#define ETH_PHY_NUM             1</span></div>
<div class="line"><span class="preprocessor">#define LAN9220_BASE            (0x53000000UL)</span></div>
</div><!-- fragment -->  </li>
<li>
Now both Ethernet instances can be accessed from the application. For example: <div class="fragment"><div class="line"><span class="preprocessor">#include &quot;Driver_ETH_MAC.h&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;Driver_ETH_PHY.h&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_ETH_MAC Driver_ETH_MAC0;</div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_ETH_MAC Driver_ETH_MAC1;</div>
<div class="line"> </div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_ETH_PHY Driver_ETH_PHY0;</div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_ETH_PHY Driver_ETH_PHY1;</div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#define eth0    (&amp;Driver_ETH_MAC0)</span></div>
<div class="line"><span class="preprocessor">#define eth1    (&amp;Driver_ETH_MAC1)</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#define phy0    (&amp;Driver_ETH_PHY0)</span></div>
<div class="line"><span class="preprocessor">#define phy1    (&amp;Driver_ETH_PHY1)</span></div>
</div><!-- fragment -->  </li>
</ul>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
