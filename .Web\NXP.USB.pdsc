<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>USB</name>
  <vendor>NXP</vendor>
  <description>Software Pack for usb</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="3.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="2.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.usb.common_header.condition_id">
      <require condition="allOf.component.osa.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
    </condition>
    <condition id="middleware.usb.device.audio.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.device.stack.external.internal_condition">
      <require Cclass="USB" Cgroup="USB Device" Csub="stack"/>
    </condition>
    <condition id="middleware.usb.device.ccid.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.cdc.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.cdc.rndis.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external, middleware.usb.device.cdc.external.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.device.stack.external, middleware.usb.device.cdc.external.internal_condition">
      <require Cclass="USB" Cgroup="USB Device" Csub="stack"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="cdc"/>
    </condition>
    <condition id="middleware.usb.device.common_header.condition_id">
      <require condition="allOf.component.osa, middleware.usb.common_header.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa, middleware.usb.common_header.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
      <require Cclass="USB" Cgroup="USB Common Header" Csub="common_header"/>
    </condition>
    <condition id="middleware.usb.device.controller.driver.condition_id">
      <require condition="allOf.component.osa, anyOf=middleware.usb.device.khci, middleware.usb.device.ehci, middleware.usb.device.ip3511fs, middleware.usb.device.ip3511hs.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa, anyOf=middleware.usb.device.khci, middleware.usb.device.ehci, middleware.usb.device.ip3511fs, middleware.usb.device.ip3511hs.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
      <require condition="anyOf.middleware.usb.device.khci, middleware.usb.device.ehci, middleware.usb.device.ip3511fs, middleware.usb.device.ip3511hs.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.usb.device.khci, middleware.usb.device.ehci, middleware.usb.device.ip3511fs, middleware.usb.device.ip3511hs.internal_condition">
      <accept Cclass="USB" Cgroup="USB Device" Csub="khci"/>
      <accept Cclass="USB" Cgroup="USB Device" Csub="ehci"/>
      <accept Cclass="USB" Cgroup="USB Device" Csub="ip3511fs"/>
      <accept Cclass="USB" Cgroup="USB Device" Csub="ip3511hs"/>
    </condition>
    <condition id="middleware.usb.device.dfu.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="device_id.MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236.internal_condition">
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011CAE4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011DAE5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition">
      <accept Dname="MCXN235VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011CAE4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011DAE5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.RW612, RW610.internal_condition">
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.device.ehci.condition_id">
      <require condition="anyOf.allOf=device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610, allOf=device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, driver.memory, device_id=RW612, RW610.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610, allOf=device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, driver.memory, device_id=RW612, RW610.internal_condition">
      <accept condition="allOf.device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610.internal_condition"/>
      <accept condition="allOf.device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, driver.memory, device_id=RW612, RW610.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610.internal_condition">
      <require condition="device_id.MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="ehci_config_header"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="common_header"/>
      <require condition="anyOf.allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
      <require condition="not.device_id=RW612, RW610.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition">
      <accept condition="allOf.middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
      <accept condition="not.device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition">
      <require Cclass="USB" Cgroup="USB PHY" Csub="phy"/>
      <require condition="device_id.MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="not.device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition">
      <deny condition="device_id.MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="not.device_id=RW612, RW610.internal_condition">
      <deny condition="device_id.RW612, RW610.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, middleware.usb.device.ehci_config_header, middleware.usb.device.common_header, driver.memory, device_id=RW612, RW610.internal_condition">
      <require condition="device_id.MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="ehci_config_header"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="common_header"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="memory"/>
      <require condition="device_id.RW612, RW610.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.ehci_config_header.condition_id">
      <require condition="allOf.device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236.internal_condition">
      <require condition="device_id.MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.hid.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="device_id.LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition">
      <accept Dname="LPC51U68JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC51U68JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54628J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC5512JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5512JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.device.ip3511fs.condition_id">
      <require condition="allOf.device_id=LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, middleware.usb.device.ip3511fs_config_header, middleware.usb.device.common_header.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, middleware.usb.device.ip3511fs_config_header, middleware.usb.device.common_header.internal_condition">
      <require condition="device_id.LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="ip3511fs_config_header"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="common_header"/>
    </condition>
    <condition id="middleware.usb.device.ip3511fs_config_header.condition_id">
      <require condition="allOf.device_id=LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition">
      <require condition="device_id.LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition">
      <accept Dname="LPC54005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54628J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.device.ip3511hs.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, middleware.usb.device.ip3511hs_config_header, middleware.usb.device.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, middleware.usb.device.ip3511hs_config_header, middleware.usb.device.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="ip3511hs_config_header"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="common_header"/>
      <require condition="anyOf.allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.ip3511hs_config_header.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
    </condition>
    <condition id="device_id.K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156.internal_condition">
      <accept Dname="K32L2B11VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VMP0A" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC243VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VDC10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLL10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VMP10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VMP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VMP12" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC142VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VLH" Dvendor="NXP:11"/>
      <accept Dname="K32L2A31VLH1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A31VLL1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A41VLH1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A41VLL1A" Dvendor="NXP:11"/>
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VPJ" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.device.khci.condition_id">
      <require condition="allOf.device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, middleware.usb.device.khci_config_header, middleware.usb.device.common_header.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, middleware.usb.device.khci_config_header, middleware.usb.device.common_header.internal_condition">
      <require condition="device_id.K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="khci_config_header"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="common_header"/>
    </condition>
    <condition id="middleware.usb.device.khci_config_header.condition_id">
      <require condition="allOf.device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156.internal_condition">
      <require condition="device_id.K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC143, MCXC144, MCXC243, MCXC244, MCXC443, MCXC444, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MCXC141, MCXC142, MCXC242, K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.msd.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.phdc.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.printer.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="middleware.usb.device.stack.external.condition_id">
      <require condition="allOf.component.osa, middleware.usb.device.controller.driver.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa, middleware.usb.device.controller.driver.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
      <require Cclass="USB" Cgroup="USB Device" Csub="driver"/>
    </condition>
    <condition id="middleware.usb.device.video.external.condition_id">
      <require condition="allOf.middleware.usb.device.stack.external.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.audio.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.host.stack.internal_condition">
      <require Cclass="USB" Cgroup="USB Host" Csub="stack"/>
    </condition>
    <condition id="middleware.usb.host.cdc.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.cdc_ecm.condition_id">
      <require condition="allOf.middleware.usb.host.stack, middleware.usb.host.cdc.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.host.stack, middleware.usb.host.cdc.internal_condition">
      <require Cclass="USB" Cgroup="USB Host" Csub="stack"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="cdc"/>
    </condition>
    <condition id="middleware.usb.host.cdc_rndis.condition_id">
      <require condition="allOf.middleware.usb.host.stack, middleware.usb.host.cdc.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.common_header.condition_id">
      <require condition="allOf.component.osa, middleware.usb.common_header.internal_condition"/>
    </condition>
    <condition id="device_id.MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011CAE4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011DAE5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.host.ehci.condition_id">
      <require condition="anyOf.allOf=device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610, allOf=device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, driver.memory, device_id=RW612, RW610.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610, allOf=device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, driver.memory, device_id=RW612, RW610.internal_condition">
      <accept condition="allOf.device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610.internal_condition"/>
      <accept condition="allOf.device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, driver.memory, device_id=RW612, RW610.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=RW612, RW610.internal_condition">
      <require condition="device_id.MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="ehci_config_header"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="common_header"/>
      <require condition="anyOf.allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
      <require condition="not.device_id=RW612, RW610.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, middleware.usb.host.ehci_config_header, middleware.usb.host.common_header, driver.memory, device_id=RW612, RW610.internal_condition">
      <require condition="device_id.MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="ehci_config_header"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="common_header"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="memory"/>
      <require condition="device_id.RW612, RW610.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.ehci_config_header.condition_id">
      <require condition="allOf.device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <require condition="device_id.MCXN546, MCXN547, MCXN946, MCXN947, MCXN235, MCXN236, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, RW610, RW612, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.hid.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.ip3516hs.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, middleware.usb.host.ip3516hs_config_header, middleware.usb.host.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, middleware.usb.host.ip3516hs_config_header, middleware.usb.host.common_header, anyOf=allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="ip3516hs_config_header"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="common_header"/>
      <require condition="anyOf.allOf=middleware.usb.phy, device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, not=device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.ip3516hs_config_header.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
    </condition>
    <condition id="device_id.K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12.internal_condition">
      <accept Dname="K32L2A31VLH1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A31VLL1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A41VLH1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A41VLL1A" Dvendor="NXP:11"/>
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VDC10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLL10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VMP10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VMP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VMP12" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.host.khci.condition_id">
      <require condition="allOf.device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, middleware.usb.host.khci_config_header, middleware.usb.host.common_header.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, middleware.usb.host.khci_config_header, middleware.usb.host.common_header.internal_condition">
      <require condition="device_id.K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="khci_config_header"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="common_header"/>
    </condition>
    <condition id="middleware.usb.host.khci_config_header.condition_id">
      <require condition="allOf.device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12.internal_condition">
      <require condition="device_id.K32L2A31xxxxA, K32L2A41xxxxA, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.msd.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition">
      <accept Dname="LPC54005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54628J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC5512JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5512JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.host.ohci.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, middleware.usb.host.ohci_config_header, middleware.usb.host.common_header.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, middleware.usb.host.ohci_config_header, middleware.usb.host.common_header.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="ohci_config_header"/>
      <require Cclass="USB" Cgroup="USB Host" Csub="common_header"/>
    </condition>
    <condition id="middleware.usb.host.ohci_config_header.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.phdc.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.printer.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="middleware.usb.host.stack.condition_id">
      <require condition="allOf.component.osa, anyOf=middleware.usb.host.khci, middleware.usb.host.ehci, middleware.usb.host.ohci, middleware.usb.host.ip3516hs.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa, anyOf=middleware.usb.host.khci, middleware.usb.host.ehci, middleware.usb.host.ohci, middleware.usb.host.ip3516hs.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
      <require condition="anyOf.middleware.usb.host.khci, middleware.usb.host.ehci, middleware.usb.host.ohci, middleware.usb.host.ip3516hs.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.usb.host.khci, middleware.usb.host.ehci, middleware.usb.host.ohci, middleware.usb.host.ip3516hs.internal_condition">
      <accept Cclass="USB" Cgroup="USB Host" Csub="khci"/>
      <accept Cclass="USB" Cgroup="USB Host" Csub="ehci"/>
      <accept Cclass="USB" Cgroup="USB Host" Csub="ohci"/>
      <accept Cclass="USB" Cgroup="USB Host" Csub="ip3516hs"/>
    </condition>
    <condition id="middleware.usb.host.video.condition_id">
      <require condition="allOf.middleware.usb.host.stack.internal_condition"/>
    </condition>
    <condition id="device_id.MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition">
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.hsdcd.condition_id">
      <require condition="allOf.middleware.usb.hsdcd_config_header, device_id=MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.hsdcd_config_header, device_id=MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition">
      <require Cclass="USB" Cgroup="USB DCD" Csub="hsdcd_config_header"/>
      <require condition="device_id.MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
    </condition>
    <condition id="device_id.MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.phy.condition_id">
      <require condition="allOf.device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, middleware.usb.common_header, anyOf=allOf=driver.memory, device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, not=device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69, middleware.usb.common_header, anyOf=allOf=driver.memory, device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, not=device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <require condition="device_id.MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, LPC5514, LPC5516, LPC5526, LPC5528, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S66, LPC55S69.internal_condition"/>
      <require Cclass="USB" Cgroup="USB Common Header" Csub="common_header"/>
      <require condition="anyOf.allOf=driver.memory, device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, not=device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=driver.memory, device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, not=device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <accept condition="allOf.driver.memory, device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
      <accept condition="not.device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="allOf.driver.memory, device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="memory"/>
      <require condition="device_id.MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="not.device_id=MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition">
      <deny condition="device_id.MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx.internal_condition"/>
    </condition>
    <condition id="device_id.MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition">
      <accept Dname="MIMXRT1011CAE4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011DAE5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.usb.phydcd.condition_id">
      <require condition="allOf.middleware.usb.phydcd_config_header, device_id=MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.phydcd_config_header, device_id=MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition">
      <require Cclass="USB" Cgroup="USB DCD" Csub="phydcd_config_header"/>
      <require condition="device_id.MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="USB" Cgroup="USB Common Header" Csub="common_header" Cversion="2.10.0" condition="middleware.usb.common_header.condition_id">
      <description>Middleware usb common_header</description>
      <files>
        <file category="header" name="middleware/usb/include/usb.h" projectpath="usb/include"/>
        <file category="header" name="middleware/usb/include/usb_misc.h" projectpath="usb/include"/>
        <file category="header" name="middleware/usb/include/usb_spec.h" projectpath="usb/include"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="audio" Cversion="2.10.0" condition="middleware.usb.device.audio.external.condition_id">
      <description>Middleware usb device audio external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_audio.c" projectpath="usb/device/class/audio"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_audio.h" projectpath="usb/device/class/audio"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ccid" Cversion="2.10.0" condition="middleware.usb.device.ccid.external.condition_id">
      <description>Middleware usb device ccid external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_ccid.c" projectpath="usb/device/class/ccid"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_ccid.h" projectpath="usb/device/class/ccid"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="cdc" Cversion="2.10.0" condition="middleware.usb.device.cdc.external.condition_id">
      <description>Middleware usb device cdc external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_cdc_acm.c" projectpath="usb/device/class/cdc"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_cdc_acm.h" projectpath="usb/device/class/cdc"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="cdc rndis" Cversion="2.10.0" condition="middleware.usb.device.cdc.rndis.external.condition_id">
      <description>Middleware usb device cdc rndis external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_cdc_rndis.c" projectpath="usb/device/class/cdc_rndis"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_cdc_rndis.h" projectpath="usb/device/class/cdc_rndis"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="common_header" Cversion="2.10.0" condition="middleware.usb.device.common_header.condition_id">
      <description>Middleware usb device common_header</description>
      <files>
        <file category="header" name="middleware/usb/device/usb_device.h" projectpath="usb/device/include"/>
        <file category="header" name="middleware/usb/device/usb_device_dci.h" projectpath="usb/device/source"/>
        <file category="include" name="middleware/usb/device/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="driver" Cversion="2.10.0" condition="middleware.usb.device.controller.driver.condition_id">
      <description>Middleware usb device controller driver</description>
      <files>
        <file category="header" name="middleware/usb/device/usb_device.h" projectpath="usb/device/include"/>
        <file category="sourceC" name="middleware/usb/device/usb_device_dci.c" projectpath="usb/device/source"/>
        <file category="header" name="middleware/usb/device/usb_device_dci.h" projectpath="usb/device/source"/>
        <file category="doc" name="middleware/usb/ChangeLogKSDK.txt" projectpath="usb"/>
        <file category="include" name="middleware/usb/device/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="dfu" Cversion="2.10.0" condition="middleware.usb.device.dfu.external.condition_id">
      <description>Middleware usb device dfu external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_dfu.c" projectpath="usb/device/class/dfu"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_dfu.h" projectpath="usb/device/class/dfu"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ehci" Cversion="2.10.0" condition="middleware.usb.device.ehci.condition_id">
      <description>Middleware usb device ehci</description>
      <files>
        <file category="sourceC" name="middleware/usb/device/usb_device_ehci.c" projectpath="usb/device/source/ehci"/>
        <file category="header" name="middleware/usb/device/usb_device_ehci.h" projectpath="usb/device/source/ehci"/>
        <file category="include" name="middleware/usb/device/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ehci_config_header" Cversion="2.10.0" condition="middleware.usb.device.ehci_config_header.condition_id">
      <description>USB device ehci config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/device_config/ehci/usb_device_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="hid" Cversion="2.10.0" condition="middleware.usb.device.hid.external.condition_id">
      <description>Middleware usb device hid external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_hid.c" projectpath="usb/device/class/hid"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_hid.h" projectpath="usb/device/class/hid"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ip3511fs" Cversion="2.10.0" condition="middleware.usb.device.ip3511fs.condition_id">
      <description>Middleware usb device ip3511fs</description>
      <files>
        <file category="sourceC" name="middleware/usb/device/usb_device_lpcip3511.c" projectpath="usb/device/source/lpcip3511"/>
        <file category="header" name="middleware/usb/device/usb_device_lpcip3511.h" projectpath="usb/device/source/lpcip3511"/>
        <file category="include" name="middleware/usb/device/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ip3511fs_config_header" Cversion="2.10.0" condition="middleware.usb.device.ip3511fs_config_header.condition_id">
      <description>USB device ip3511fs config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/device_config/ip3511fs/usb_device_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ip3511hs" Cversion="2.10.0" condition="middleware.usb.device.ip3511hs.condition_id">
      <description>Middleware usb device ip3511hs</description>
      <files>
        <file category="sourceC" name="middleware/usb/device/usb_device_lpcip3511.c" projectpath="usb/device/source/lpcip3511"/>
        <file category="header" name="middleware/usb/device/usb_device_lpcip3511.h" projectpath="usb/device/source/lpcip3511"/>
        <file category="include" name="middleware/usb/device/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="ip3511hs_config_header" Cversion="2.10.0" condition="middleware.usb.device.ip3511hs_config_header.condition_id">
      <description>USB device ip3511hs config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/device_config/ip3511hs/usb_device_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="khci" Cversion="2.10.0" condition="middleware.usb.device.khci.condition_id">
      <description>Middleware usb device khci</description>
      <files>
        <file category="sourceC" name="middleware/usb/device/usb_device_khci.c" projectpath="usb/device/source/khci"/>
        <file category="header" name="middleware/usb/device/usb_device_khci.h" projectpath="usb/device/source/khci"/>
        <file category="header" name="middleware/usb/include/usb.h" projectpath="usb/include"/>
        <file category="include" name="middleware/usb/device/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="khci_config_header" Cversion="2.10.0" condition="middleware.usb.device.khci_config_header.condition_id">
      <description>USB device khci config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/device_config/khci/usb_device_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="msd" Cversion="2.10.0" condition="middleware.usb.device.msd.external.condition_id">
      <description>Middleware usb device msd external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_msc.c" projectpath="usb/device/class/msc"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_msc.h" projectpath="usb/device/class/msc"/>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_msc_ufi.c" projectpath="usb/device/class/msc"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_msc_ufi.h" projectpath="usb/device/class/msc"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="phdc" Cversion="2.10.0" condition="middleware.usb.device.phdc.external.condition_id">
      <description>Middleware usb device phdc external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_phdc.c" projectpath="usb/device/class/phdc"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_phdc.h" projectpath="usb/device/class/phdc"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="printer" Cversion="2.10.0" condition="middleware.usb.device.printer.external.condition_id">
      <description>Middleware usb device printer external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_printer.c" projectpath="usb/device/class/printer"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_printer.h" projectpath="usb/device/class/printer"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="stack" Cversion="2.10.0" condition="middleware.usb.device.stack.external.condition_id">
      <description>Middleware usb device stack external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_class.c" projectpath="usb/device/class"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_class.h" projectpath="usb/device/class"/>
        <file category="sourceC" name="middleware/usb/output/source/device/usb_device_ch9.c" projectpath="usb/device/source"/>
        <file category="header" name="middleware/usb/output/source/device/usb_device_ch9.h" projectpath="usb/device/source"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
        <file category="include" name="middleware/usb/output/source/device/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Device" Csub="video" Cversion="2.10.0" condition="middleware.usb.device.video.external.condition_id">
      <description>Middleware usb device video external</description>
      <files>
        <file category="sourceC" name="middleware/usb/output/source/device/class/usb_device_video.c" projectpath="usb/device/class/video"/>
        <file category="header" name="middleware/usb/output/source/device/class/usb_device_video.h" projectpath="usb/device/class/video"/>
        <file category="include" name="middleware/usb/output/source/device/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="audio" Cversion="2.10.0" condition="middleware.usb.host.audio.condition_id">
      <description>Middleware usb host audio</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_audio.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_audio.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="cdc" Cversion="2.10.0" condition="middleware.usb.host.cdc.condition_id">
      <description>Middleware usb host cdc</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_cdc.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_cdc.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="cdc_ecm" Cversion="2.9.1" condition="middleware.usb.host.cdc_ecm.condition_id">
      <description>Middleware usb host cdc_ecm</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_cdc_ecm.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_cdc_ecm.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="cdc_rndis" Cversion="2.10.0" condition="middleware.usb.host.cdc_rndis.condition_id">
      <description>Middleware usb host cdc_rndis</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_cdc_rndis.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_cdc_rndis.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="common_header" Cversion="2.10.0" condition="middleware.usb.host.common_header.condition_id">
      <description>Middleware usb host common_header</description>
      <files>
        <file category="header" name="middleware/usb/host/usb_host.h" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_hci.h" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_devices.h" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_framework.h" projectpath="usb/host"/>
        <file category="include" name="middleware/usb/host/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="ehci" Cversion="2.10.0" condition="middleware.usb.host.ehci.condition_id">
      <description>Middleware usb host ehci</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/usb_host_ehci.c" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_ehci.h" projectpath="usb/host"/>
        <file category="include" name="middleware/usb/host/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="ehci_config_header" Cversion="2.10.0" condition="middleware.usb.host.ehci_config_header.condition_id">
      <description>USB host ehci config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/host_config/ehci/usb_host_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="hid" Cversion="2.10.0" condition="middleware.usb.host.hid.condition_id">
      <description>Middleware usb host hid</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_hid.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_hid.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="ip3516hs" Cversion="2.10.0" condition="middleware.usb.host.ip3516hs.condition_id">
      <description>Middleware usb host ip3516hs</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/usb_host_ip3516hs.c" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_ip3516hs.h" projectpath="usb/host"/>
        <file category="include" name="middleware/usb/host/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="ip3516hs_config_header" Cversion="2.10.0" condition="middleware.usb.host.ip3516hs_config_header.condition_id">
      <description>USB host ip3516hs config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/host_config/ip3516hs/usb_host_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="khci" Cversion="2.10.0" condition="middleware.usb.host.khci.condition_id">
      <description>Middleware usb host khci</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/usb_host_khci.c" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_khci.h" projectpath="usb/host"/>
        <file category="include" name="middleware/usb/host/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="khci_config_header" Cversion="2.10.0" condition="middleware.usb.host.khci_config_header.condition_id">
      <description>USB host khci config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/host_config/khci/usb_host_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="msd" Cversion="2.10.0" condition="middleware.usb.host.msd.condition_id">
      <description>Middleware usb host msd</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_msd.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_msd.h" projectpath="usb/host/class"/>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_msd_ufi.c" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="ohci" Cversion="2.10.0" condition="middleware.usb.host.ohci.condition_id">
      <description>Middleware usb host ohci</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/usb_host_ohci.c" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_ohci.h" projectpath="usb/host"/>
        <file category="include" name="middleware/usb/host/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="ohci_config_header" Cversion="2.10.0" condition="middleware.usb.host.ohci_config_header.condition_id">
      <description>USB host ohci config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/host_config/ohci/usb_host_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="phdc" Cversion="2.10.0" condition="middleware.usb.host.phdc.condition_id">
      <description>Middleware usb host phdc</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_phdc.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_phdc.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="printer" Cversion="2.10.0" condition="middleware.usb.host.printer.condition_id">
      <description>Middleware usb host printer</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_printer.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_printer.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="stack" Cversion="2.10.0" condition="middleware.usb.host.stack.condition_id">
      <description>Middleware usb host stack</description>
      <files>
        <file category="header" name="middleware/usb/host/usb_host.h" projectpath="usb/host"/>
        <file category="sourceC" name="middleware/usb/host/usb_host_hci.c" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_hci.h" projectpath="usb/host"/>
        <file category="sourceC" name="middleware/usb/host/usb_host_devices.c" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_devices.h" projectpath="usb/host"/>
        <file category="header" name="middleware/usb/host/usb_host_framework.h" projectpath="usb/host"/>
        <file category="sourceC" name="middleware/usb/host/usb_host_framework.c" projectpath="usb/host"/>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_hub.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_hub.h" projectpath="usb/host/class"/>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_hub_app.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_hub_app.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/"/>
        <file category="include" name="middleware/usb/host/class/"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Host" Csub="video" Cversion="2.10.0" condition="middleware.usb.host.video.condition_id">
      <description>Middleware usb host video</description>
      <files>
        <file category="sourceC" name="middleware/usb/host/class/usb_host_video.c" projectpath="usb/host/class"/>
        <file category="header" name="middleware/usb/host/class/usb_host_video.h" projectpath="usb/host/class"/>
        <file category="include" name="middleware/usb/host/class/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB DCD" Csub="hsdcd_driver" Cversion="2.8.4" condition="middleware.usb.hsdcd.condition_id">
      <description>Middleware usb HSDCD (Select manually if needed)</description>
      <files>
        <file category="sourceC" name="middleware/usb/dcd/usb_hsdcd.c" projectpath="usb/dcd"/>
        <file category="header" name="middleware/usb/dcd/usb_hsdcd.h" projectpath="usb/dcd"/>
        <file category="header" name="middleware/usb/dcd/usb_charger_detect.h" projectpath="usb/dcd"/>
        <file category="include" name="middleware/usb/dcd/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB DCD" Csub="hsdcd_config_header" Cversion="2.8.4">
      <description>USB device hsdcd config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/dcd_config/hsdcd/usb_hsdcd_config.h" version="2.8.4" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB PHY" Csub="phy" Cversion="2.10.0" condition="middleware.usb.phy.condition_id">
      <description>Middleware usb phy</description>
      <files>
        <file category="sourceC" name="middleware/usb/phy/usb_phy.c" projectpath="usb/phy"/>
        <file category="header" name="middleware/usb/phy/usb_phy.h" projectpath="usb/phy"/>
        <file category="include" name="middleware/usb/phy/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB DCD" Csub="phydcd_driver" Cversion="2.8.4" condition="middleware.usb.phydcd.condition_id">
      <description>Middleware usb PHYDCD (Select manually if needed)</description>
      <files>
        <file category="sourceC" name="middleware/usb/dcd/usb_phydcd.c" projectpath="usb/dcd"/>
        <file category="header" name="middleware/usb/dcd/usb_phydcd.h" projectpath="usb/dcd"/>
        <file category="header" name="middleware/usb/dcd/usb_charger_detect.h" projectpath="usb/dcd"/>
        <file category="include" name="middleware/usb/dcd/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB DCD" Csub="phydcd_config_header" Cversion="2.8.4">
      <description>USB device phydcd config header</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/dcd_config/phydcd/usb_phydcd_config.h" version="2.8.4" projectpath="source/generated"/>
      </files>
    </component>
  </components>
</package>
