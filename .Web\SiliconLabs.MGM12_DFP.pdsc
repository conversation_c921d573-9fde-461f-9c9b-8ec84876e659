<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>MGM12_DFP</name>
  <description>Silicon Labs MGM12 Mighty Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_MGM12_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_MGM12_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>MGM12</keyword>
    <keyword>MGM12</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="MGM12 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="38400000"/>
      <book name="Documents/cortex_m4_dgug.pdf"      title="Cortex-M4 Generic User Guide"/>
      <book name="Documents/efr32xg12-rm.pdf"  title="MGM12 Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M4 core with 40 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in several footprint compatible QFN packages&#xD;&#xA;- 12-channel Peripheral Reflex System enabling autonomous interaction of MCU peripherals&#xD;&#xA;- Autonomous Hardware Crypto Accelerator and Random Number Generator&#xD;&#xA;- Integrated balun for 2.4 GHz and integrated PA with up to 19.5 dBm transmit power for 2.4 GHz and 20 dBm transmit power for Sub-GHz radios&#xD;&#xA;- Integrated DC-DC with RF noise mitigation&#xD;&#xA;&#xD;&#xA;The Wireless Gecko portfolio of SoCs (EFR32) include Mighty Gecko (EFR32MG12), Blue Gecko (EFR32BG12), and Flex Gecko (EFR32FG12) families. With support for ZigBee, Thread, Bluetooth Low Energy (BLE) and proprietary protocols, the Wireless Gecko portfolio is ideal for enabling energy-friendly wireless networking for IoT devices. The single-die solution provides industry-leading energy efficiency, ultra-fast wakeup times, a scalable high-power amplifier, an integrated balun and no-compromise MCU features.
      </description>

      <subFamily DsubFamily="MGM12P">
        <book         name="Documents/mgm12p-datasheet.pdf"      title="MGM12P Data Sheet"/>
        <book         name="Documents/mgm12p-errata.pdf"         title="MGM12P Errata"/>
        <!-- *************************  Device 'MGM12P02F1024GA'  ***************************** -->
        <device Dname="MGM12P02F1024GA">
          <compile header="Device/SiliconLabs/MGM12/Include/em_device.h"  define="MGM12P02F1024GA"/>
          <debug      svd="SVD/MGM12/MGM12P02F1024GA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM12P02F1024GE'  ***************************** -->
        <device Dname="MGM12P02F1024GE">
          <compile header="Device/SiliconLabs/MGM12/Include/em_device.h"  define="MGM12P02F1024GE"/>
          <debug      svd="SVD/MGM12/MGM12P02F1024GE.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM12P22F1024GA'  ***************************** -->
        <device Dname="MGM12P22F1024GA">
          <compile header="Device/SiliconLabs/MGM12/Include/em_device.h"  define="MGM12P22F1024GA"/>
          <debug      svd="SVD/MGM12/MGM12P22F1024GA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM12P22F1024GE'  ***************************** -->
        <device Dname="MGM12P22F1024GE">
          <compile header="Device/SiliconLabs/MGM12/Include/em_device.h"  define="MGM12P22F1024GE"/>
          <debug      svd="SVD/MGM12/MGM12P22F1024GE.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM12P32F1024GA'  ***************************** -->
        <device Dname="MGM12P32F1024GA">
          <compile header="Device/SiliconLabs/MGM12/Include/em_device.h"  define="MGM12P32F1024GA"/>
          <debug      svd="SVD/MGM12/MGM12P32F1024GA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM12P32F1024GE'  ***************************** -->
        <device Dname="MGM12P32F1024GE">
          <compile header="Device/SiliconLabs/MGM12/Include/em_device.h"  define="MGM12P32F1024GE"/>
          <debug      svd="SVD/MGM12/MGM12P32F1024GE.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="MGM12">
      <description>Silicon Labs MGM12 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="MGM12*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="MGM12">
      <description>System Startup for Silicon Labs MGM12 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/MGM12/Include/"/>
        <file category="header"  name="Device/SiliconLabs/MGM12/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/MGM12/Source/ARM/startup_mgm12.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/MGM12/Source/GCC/startup_mgm12.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/MGM12/Source/GCC/mgm12.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/MGM12/Source/system_mgm12.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
