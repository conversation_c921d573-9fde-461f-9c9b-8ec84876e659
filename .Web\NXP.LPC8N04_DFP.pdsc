<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPC8N04_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for LPC8N04</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="LPC8N04" Dvendor="NXP:11">
      <description>Low-Cost Microcontrollers (MCUs) based on Arm Cortex-M0+ Core</description>
      <device Dname="LPC8N04">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="8000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/LPC8N04_flash.icf"/>
        </environment>
        <memory name="IAP_SRAM" start="0x10001fe0" size="0x20" access="rw" default="1"/>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x8000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x10000000" size="0x1fe0" access="rw" default="1"/>
        <algorithm name="arm/LPC8N04_30.FLM" start="0x00000000" size="0x00008000" RAMstart="0x10000000" RAMsize="0x00001000" default="1"/>
        <debug svd="LPC8N04.xml"/>
        <variant Dvariant="LPC8N04FHI24">
          <compile header="fsl_device_registers.h" define="CPU_LPC8N04FHI24"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.LPC8N04">
      <accept Dname="LPC8N04" Dvariant="LPC8N04FHI24" Dvendor="NXP:11"/>
      <accept Dname="LPC8N04FHI24" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC8N04_AND_device.LPC8N04_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_1_AND_driver.lpc_iocon_AND_driver.reset">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="Startup" Csub="LPC8N04_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
    </condition>
    <condition id="device.LPC8N04_AND_component.osa_AND_driver.common">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="osa"/>
    </condition>
    <condition id="device.LPC8N04_AND_driver.common_AND_driver.ctimer">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer"/>
    </condition>
    <condition id="device.LPC8N04_AND_driver.common">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.LPC8N04_AND_component.lists_AND_driver.common">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.LPC8N04_AND_component.ctimer_adapter_AND_component.lists_AND_driver.common">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer_adapter"/>
    </condition>
    <condition id="device.LPC8N04_AND_CMSIS_Include_core_cm0plus">
      <require condition="device.LPC8N04"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="armclang_OR_iar">
      <accept Tcompiler="ARMCC" Toptions="AC6"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="device.LPC8N04_AND__armclang_OR_iar_">
      <require condition="device.LPC8N04"/>
      <require condition="armclang_OR_iar"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="device.LPC8N04_AND_driver.common_AND_driver.power_no_lib">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
    </condition>
    <condition id="device.LPC8N04_AND_device.LPC8N04_CMSIS_AND_driver.clock_AND_driver.reset">
      <require condition="device.LPC8N04"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="LPC8N04_header"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="LPC8N04" Cversion="1.0.0" condition="device.LPC8N04_AND_device.LPC8N04_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_1_AND_driver.lpc_iocon_AND_driver.reset" isDefaultVariant="1">
      <description>Devices_project_template LPC8N04</description>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common_task" Cversion="1.0.0" condition="device.LPC8N04_AND_component.osa_AND_driver.common">
      <description>Component common_task</description>
      <files>
        <file category="sourceC" name="components/common_task/common_task.c"/>
        <file category="header" name="components/common_task/common_task.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer_adapter" Cversion="1.0.0" condition="device.LPC8N04_AND_driver.common_AND_driver.ctimer">
      <description>Component ctimer_adapter</description>
      <files>
        <file category="sourceC" name="components/timer/ctimer_adapter.c"/>
        <file category="header" name="components/timer/timer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.LPC8N04_AND_driver.common">
      <description>Component lists</description>
      <files>
        <file category="sourceC" name="components/lists/generic_list.c"/>
        <file category="header" name="components/lists/generic_list.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager" Cversion="1.0.0" condition="device.LPC8N04_AND_component.lists_AND_driver.common">
      <description>Component mem_manager</description>
      <files>
        <file category="sourceC" name="components/mem_manager/mem_manager.c"/>
        <file category="header" name="components/mem_manager/mem_manager.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa" Cversion="1.0.0" condition="device.LPC8N04_AND_component.lists_AND_driver.common">
      <description>Component osa</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa_bm" Cversion="1.0.0" condition="device.LPC8N04_AND_component.lists_AND_driver.common">
      <description>Component osa_bm</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pwm_ctimer_adapter" Cversion="1.0.0" condition="device.LPC8N04_AND_driver.common_AND_driver.ctimer">
      <description>Component pwm_ctimer_adapter</description>
      <files>
        <file category="header" name="components/pwm/pwm.h"/>
        <file category="sourceC" name="components/pwm/pwm_ctimer_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_crc_adapter" Cversion="1.0.0" condition="device.LPC8N04_AND_driver.common">
      <description>Component software_crc_adapter</description>
      <files>
        <file category="header" name="components/crc/crc.h"/>
        <file category="sourceC" name="components/crc/software_crc_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager" Cversion="1.0.0" condition="device.LPC8N04_AND_component.ctimer_adapter_AND_component.lists_AND_driver.common">
      <description>Component timer_manager</description>
      <files>
        <file category="sourceC" name="components/timer_manager/timer_manager.c"/>
        <file category="header" name="components/timer_manager/timer_manager.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="LPC8N04_header" Cversion="1.0.0" condition="device.LPC8N04_AND_CMSIS_Include_core_cm0plus">
      <description>Device LPC8N04_cmsis</description>
      <files>
        <file category="header" name="LPC8N04.h"/>
        <file category="header" name="LPC8N04_features.h"/>
        <file category="header" name="fsl_device_registers.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Csub="LPC8N04_startup" Cversion="1.1.0" condition="device.LPC8N04_AND__armclang_OR_iar_">
      <description>Device LPC8N04_startup</description>
      <files>
        <file condition="mdk" category="sourceAsm" name="arm/startup_LPC8N04.S"/>
        <file condition="iar" category="sourceAsm" name="iar/startup_LPC8N04.s"/>
        <file category="sourceC" name="system_LPC8N04.c"/>
        <file category="header" name="system_LPC8N04.h"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/LPC8N04_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/LPC8N04_ram.scf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/LPC8N04_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/LPC8N04_ram.icf" version="1.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.0.4" condition="device.LPC8N04_AND_driver.common_AND_driver.power_no_lib">
      <description>Clock Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
        <file category="header" name="drivers/fsl_clock.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.2.4" condition="device.LPC8N04_AND_device.LPC8N04_CMSIS_AND_driver.clock_AND_driver.reset">
      <description>COMMON Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_common.c"/>
        <file category="header" name="drivers/fsl_common.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer" Cversion="2.0.3" condition="device.LPC8N04_AND_driver.common">
      <description>CTimer Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ctimer.c"/>
        <file category="header" name="drivers/fsl_ctimer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="eeprom" Cversion="2.1.1" condition="device.LPC8N04_AND_driver.common">
      <description>eeprom Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_eeprom.c"/>
        <file category="header" name="drivers/fsl_eeprom.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="iap" Cversion="2.0.4" condition="device.LPC8N04_AND_driver.common">
      <description>IAP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_iap.c"/>
        <file category="header" name="drivers/fsl_iap.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.0.0" condition="device.LPC8N04_AND_driver.common">
      <description>GPIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
        <file category="header" name="drivers/fsl_gpio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.0.1" condition="device.LPC8N04_AND_driver.common">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c.c"/>
        <file category="header" name="drivers/fsl_i2c.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="iocon" Cversion="2.1.1" condition="device.LPC8N04_AND_driver.common">
      <description>IOCON Driver</description>
      <files>
        <file category="header" name="drivers/fsl_iocon.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_rtc" Cversion="2.0.2" condition="device.LPC8N04_AND_driver.common">
      <description>RTC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rtc.c"/>
        <file category="header" name="drivers/fsl_rtc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi" Cversion="2.0.1" condition="device.LPC8N04_AND_driver.common">
      <description>SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_spi.c"/>
        <file category="header" name="drivers/fsl_spi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="nfc" Cversion="2.0.1" condition="device.LPC8N04_AND_driver.common">
      <description>NFC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_nfc.c"/>
        <file category="header" name="drivers/fsl_nfc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="power" Cversion="2.0.0" condition="device.LPC8N04_AND_driver.common">
      <description>Power Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_power.c"/>
        <file category="header" name="drivers/fsl_power.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="reset" Cversion="2.0.1" condition="device.LPC8N04_AND_driver.common">
      <description>Reset Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_reset.c"/>
        <file category="header" name="drivers/fsl_reset.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="tsens" Cversion="2.0.1" condition="device.LPC8N04_AND_driver.common">
      <description>tsens Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_tsens.c"/>
        <file category="header" name="drivers/fsl_tsens.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wdt" Cversion="2.0.1" condition="device.LPC8N04_AND_driver.common">
      <description>WDT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_wdt.c"/>
        <file category="header" name="drivers/fsl_wdt.h"/>
      </files>
    </component>
  </components>
</package>
