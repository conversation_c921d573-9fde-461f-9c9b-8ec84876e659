<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXN947_FMSTR_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware fmstr Examples Pack for FRDM-MCXN947</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="FRDM-MCXN947_BSP" vendor="NXP" version="19.0.0"/>
      <package name="FREEMASTER" vendor="NXP" version="2.0.0"/>
      <package name="MCXN947_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="LWIP" vendor="NXP" version="3.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="fmstr_example_any_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_any/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example fully configured by MCUXpresso ConfigTools. Serial communication is used by default, but it can be changed easily to CAN or other in the MCUXpresso Peripheral Tool. Also FreeMASTER driver features...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_any_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_any_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_can_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_can/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example using the CAN bus to communicate with target microcontroller. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow by...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_can_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_can_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_net_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_net/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example using TCP/UDP socket communication with the target microcontroller. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_net_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_net_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_pdbdm_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_pdbdm/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example using a special packet-driven protocol on top of JTAG or BDM direct memory access. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_pdbdm_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_pdbdm_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_rtt_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_rtt/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example using Segger RTT communication over J-Link interface. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow by modifying...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_rtt_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_rtt_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_uart_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_uart/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example using Serial-UART communication with the target microcontroller. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow by...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_uart_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_uart_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_usb_cdc_cm33_core0" folder="boards/frdmmcxn947/freemaster_examples/fmstr_usb_cdc/cm33_core0" doc="readme.txt">
      <description>FreeMASTER example using virtual serial communication at USB port and CDC VCOM class. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_usb_cdc_cm33_core0.uvprojx"/>
        <environment name="csolution" load="fmstr_example_usb_cdc_cm33_core0.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
