<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MBEDTLS3X</name>
  <vendor>NXP</vendor>
  <description>Software Pack for mbedtls3x</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Security" Cgroup="MbedTLSv3 library">Mbed_TLS_3 NXP</description>
  </taxonomy>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.mbedtls3x.condition_id">
      <require condition="allOf.middleware.mbedtls3x.template, middleware.mbedtls3x.crypto, middleware.mbedtls3x.ssl, middleware.mbedtls3x.x509.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.template, middleware.mbedtls3x.crypto, middleware.mbedtls3x.ssl, middleware.mbedtls3x.x509.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="template"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="ssl"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="x509"/>
    </condition>
    <condition id="middleware.mbedtls3x.crypto.condition_id">
      <require condition="allOf.middleware.mbedtls3x.crypto.no_psa.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.crypto.no_psa.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto_no_psa"/>
    </condition>
    <condition id="middleware.mbedtls3x.crypto_storage_default.condition_id">
      <require condition="allOf.middleware.mbedtls3x.crypto.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.crypto.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto"/>
    </condition>
    <condition id="middleware.mbedtls3x.crypto_storage_fatfs.condition_id">
      <require condition="allOf.middleware.mbedtls3x.crypto, driver.usdhc, component.rgpio_adapter, middleware.sdmmc.host.usdhc, middleware.sdmmc.host.usdhc.interrupt, middleware.sdmmc.sd, middleware.fatfs.sd, middleware.fatfs.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.crypto, driver.usdhc, component.rgpio_adapter, middleware.sdmmc.host.usdhc, middleware.sdmmc.host.usdhc.interrupt, middleware.sdmmc.sd, middleware.fatfs.sd, middleware.fatfs.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdhc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rgpio_adapter"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_interrupt"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="sd"/>
      <require Cclass="File System" Cgroup="FAT File System" Csub="sd"/>
      <require Cclass="File System" Cgroup="FAT File System" Csub="fatfs"/>
    </condition>
    <condition id="middleware.mbedtls3x.crypto_storage_ram.condition_id">
      <require condition="allOf.middleware.mbedtls3x.crypto.internal_condition"/>
    </condition>
    <condition id="middleware.mbedtls3x.no_psa.condition_id">
      <require condition="allOf.middleware.mbedtls3x.template, middleware.mbedtls3x.crypto.no_psa, middleware.mbedtls3x.ssl.no_psa, middleware.mbedtls3x.x509.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.template, middleware.mbedtls3x.crypto.no_psa, middleware.mbedtls3x.ssl.no_psa, middleware.mbedtls3x.x509.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="template"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto_no_psa"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="ssl_no_psa"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="x509"/>
    </condition>
    <condition id="device_id.LPC55S16.internal_condition">
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MIMXRT685S, RW610, RW612.internal_condition">
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition">
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.mbedtls3x.port.rng.condition_id">
      <require condition="allOf.middleware.mbedtls3x, middleware.mbedtls3x.port.config, anyOf=allOf=driver.rng_1, device_id=LPC55S16, allOf=driver.trng, device_id=MIMXRT685S, RW610, RW612, not=device_id=LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x, middleware.mbedtls3x.port.config, anyOf=allOf=driver.rng_1, device_id=LPC55S16, allOf=driver.trng, device_id=MIMXRT685S, RW610, RW612, not=device_id=LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="mbedtls3x"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="config"/>
      <require condition="anyOf.allOf=driver.rng_1, device_id=LPC55S16, allOf=driver.trng, device_id=MIMXRT685S, RW610, RW612, not=device_id=LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=driver.rng_1, device_id=LPC55S16, allOf=driver.trng, device_id=MIMXRT685S, RW610, RW612, not=device_id=LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition">
      <accept condition="allOf.driver.rng_1, device_id=LPC55S16.internal_condition"/>
      <accept condition="allOf.driver.trng, device_id=MIMXRT685S, RW610, RW612.internal_condition"/>
      <accept condition="not.device_id=LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition"/>
    </condition>
    <condition id="allOf.driver.rng_1, device_id=LPC55S16.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rng"/>
      <require condition="device_id.LPC55S16.internal_condition"/>
    </condition>
    <condition id="allOf.driver.trng, device_id=MIMXRT685S, RW610, RW612.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="trng"/>
      <require condition="device_id.MIMXRT685S, RW610, RW612.internal_condition"/>
    </condition>
    <condition id="not.device_id=LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition">
      <deny condition="device_id.LPC55S16, MIMXRT685S, RW610, RW612, MIMXRT1189xxxxx, MIMXRT1187xxxxx.internal_condition"/>
    </condition>
    <condition id="middleware.mbedtls3x.ssl.condition_id">
      <require condition="allOf.middleware.mbedtls3x.ssl.no_psa, middleware.mbedtls3x.crypto, middleware.mbedtls3x.x509.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.ssl.no_psa, middleware.mbedtls3x.crypto, middleware.mbedtls3x.x509.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="ssl_no_psa"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="x509"/>
    </condition>
    <condition id="middleware.mbedtls3x.ssl.no_psa.condition_id">
      <require condition="allOf.middleware.mbedtls3x.crypto.no_psa, middleware.mbedtls3x.x509.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.mbedtls3x.crypto.no_psa, middleware.mbedtls3x.x509.internal_condition">
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="crypto_no_psa"/>
      <require Cclass="Security" Cgroup="MbedTLSv3 library" Csub="x509"/>
    </condition>
  </conditions>
  <components>
    <bundle Cbundle="Mbed_TLS_3 NXP" Cclass="Security" Cversion="3.6.0">
      <description>Mbed_TLS_3 NXP</description>
      <doc>middleware/mbedtls3x/Mbed_TLS_3 NXP_dummy.txt</doc>
      <component Cgroup="MbedTLSv3 library" Csub="mbedtls3x" Cversion="3.6.0" condition="middleware.mbedtls3x.condition_id">
        <description>mbedTLS library v3.x</description>
        <files>
          <file category="doc" name="middleware/mbedtls3x/middleware.mbedtls3x_dummy.txt"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="crypto" Cversion="3.6.0" condition="middleware.mbedtls3x.crypto.condition_id">
        <description>mbedTLS crypto library</description>
        <files>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_aead.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_cipher.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_client.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_driver_wrappers_no_static.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_ecp.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_ffdh.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_hash.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_mac.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_pake.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_rsa.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_se.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_slot_management.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_crypto_storage.c" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="crypto_no_psa" Cversion="3.6.0">
        <description>mbedTLS crypto library without PSA</description>
        <files>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/aes.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/aria.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/asn1.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/asn1write.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/base64.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/bignum.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/block_cipher.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/build_info.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/camellia.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ccm.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/chacha20.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/chachapoly.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/check_config.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/cipher.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/cmac.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/compat-2.x.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_adjust_legacy_crypto.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_adjust_legacy_from_psa.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_adjust_psa_from_legacy.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_adjust_psa_superset_legacy.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_adjust_ssl.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_adjust_x509.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/config_psa.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/constant_time.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ctr_drbg.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/debug.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/des.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/dhm.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ecdh.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ecdsa.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ecjpake.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ecp.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/entropy.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/error.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/gcm.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/hkdf.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/hmac_drbg.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/lms.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/mbedtls_config.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/md.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/md5.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/memory_buffer_alloc.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/nist_kw.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/oid.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/pem.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/pk.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/pkcs5.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/pkcs12.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/platform.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/platform_time.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/platform_util.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/poly1305.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/private_access.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/psa_util.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ripemd160.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/rsa.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/sha1.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/sha3.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/sha256.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/sha512.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/threading.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/timing.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/version.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/build_info.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_adjust_auto_enabled.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_adjust_config_key_pair_types.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_adjust_config_synonyms.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_builtin_composites.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_builtin_key_derivation.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_builtin_primitives.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_compat.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_config.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_driver_common.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_driver_contexts_composites.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_driver_contexts_key_derivation.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_driver_contexts_primitives.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_extra.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_legacy.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_platform.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_se_driver.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_sizes.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_struct.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_types.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/psa/crypto_values.h" projectpath="mbedtls3x/include/psa" path="middleware/mbedtls3x/include"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/aes.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/aesni.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/aesni.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/alignment.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/aesce.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/aesce.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/aria.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/asn1parse.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/asn1write.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/base64.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/base64_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/bignum.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/bignum_core.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/bignum_core.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/bignum_mod.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/bignum_mod.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/bignum_mod_raw.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/bignum_mod_raw.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/bignum_mod_raw_invasive.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/block_cipher.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/block_cipher_internal.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/bn_mul.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/camellia.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ccm.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/chacha20.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/chachapoly.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/check_crypto_config.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/cipher.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/cipher_wrap.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/cipher_wrap.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/cmac.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/common.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/constant_time.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/constant_time_impl.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/constant_time_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ctr_drbg.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ctr.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/des.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/dhm.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ecdh.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ecdsa.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ecjpake.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ecp.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ecp_curves.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ecp_curves_new.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ecp_internal_alt.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ecp_invasive.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/entropy.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/entropy_poll.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/entropy_poll.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/error.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/gcm.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/hkdf.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/hmac_drbg.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/lmots.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/lmots.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/lms.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/md.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/md_psa.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/md_wrap.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/md5.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/memory_buffer_alloc.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/nist_kw.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/oid.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/padlock.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/padlock.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pem.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pk.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pk_ecc.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/pk_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pk_wrap.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/pk_wrap.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pkcs5.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pkcs12.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pkparse.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pkwrite.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/pkwrite.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/platform.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/platform_util.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/poly1305.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_aead.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_cipher.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_core.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_core_common.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_driver_wrappers_no_static.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_driver_wrappers.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_ecp.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_ffdh.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_hash.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_invasive.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_its.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_mac.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_pake.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_random_impl.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_rsa.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_se.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_slot_management.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_storage.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_util_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_util.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ripemd160.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/rsa.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/rsa_alt_helpers.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/rsa_alt_helpers.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/rsa_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/sha1.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/sha3.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/sha256.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/sha512.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/threading.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/timing.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/version.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/version_features.c" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="crypto_storage_default" Cversion="3.6.0" condition="middleware.mbedtls3x.crypto_storage_default.condition_id">
        <description>PSA ITS simulator over stdio files.</description>
        <files>
          <file category="sourceC" name="middleware/mbedtls3x/library/psa_its_file.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_its.h" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="crypto_storage_fatfs" Cversion="3.6.0" condition="middleware.mbedtls3x.crypto_storage_fatfs.condition_id">
        <description>PSA ITS simulator over fatfs files.</description>
        <Pre_Include_Global_h>
#ifndef MBEDTLS_PSA_ITS_FILE_FATFS
#define MBEDTLS_PSA_ITS_FILE_FATFS 
#endif
</Pre_Include_Global_h>
        <files>
          <file category="sourceC" name="middleware/mbedtls3x/port/psa_its/psa_its_fatfs.c" projectpath="mbedtls3x/port/psa_its"/>
          <file category="header" name="middleware/mbedtls3x/port/psa_its/psa_its_fatfs.h" projectpath="mbedtls3x/port/psa_its"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_its.h" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
          <file category="include" name="middleware/mbedtls3x/port/psa_its/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="crypto_storage_ram" Cversion="3.6.0" condition="middleware.mbedtls3x.crypto_storage_ram.condition_id">
        <description>PSA ITS simulator over objects in RAM.</description>
        <Pre_Include_Global_h>
#ifndef MBEDTLS_PSA_ITS_RAM
#define MBEDTLS_PSA_ITS_RAM 
#endif
</Pre_Include_Global_h>
        <files>
          <file category="sourceC" name="middleware/mbedtls3x/port/psa_its/psa_its_ram.c" projectpath="mbedtls3x/port/psa_its"/>
          <file category="header" name="middleware/mbedtls3x/library/psa_crypto_its.h" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="mbedtls3x_no_psa" Cversion="3.6.0" condition="middleware.mbedtls3x.no_psa.condition_id">
        <description>mbedTLS library v3.x without PSA</description>
        <files>
          <file category="doc" name="middleware/mbedtls3x/middleware.mbedtls3x.no_psa_dummy.txt"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="config" Cversion="3.6.0">
        <description>mbedTLS port library common files for MCUX</description>
        <Pre_Include_Global_h>
#ifndef MBEDTLS_CONFIG_FILE
#define MBEDTLS_CONFIG_FILE "mcux_mbedtls_config.h"
#endif
</Pre_Include_Global_h>
        <files>
          <file category="header" name="middleware/mbedtls3x/port/mcux_mbedtls_config.h" projectpath="mbedtls3x/port"/>
          <file category="header" name="middleware/mbedtls3x/port/mcux_mbedtls_accelerator_config.h" projectpath="mbedtls3x/port"/>
          <file category="header" name="middleware/mbedtls3x/port/mcux_psa_defines.h" projectpath="mbedtls3x/port"/>
          <file category="include" name="middleware/mbedtls3x/port/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="psa_crypto_config" Cversion="3.6.0">
        <description>mbedTLS port library common files for MCUX</description>
        <Pre_Include_Global_h>
#ifndef MBEDTLS_CONFIG_FILE
#define MBEDTLS_CONFIG_FILE "mcux_mbedtls_psa_crypto_config.h"
#endif
</Pre_Include_Global_h>
        <files>
          <file category="header" name="middleware/mbedtls3x/port/mcux_mbedtls_psa_crypto_config.h" projectpath="mbedtls3x/port"/>
          <file category="header" name="middleware/mbedtls3x/port/mcux_mbedtls_accelerator_config.h" projectpath="mbedtls3x/port"/>
          <file category="header" name="middleware/mbedtls3x/port/mcux_psa_defines.h" projectpath="mbedtls3x/port"/>
          <file category="include" name="middleware/mbedtls3x/port/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="rng" Cversion="3.6.0" condition="middleware.mbedtls3x.port.rng.condition_id">
        <description>mbedTLS port library for entropy for all RNG and TRNG based devices</description>
        <files>
          <file category="sourceC" name="middleware/mbedtls3x/port/rng/psa_mcux_entropy.c" projectpath="mbedtls3x/port/rng"/>
          <file category="include" name="middleware/mbedtls3x/port/rng/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="ssl" Cversion="3.6.0" condition="middleware.mbedtls3x.ssl.condition_id">
        <description>mbedTLS SSL library</description>
        <files>
          <file category="doc" name="middleware/mbedtls3x/middleware.mbedtls3x.ssl_dummy.txt"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="ssl_no_psa" Cversion="3.6.0" condition="middleware.mbedtls3x.ssl.no_psa.condition_id">
        <description>mbedTLS SSL library without PSA</description>
        <files>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ssl.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ssl_cache.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ssl_ciphersuites.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ssl_cookie.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/ssl_ticket.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/net_sockets.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/debug.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/debug_internal.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/mps_common.h" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/mps_error.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/mps_reader.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/mps_reader.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/mps_trace.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/mps_trace.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/net_sockets.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_cache.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_ciphersuites.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ssl_ciphersuites_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_client.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ssl_client.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_cookie.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ssl_debug_helpers.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_debug_helpers_generated.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ssl_misc.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_msg.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_ticket.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls12_client.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls12_server.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls13_client.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls13_generic.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ssl_tls13_invasive.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls13_keys.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/ssl_tls13_keys.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/ssl_tls13_server.c" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="template" Cversion="3.6.0">
        <description>mbedTLS Template with config files.</description>
        <Pre_Include_Global_h>
#ifndef MBEDTLS_CONFIG_FILE
#define MBEDTLS_CONFIG_FILE "mcux_mbedtls_config.h"
#endif
</Pre_Include_Global_h>
        <files>
          <file category="header" attr="config" name="middleware/mbedtls3x/port/mcux_mbedtls_config.h" version="3.6.0" projectpath="mbedtls3x/port"/>
          <file category="header" attr="config" name="middleware/mbedtls3x/port/mcux_mbedtls_accelerator_config.h" version="3.6.0" projectpath="mbedtls3x/port"/>
          <file category="doc" name="middleware/mbedtls3x/Mbed_TLS_3 NXP_dummy.txt"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="tests" Cversion="3.6.0">
        <description>mbedTLS test suite</description>
        <files>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/asn1_helpers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/bignum_helpers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/certs.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/helpers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/psa_crypto_helpers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/psa_crypto_stubs.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/psa_exercise_key.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/psa_memory_poisoning_wrappers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/psa_test_wrappers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/random.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/test_memory.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/threading_helpers.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/fake_external_rng_for_test.c" projectpath="mbedtls3x/tests/src"/>
          <file category="sourceC" name="middleware/mbedtls3x/tests/src/test_helpers/ssl_helpers.c" projectpath="mbedtls3x/tests/src/test_helpers"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/asn1_helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/bignum_helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/certs.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/constant_flow.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/fake_external_rng_for_test.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/macros.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/memory.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/psa_crypto_helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/psa_exercise_key.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/psa_helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/psa_memory_poisoning_wrappers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/psa_test_wrappers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/random.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/ssl_helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="header" name="middleware/mbedtls3x/tests/include/test/threading_helpers.h" projectpath="mbedtls3x/tests/include/test"/>
          <file category="include" name="middleware/mbedtls3x/tests/include/test/"/>
          <file category="include" name="middleware/mbedtls3x/tests/include/"/>
        </files>
      </component>
      <component Cgroup="MbedTLSv3 library" Csub="x509" Cversion="3.6.0">
        <description>mbedTLS X.509 library</description>
        <files>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/pkcs7.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/x509.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/x509_crl.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/x509_crt.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="header" name="middleware/mbedtls3x/include/mbedtls/x509_csr.h" projectpath="mbedtls3x/include/mbedtls" path="middleware/mbedtls3x/include"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509_create.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509_crl.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509_crt.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509_csr.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509write.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509write_crt.c" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/x509write_csr.c" projectpath="mbedtls3x/library"/>
          <file category="header" name="middleware/mbedtls3x/library/x509_internal.h" projectpath="mbedtls3x/library"/>
          <file category="sourceC" name="middleware/mbedtls3x/library/pkcs7.c" projectpath="mbedtls3x/library"/>
          <file category="include" name="middleware/mbedtls3x/include/"/>
          <file category="include" name="middleware/mbedtls3x/library/"/>
        </files>
      </component>
    </bundle>
  </components>
</package>
