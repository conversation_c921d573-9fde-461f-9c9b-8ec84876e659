<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>MCXE31B_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MCXE31B</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.09.00-pvw1' date='2025-07-14'>NXP CMSIS Packs based on MCUXpresso SDK 25.09.00-pvw1</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='MCXE31B' Dvendor='NXP:11'>
      <description>MCXE31x</description>
      <device Dname='MCXE31B'>
        <processor Dcore='Cortex-M7' Dfpu='SP_FPU' Dmpu='MPU' Dendian='Little-endian' Dclock='160000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/MCXE31B/iar/MCXE31B_flash.icf'/>
        </environment>
        <sequences>
          <!-- The default implementation of ResetHardwareDeassert -->
          <sequence name='ResetHardwareDeassert_Default'>
            <block>
              __var nReset      = 0x80;
              __var canReadPins = 0;
              // Assert nRESET line and check if nRESET is readable
              canReadPins = (DAP_SWJ_Pins(nReset, nReset, 0) != 0xFFFFFFFF);
            </block>
            <!-- Wait max. 1s for nRESET to recover from reset if readable-->
            <control if='canReadPins' while='(DAP_SWJ_Pins(nReset, nReset, 0) &amp; nReset) == 0' timeout='1000000'/>
            <!-- Wait 100ms for recovery if nRESET not readable -->
            <control if='!canReadPins' while='1' timeout='100000'/>
          </sequence>
          <sequence name='EnableM7Debug'>
            <block info='Enable M7 cores debugging in SDA_AP'>
              __var ap = __ap;                                  // Save current
              __ap = 7;                                         // Select SDA_AP
              WriteAP(0x80, 0x300000F0);                        // Enable M7 Debug in SDA_AP.DBGENCTRL
              __ap = ap;                                        // Restore AP
            </block>
          </sequence>
          <sequence name='FunctionalReset'>
            <block info='Initiate functional reset'>
              __var ap = __ap;                                  // Save current
              __ap = 6;                                         // Select MDM_AP
              WriteAP(0x04, 0x00400B00);                        // Assert RSTRELCM7/RSTRELTLn, CMnDBGREQ (MDMAPCTL)
              WriteAP(0x04, 0x00400B20);                        // Assert RSTRELCM7/RSTRELTLn, CMnDBGREQ and SYSFUNCRST (MDMAPCTL)
              WriteAP(0x04, 0x00400B00);                        // Assert RSTRELCM7/RSTRELTLn, CMnDBGREQ (MDMAPCTL)
              WriteAP(0x04, 0x00400000);                        // Assert RSTRELCM7/RSTRELTLn (MDMAPCTL)
              __ap = ap;                                        // Restore AP
            </block>
          </sequence>
          <sequence name='DebugFromFirstInstruction'>
            <block>__var ap = __ap;                                  // Save current AP</block>
            <block>Sequence("EnableM7Debug");</block>
            <block>
              __ap = 6;                                         // set MDM-AP
              WriteAP(0x04, 0x00430000);                        // MDMAPCTL: Bits 16 and 17 CM7_x_CORE_ACCESS
            </block>
            <block>
              __ap = 7;                                         // SDA_AP;
              WriteAP(0x90, 0x00000000);                        // SDAAPRSTCTRL: Ensure M7s are held in reset
            </block>
          </sequence>
          <sequence name='DebugEnablement'>
            <block>Sequence("EnableM7Debug");                        // Enable M7 debugging</block>
            <control if='DoRAMInitialize'>
              <block>
                Sequence("EnablePeripheralClocks");             // Enable peripheral clocks
                Sequence("RAMInitialize");                      // Do RAM Initialize
              </block>
            </control>
          </sequence>
          <!-- Default sequence overrides -->
          <sequence name='ResetHardwareDeassert'>
            <block>
              __var ap = __ap;                                  // Save current AP
              __var sdaaprstctrl_val;
            </block>
            <control if='ap == 4'>
              <block>
                sdaaprstctrl_val = 0x02000000;                  // release CM7_0 from reset (RSTRELTLCM70 = 1)
              </block>
            </control>
            <control if='(__connection &amp; 0x00010000)'>
              <block>
                __errorcontrol = 1;
                __ap = 7;                                       // __ap = SDA_AP;
                WriteAP(0x90, sdaaprstctrl_val);                // SDAAPRSTCTRL: Release M7_0 from reset
                __ap = ap;
                __errorcontrol = 0;
              </block>
              <block>
                //DAP_Delay(1000000);
                __errorcontrol = 1;
                Sequence("DebugEnablement");                    // Call DebugEnablement
                __errorcontrol = 0;
              </block>
            </control>
            <control if='(__connection &amp; 0x00010000) == 0x00000000' info='connect normal'>
              <block>Sequence("ResetHardwareDeassert_Default");</block>
            </control>
          </sequence>
          <sequence name='DebugDeviceUnlock'>
            <control if='(__connection &amp; 0x00010000)' info='connect under reset'>
              <block>
                Sequence("DebugFromFirstInstruction");
                Sequence("ResetHardwareDeassert_Default");
              </block>
            </control>
            <control if='(__connection &amp; 0x00010000) == 0x00000000' info='connect normal'>
              <block>Sequence("DebugEnablement");                    // Call DebugEnablement</block>
            </control>
          </sequence>
          <sequence name='ResetSystem'>
            <block>Sequence("FunctionalReset");                      // Call FunctionalReset</block>
          </sequence>
          <!-- Device specific configuration. -->
          <sequence name='EnablePeripheralClocks'>
            <block info='Enable peripheral clocks'>
              // partition 0
              Write32(0x402DC134,0x0000F7DF);                   // Enable clock for block 1 in partition 0 (MC_ME.PRTN0_COFB1_CLKEN)
              Write32(0x402DC100,0x00000001);                   // Enable clock to IPs (MC_ME.PRTN0_PCONF)
              Write32(0x402DC104,0x00000001);                   // Trigger the hardware process (MC_ME.PRTN0_PUPD)
              Write32(0x402DC000,0x00005AF0);                   // Start the hardware process; key '5AF0' (MC_ME.MC_ME_CTL_KEY
              Write32(0x402DC000,0x0000A50F);                   //                             key 'A50F' (MC_ME.MC_ME_CTL_KEY
              // partition 1
              Write32(0x402DC330,0xB1E0FFF8);                   // Enable clock for block 0 in partition 1 (MC_ME.PRTN1_COFB0_CLKEN)
              Write32(0x402DC334,0x812AA407);                   // Enable clock for block 1 in partition 1 (MC_ME.PRTN1_COFB1_CLKEN)
              Write32(0x402DC338,0xBBF3FE7E);                   // Enable clock for block 2 in partition 1 (MC_ME.PRTN1_COFB2_CLKEN)
              Write32(0x402DC33C,0x00000141);                   // Enable clock for block 3 in partition 1 (MC_ME.PRTN1_COFB3_CLKEN)
              Write32(0x402DC300,0x00000001);                   // Enable clock to IPs (MC_ME.PRTN1_PCONF)
              Write32(0x402DC304,0x00000001);                   // Trigger the hardware process (MC_ME.PRTN1_PUPD)
              Write32(0x402DC000,0x00005AF0);                   // Start the hardware process; key '5AF0' (MC_ME.MC_ME_CTL_KEY
              Write32(0x402DC000,0x0000A50F);                   //                             key 'A50F' (MC_ME.MC_ME_CTL_KEY
              // partition 2
              Write32(0x402DC530,0x29FFFFF0);                   // Enable clock for block 0 in partition 2 (MC_ME.PRTN2_COFB0_CLKEN)
              Write32(0x402DC534,0xC48987F9);                   // Enable clock for block 1 in partition 2 (MC_ME.PRTN2_COFB1_CLKEN)
              Write32(0x402DC500,0x00000001);                   // Enable clock to IPs (MC_ME.PRTN2_PCONF)
              Write32(0x402DC504,0x00000001);                   // Trigger the hardware process (MC_ME.PRTN2_PUPD)
              Write32(0x402DC000,0x00005AF0);                   // Start the hardware process; key '5AF0' (MC_ME.MC_ME_CTL_KEY
              Write32(0x402DC000,0x0000A50F);                   //                             key 'A50F' (MC_ME.MC_ME_CTL_KEY
            </block>
          </sequence>
          <sequence name='RAMInitialize'>
            <block>
              __var TCD_B0 = 0x40210000;                        // TCD Base Address; TCD0 = 0x40210000, TCD1 = 0x40214000
              __var CHCFG0 =       0x03;                        // CH0 = 3, CH1 = 2, CH2 = 1, CH3 = 0
              __var TCD_B1 = 0x40214000;                        // TCD Base Address; TCD0 = 0x40210000, TCD1 = 0x40214000
              __var CHCFG1 =       0x02;                        // CH0 = 3, CH1 = 2, CH2 = 1, CH3 = 0
            </block>
            <block info='Initialize SRAM via DMA'>
              // SRAM: 2 x 160kB @ 0x20400000
              Write8(0x40280000 + CHCFG0, 0x80);                // Enable DMA CH0 (DMAMUX_0 register CHCFG0)
              Write32(TCD_B0 + 0x020, 0x00400000);              // Source address '0x00400000' (DMA TCD register TCD0_SADDR)
              Write32(TCD_B0 + 0x024, 0x03030000);              // Offset '0' and attributes SSIZE, DSIZE '64bit' (DMA TCD register TCD0_SOFF, TCD0_ATTR)
              Write32(TCD_B0 + 0x028, RAMInitializeSRAMSize);   // Transfer size Nbytes '320Kb' (DMA TCD register TCD0_NBYTES_MLOFFNO)
              Write32(TCD_B0 + 0x02C, 0x00000000);              // Last source address adjustment '0' (DMA TCD register TCD0_SLAST_SDA)
              Write32(TCD_B0 + 0x030, 0x20400000);              // Destination address '0x20400000' (DMA TCD register TCD0_DADDR)
              Write32(TCD_B0 + 0x034, 0x00010008);              // Destination signed offset '8', Current Major Iteration Count '1' (DMA TCD register TCD0_DOFF, TCD0_CITER_ELINKNO)
              Write32(TCD_B0 + 0x038, 0xFFFB0000);              // Last destination address adjustment -NBYTES '0xFFFB0000' (DMA TCD register TCD0_DLAST_SGA)
              Write32(TCD_B0 + 0x03C, 0x00000001);              // Start DMA transfer (DMA TCD register TCD0_CSR)
            </block>
            <control while='(Read32(TCD_B0 + 0x000) &amp; 0x80000000)' timeout='1000000' info='Poll DMA is ACTIVE'/>
            <block info='Initialize DTCM via DMA'>
              // DTCM: 1 x 128kB @ 0x20000000 (0x21000000 backdoor)
              Write8(0x40280000 + CHCFG0, 0x80);                // Enable DMA CH0 (DMAMUX_0 register CHCFG0)
              Write32(TCD_B0 + 0x020, 0x00400000);              // Source address '0x00400000' (DMA TCD register TCD0_SADDR)
              Write32(TCD_B0 + 0x024, 0x03030000);              // Offset '0' and attributes SSIZE, DSIZE '64bit' (DMA TCD register TCD0_SOFF, TCD0_ATTR)
              Write32(TCD_B0 + 0x028, RAMInitializeDTCMSize);   // Transfer size Nbytes '128Kb' (DMA TCD register TCD0_NBYTES_MLOFFNO)
              Write32(TCD_B0 + 0x02C, 0x00000000);              // Last source address adjustment '0' (DMA TCD register TCD0_SLAST_SDA)
              Write32(TCD_B0 + 0x030, 0x21000000);              // Destination address '0x20000000' (DMA TCD register TCD0_DADDR)
              Write32(TCD_B0 + 0x034, 0x00010008);              // Destination signed offset '8', Current Major Iteration Count '1' (DMA TCD register TCD0_DOFF, TCD0_CITER_ELINKNO)
              Write32(TCD_B0 + 0x038, 0xFFFF0000);              // Last destination address adjustment -NBYTES '0xFFFF0000' (DMA TCD register TCD0_DLAST_SGA)
              Write32(TCD_B0 + 0x03C, 0x00000001);              // Start DMA transfer (DMA TCD register TCD0_CSR)
            </block>
            <control while='(Read32(TCD_B0 + 0x000) &amp; 0x80000000)' timeout='1000000' info='Poll DMA is ACTIVE'/>
          </sequence>
        </sequences>
        <debugvars configfile='devices/MCXE31B/arm/MCXE31x.dbgconf'>
          // Debug Access Variables, can be modified by user via copies of DBGCONF files as created by uVision. Also see sub-family level.
          __var DoRAMInitialize = 1;                            // Enabled
          __var RAMInitializeSRAMSize = 0x00050000;             // 320KB
          __var RAMInitializeDTCMSize = 0x00010000;             // 64KB
        </debugvars>
        <memory name='DATA_FLASH' start='0x10000000' size='0x020000' access='rw' default='1'/>
        <memory name='DTCM' start='0x20000000' size='0x010000' access='rw' default='1'/>
        <memory name='DTCM1' start='0x21400000' size='0x010000' access='rw' default='1'/>
        <memory name='ITCM' start='0x00000000' size='0x8000' access='rw' default='1'/>
        <memory name='ITCM1' start='0x11400000' size='0x8000' access='rw' default='1'/>
        <memory name='PROGRAM_FLASH' start='0x00400000' size='0x400000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x20400000' size='0x050000' access='rw' default='1'/>
        <algorithm name='devices/MCXE31B/arm/MCXE31x_Code.FLM' start='0x00400000' size='0x00400000' RAMstart='0x20400000' RAMsize='0x00008000' default='1'/>
        <algorithm name='devices/MCXE31B/arm/MCXE31x_Data.FLM' start='0x10000000' size='0x00020000' RAMstart='0x20400000' RAMsize='0x00008000' default='1'/>
        <debug svd='devices/MCXE31B/MCXE31B.xml' __dp='0' __ap='4'/>
        <variant Dvariant='MCXE31BMPB'>
          <compile header='devices/MCXE31B/fsl_device_registers.h' define='CPU_MCXE31BMPB'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.MCXE31B.internal_condition'>
      <accept Dname='MCXE31BMPB' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.device=MCXE31B.internal_condition'>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.ak4497_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.codec_adapters.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.crc_adapter.condition_id'>
      <require condition='allOf.driver.crc, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.crc, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='crc'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.cs42448_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.cs42888_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.da7212_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.flash_nor_lpspi.condition_id'>
      <require condition='allOf.driver.lpspi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpspi, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.log.condition_id'>
      <require condition='allOf.driver.common, utility.str, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.str, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console_lite.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console_lite, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console_lite, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.log.backend.ringbuffer.condition_id'>
      <require condition='allOf.component.log, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.lpspi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.lpspi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.lpspi, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.lpuart_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.lpuart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
    </condition>
    <condition id='component.mflash_offchip.condition_id'>
      <require condition='allOf.anyOf=driver.cache_armv7_m7, driver.qspi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.cache_armv7_m7, driver.qspi, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.cache_armv7_m7, driver.qspi.internal_condition'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.cache_armv7_m7, driver.qspi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_armv7_m7'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='qspi'/>
    </condition>
    <condition id='component.mflash_onchip.condition_id'>
      <require condition='allOf.anyOf=driver.cache_armv7_m7, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.cache_armv7_m7, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.cache_armv7_m7.internal_condition'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.cache_armv7_m7.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_armv7_m7'/>
    </condition>
    <condition id='component.mflash_onchip_fmu.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.pcm186x_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.pcm512x_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.pit_adapter.condition_id'>
      <require condition='allOf.driver.pit, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.pit, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=component.software_rng_adapter, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.software_rng_adapter, device=MCXE31B.internal_condition'>
      <require condition='anyOf.component.software_rng_adapter.internal_condition'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.component.software_rng_adapter.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MCXE31B.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpspi_adapter, driver.lpspi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpspi_adapter, driver.lpspi, device=MCXE31B.internal_condition'>
      <require condition='anyOf.allOf=component.lpspi_adapter, driver.lpspi.internal_condition'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpspi_adapter, driver.lpspi.internal_condition'>
      <accept condition='allOf.component.lpspi_adapter, driver.lpspi.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpspi_adapter, driver.lpspi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.component.lpuart_adapter, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpuart_adapter, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.sgtl_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.tfa9896_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.tfa9xxx_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.pit_adapter, driver.pit, component.lists, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.pit_adapter, driver.pit, component.lists, driver.common, device=MCXE31B.internal_condition'>
      <require condition='anyOf.allOf=component.pit_adapter, driver.pit.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.pit_adapter, driver.pit.internal_condition'>
      <accept condition='allOf.component.pit_adapter, driver.pit.internal_condition'/>
    </condition>
    <condition id='allOf.component.pit_adapter, driver.pit.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit'/>
    </condition>
    <condition id='component.wm8524_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.wm8904_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.wm8960_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='component.wm8962_adapter.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=MCXE31B.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='device.MCXE31B_flash.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='device_id.MCXE31B.internal_condition'>
      <accept Dname='MCXE31BMPB' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MCXE31B.internal_condition'>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='device_ids.MCXE31B.internal_condition'>
      <accept Dname='MCXE31BMPB' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=MCXE31B.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.MCXE31B.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=MCXE31B.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.MCXE31B.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=MCXE31B.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.MCXE31B.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.device_id=MCXE31B, driver.common, driver.siul2, driver.clock, driver.memory, component.lpuart_adapter, CMSIS_Include_core_cm, device.CMSIS, device.system, device.startup, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MCXE31B, driver.common, driver.siul2, driver.clock, driver.memory, component.lpuart_adapter, CMSIS_Include_core_cm, device.CMSIS, device.system, device.startup, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require condition='device_id.MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='Siul2'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='memory'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXE31B_header'/>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXE31B_system'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require condition='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <accept condition='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'/>
      <accept condition='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config'/>
    </condition>
    <condition id='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXE31B_system'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, iar, mdk.condition_id'>
      <require condition='toolchains.armgcc, iar, mdk.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, iar, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='IAR'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXE31B_header'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.bctu.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.cache_armv7_m7.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.video-common, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ap1302.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.video-i2c, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-common, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-max9286.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-mt9m114.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov5640.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7670.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7725.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-sccb.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.lpi2c.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='driver.camera-receiver-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpi2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, anyOf=allOf=driver.lpi2c, device_id=MCXE31B, device.RTE, device_id=MCXE31B, driver.lpi2c_edma.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, anyOf=allOf=driver.lpi2c, device_id=MCXE31B, device.RTE, device_id=MCXE31B, driver.lpi2c_edma.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpi2c, device_id=MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpi2c, device_id=MCXE31B.internal_condition'>
      <accept condition='allOf.driver.lpi2c, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpi2c, device_id=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpspi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, anyOf=allOf=driver.lpspi, device_id=MCXE31B, device.RTE, device_id=MCXE31B, driver.lpspi_edma.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, anyOf=allOf=driver.lpspi, device_id=MCXE31B, device.RTE, device_id=MCXE31B, driver.lpspi_edma.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require condition='anyOf.allOf=driver.lpspi, device_id=MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_edma'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpspi, device_id=MCXE31B.internal_condition'>
      <accept condition='allOf.driver.lpspi, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpspi, device_id=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpuart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MCXE31B, device.RTE, device_id=MCXE31B, driver.lpuart_edma.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MCXE31B, device.RTE, device_id=MCXE31B, driver.lpuart_edma.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpuart, device_id=MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_edma'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpuart, device_id=MCXE31B.internal_condition'>
      <accept condition='allOf.driver.lpuart, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpuart, device_id=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.cmu_fc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.cmu_fm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.codec.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm7f.condition_id'>
      <require condition='cores.cm7f.internal_condition'/>
    </condition>
    <condition id='cores.cm7f.internal_condition'>
      <accept Dcore='Cortex-M7'/>
    </condition>
    <condition id='driver.crc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dbi.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dbi_flexio_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.edma4, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='edma'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dbi.condition_id'>
      <require condition='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-ssd1963.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dcm.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dcm_gpr.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.display-adv7535.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.video-i2c, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.display-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.display-it6161.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.display-it6263.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.display-sn65dsi83.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.dmamux.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.eMIOS.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.edma4.condition_id'>
      <require condition='allOf.driver.edma_soc, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.edma_soc, device_id=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='edma_soc'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.edma_soc.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ektf2k.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flash_c40.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexcan.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexcan_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.flexcan, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.flexcan, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.edma4.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='edma'/>
    </condition>
    <condition id='driver.flexio.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2c_master.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexio, device_id=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_mculcd.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_mculcd_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.flexio_mculcd, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.flexio_mculcd, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.flexio_spi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.flexio_spi, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.flexio_uart, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.flexio_uart, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ft3267.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ft5406.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ft5406_rt.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ft6x06.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device=MCXE31B.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.fxas21002cq.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.fxls8974cf.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.fxos8700cq.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.gt911.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.htu21d.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.icm42688p.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ili9341.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.intm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lcu.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpcmp.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpi2c.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpi2c_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.lpi2c, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.lpi2c, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpspi.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpspi_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.lpspi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.lpspi, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpuart.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lpuart_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.lpuart, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.lpuart, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.lsm6dso.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.max30101.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.mc_rgm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.memory.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.mma8451q.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.mma8652fc.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.nmh1000.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.p3t1755.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.pca9420.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, driver.power, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, driver.common, driver.power, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.pca9422.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, driver.power, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.pf1550.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.pf3000.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.pf5020.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.pit.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.power.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.psp27801.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.qspi.condition_id'>
      <require condition='allOf.anyOf=allOf=device_id=MCXE31B, driver.qspi_mcxe31b, driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=device_id=MCXE31B, driver.qspi_mcxe31b, driver.common, device_id=MCXE31B.internal_condition'>
      <require condition='anyOf.allOf=device_id=MCXE31B, driver.qspi_mcxe31b.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MCXE31B.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=device_id=MCXE31B, driver.qspi_mcxe31b.internal_condition'>
      <accept condition='allOf.device_id=MCXE31B, driver.qspi_mcxe31b.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MCXE31B, driver.qspi_mcxe31b.internal_condition'>
      <require condition='device_id.MCXE31B.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='qspi_mcxe31b'/>
    </condition>
    <condition id='driver.qspi_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma4, driver.qspi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma4, driver.qspi, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.edma4.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='qspi'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.qspi_mcxe31b.condition_id'>
      <require condition='allOf.device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.rtc_jdp.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.rtt.condition_id'>
      <require condition='allOf.driver.rtt.template, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.rtt.template, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rtt template'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.rtt.template.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.sar_adc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.siul2.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.ssd1963.condition_id'>
      <require condition='allOf.driver.dbi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.st7796s.condition_id'>
      <require condition='allOf.driver.dbi, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.stm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.swt.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.tempsense.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.tma525b.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.trgmux.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.tsl2561.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.tspc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.video-common.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.video-i2c.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.virt_wrapper.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.wkpu.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.xbic.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='driver.xrdc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXE31B.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk, cores=cm7f.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
      <require condition='cores.cm7f.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, driver.common, not=utility.debug_console, utility.str, component.lpuart_adapter, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, driver.common, not=utility.debug_console, utility.str, component.lpuart_adapter, device=MCXE31B.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.debug_console.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='not.utility.debug_console.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='utility.notifier.condition_id'>
      <require condition='allOf.device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=MCXE31B.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=MCXE31B.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXE31B.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=MCXE31B.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter' Cversion='2.2.0' condition='component.ak4497_adapter.condition_id'>
      <description>Component ak4497 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.h' projectpath='codec/port/ak4497'/>
        <file category='sourceC' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.c' projectpath='codec/port/ak4497'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/ak4497/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters' Cversion='2.2.0' condition='component.codec_adapters.condition_id'>
      <description>Component codec adapters for multi codec</description>
      <RTE_Components_h>
#ifndef CODEC_MULTI_ADAPTERS
#define CODEC_MULTI_ADAPTERS 1
#endif
</RTE_Components_h>
      <files>
        <file category='sourceC' name='components/codec/port/fsl_codec_adapter.c' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='crc_adapter' Cversion='1.0.0' condition='component.crc_adapter.condition_id'>
      <description>Component crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter' Cversion='2.2.1' condition='component.cs42448_adapter.condition_id'>
      <description>Component cs42448 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.h' projectpath='codec/port/cs42448'/>
        <file category='sourceC' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.c' projectpath='codec/port/cs42448'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42448/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter' Cversion='2.2.1' condition='component.cs42888_adapter.condition_id'>
      <description>Component cs42888 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.h' projectpath='codec/port/cs42888'/>
        <file category='sourceC' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.c' projectpath='codec/port/cs42888'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42888/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter' Cversion='2.2.0' condition='component.da7212_adapter.condition_id'>
      <description>Component da7212 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/da7212/fsl_codec_da7212_adapter.h' projectpath='codec/port/da7212'/>
        <file category='sourceC' name='components/codec/port/da7212/fsl_codec_da7212_adapter.c' projectpath='codec/port/da7212'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/da7212/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nor_lpspi' Cversion='1.0.0' condition='component.flash_nor_lpspi.condition_id'>
      <description>Component flash_nor_lpspi</description>
      <files>
        <file category='header' name='components/flash/nor/fsl_nor_flash.h' projectpath='nor_flash/nor'/>
        <file category='sourceC' name='components/flash/nor/lpspi/fsl_lpspi_nor_flash.c' projectpath='nor_flash/nor/lpspi'/>
        <file category='header' name='components/flash/nor/lpspi/fsl_lpspi_nor_flash.h' projectpath='nor_flash/nor/lpspi'/>
        <file category='sourceC' name='components/flash/nor/lpspi/fsl_lpspi_mem_adapter.c' projectpath='nor_flash/nor/lpspi'/>
        <file category='header' name='components/flash/nor/lpspi/fsl_lpspi_mem_adapter.h' projectpath='nor_flash/nor/lpspi'/>
        <file category='include' name='components/flash/nor/'/>
        <file category='include' name='components/flash/nor/lpspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='log' Cversion='1.0.0' condition='component.log.condition_id'>
      <description>Component log</description>
      <files>
        <file category='header' name='components/log/fsl_component_log.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log.c' projectpath='component/log'/>
        <file category='header' name='components/log/fsl_component_log_config.h' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole' Cversion='1.0.0' condition='component.log.backend.debug_console.condition_id'>
      <description>Component log backend debug console</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole_lite' Cversion='1.0.0' condition='component.log.backend.debug_console_lite.condition_id'>
      <description>Component log backend debug console lite</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ringbuffer' Cversion='1.0.0' condition='component.log.backend.ringbuffer.condition_id'>
      <description>Component log backend ring buffer</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_ringbuffer.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_ringbuffer.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_adapter' Cversion='1.0.0' condition='component.lpspi_adapter.condition_id'>
      <description>Component lpspi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_lpspi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter' Cversion='1.0.0' condition='component.lpuart_adapter.condition_id'>
      <description>Component lpuart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_lpuart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_offchip' Cversion='1.0.0' condition='component.mflash_offchip.condition_id'>
      <description>mflash offchip</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_offchip_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip' Cversion='1.0.0' condition='component.mflash_onchip.condition_id'>
      <description>mflash onchip</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip_fmu' Cversion='1.0.0' condition='component.mflash_onchip_fmu.condition_id'>
      <description>mflash onchip_fmu</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_fmu_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter' Cversion='2.0.0' condition='component.pcm186x_adapter.condition_id'>
      <description>Component pcm186x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.h' projectpath='codec/port/pcm186x'/>
        <file category='sourceC' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.c' projectpath='codec/port/pcm186x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm186x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter' Cversion='2.0.0' condition='component.pcm512x_adapter.condition_id'>
      <description>Component pcm512x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.h' projectpath='codec/port/pcm512x'/>
        <file category='sourceC' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.c' projectpath='codec/port/pcm512x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm512x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pit_adapter' Cversion='1.0.0' condition='component.pit_adapter.condition_id'>
      <description>Component pit_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_PIT
#define TIMER_PORT_TYPE_PIT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_pit.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter' Cversion='2.2.0' condition='component.sgtl_adapter.condition_id'>
      <description>Component sgtl5000 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.h' projectpath='codec/port/sgtl5000'/>
        <file category='sourceC' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.c' projectpath='codec/port/sgtl5000'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/sgtl5000/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter' Cversion='2.2.0' condition='component.tfa9896_adapter.condition_id'>
      <description>Component tfa9896 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.h' projectpath='codec/port/tfa9896'/>
        <file category='sourceC' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.c' projectpath='codec/port/tfa9896'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9896/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter' Cversion='2.2.0' condition='component.tfa9xxx_adapter.condition_id'>
      <description>Component tfa9xxx adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.h' projectpath='codec/port/tfa9xxx'/>
        <file category='sourceC' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.c' projectpath='codec/port/tfa9xxx'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9xxx/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter' Cversion='2.2.0' condition='component.wm8524_adapter.condition_id'>
      <description>Component wm8524 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.h' projectpath='codec/port/wm8524'/>
        <file category='sourceC' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.c' projectpath='codec/port/wm8524'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8524/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter' Cversion='2.2.0' condition='component.wm8904_adapter.condition_id'>
      <description>Component wm8904 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.h' projectpath='codec/port/wm8904'/>
        <file category='sourceC' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.c' projectpath='codec/port/wm8904'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8904/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter' Cversion='2.2.0' condition='component.wm8960_adapter.condition_id'>
      <description>Component wm8960 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.h' projectpath='codec/port/wm8960'/>
        <file category='sourceC' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.c' projectpath='codec/port/wm8960'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8960/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter' Cversion='2.2.0' condition='component.wm8962_adapter.condition_id'>
      <description>Component wm8962 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.h' projectpath='codec/port/wm8962'/>
        <file category='sourceC' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.c' projectpath='codec/port/wm8962'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8962/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MCXE31B_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device MCXE31B_cmsis</description>
      <files>
        <file category='header' name='devices/MCXE31B/fsl_device_registers.h' projectpath='device'/>
        <file category='header' name='devices/MCXE31B/MCXE31B.h' projectpath='device'/>
        <file category='header' name='devices/MCXE31B/MCXE31B_features.h' projectpath='device'/>
        <file category='header' name='devices/MCXE31B/MCXE31B_COMMON.h' projectpath='device'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_ADC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_AXBS_LITE.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_BCTU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_CAN.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_CMU_FC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_CMU_FM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_CONFIGURATION.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_CRC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_DCM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_DCM_GPR.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_DMA.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_DMAMUX.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_EIM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_EMAC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_EMIOS.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_ERM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_FCCU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_FIRC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_FLASH.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_FLEXIO.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_FXOSC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_I2S.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_INTM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_JDC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_LCU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_LPCMP.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_LPI2C.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_LPSPI.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_LPUART.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MCM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MC_CGM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MC_ME.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MC_RGM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MDM_AP.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MSCM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_MU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_PFLASH.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_PIT.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_PLL.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_PMC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_PRAMC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_QUADSPI.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_QUADSPI_ARDB.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_RTC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SDA_AP.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SELFTEST.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SEMA42.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SIRC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SIUL2.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_STCU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_STM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SWT.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_SXOSC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_TEMPSENSE.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_TRGMUX.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_TSPC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_VIRT_WRAPPER.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_WKPU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_XBIC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXE31B/periph1/PERI_XRDC.h' projectpath='device/periph1'/>
        <file category='include' name='devices/MCXE31B/'/>
        <file category='include' name='devices/MCXE31B/periph1/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='MCXE31B_flash' Cversion='1.0.0' condition='device.MCXE31B_flash.condition_id'>
      <description>Device MCXE31B_flash</description>
      <files>
        <file category='doc' name='devices/MCXE31B/mcuxpresso/device.MCXE31B_flash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='doc' name='devices/MCXE31B/template/device.RTE_dummy.txt'/>
        <file category='include' name='devices/MCXE31B/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='MCXE31B_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device MCXE31B_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, device_ids=MCXE31B.condition_id' category='linkerScript' attr='config' name='devices/MCXE31B/arm/MCXE31B_flash.scf' version='1.0.0' projectpath='MCXE31B/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MCXE31B.condition_id' category='linkerScript' attr='config' name='devices/MCXE31B/arm/MCXE31B_ram.scf' version='1.0.0' projectpath='MCXE31B/arm'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MCXE31B.condition_id' category='linkerScript' attr='config' name='devices/MCXE31B/gcc/MCXE31B_flash.ld' version='1.0.0' projectpath='MCXE31B/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MCXE31B.condition_id' category='linkerScript' attr='config' name='devices/MCXE31B/gcc/MCXE31B_ram.ld' version='1.0.0' projectpath='MCXE31B/gcc'/>
        <file condition='allOf.toolchains=iar, device_ids=MCXE31B.condition_id' category='linkerScript' attr='config' name='devices/MCXE31B/iar/MCXE31B_flash.icf' version='1.0.0' projectpath='MCXE31B/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MCXE31B.condition_id' category='linkerScript' attr='config' name='devices/MCXE31B/iar/MCXE31B_ram.icf' version='1.0.0' projectpath='MCXE31B/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='MCXE31B' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template MCXE31B</description>
      <files>
        <file category='header' attr='config' name='devices/MCXE31B/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXE31B/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXE31B/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXE31B/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXE31B/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXE31B/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXE31B/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXE31B/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MCXE31B/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device MCXE31B_startup</description>
      <files>
        <file condition='allOf.toolchains=armgcc, iar, mdk.condition_id' category='sourceC' attr='config' name='devices/MCXE31B/startup_MCXE31B.c' version='1.0.0' projectpath='startup'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MCXE31B_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device MCXE31B_system</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/system_MCXE31B.c' projectpath='device'/>
        <file category='header' name='devices/MCXE31B/system_MCXE31B.h' projectpath='device'/>
        <file category='include' name='devices/MCXE31B/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='bctu' Cversion='2.1.0' condition='driver.bctu.condition_id'>
      <description>bctu Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_bctu.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_bctu.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cache_armv7_m7' Cversion='2.0.4' condition='driver.cache_armv7_m7.condition_id'>
      <description>CACHE Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_cache.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_cache.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common' Cversion='1.0.0' condition='driver.camera-common.condition_id'>
      <description>Driver camera-common</description>
      <files>
        <file category='header' name='components/video/camera/fsl_camera.h' projectpath='video'/>
        <file category='include' name='components/video/camera/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ap1302' Cversion='1.0.1' condition='driver.camera-device-ap1302.condition_id'>
      <description>Driver camera-device-ap1302</description>
      <files>
        <file category='header' name='components/video/camera/device/ap1302/fsl_ap1302.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ap1302/fsl_ap1302.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ap1302/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common' Cversion='1.0.0' condition='driver.camera-device-common.condition_id'>
      <description>Driver camera-device-common</description>
      <files>
        <file category='header' name='components/video/camera/device/fsl_camera_device.h' projectpath='video'/>
        <file category='include' name='components/video/camera/device/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-max9286' Cversion='1.0.2' condition='driver.camera-device-max9286.condition_id'>
      <description>Driver camera-device-max9286</description>
      <files>
        <file category='header' name='components/video/camera/device/max9286/fsl_max9286.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/max9286/fsl_max9286.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/max9286/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-mt9m114' Cversion='1.0.2' condition='driver.camera-device-mt9m114.condition_id'>
      <description>Driver camera-device-mt9m114</description>
      <files>
        <file category='header' name='components/video/camera/device/mt9m114/fsl_mt9m114.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/mt9m114/fsl_mt9m114.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/mt9m114/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov5640' Cversion='1.0.1' condition='driver.camera-device-ov5640.condition_id'>
      <description>Driver camera-device-ov5640</description>
      <files>
        <file category='header' name='components/video/camera/device/ov5640/fsl_ov5640.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov5640/fsl_ov5640.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov5640/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7670' Cversion='1.0.2' condition='driver.camera-device-ov7670.condition_id'>
      <description>Driver camera-device-ov7670</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7670/fsl_ov7670.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7670/fsl_ov7670.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7670/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7725' Cversion='1.0.1' condition='driver.camera-device-ov7725.condition_id'>
      <description>Driver camera-device-ov7725</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7725/fsl_ov7725.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7725/fsl_ov7725.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7725/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb' Cversion='1.0.1' condition='driver.camera-device-sccb.condition_id'>
      <description>Driver camera-device-sccb</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/camera/device/sccb/fsl_sccb.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/sccb/fsl_sccb.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/sccb/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-common' Cversion='1.0.0' condition='driver.camera-receiver-common.condition_id'>
      <description>Driver camera-receiver-common</description>
      <files>
        <file category='header' name='components/video/camera/receiver/fsl_camera_receiver.h' projectpath='video'/>
        <file category='include' name='components/video/camera/receiver/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.1.1' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='lpi2c_cmsis' Cversion='2.6.0' Capiversion='2.3.0' condition='driver.cmsis_lpi2c.condition_id'>
      <description>LPI2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/cmsis_drivers/fsl_lpi2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/cmsis_drivers/fsl_lpi2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='lpspi_cmsis' Cversion='2.12.0' Capiversion='2.2.0' condition='driver.cmsis_lpspi.condition_id'>
      <description>LPSPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/cmsis_drivers/fsl_lpspi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/cmsis_drivers/fsl_lpspi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='lpuart_cmsis' Cversion='2.7.0' Capiversion='2.3.0' condition='driver.cmsis_lpuart.condition_id'>
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/cmsis_drivers/fsl_lpuart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/cmsis_drivers/fsl_lpuart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cmu_fc' Cversion='2.0.0' condition='driver.cmu_fc.condition_id'>
      <description>cmu_fc Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_cmu_fc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_cmu_fc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cmu_fm' Cversion='2.0.0' condition='driver.cmu_fm.condition_id'>
      <description>cmu_fm Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_cmu_fm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_cmu_fm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec' Cversion='2.3.1' condition='driver.codec.condition_id'>
      <description>Driver codec</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/fsl_codec_common.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/fsl_codec_common.c' projectpath='codec'/>
        <file category='include' name='components/codec/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm7f.condition_id' category='sourceC' name='devices/MCXE31B/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm7f.condition_id' category='header' name='devices/MCXE31B/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='crc' Cversion='2.0.4' condition='driver.crc.condition_id'>
      <description>CRC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_crc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_crc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi' Cversion='1.0.0' condition='driver.dbi.condition_id'>
      <description>Driver dbi</description>
      <files>
        <file category='header' name='components/video/display/dbi/fsl_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/fsl_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi_flexio_edma' Cversion='1.0.1' condition='driver.dbi_flexio_edma.condition_id'>
      <description>Driver dbi_flexio_edma</description>
      <files>
        <file category='header' name='components/video/display/dbi/flexio/fsl_dbi_flexio_edma.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/flexio/fsl_dbi_flexio_edma.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/flexio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common' Cversion='1.0.0' condition='driver.dc-fb-common.condition_id'>
      <description>Driver dc-fb-common</description>
      <files>
        <file category='header' name='components/video/display/dc/fsl_dc_fb.h' projectpath='video'/>
        <file category='include' name='components/video/display/dc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dbi' Cversion='1.0.0' condition='driver.dc-fb-dbi.condition_id'>
      <description>Driver dc-fb-dbi</description>
      <RTE_Components_h>
#ifndef MCUX_DBI_LEGACY
#define MCUX_DBI_LEGACY 0
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-ssd1963' Cversion='1.0.2' condition='driver.dc-fb-ssd1963.condition_id'>
      <description>Driver dc-fb-ssd1963</description>
      <files>
        <file category='header' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dcm' Cversion='2.0.0' condition='driver.dcm.condition_id'>
      <description>DCM Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_dcm.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_dcm.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dcm_gpr' Cversion='2.0.0' condition='driver.dcm_gpr.condition_id'>
      <description>DCM_GPR Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_dcm_gpr.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_dcm_gpr.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-adv7535' Cversion='1.0.1' condition='driver.display-adv7535.condition_id'>
      <description>Driver display-adv7535</description>
      <files>
        <file category='header' name='components/video/display/adv7535/fsl_adv7535.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/adv7535/fsl_adv7535.c' projectpath='video'/>
        <file category='include' name='components/video/display/adv7535/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-common' Cversion='1.0.0' condition='driver.display-common.condition_id'>
      <description>Driver display-common</description>
      <files>
        <file category='header' name='components/video/display/fsl_display.h' projectpath='video'/>
        <file category='include' name='components/video/display/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6161' Cversion='1.0.0' condition='driver.display-it6161.condition_id'>
      <description>Driver display-it6161</description>
      <files>
        <file category='header' name='components/video/display/it6161/fsl_it6161.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/fsl_it6161.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/hdmi_tx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/hdmi_tx.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/mipi_rx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/mipi_rx.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6161/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6263' Cversion='1.0.1' condition='driver.display-it6263.condition_id'>
      <description>Driver display-it6263</description>
      <files>
        <file category='header' name='components/video/display/it6263/fsl_it6263.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6263/fsl_it6263.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6263/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-sn65dsi83' Cversion='1.0.0' condition='driver.display-sn65dsi83.condition_id'>
      <description>Driver display-sn65dsi83</description>
      <files>
        <file category='header' name='components/video/display/sn65dsi83/fsl_sn65dsi83.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/sn65dsi83/fsl_sn65dsi83.c' projectpath='video'/>
        <file category='include' name='components/video/display/sn65dsi83/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmamux' Cversion='2.1.2' condition='driver.dmamux.condition_id'>
      <description>DMAMUX Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_dmamux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_dmamux.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='emios' Cversion='2.0.0' condition='driver.eMIOS.condition_id'>
      <description>eMIOS Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_emios.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_emios.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='edma' Cversion='2.10.6' condition='driver.edma4.condition_id'>
      <description>EDMA Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_edma.h' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_edma_core.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='edma_soc' Cversion='2.0.0' condition='driver.edma_soc.condition_id'>
      <description>EDMA SOC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_edma_soc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_edma_soc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ektf2k' Cversion='1.0.0' condition='driver.ektf2k.condition_id'>
      <description>Driver ektf2k</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/ektf2k/fsl_ektf2k.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ektf2k/fsl_ektf2k.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ektf2k/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_c40' Cversion='1.0.0' condition='driver.flash_c40.condition_id'>
      <description>flash_c40 Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flash.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_c40_flash.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_c40_flash.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan' Cversion='2.14.1' condition='driver.flexcan.condition_id'>
      <description>FLEXCAN Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexcan.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexcan.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan_edma' Cversion='2.12.0' condition='driver.flexcan_edma.condition_id'>
      <description>FLEXCAN Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexcan_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexcan_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio' Cversion='2.3.0' condition='driver.flexio.condition_id'>
      <description>FLEXIO Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2c_master' Cversion='2.6.1' condition='driver.flexio_i2c_master.condition_id'>
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_i2c_master.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_i2c_master.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd' Cversion='2.3.0' condition='driver.flexio_mculcd.condition_id'>
      <description>FLEXIO MCULCD Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_mculcd.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_mculcd.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd_edma' Cversion='2.0.6' condition='driver.flexio_mculcd_edma.condition_id'>
      <description>FLEXIO MCULCD EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_mculcd_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_mculcd_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi' Cversion='2.4.2' condition='driver.flexio_spi.condition_id'>
      <description>FLEXIO SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_spi.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_spi.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi_edma' Cversion='2.3.0' condition='driver.flexio_spi_edma.condition_id'>
      <description>FLEXIO SPI EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_spi_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_spi_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart' Cversion='2.6.3' condition='driver.flexio_uart.condition_id'>
      <description>FLEXIO UART Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_uart.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_uart.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart_edma' Cversion='2.4.1' condition='driver.flexio_uart_edma.condition_id'>
      <description>FLEXIO UART EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_flexio_uart_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_flexio_uart_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft3267' Cversion='1.0.1' condition='driver.ft3267.condition_id'>
      <description>Touch panel controller FT3267 driver</description>
      <files>
        <file category='header' name='components/touch/ft3267/fsl_ft3267.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft3267/fsl_ft3267.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft3267/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406' Cversion='1.0.0' condition='driver.ft5406.condition_id'>
      <description>Driver ft5406</description>
      <files>
        <file category='header' name='components/touch/ft5406/fsl_ft5406.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406/fsl_ft5406.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406_rt' Cversion='1.0.0' condition='driver.ft5406_rt.condition_id'>
      <description>Driver ft5406_rt</description>
      <files>
        <file category='header' name='components/touch/ft5406_rt/fsl_ft5406_rt.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406_rt/fsl_ft5406_rt.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406_rt/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft6x06' Cversion='1.0.0' condition='driver.ft6x06.condition_id'>
      <description>Driver ft6x06</description>
      <files>
        <file category='header' name='components/touch/ft6x06/fsl_ft6x06.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft6x06/fsl_ft6x06.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft6x06/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxas21002cq' Cversion='1.0.0' condition='driver.fxas21002cq.condition_id'>
      <description>Driver fxas21002cq</description>
      <files>
        <file category='header' name='components/sensor/fxas21002cq/fsl_fxas.h' projectpath='gyroscope'/>
        <file category='sourceC' name='components/sensor/fxas21002cq/fsl_fxas.c' projectpath='gyroscope'/>
        <file category='include' name='components/sensor/fxas21002cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxls8974cf' Cversion='1.0.0' condition='driver.fxls8974cf.condition_id'>
      <description>Driver fxls8974cf</description>
      <files>
        <file category='header' name='components/sensor/fxls8974cf/fsl_fxls.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxls8974cf/fsl_fxls.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxls8974cf/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxos8700cq' Cversion='1.0.0' condition='driver.fxos8700cq.condition_id'>
      <description>Driver fxos8700cq</description>
      <files>
        <file category='header' name='components/sensor/fxos8700cq/fsl_fxos.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxos8700cq/fsl_fxos.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxos8700cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gt911' Cversion='1.0.4' condition='driver.gt911.condition_id'>
      <description>Driver gt911</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/gt911/fsl_gt911.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/gt911/fsl_gt911.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/gt911/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='htu21d' Cversion='1.0.0' condition='driver.htu21d.condition_id'>
      <description>Driver htu21d</description>
      <files>
        <file category='header' name='components/sensor/htu21d/fsl_htu21d.h' projectpath='htu'/>
        <file category='sourceC' name='components/sensor/htu21d/fsl_htu21d.c' projectpath='htu'/>
        <file category='include' name='components/sensor/htu21d/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='icm42688p' Cversion='1.0.0' condition='driver.icm42688p.condition_id'>
      <description>Driver icm42688p</description>
      <files>
        <file category='header' name='components/sensor/icm42688p/fsl_icm42688p.h' projectpath='icm42688p'/>
        <file category='sourceC' name='components/sensor/icm42688p/fsl_icm42688p.c' projectpath='icm42688p'/>
        <file category='include' name='components/sensor/icm42688p/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ili9341' Cversion='1.0.2' condition='driver.ili9341.condition_id'>
      <description>Driver ili9341</description>
      <files>
        <file category='header' name='components/display/ili9341/fsl_ili9341.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ili9341/fsl_ili9341.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ili9341/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='intm' Cversion='2.1.0' condition='driver.intm.condition_id'>
      <description>INTM Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_intm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_intm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lcu' Cversion='2.0.0' condition='driver.lcu.condition_id'>
      <description>lcu Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lcu.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lcu.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpcmp' Cversion='2.3.2' condition='driver.lpcmp.condition_id'>
      <description>LPCMP Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpcmp.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpcmp.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.6.1' condition='driver.lpi2c.condition_id'>
      <description>LPI2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpi2c.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpi2c.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma' Cversion='2.4.4' condition='driver.lpi2c_edma.condition_id'>
      <description>LPI2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpi2c_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpi2c_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi' Cversion='2.7.2' condition='driver.lpspi.condition_id'>
      <description>LPSPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpspi.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpspi.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_edma' Cversion='2.4.7' condition='driver.lpspi_edma.condition_id'>
      <description>LPSPI Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpspi_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpspi_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart' Cversion='2.9.1' condition='driver.lpuart.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpuart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpuart.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_edma' Cversion='2.6.0' condition='driver.lpuart_edma.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_lpuart_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_lpuart_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lsm6dso' Cversion='1.0.0' condition='driver.lsm6dso.condition_id'>
      <description>Driver lsm6dso</description>
      <files>
        <file category='header' name='components/sensor/lsm6dso/fsl_lsm.h' projectpath='tilt_pedo'/>
        <file category='sourceC' name='components/sensor/lsm6dso/fsl_lsm.c' projectpath='tilt_pedo'/>
        <file category='include' name='components/sensor/lsm6dso/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='max30101' Cversion='1.0.0' condition='driver.max30101.condition_id'>
      <description>Driver max30101</description>
      <files>
        <file category='header' name='components/sensor/max30101/fsl_max.h' projectpath='heartrate'/>
        <file category='sourceC' name='components/sensor/max30101/fsl_max.c' projectpath='heartrate'/>
        <file category='include' name='components/sensor/max30101/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mc_rgm' Cversion='2.0.0' condition='driver.mc_rgm.condition_id'>
      <description>Reset generation module driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_mc_rgm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='memory' Cversion='2.0.0' condition='driver.memory.condition_id'>
      <description>MEMORY Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_memory.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8451q' Cversion='1.0.0' condition='driver.mma8451q.condition_id'>
      <description>Driver mma8451q</description>
      <files>
        <file category='header' name='components/sensor/mma8451q/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8451q/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8451q/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8652fc' Cversion='1.0.0' condition='driver.mma8652fc.condition_id'>
      <description>Driver mma8652fc</description>
      <files>
        <file category='header' name='components/sensor/mma8652fc/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8652fc/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8652fc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nmh1000' Cversion='1.0.0' condition='driver.nmh1000.condition_id'>
      <description>Driver nmh1000</description>
      <files>
        <file category='header' name='components/sensor/nmh1000/fsl_nmh1000.h' projectpath='nmh1000'/>
        <file category='sourceC' name='components/sensor/nmh1000/fsl_nmh1000.c' projectpath='nmh1000'/>
        <file category='include' name='components/sensor/nmh1000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='p3t1755' Cversion='2.0.0' condition='driver.p3t1755.condition_id'>
      <description>Driver p3t1755</description>
      <files>
        <file category='header' name='components/sensor/p3t1755/fsl_p3t1755.h' projectpath='component/p3t1755'/>
        <file category='sourceC' name='components/sensor/p3t1755/fsl_p3t1755.c' projectpath='component/p3t1755'/>
        <file category='include' name='components/sensor/p3t1755/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pca9420' Cversion='1.0.0' condition='driver.pca9420.condition_id'>
      <description>Driver pca9420</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/pmic/pca9420/fsl_pca9420.c' projectpath='component/pmic/pca9420'/>
        <file category='header' name='components/pmic/pca9420/fsl_pca9420.h' projectpath='component/pmic/pca9420'/>
        <file category='include' name='components/pmic/pca9420/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pca9422' Cversion='1.0.0' condition='driver.pca9422.condition_id'>
      <description>Driver pca9422</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/pmic/pca9422/fsl_pca9422.c' projectpath='component/pmic/pca9422'/>
        <file category='header' name='components/pmic/pca9422/fsl_pca9422.h' projectpath='component/pmic/pca9422'/>
        <file category='include' name='components/pmic/pca9422/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf1550' Cversion='1.0.0' condition='driver.pf1550.condition_id'>
      <description>Driver pf1550</description>
      <files>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550.h' projectpath='component/pmic/pf1550'/>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550_charger.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550_charger.h' projectpath='component/pmic/pf1550'/>
        <file category='include' name='components/pmic/pf1550/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf3000' Cversion='1.0.0' condition='driver.pf3000.condition_id'>
      <description>Driver pf3000</description>
      <files>
        <file category='sourceC' name='components/pmic/pf3000/fsl_pf3000.c' projectpath='component/pmic/pf3000'/>
        <file category='header' name='components/pmic/pf3000/fsl_pf3000.h' projectpath='component/pmic/pf3000'/>
        <file category='include' name='components/pmic/pf3000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf5020' Cversion='2.0.0' condition='driver.pf5020.condition_id'>
      <description>Driver pf5020</description>
      <files>
        <file category='sourceC' name='components/pmic/pf5020/fsl_pf5020.c' projectpath='component/pmic/pf5020'/>
        <file category='header' name='components/pmic/pf5020/fsl_pf5020.h' projectpath='component/pmic/pf5020'/>
        <file category='include' name='components/pmic/pf5020/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pit' Cversion='2.2.0' condition='driver.pit.condition_id'>
      <description>PIT Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_pit.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_pit.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='power' Cversion='2.0.0' condition='driver.power.condition_id'>
      <description>Power Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_power.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_power.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='psp27801' Cversion='1.0.0' condition='driver.psp27801.condition_id'>
      <description>Driver psp27801</description>
      <files>
        <file category='header' name='components/display/psp27801/fsl_psp27801.h' projectpath='oled'/>
        <file category='sourceC' name='components/display/psp27801/fsl_psp27801.c' projectpath='oled'/>
        <file category='include' name='components/display/psp27801/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='qspi' Cversion='2.3.0' condition='driver.qspi.condition_id'>
      <description>QSPI Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_qspi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_qspi.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='qspi_edma' Cversion='2.2.4' condition='driver.qspi_edma.condition_id'>
      <description>QSPI Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_qspi_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_qspi_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='qspi_mcxe31b' Cversion='2.0.0' condition='driver.qspi_mcxe31b.condition_id'>
      <description>QSPI mcxe31b Soc Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_qspi_soc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_qspi_soc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtc_jdp' Cversion='2.0.0' condition='driver.rtc_jdp.condition_id'>
      <description>RTC JDP Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_rtc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_rtc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtt' Cversion='7.22.0' condition='driver.rtt.condition_id'>
      <description>SEGGER Real Time Transfer(RTT)</description>
      <files>
        <file category='doc' name='components/rtt/License.txt' projectpath='rtt'/>
        <file category='doc' name='components/rtt/README.txt' projectpath='rtt'/>
        <file category='sourceC' name='components/rtt/RTT/SEGGER_RTT.c' projectpath='rtt/RTT'/>
        <file category='header' name='components/rtt/RTT/SEGGER_RTT.h' projectpath='rtt/RTT'/>
        <file category='sourceAsm' name='components/rtt/RTT/SEGGER_RTT_ASM_ARMv7M.S' projectpath='rtt/RTT'/>
        <file category='sourceC' name='components/rtt/RTT/SEGGER_RTT_printf.c' projectpath='rtt/RTT'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_GCC.c' projectpath='rtt/Syscalls'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_IAR.c' projectpath='rtt/Syscalls'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_KEIL.c' projectpath='rtt/Syscalls'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_SES.c' projectpath='rtt/Syscalls'/>
        <file category='doc' name='components/rtt/License.txt' projectpath='rtt'/>
        <file category='doc' name='components/rtt/README.txt' projectpath='rtt'/>
        <file category='include' name='components/rtt/RTT/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtt template' Cversion='7.22.0' condition='driver.rtt.template.condition_id'>
      <description>RTT template configuration</description>
      <files>
        <file category='header' attr='config' name='components/rtt/template/SEGGER_RTT_Conf.h' version='7.22.0' projectpath='rtt/template'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sar_adc' Cversion='2.3.0' condition='driver.sar_adc.condition_id'>
      <description>SAR_ADC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_sar_adc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_sar_adc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='Siul2' Cversion='2.0.3' condition='driver.siul2.condition_id'>
      <description>Siul2 Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_siul2.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_siul2.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963' Cversion='1.2.0' condition='driver.ssd1963.condition_id'>
      <description>Driver ssd1963</description>
      <files>
        <file category='header' name='components/display/ssd1963/fsl_ssd1963.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ssd1963/fsl_ssd1963.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='st7796s' Cversion='1.0.0' condition='driver.st7796s.condition_id'>
      <description>Driver st7796s</description>
      <files>
        <file category='header' name='components/display/st7796s/fsl_st7796s.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/st7796s/fsl_st7796s.c' projectpath='lcdc'/>
        <file category='include' name='components/display/st7796s/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='stm' Cversion='2.0.0' condition='driver.stm.condition_id'>
      <description>stm Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_stm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_stm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='swt' Cversion='2.1.1' condition='driver.swt.condition_id'>
      <description>swt Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_swt.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_swt.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tempsense' Cversion='2.0.0' condition='driver.tempsense.condition_id'>
      <description>tempsense Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_tempsense.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_tempsense.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tma525b' Cversion='1.0.0' condition='driver.tma525b.condition_id'>
      <description>Driver tma525b</description>
      <files>
        <file category='header' name='components/touch/tma525b/fsl_tma525b.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/tma525b/fsl_tma525b.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/tma525b/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='trgmux' Cversion='2.0.1' condition='driver.trgmux.condition_id'>
      <description>TRGMUX Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_trgmux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_trgmux.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tsl2561' Cversion='1.0.0' condition='driver.tsl2561.condition_id'>
      <description>Driver tsl2561</description>
      <files>
        <file category='header' name='components/sensor/tsl2561/fsl_tsl2561.h' projectpath='tsl'/>
        <file category='sourceC' name='components/sensor/tsl2561/fsl_tsl2561.c' projectpath='tsl'/>
        <file category='include' name='components/sensor/tsl2561/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tspc' Cversion='2.0.0' condition='driver.tspc.condition_id'>
      <description>the Touch Sensing Pin Coupling Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_tspc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_tspc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-common' Cversion='1.1.0' condition='driver.video-common.condition_id'>
      <description>Driver video-common</description>
      <files>
        <file category='header' name='components/video/fsl_video_common.h' projectpath='video'/>
        <file category='sourceC' name='components/video/fsl_video_common.c' projectpath='video'/>
        <file category='include' name='components/video/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c' Cversion='1.0.1' condition='driver.video-i2c.condition_id'>
      <description>Driver video-i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/i2c/fsl_video_i2c.h' projectpath='video'/>
        <file category='sourceC' name='components/video/i2c/fsl_video_i2c.c' projectpath='video'/>
        <file category='include' name='components/video/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='virt_wrapper' Cversion='2.0.0' condition='driver.virt_wrapper.condition_id'>
      <description>VIRT_WRAPPER Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_virt_wrapper.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_virt_wrapper.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wkpu' Cversion='2.0.0' condition='driver.wkpu.condition_id'>
      <description>Wakeup Unit Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_wkpu.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXE31B/drivers/fsl_wkpu.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xbic' Cversion='2.0.1' condition='driver.xbic.condition_id'>
      <description>xbic Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_xbic.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_xbic.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xrdc' Cversion='2.0.7' condition='driver.xrdc.condition_id'>
      <description>XRDC Driver</description>
      <files>
        <file category='header' name='devices/MCXE31B/drivers/fsl_xrdc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXE31B/drivers/fsl_xrdc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXE31B/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MCXE31B/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MCXE31B/utilities/fsl_sbrk.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc, mdk, cores=cm7f.condition_id' category='sourceAsm' name='devices/MCXE31B/utilities/fsl_memcpy.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/MCXE31B/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXE31B/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXE31B/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/MCXE31B/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXE31B/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXE31B/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/MCXE31B/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/MCXE31B/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/MCXE31B/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/MCXE31B/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/MCXE31B/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/MCXE31B/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/MCXE31B/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/MCXE31B/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='notifier' Cversion='1.0.0' condition='utility.notifier.condition_id'>
      <description>Utility notifier</description>
      <files>
        <file category='sourceC' name='devices/MCXE31B/utilities/fsl_notifier.c' projectpath='utilities/utilities'/>
        <file category='header' name='devices/MCXE31B/utilities/fsl_notifier.h' projectpath='utilities/utilities'/>
        <file category='include' name='devices/MCXE31B/utilities/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/MCXE31B/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXE31B/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXE31B/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/MCXE31B/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/MCXE31B/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/MCXE31B/utilities/str/'/>
      </files>
    </component>
  </components>
</package>