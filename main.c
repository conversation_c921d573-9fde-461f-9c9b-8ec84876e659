#ifndef MAIN_H

#define MAIN_H

#endif

#include "main.h"
#include "json_parser.h"
#include "motor_unified_config.h"
#include "motor_config.h"           // если есть
#include "motor_autocalibration.h"  // если нужен автотюнинг
#include "UserFunction.h"

uint8_t u8_Uart1_Cmd = 0;
uint8_t u8_CmdIndex_1 = 0;
uint8_t u8_CmdBuffer_1[7] = {0};
uint8_t u8_ReceivedCommand[7] = {0};
uint8_t u8_CmdNumber = 0;

uint8_t T_Impulse = 0;
uint8_t T_Pause = 0;

uint16_t M4_StartStepTime = 500;
uint16_t M4_StopStepTime = 100;
uint16_t M4_DeltaStepTime = 10;
uint16_t M4_StepNumber;

uint16_t M3_StartStepTime = 500;
uint16_t M3_StopStepTime = 100;
uint16_t M3_DeltaStepTime = 50;
uint16_t M3_StepNumber;


uint16_t Encoders_Angele = 0;
uint16_t Encoder1_Position = 0;
uint16_t Encoder2_Position = 0;

uint16_t M1_Angele = 0;
uint16_t M2_Angele = 0;

uint16_t Rotate_Angele = 0;

uint8_t M7_Error = 0;
uint8_t SensorPositionError = 0;//D1,D3,D5,D7,D10

uint8_t projectile_number = 0;

#define UART1_RX_BUFFER_SIZE 128
uint8_t uart1_json_buffer[UART1_RX_BUFFER_SIZE];
uint8_t uart1_json_index = 0;
uint8_t uart1_json_ready = 0;

// =================================================================
// НАСТРОЙКИ ВСЕХ МОТОРОВ - МОЖНО МЕНЯТЬ ВРУЧНУЮ
// =================================================================

// M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ (NEMA23 + редуктор 1:40)
uint16_t M1_StepDelay      = 1;    // мс
uint16_t M1_PulseWidth     = 1;    // мс
uint16_t M1_MaxSpeed       = 2000; // Гц
uint16_t M1_StepDelay_uS   = 100;  // мкс
uint16_t M1_PulseWidth_uS  = 100;  // мкс

// M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ
uint16_t M2_StepDelay_CW     = 1;   // мс
uint16_t M2_StepDelay_CCW    = 1;   // мс
uint16_t M2_PulseWidth_CCW   = 1;   // мс
uint16_t M2_ExtraDelay_CCW   = 1;   // мс
uint16_t M2_MaxSpeed         = 1500;// Гц
uint16_t M2_StepDelay_uS     = 150; // мкс
uint16_t M2_PulseWidth_uS    = 150; // мкс

// M3 - МОТОР КАРЕТКИ
// M3_StartStepTime, M3_StopStepTime, M3_DeltaStepTime already defined above
uint16_t M3_StepNumber       = 400; // шаги
uint16_t M3_StepDelay        = 1;   // мс
uint16_t M3_PulseWidth       = 1;   // мс
uint16_t M3_MaxSpeed         = 2000;// Гц
uint16_t M3_StepDelay_uS     = 150; // мкс
uint16_t M3_PulseWidth_uS    = 150; // мкс

// M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
// M4_StartStepTime, M4_StopStepTime, M4_DeltaStepTime already defined above
uint16_t M4_StepNumber       = 150; // шаги
uint16_t M4_StepDelay        = 1;   // мс
uint16_t M4_PulseWidth       = 1;   // мс
uint16_t M4_MaxSpeed         = 800; // Гц
uint16_t M4_StepDelay_uS     = 625; // мкс
uint16_t M4_PulseWidth_uS    = 625; // мкс

// M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
uint16_t M5_StepDelay        = 1;   // мс
uint16_t M5_PulseWidth       = 1;   // мс
uint16_t M5_MaxSpeed         = 1200;// Гц
uint16_t M5_StepDelay_uS     = 417; // мкс
uint16_t M5_PulseWidth_uS    = 417; // мкс

// M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА
uint16_t M6_StepDelay        = 1;   // мс
uint16_t M6_PulseWidth       = 1;   // мс
uint16_t M6_MaxSpeed         = 1000;// Гц

// M7 - DC МОТОР СЖАТИЯ И РАЗЖАТИЯ МЕХАНИЗМА ЗАХВАТА
uint16_t M7_Timeout          = 3000; // мс
uint16_t M7_CheckDelay       = 250;  // мс
uint16_t M7_HoldTime         = 5000; // мс
uint16_t M7_HoldPulse        = 100;  // мс

// =================================================================

int main(void)
{

	uint16_t sw = 0;

	RCCInit();//Set up clocks for peripherals using by App

	SetupTimers();
	
  SetupGpioIO(); //Set up GPIO using by App peripherals	
	
  ADC1_Config();	
	
	SetUp_I2C1();
	
  Delay_mS(5);

/************* Enable INTs in NVIC *****************************/
	 
// For Timer TIM4, TIM3:
   NVIC_EnableIRQ(TIM4_IRQn);  // Enable IRQ from TIM4 for LED PC13 blinking
	 NVIC_SetPriority(TIM4_IRQn,8);
	
// Enable  TIM2 INT in NVIC :	 
   NVIC_EnableIRQ(TIM2_IRQn);  //	
	 NVIC_SetPriority(TIM2_IRQn,2);	

// For UART2	
	 NVIC_SetPriority(USART2_IRQn,10);
	 NVIC_EnableIRQ (USART2_IRQn);
  Delay_mS(5);	 
	
// For UART1	 
	 NVIC_EnableIRQ (USART1_IRQn);
	 NVIC_SetPriority(USART1_IRQn,12);
  Delay_mS(5);	 

  Delay_mS(5);
// For CAN1
   //NVIC_EnableIRQ (CAN1_RX1_IRQn);
	 //NVIC_SetPriority(CAN1_RX1_IRQn,6);

// Set priority for TIM3 Higher then TIM4 and TIM3
	 //NVIC_SetPriority (TIM3_IRQn, 5);

// ��������� ���������� ����������:1
	__enable_irq(); //	
	
/*************************************************************************/	
		//GPIOC -> BSRR |= GPIO_BSRR_BS13; // Set ON LED on PC13

BEEP_ON;
Delay_mS(250);
BEEP_OFF;
Delay_mS(250);
Delay_mS(250);
Delay_mS(250);
Delay_mS(250);

    SetUp_I2C1(); //I2C1 enabled here		
    Delay_mS(5);		 
		 

		Signal_3p1D(); 
		Delay_mS(250);
		
		LCD_Setup();
		 
		LCD_SendString((uint8_t *)"===== CORDON-82 ====",20);

		Signal_3p1D(); 
		Delay_mS(250);

		LCD_Send_Command(LCD_2_LINE_POS_0);
		LCD_SendString((uint8_t *)" Board self testing  ",20);
		
		LCD_Send_Command(LCD_3_LINE_POS_0);
		LCD_SendString((uint8_t *)"  Please waite...   ",20);		

		Signal_3p1D(); 
		Delay_mS(250);
		
		LCD_Send_Command(LCD_4_LINE_POS_0);	
    for(uint8_t i =0; i<20; i++)
 		{
			LCD_SendString((uint8_t *)"'-'",1);
      BEEP_ON;
      Delay_mS(100);
      BEEP_OFF;	
      Delay_mS(250);			
 		}			

Encoder1_Disable;
Encoder2_Disable;	

		LCD_Send_Command(LCD_3_LINE_POS_0);
		LCD_SendString((uint8_t *)"  Waite for Commnd  ",20);
		LCD_Send_Command(LCD_4_LINE_POS_0);	
		
		//UART1 and CMD Tsting:
while(1)
		{
			sw = 0;
			if(u8_Uart1_Cmd)//Handle Commnd from UART:
			  {
					BEEP_ON;
          Delay_mS(250);
          BEEP_OFF;	
          Delay_mS(250);
					Delay_mS(250);
					
					//Save received command:
					SaveReceivedCommand();
					
					//Define Cmd Number:
					u8_CmdNumber = u8_ReceivedCommand[1];
					
					switch(u8_CmdNumber)
					 {
						case 0:  //�reset� M1, M2 set to D14,D13 positions
							     //RotateM1
						       RotateM1_D14_Position();//Go to D14
						       //Rotate M2
						       RotateM2_D13_Position();//Go to D13
						
						       //Sed Replay for Cmd_0
						       Send_To_Main(u8_ReceivedCommand, 7); //Replay
		               LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"   CMD_0 Received   ",20);
							break;

						case 1:  //�state� Current M1, M2 positions
						       Delay_mS(10);							
						       //Get current angele M1:
						       GetEncoder_1_Angele(); //M1_Angele
						       u8_ReceivedCommand[2] = ((M1_Angele & 0x0300)>> 8);//MSB
						       u8_ReceivedCommand[3] = (M1_Angele & 0x00FF);      //LSB
						       Delay_mS(10);
						       //Get current angele M2
						       GetEncoder_2_Angele(); //M2_Angele
						       u8_ReceivedCommand[4] = ((M2_Angele & 0x0300)>> 8);//MSB
						       u8_ReceivedCommand[5] = (M2_Angele & 0x00FF);      //LSB
						
						        //Send Replay for Cmd_1:	
                   Send_To_Main(u8_ReceivedCommand, 7); //Replay
		               LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"   CMD_1 Received   ",20);						
							break;
						
						case 2:  //rotate_horizontal CW from current position
						           //Send Replay for Cmd_2	
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
		                LCD_SendString((uint8_t *)"   CMD_2 Received   ",20);							
							        //Get rotate angele from command:
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						        Rotate_Angele |= u8_ReceivedCommand[3];
						          //Rotate M1:
						        Rotate_M1_CW(Rotate_Angele);
						
						
							break;
						
						case 3:  //rotate_horizontal CWW from current position 
							              //Send Replay for Cmd_3:	
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
		                LCD_SendString((uint8_t *)"   CMD_3 Received   ",20);							
							     //Get rotate angele from command
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						        Rotate_Angele |= u8_ReceivedCommand[3];						
						       //Rotate M1
						        Rotate_M1_CCW(Rotate_Angele);
						       //Send Replay for Cmd_3	
                   //Send_To_Main(u8_ReceivedCommand, 7); //Replay						
							break;						
						
						case 4:  //rotate_vertical CW from current position
						           //Send Replay for Cmd_4	
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
		                LCD_SendString((uint8_t *)"   CMD_4 Received   ",20);							
							     //Get rotate angele from command	
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						       //Rotate M2
						        Rotate_M2_CW(Rotate_Angele);
						       //Send Replay for Cmd_4
                    //Send_To_Main(u8_ReceivedCommand, 7); //Replay						
							break;
	
						case 5:  //rotate_vertical CCW from current position
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"   CMD_5 Received   ",20);						
							         //Get rotate angele from command	
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						           //Rotate M2
						        Rotate_M2_CCW(Rotate_Angele);						
						           //Send Replay for Cmd_4
                    //Send_To_Main(u8_ReceivedCommand, 7); //Replay						
							break;

						
						case 6:  //�get_projectiles� 
							      projectile_number = 0;
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"   CMD_6 Received   ",20);							
							      //u8_ReceivedCommand[2] = 7;
						        for(uint8_t i = 0; i<6; i++)
										 {
						           Rotate_M6_Step(M6_Forward);
										 }
										u8_ReceivedCommand[2] = projectile_number;
							      Send_To_Main(u8_ReceivedCommand, 7); //Replay
							
							break;
						
						case 7:  //�fire�
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"   CMD_7 Received   ",20);
						        LoadUp();
						        if(SensorPositionError)
										 {
		                   LCD_Send_Command(LCD_4_LINE_POS_0);	
                       LCD_SendString((uint8_t *)"   Sensors Error    ",20);
                       while(1)
 											  {
											  }												 
										 }
                   						  
							break;
			//Reserved commands:						
						case 8:  //Rotate_M3 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M3 Forward...",20);						
						           //Rotate M3
						        Rotate_M3(M3_Forward);		//Stop if D2					
							break;
						
						case 9:  //Rotate_M3 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M3 Back......",20);						
						           //Rotate M3
						        Rotate_M3(M3_Back);			//Stop if D1						
							break;
						
						case 10: //Rotate_M4 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M4 Forward...",20);						
						           //Rotate M4
						        Rotate_M4(M4_Forward);			//Stop if D8							
							
							break;
						
						case 11:  //Rotate_M4 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M4 Back......",20);						
						           //Rotate M5
						        Rotate_M4(M4_Back);			//Stop if D9						
							break;
						
						case 12: //Rotate_M5 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M5 Forward...",20);						
						           //Rotate M5
						        Rotate_M5(M5_Forward);				//Stop if D6						
							break;
						
						case 13:  //Rotate_M5 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M5 Back......",20);						
						           //Rotate M5
						        Rotate_M5(M5_Back);			//Stop if D5						
							break;

						case 14: //Rotate_M6 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M6 Forward...",20);						
						           //Rotate M6
						        Rotate_M6(M6_Forward);			//Stop if D4							
							break;

						case 15:  //Rotate_M6 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M6 Back......",20);						
						           //Rotate M6
						        Rotate_M6(M6_Back);					//Stop if D4						
							break;

						case 16: //Rotate_M7 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M7 Forward...",20);						
						           //Rotate M7
						        Rotate_M7(M7_Forward);				//Stop if D11	
                    if(M7_Error)
 										{
		                  LCD_Send_Command(LCD_4_LINE_POS_0);	
                      LCD_SendString((uint8_t *)"M7 ERROR............",20);											
 										}
                    else
 										{
		                  LCD_Send_Command(LCD_4_LINE_POS_0);	
                      LCD_SendString((uint8_t *)"M7 STOP.............",20);											
 										}								
							break;

						case 17:  //Rotate_M7 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M7 Back......",20);						
						           //Rotate M6
						        Rotate_M7(M7_Back);				//Stop if D10
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"M7 STOP.............",20);						
								
							break;						
			//Service commands:
						case 100:
							break;
						case 101:
							break;
						case 102:
							break;
						case 103:
							break;
						case 104:
							break;
						case 105:
							break;
						
						default: 
							break;						
					 } //End OF switch(u8_CmdNumber)
					
					 //Get ready for new command:
          ClearCmdBuffer();
					u8_Uart1_Cmd = 0;
					u8_CmdIndex_1 = 0;
			   }//End OF if(u8_Uart1_Cmd)

				 // Handle JSON commands
				 else if (uart1_json_ready) {
				     uart1_json_ready = 0;
				     JSON_ParsedCommand_t parsed_cmd;
				     if (JSON_Parse((const char*)uart1_json_buffer, &parsed_cmd) == JSON_OK) {
				         JSON_ExecuteCommand(&parsed_cmd); // <-- выполнение сразу после парсинга
				     } else {
				         LCD_SendString((uint8_t *)"JSON Parse Error", 16);
				     }
				     uart1_json_index = 0;
				 }

				 //Commands for testing:
			 else if(!(SW1) || !(SW2) || !(SW3) || !(SW4)  || !(SW5) || !(SW6) )//Command from SW1-SW6
			  {
					sw |= ((SW6) | (SW5) | (SW4) | (SW3) |  (SW2) | (SW1));	
					
				  LCD_Send_Command(LCD_3_LINE_POS_0);
		      LCD_SendString((uint8_t *)" Waite for command: ",20);					
					
					BEEP_ON;
          Delay_mS(250);
          BEEP_OFF;					
          
					//Define pressed SW 
					sw = sw >> 8;
					sw |= 0x0C;
					sw = ~sw;
					sw &= 0x00FF; 
					switch(sw)
					 {
						case 1:     //SW1 Pressed
							     Encoder1_Enable;
							     //T_Impulse = 5;
						       //T_Pause = 100;
						       LCD_Send_Command(LCD_4_LINE_POS_0);
						       if( (GPIOB->ODR | (GPIO_ODR_ODR1)))
									   {
		                  LCD_SendString((uint8_t *)"  M1 Rotate CW ...  ",20);
									   }
										else if((GPIOB->ODR & (GPIO_ODR_ODR1)) == 0) 
											LCD_SendString((uint8_t *)"  M1 Rotate CCW ...  ",20);
		               //Choose Motor_1:
						        Choose_M1;
		                //GPIOB->ODR |= GPIO_ODR_ODR3;
		                //GPIOB->ODR &= ~(GPIO_ODR_ODR4);
		                //GPIOB->ODR &= ~(GPIO_ODR_ODR5);
	
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }						
						
							      while(!(SW1))
										  {
												//M1 Rotate
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              //Delay_mS(50);//
												Delay_mS(5);
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              //Delay_mS(50);//
												Delay_mS(5);
												Encoders_Angele = (uint16_t)GPIOD->IDR;
												Encoders_Angele &= 0x03FF;//Maska
												M1_Angele |= Encoders_Angele;
											/*	if(D1 || D2 || D3 || D4 || D5 || D6 || D7 || D8 || D9 || D10 || D11 || D12 || D13 || D14)
												 {
													 break;
												 }*/
											/*	if((Encoders_Angele >= M1_Angele) && (Encoders_Angele <= M1_Angele))
												 {
													 break;
												 }*/
												
										/*		if(Encoders_Angele <= 0x0002)
												 {
													 break;
												 }
												else if( (Encoders_Angele >= 0x03FD) && (Encoders_Angele <= 0x03FF) )
												 {
													 break;
												 }*/
										
										  }
											  //M1 Stop 
						       LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"     M1 Stoped...   ",20);											
										//Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
										
                    //DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable
											Encoder1_Disable;
											//while(!(SW1)){}
                  break;
						
						case 2:     //SW2 Pressed
		               //Choose Motor_2:
						        Encoder2_Enable;
						        Choose_M2;
                    
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M2 Start     ",20);
						
						        DD16_Enble;
		                //0GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }							
							       while(!(SW2))
										  {
												//M2 Rotate
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              //Delay_mS(100);
												//Delay_mS(5);
												//Delay_mS(200);
												Delay_mS(10);
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              Delay_mS(10);
                        //Delay_mS(5);												
										  }	
											  //M2 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor

						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M2 Stop      ",20);
											
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable
                    Encoder2_Disable;											
                  break;
						
						case 16:    //SW3 Pressed
		               //Choose Motor_3:
						        Choose_M3;

						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M3 Start     ",20);						
						
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }										
							       while(!(SW3))
										  {
												//M3 Rotate
												for(uint16_t i = 0; i < 2000 ;i++)
												{
 					               GPIOB->ODR |= GPIO_ODR_ODR0;
					              //Delay_mS(50);
												//Delay_mS(5);
												//Delay_mS(1);
												//Delay_uS(200);
												//Delay_uS(400);
												//Delay_uS(500);
													
                         Delay_uS(M3_StartStepTime);
													
					               GPIOB->ODR &= (~GPIO_ODR_ODR0);
													
					              //Delay_mS(50);
                        //Delay_mS(5);	
												//Delay_mS(1);
												//Delay_uS(200);
												//Delay_uS(400);
												//Delay_uS(500);
													Delay_uS(M3_StartStepTime);
												}
												if(M3_StartStepTime > M3_StopStepTime)
												 {
													 M3_StartStepTime = M3_StartStepTime - M3_DeltaStepTime;//50 uS
												 }
										  }		
											  //M3 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M3 Stop      ",20);										
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
                  break;
						
						case 32:    //SW4 Pressed
		               //Choose Motor_4:
						        Choose_M4;
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M4 Start     ",20);
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }								
							       while(!(SW4))
										  {
												//M4 Rotate
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              //Delay_mS(50);
												//Delay_mS(5);
												//Delay_mS(1);
												//Delay_uS(1000);
												//Delay_uS(250);
												//Delay_uS(80);
												Delay_uS(M4_StartStepTime);//500uS
												
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              //Delay_mS(50);
                        //Delay_mS(5);
                         //Delay_mS(1);
                        //Delay_uS(1000);
												
                        //Delay_uS(250);
                        //Delay_uS(80);	
                        Delay_uS(M4_StartStepTime);//500uS
                      /*  if(M4_StartStepTime > M4_StopStepTime )
												 {
													 M4_StartStepTime -= M4_DeltaStepTime;
												 }*/													
										  }	
											  //M4 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M4 Stop      ",20);
											
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
                  break;
						
						case 64:    //SW5 Pressed
		               //Choose Motor_5:
						        Choose_M5;
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M5 Start     ",20);
						
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }								
							       while(!(SW5))
										  {
												//M5 Rotate
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              //Delay_mS(50);
												//Delay_mS(5);
												//Delay_mS(1);
												Delay_uS(500);
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              //Delay_mS(50);													
												//Delay_mS(5);
												//Delay_mS(1);
												Delay_uS(500);
										  }	
											  //M5 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Moto
										DD16_Disble;
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M5 Stop      ",20);
											
											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
                  break;
						
						case 128:   //SW6 Pressed
		               //Choose Motor_6:
						        Choose_M6;
						        LCD_Send_Command(LCD_4_LINE_POS_0);						
                    LCD_SendString((uint8_t *)"       M6 Start     ",20);                    
						
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }									
							       while(!(SW6))
										  {
												//M6 Rotate
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              //Delay_mS(50);
												//Delay_mS(5);
												//Delay_mS(1);
												//Delay_mS(2);
												Delay_mS(4);
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              //Delay_mS(50);												
												//Delay_mS(5);
												Delay_mS(1);
										  }
											  //M6 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
						        LCD_Send_Command(LCD_4_LINE_POS_0);											
                    LCD_SendString((uint8_t *)"       M5 Stop     ",20);						
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable											
                  break;
						
						default:
                  break;													
					 }
					
			  }
		}//END OF While(1), testing

while(1)//Test M7
{
	//DPT Motor testing:
      M7_Stop;
        Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);	
			M7_GO_Left;	
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);	
      M7_Stop;
        Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);	
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);				
      M7_GO_Right	;
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);				

}

}//End OF Main


//Some user function:

void SaveReceivedCommand()
{
	for(uint8_t i = 0;i<7; i++)
	 {
		 u8_ReceivedCommand[i] = u8_CmdBuffer_1[i];
	 }
}

void Delay_mS(uint8_t Delay)
{
 uint16_t Delay_Ticks = 0;
 Delay_Ticks = Delay * 100;
	
 TIM2->CR1 |= TIM_CR1_CEN;   // Start TIM2 count
 while(1)
	{
		if(TIM2->CNT >= Delay_Ticks)
			{
        TIM2->CR1 &= ~TIM_CR1_CEN;   // Stop TIM2 count	
				TIM2->CNT = 0x0000;
        break;						
			}
	}	
}

void Delay_uS(uint16_t Delay)//time in uS 
{
 //uint16_t Delay_Ticks = 0;
 //Delay_Ticks = Delay * 10; //time in uS
	
 TIM3->CNT = 0x0000;
 TIM3->CR1 |= TIM_CR1_CEN;   // Start TIM3 count
 while(1)
	{
		if(TIM3->CNT >= Delay)
			{
        TIM3->CR1 &= ~TIM_CR1_CEN;   // Stop TIM3 count	
				TIM3->CNT = 0x0000;
        break;						
			}
	}	
}

//Converts I2C Data to ASCII string
void Int_To_Char(uint16_t data, uint8_t *buffer)
{
	 //uint16_t pow10[4] = {1000,100,10,1};
	 uint16_t pow10[2] = {10,1};
	 uint8_t count = 0; // 
   uint8_t i = 0;
	 uint8_t *ptr = buffer;
	 
	 do
	 {
		 while(data >= pow10[i])
		  {
			 count++;
			 data = data - pow10[i];
		  }
		 *ptr = count + '0';
			//Ticks_To_Char[i] = count + '0';
			ptr++;
		  i++;
			count = 0;
   }while(i < 2); 
	 
 } // End of Int_To_Char(int Ticks, char *buffer)

void USART1_IRQHandler(void) // ISR of USART1 
{
  if (USART1->SR & USART_SR_RXNE) // If INT from RX buffer
  { 
    uint8_t data = (uint8_t)USART1->DR;

    // Если это JSON-команда (начинается с '{'), копим в отдельный буфер
    if (uart1_json_index == 0 && data == '{') {
      uart1_json_buffer[uart1_json_index++] = data;
    } else if (uart1_json_index > 0) {
      if (uart1_json_index < UART1_RX_BUFFER_SIZE - 1) {
        uart1_json_buffer[uart1_json_index++] = data;
        if (data == '}') { // конец JSON-строки
          uart1_json_buffer[uart1_json_index] = 0;
          uart1_json_ready = 1;
        }
      } else {
        uart1_json_index = 0; // overflow protection
      }
    } else {
      // Старый бинарный протокол (7 байт, начинается с '$')
      u8_CmdBuffer_1[u8_CmdIndex_1] = data;
      if ((u8_CmdIndex_1 == 6) && (u8_CmdBuffer_1[6] == ';') && u8_CmdBuffer_1[0] == 0x24) {
        u8_Uart1_Cmd = 1;
        u8_CmdIndex_1 = 0;
      } else if (u8_CmdIndex_1 != 6) {
        u8_CmdIndex_1++;
      }
    }
  }
  USART1->SR;
  USART1->DR;
 }

void USART2_IRQHandler(void) // ISR of USART2 
{
	//Send_To_Main("\r\n INT UART2 \r\n",15);
  if (USART2->SR & USART_SR_RXNE) // If INT from RX buffer
   { 
		 	//Send_To_Main("\r\n RX UART1 \r\n",14);
		 //UART1_Silence = 0; // Reset Silence Flag
	   //u8_CmdBuffer_2[u8_CmdIndex_2] = (uint8_t)USART1->DR; //
		 
		 //if((u8_CmdIndex_2 == 7) && (u8_CmdBuffer_2[7] == ';'))
		 //if(u8_CmdIndex_2 == 7)
		 //  {
			//  u8_Uart2_Cmd = 1;// �������� �������
		  // }
		 //else 
			//   u8_CmdIndex_2++;
    }
	 USART2->SR;
 }

 void ClearCmdBuffer(void)
 {
	 for(uint8_t i = 0; i<7; i++)
	  {
			u8_ReceivedCommand[i] = 0x00;
	  }
 }



