<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EZR32LG_DFP</name>
  <description>Silicon Labs EZR32LG EZR Leopard Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EZR32LG_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EZR32LG_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EZR32LG</keyword>
    <keyword>EZR32</keyword>
    <keyword>EZR Leopard Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EZR32LG Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M3" DcoreVersion="r2p1" Dfpu="0" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="48000000"/>
      <book name="Documents/cortex_m3_dgug.pdf"      title="Cortex-M3 Generic User Guide"/>
      <book name="Documents/efm32-cortex-m3-rm.pdf"  title="EFM32 Cortex-M3 Reference Manual"/>
      <book name="Documents/EZR32LG-RM.pdf"  title="EZR32LG Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M3 processor running at up to 48 MHz&#xD;&#xA;- Up to 256 kB Flash and 32 kB RAM memory&#xD;&#xA;- Energy efficient and autonomous peripherals&#xD;&#xA;- Ultra low power Energy Modes with sub-uA operation&#xD;&#xA;- Fast wake-up time of only 2 us&#xD;&#xA;&#xD;&#xA;The EFM32LG microcontroller series revolutionizes the 8- to 32-bit market with a combination of unmatched performance and ultra low power consumption in both active- and sleep modes. EFM32LG devices consume as little as 211 uA/MHz in run mode.
      </description>

      <subFamily DsubFamily="EZR32LG230">
        <book         name="Documents/ezr32lg230_datasheet.pdf"      title="EZR32LG230 Data Sheet"/>
        <book         name="Documents/ezr32lg-errata.pdf"         title="EZR32LG230 Errata"/>
        <book         name="Documents/EZR32LG_DataShort.pdf"      title="EZR32LG230 Data Short"/>
        <!-- *************************  Device 'EZR32LG230F128R55'  ***************************** -->
        <device Dname="EZR32LG230F128R55">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R55"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F128R60'  ***************************** -->
        <device Dname="EZR32LG230F128R60">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R60"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F128R61'  ***************************** -->
        <device Dname="EZR32LG230F128R61">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R61"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F128R63'  ***************************** -->
        <device Dname="EZR32LG230F128R63">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R63"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F128R67'  ***************************** -->
        <device Dname="EZR32LG230F128R67">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R67"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F128R68'  ***************************** -->
        <device Dname="EZR32LG230F128R68">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R68"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F128R69'  ***************************** -->
        <device Dname="EZR32LG230F128R69">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F128R69"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F128R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R55'  ***************************** -->
        <device Dname="EZR32LG230F256R55">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R55"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R60'  ***************************** -->
        <device Dname="EZR32LG230F256R60">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R60"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R61'  ***************************** -->
        <device Dname="EZR32LG230F256R61">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R61"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R63'  ***************************** -->
        <device Dname="EZR32LG230F256R63">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R63"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R67'  ***************************** -->
        <device Dname="EZR32LG230F256R67">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R67"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R68'  ***************************** -->
        <device Dname="EZR32LG230F256R68">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R68"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F256R69'  ***************************** -->
        <device Dname="EZR32LG230F256R69">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F256R69"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F256R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R55'  ***************************** -->
        <device Dname="EZR32LG230F64R55">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R55"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R60'  ***************************** -->
        <device Dname="EZR32LG230F64R60">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R60"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R61'  ***************************** -->
        <device Dname="EZR32LG230F64R61">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R61"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R63'  ***************************** -->
        <device Dname="EZR32LG230F64R63">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R63"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R67'  ***************************** -->
        <device Dname="EZR32LG230F64R67">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R67"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R68'  ***************************** -->
        <device Dname="EZR32LG230F64R68">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R68"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG230F64R69'  ***************************** -->
        <device Dname="EZR32LG230F64R69">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG230F64R69"/>
          <debug      svd="SVD/EZR32LG/EZR32LG230F64R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EZR32LG330">
        <book         name="Documents/ezr32lg330_datasheet.pdf"      title="EZR32LG330 Data Sheet"/>
        <book         name="Documents/ezr32lg-errata.pdf"         title="EZR32LG330 Errata"/>
        <book         name="Documents/EZR32LG_DataShort.pdf"      title="EZR32LG330 Data Short"/>
        <!-- *************************  Device 'EZR32LG330F128R55'  ***************************** -->
        <device Dname="EZR32LG330F128R55">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R55"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F128R60'  ***************************** -->
        <device Dname="EZR32LG330F128R60">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R60"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F128R61'  ***************************** -->
        <device Dname="EZR32LG330F128R61">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R61"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F128R63'  ***************************** -->
        <device Dname="EZR32LG330F128R63">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R63"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F128R67'  ***************************** -->
        <device Dname="EZR32LG330F128R67">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R67"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F128R68'  ***************************** -->
        <device Dname="EZR32LG330F128R68">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R68"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F128R69'  ***************************** -->
        <device Dname="EZR32LG330F128R69">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F128R69"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F128R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R55'  ***************************** -->
        <device Dname="EZR32LG330F256R55">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R55"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R60'  ***************************** -->
        <device Dname="EZR32LG330F256R60">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R60"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R61'  ***************************** -->
        <device Dname="EZR32LG330F256R61">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R61"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R63'  ***************************** -->
        <device Dname="EZR32LG330F256R63">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R63"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R67'  ***************************** -->
        <device Dname="EZR32LG330F256R67">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R67"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R68'  ***************************** -->
        <device Dname="EZR32LG330F256R68">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R68"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F256R69'  ***************************** -->
        <device Dname="EZR32LG330F256R69">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F256R69"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F256R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R55'  ***************************** -->
        <device Dname="EZR32LG330F64R55">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R55"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R60'  ***************************** -->
        <device Dname="EZR32LG330F64R60">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R60"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R61'  ***************************** -->
        <device Dname="EZR32LG330F64R61">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R61"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R63'  ***************************** -->
        <device Dname="EZR32LG330F64R63">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R63"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R67'  ***************************** -->
        <device Dname="EZR32LG330F64R67">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R67"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R68'  ***************************** -->
        <device Dname="EZR32LG330F64R68">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R68"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32LG330F64R69'  ***************************** -->
        <device Dname="EZR32LG330F64R69">
          <compile header="Device/SiliconLabs/EZR32LG/Include/em_device.h"  define="EZR32LG330F64R69"/>
          <debug      svd="SVD/EZR32LG/EZR32LG330F64R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EZR32LG">
      <description>Silicon Labs EZR32LG device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EZR32LG*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EZR32LG">
      <description>System Startup for Silicon Labs EZR32LG device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EZR32LG/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EZR32LG/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EZR32LG/Source/ARM/startup_ezr32lg.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/EZR32LG/Source/GCC/startup_ezr32lg.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EZR32LG/Source/GCC/ezr32lg.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EZR32LG/Source/system_ezr32lg.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
