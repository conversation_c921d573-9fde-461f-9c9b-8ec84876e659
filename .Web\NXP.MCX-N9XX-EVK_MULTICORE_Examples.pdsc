<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MCX-N9XX-EVK_MULTICORE_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware multicore Examples Pack for MCX-N9XX-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCX-N9XX-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MULTICORE" vendor="NXP" version="12.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="MCXN947_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="freertos_message_buffers_cm33_core0" folder="boards/mcxn9xxevk/multicore_examples/freertos_message_buffers/cm33_core0" doc="readme.md">
      <description>The FreeRTOS Message Buffers multicore project is a simple demonstration program that uses the MCUXpresso SDK software and the Message Buffers component of FreeRTOS. It shows how to implement lightweight core to core...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freertos_message_buffers_cm33_core0.uvprojx"/>
        <environment name="csolution" load="../freertos_message_buffers.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_cm33_core0" folder="boards/mcxn9xxevk/multicore_examples/hello_world/cm33_core0" doc="readme.md">
      <description>The Multicore Hello World demo application demonstrates how to set up projects for individualcores on a multicore system. In this demo, the primary core prints the "Hello World from the Primary Core!"string to the...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_cm33_core0.uvprojx"/>
        <environment name="csolution" load="../hello_world_multicore.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="multicore_manager_cm33_core0" folder="boards/mcxn9xxevk/multicore_examples/multicore_manager/cm33_core0" doc="readme.md">
      <description>The Multicore Manager example application demonstrates advanced features of the MCMgr component. In this demo, the primary core prints the "Hello World from the Primary Core!" string to the terminal and then releases...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/multicore_manager_cm33_core0.uvprojx"/>
        <environment name="csolution" load="../multicore_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_cm33_core0" folder="boards/mcxn9xxevk/multicore_examples/rpmsg_lite_pingpong/cm33_core0" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of the...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_cm33_core0.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_rtos_cm33_core0" folder="boards/mcxn9xxevk/multicore_examples/rpmsg_lite_pingpong_rtos/cm33_core0" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong RTOS project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_rtos_cm33_core0.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong_rtos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="freertos_message_buffers_cm33_core1" folder="boards/mcxn9xxevk/multicore_examples/freertos_message_buffers/cm33_core1" doc="readme.md">
      <description>The FreeRTOS Message Buffers multicore project is a simple demonstration program that uses the MCUXpresso SDK software and the Message Buffers component of FreeRTOS. It shows how to implement lightweight core to core...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freertos_message_buffers_cm33_core1.uvprojx"/>
        <environment name="csolution" load="../freertos_message_buffers.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_cm33_core1" folder="boards/mcxn9xxevk/multicore_examples/hello_world/cm33_core1" doc="readme.md">
      <description>The Multicore Hello World demo application demonstrates how to set up projects for individualcores on a multicore system. In this demo, the primary core prints the "Hello World from the Primary Core!"string to the...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_cm33_core1.uvprojx"/>
        <environment name="csolution" load="../hello_world_multicore.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="multicore_manager_cm33_core1" folder="boards/mcxn9xxevk/multicore_examples/multicore_manager/cm33_core1" doc="readme.md">
      <description>The Multicore Manager example application demonstrates advanced features of the MCMgr component. In this demo, the primary core prints the "Hello World from the Primary Core!" string to the terminal and then releases...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/multicore_manager_cm33_core1.uvprojx"/>
        <environment name="csolution" load="../multicore_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_rtos_cm33_core1" folder="boards/mcxn9xxevk/multicore_examples/rpmsg_lite_pingpong_rtos/cm33_core1" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong RTOS project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_rtos_cm33_core1.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong_rtos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_cm33_core1" folder="boards/mcxn9xxevk/multicore_examples/rpmsg_lite_pingpong/cm33_core1" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of the...See more details in readme document.</description>
      <board name="MCX-N9XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_cm33_core1.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
