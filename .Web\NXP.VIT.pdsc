<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>VIT</name>
  <vendor>NXP</vendor>
  <description>Software Pack for vit</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MAESTRO" vendor="NXP" version="2.0.0"/>
      <package name="DSP_NN" vendor="NXP" version="1.0.0"/>
      <package name="XAF" vendor="NXP" version="1.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="device.LPC55S66.internal_condition">
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S69.internal_condition">
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.vit.cm33-lpc55s69.condition_id">
      <require condition="allOf.board=lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpcxpresso55s69.internal_condition">
      <require condition="board.lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="board.lpcxpresso55s69.internal_condition">
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
    </condition>
    <condition id="device.MCXN546.internal_condition">
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN547.internal_condition">
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.vit.cm33-mcxn94x.condition_id">
      <require condition="allOf.board=mcxn5xxevk.internal_condition"/>
    </condition>
    <condition id="allOf.board=mcxn5xxevk.internal_condition">
      <require condition="board.mcxn5xxevk.internal_condition"/>
    </condition>
    <condition id="board.mcxn5xxevk.internal_condition">
      <accept condition="device.MCXN546.internal_condition"/>
      <accept condition="device.MCXN547.internal_condition"/>
    </condition>
    <condition id="device.RW610.internal_condition">
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.RW612.internal_condition">
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.vit.cm33-rw61x.condition_id">
      <require condition="allOf.board=rdrw612bga.internal_condition"/>
    </condition>
    <condition id="allOf.board=rdrw612bga.internal_condition">
      <require condition="board.rdrw612bga.internal_condition"/>
    </condition>
    <condition id="board.rdrw612bga.internal_condition">
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
    </condition>
    <condition id="device.MIMXRT1041.internal_condition">
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1042.internal_condition">
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1051.internal_condition">
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1052.internal_condition">
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1061.internal_condition">
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1062.internal_condition">
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1064.internal_condition">
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1165.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1166.internal_condition">
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1171.internal_condition">
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1172.internal_condition">
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1173.internal_condition">
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1175.internal_condition">
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1176.internal_condition">
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.vit.cm7.condition_id">
      <require condition="allOf.board=evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1064, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1064, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <require condition="board.evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1064, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1064, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <accept condition="device.MIMXRT1041.internal_condition"/>
      <accept condition="device.MIMXRT1042.internal_condition"/>
      <accept condition="device.MIMXRT1051.internal_condition"/>
      <accept condition="device.MIMXRT1052.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1064.internal_condition"/>
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Voice" Cgroup="Voice Command" Csub="cm33_lpc55s69" Cversion="4.10.0" condition="middleware.vit.cm33-lpc55s69.condition_id">
      <description>Voice intelligent technology library for Cortex M7</description>
      <files>
        <file category="doc" name="middleware/vit/CortexM33-LPC55S69/Doc/VITIUG.pdf" projectpath="vit/CortexM33-LPC55S69/Doc"/>
        <file category="doc" name="middleware/vit/CortexM33-LPC55S69/LICENSE.txt" projectpath="vit/CortexM33-LPC55S69"/>
        <file category="doc" name="middleware/vit/CortexM33-LPC55S69/ReleaseNotes.txt" projectpath="vit/CortexM33-LPC55S69"/>
      </files>
    </component>
    <component Cclass="Voice" Cgroup="Voice Command" Csub="cm33_mcxn94x" Cversion="4.10.0" condition="middleware.vit.cm33-mcxn94x.condition_id">
      <description>Voice intelligent technology library for Cortex M33</description>
      <files>
        <file category="doc" name="middleware/vit/CortexM33-MCXN94X/Doc/VITIUG.pdf" projectpath="vit/CortexM33-MCXN94X/Doc"/>
        <file category="doc" name="middleware/vit/CortexM33-MCXN94X/LICENSE.txt" projectpath="vit/CortexM33-MCXN94X"/>
        <file category="doc" name="middleware/vit/CortexM33-MCXN94X/ReleaseNotes.txt" projectpath="vit/CortexM33-MCXN94X"/>
      </files>
    </component>
    <component Cclass="Voice" Cgroup="Voice Command" Csub="cm33_rw61x" Cversion="4.10.0" condition="middleware.vit.cm33-rw61x.condition_id">
      <description>Voice intelligent technology library for Cortex M7</description>
      <files>
        <file category="doc" name="middleware/vit/CortexM33-RW61X/Doc/VITIUG.pdf" projectpath="vit/CortexM33-RW61X/Doc"/>
        <file category="doc" name="middleware/vit/CortexM33-RW61X/LICENSE.txt" projectpath="vit/CortexM33-RW61X"/>
        <file category="doc" name="middleware/vit/CortexM33-RW61X/ReleaseNotes.txt" projectpath="vit/CortexM33-RW61X"/>
      </files>
    </component>
    <component Cclass="Voice" Cgroup="Voice Command" Csub="cm7" Cversion="4.10.0" condition="middleware.vit.cm7.condition_id">
      <description>Voice intelligent technology library for Cortex M7</description>
      <files>
        <file category="doc" name="middleware/vit/CortexM7/Doc/VITIUG.pdf" projectpath="vit/CortexM7/Doc"/>
        <file category="doc" name="middleware/vit/CortexM7/LICENSE.txt" projectpath="vit/CortexM7"/>
        <file category="doc" name="middleware/vit/CortexM7/ReleaseNotes.txt" projectpath="vit/CortexM7"/>
      </files>
    </component>
  </components>
</package>
