<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss-v3/master/en/semiconductor/product/dl/device-family-pack/</url>
  <name>TXZ4Aplus-M4M1_DFP</name>
  <description>Toshiba TXZ4A+ Series TMPM4M(1) Group Device Support</description>
  <releases>
    <release version="1.0.1" date="2024-03-25">
      Release version of TXZ4A+ Series Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4M</keyword>
    <keyword>TXZ4A+</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4A+ Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
      The TXZ4A+ microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>
      <!-- ************************  Subfamily 'TMPM4Mx'  **************************** -->
      <subFamily DsubFamily="M4M(1)">
        <!-- *************************1************ NFYAD*N=100P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4MNFYAFG'  ************************* -->
        <device Dname="TMPM4MNFYAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MNA.h" define="TMPM4MNA"/>
          <debug svd="SVD/M4MNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-LQFP144-2020-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************2*************NFWAD*N=100P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4MNFWAFG'  ************************* -->
        <device Dname="TMPM4MNFWAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MNA.h" define="TMPM4MNA"/>
          <debug svd="SVD/M4MNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-LQFP144-2020-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************3*************NFYA*N=100P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4MNFYADFG'  ************************* -->
        <device Dname="TMPM4MNFYADFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MNA.h" define="TMPM4MNA"/>
          <debug svd="SVD/M4MNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-QFP100-1420-0.65-001"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************4*************NFWA*N=100P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4MNFWADFG'  ************************* -->
        <device Dname="TMPM4MNFWADFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MNA.h" define="TMPM4MNA"/>
          <debug svd="SVD/M4MNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-QFP100-1420-0.65-001"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************5*************MFYA*M=80P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4MMFYAFG'  ************************* -->
        <device Dname="TMPM4MMFYAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MMA.h" define="TMPM4MMA"/>
          <debug svd="SVD/M4MMA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="67"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="18"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="18"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="80"                         name="P-LQFP80-1212-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************6*************MFWA*M=80P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4MMFWAFG'  ************************* -->
        <device Dname="TMPM4MMFWAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MMA.h" define="TMPM4MMA"/>
          <debug svd="SVD/M4MMA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="67"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="18"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="18"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="4"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="80"                         name="P-LQFP80-1212-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************7*************LFYA*L=64P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4MLFYAUG'  ************************* -->
        <device Dname="TMPM4MLFYAUG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MLA.h" define="TMPM4MLA"/>
          <debug svd="SVD/M4MLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1010-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************8*************LFWA*L=64P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4MLFWAUG'  ************************* -->
        <device Dname="TMPM4MLFWAUG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MLA.h" define="TMPM4MLA"/>
          <debug svd="SVD/M4MLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1010-0.50-003"/>
        </device>

        <!-- *************************9*************LFYAF*L=64P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4MLFYAFG'  ************************* -->
        <device Dname="TMPM4MLFYAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MLA.h" define="TMPM4MLA"/>
          <debug svd="SVD/M4MLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1414-0.8-002"/>
          <!--book name=""/-->

        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************10*************LFWAF*L=64P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4MLFWAFG'  ************************* -->
        <device Dname="TMPM4MLFWAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4MLA.h" define="TMPM4MLA"/>
          <debug svd="SVD/M4MLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Mx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Mx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="CAN"           n="1"                           name="Controller Area Network"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1414-0.8-002"/>
          <!--book name=""/-->

        </device>
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="TMPM4MYA Compiler">
      <accept condition="Compiler ARMCC"/>
    </condition>

    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="TMPM4MNx CMSIS">
      <description>Toshiba TMPM4MNx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4MN*"/>
      <require Cclass="CMSIS" Cgroup="CORE" Cversion="5.9.0"/>
    </condition>
    <condition id="TMPM4MMx CMSIS">
      <description>Toshiba TMPM4MMx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4MM*"/>
      <require Cclass="CMSIS" Cgroup="CORE" Cversion="5.9.0"/>
    </condition>
    <condition id="TMPM4MLx CMSIS">
      <description>Toshiba TMPM4MLx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4ML*"/>
      <require Cclass="CMSIS" Cgroup="CORE" Cversion="5.9.0"/>
    </condition>
  </conditions>

  <components>

    <!-- Startup TMPM4MNx -->
    <!-- Startup TMPM4MNx N=100P -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="TMPM4MNx CMSIS">
      <description>System Startup for Toshiba TMPM4MNx Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4MYA Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4MNA.s" attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4MNA.scat"      attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM4MxA.c"      attr="config" version="1.0.1" condition="TMPM4MYA Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4MMx -->
    <!-- Startup TMPM4MMx M=80P -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="TMPM4MMx CMSIS">
      <description>System Startup for Toshiba TMPM4MMx Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4MYA Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4MMA.s" attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4MMA.scat"      attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM4MxA.c"      attr="config" version="1.0.1" condition="TMPM4MYA Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4MLx -->
    <!-- Startup TMPM4MLx L=64P -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="TMPM4MLx CMSIS">
      <description>System Startup for Toshiba TMPM4MLx Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4MYA Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4MLA.s" attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4MLA.scat"      attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM4MxA.c"      attr="config" version="1.0.1" condition="TMPM4MYA Compiler"/>
      </files>
    </component>
  </components>

</package>
