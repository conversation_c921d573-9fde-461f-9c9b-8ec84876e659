<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>MIMXRT1166_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MIMXRT1166</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version='18.0.0' date='2024-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version='17.0.0' date='2023-07-27'>NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version='16.0.0' date='2023-01-16'>NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version='15.1.0' date='2022-09-28'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.1</release>
    <release version='15.0.0' date='2022-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version='14.0.0' date='2022-01-05'>NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version='13.1.0' date='2021-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version='13.0.0' date='2021-05-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='MIMXRT1166' Dvendor='NXP:11'>
      <description>
        i.MX RT1160 Crossover MCU Family - High-performance MCU with Arm® Cortex®-M7 and Cortex-M4 Cores
      </description>
      <device Dname='MIMXRT1166xxxxx'>
        <processor Pname='cm4' Dcore='Cortex-M4' Dfpu='SP_FPU' Dmpu='MPU' Dendian='Little-endian' Dclock='240000000'/>
        <processor Pname='cm7' Dcore='Cortex-M7' Dfpu='DP_FPU' Dmpu='MPU' Dendian='Little-endian' Dclock='600000000'/>
        <environment Pname='cm4' name='iar'>
          <file category='linkerfile' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm4_ram.icf'/>
        </environment>
        <environment Pname='cm7' name='iar'>
          <file category='linkerfile' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm7_ram.icf'/>
        </environment>
        <sequences>
          <sequence name='dpstart'>
            <block>
              __var SW_DP_ABORT  = 0x0;
              __var DP_CTRL_STAT = 0x4;
              __var DP_SELECT    = 0x8;
              __var powered_down = 0;
              __var select = 0;
            </block>
            <control if='__ap==1'>
              <block>select = 0x01000000;</block>
            </control>
            <block>
              // Switch to DP Register Bank 0
              WriteDP(DP_SELECT, select);
              
              // Read DP CTRL/STAT Register and check if CSYSPWRUPACK and CDBGPWRUPACK bits are set
              powered_down = ((ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000);
            </block>
            <control if='powered_down'>
              <block>
                // Request Debug/System Power-Up
                WriteDP(DP_CTRL_STAT, 0x50000000);
              </block>
              <!-- Wait for Power-Up Request to be acknowledged -->
              <control while='(ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000' timeout='1000000'/>
              <!-- JTAG Specific Part of sequence -->
              <control if='(__protocol &amp; 0xFFFF) == 1'>
                <block>
                  // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                  // Additionally clear STICKYORUN, STICKYCMP, and STICKYERR bits by writing '1'
                  WriteDP(DP_CTRL_STAT, 0x50000F32);
                </block>
              </control>
              <!-- SWD Specific Part of sequence -->
              <control if='(__protocol &amp; 0xFFFF) == 2'>
                <block>
                  // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                  WriteDP(DP_CTRL_STAT, 0x50000F00);
                  
                  // Clear WDATAERR, STICKYORUN, STICKYCMP, and STICKYERR bits of CTRL/STAT Register by write to ABORT register
                  WriteDP(SW_DP_ABORT, 0x0000001E);
                </block>
              </control>
            </control>
          </sequence>
          <sequence Pname='cm4' name='DebugPortStart'>
            <block>
              __var reg;
              __var start;
              
              __ap = 0;
              Sequence("dpstart");
              
              // prepare trap code for CM4
              start = 0x20250000;
              WriteAP(0x4, start);
              WriteAP(0xC, start + 0x20);
              
              WriteAP(0x4, start + 4);
              WriteAP(0xC, 0x23F041);
              
              WriteAP(0x4, 0x40c0c000);
              WriteAP(0xC, start &amp; 0xFFFF);
              
              WriteAP(0x4, 0x40c0c004);
              WriteAP(0xC, (start &amp; 0xFFFF0000) &gt;&gt; 16);
              
              //release cm4
              WriteAP(0x4, 0x40c04000);
              WriteAP(0xC, 1);
              
              //change reset mode of cm4
              WriteAP(0x4, 0x40c04004);
              reg = ReadAP(0xC);
              reg |= (0x3 &lt;&lt; 10);
              WriteAP(0xC, reg);
              
              // setup debug port for CM4
              __ap=1;
              Sequence("dpstart");
            </block>
          </sequence>
          <sequence Pname='cm7' name='DebugPortStart'>
            <block>
              __var reg;
              __var start;
              
              Sequence("dpstart");
              
              //prepare trap code for CM7
              start = 0x2001FF00;
              WriteAP(0x4, start);
              WriteAP(0xC, start + 0x20);
              
              WriteAP(0x4, start + 4);
              WriteAP(0xC, 0x23105);
              
              WriteAP(0x4, 0x40C0C068);
              WriteAP(0xC, start &gt;&gt; 7);
              
              //change reset mode of cm7
              WriteAP(0x4, 0x40c04004);
              reg = ReadAP(0xC);
              reg |= (0x3 &lt;&lt; 12);
              WriteAP(0xC, reg);
            </block>
          </sequence>
        </sequences>
        <memory name='ROMCP' start='0x00200000' size='0x040000' access='rx' default='1'/>
        <memory name='SRAM_OC1' start='0x20240000' size='0x010000' access='rw' default='1'/>
        <memory name='SRAM_OC2' start='0x202c0000' size='0x010000' access='rw' default='1'/>
        <memory name='SRAM_OC_cm7' start='0x20360000' size='0x020000' access='rw' default='1'/>
        <algorithm name='devices/MIMXRT1166/arm/MIMXRT116x_QuadSPI_4KB_SEC.FLM' start='0x30000000' size='0x01000000' RAMstart='0x20000000' RAMsize='0x00008000' default='1'/>
        <algorithm name='devices/MIMXRT1166/arm/MIMXRT116x_QuadSPI_4KB_SEC_Alias.FLM' start='0x08000000' size='0x01000000' RAMstart='0x1ffe0000' RAMsize='0x00008000' default='1'/>
        <debug svd='devices/MIMXRT1166/MIMXRT1166_cm4.xml' Pname='cm4' __dp='0' __ap='1'/>
        <debug svd='devices/MIMXRT1166/MIMXRT1166_cm7.xml' Pname='cm7' __dp='0' __ap='0'/>
        <variant Dvariant='MIMXRT1166DVM6A'>
          <compile Pname='cm4' header='devices/MIMXRT1166/fsl_device_registers.h' define='CPU_MIMXRT1166DVM6A_cm4'/>
          <compile Pname='cm7' header='devices/MIMXRT1166/fsl_device_registers.h' define='CPU_MIMXRT1166DVM6A_cm7'/>
        </variant>
        <variant Dvariant='MIMXRT1166CVM5A'>
          <compile Pname='cm4' header='devices/MIMXRT1166/fsl_device_registers.h' define='CPU_MIMXRT1166CVM5A_cm4'/>
          <compile Pname='cm7' header='devices/MIMXRT1166/fsl_device_registers.h' define='CPU_MIMXRT1166CVM5A_cm7'/>
        </variant>
        <variant Dvariant='MIMXRT1166XVM5A'>
          <compile Pname='cm4' header='devices/MIMXRT1166/fsl_device_registers.h' define='CPU_MIMXRT1166XVM5A_cm4'/>
          <compile Pname='cm7' header='devices/MIMXRT1166/fsl_device_registers.h' define='CPU_MIMXRT1166XVM5A_cm7'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.MIMXRT1166.internal_condition'>
      <accept Dname='MIMXRT1166CVM5A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1166DVM6A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1166XVM5A' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.device=MIMXRT1166.internal_condition'>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.ak4497_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.audio_sai_edma_adapter.condition_id'>
      <require condition='allOf.driver.sai_edma, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.sai_edma, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='sai_edma'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.button.condition_id'>
      <require condition='allOf.anyOf=allOf=component.igpio_adapter, driver.igpio, component.timer_manager, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.igpio_adapter, driver.igpio, component.timer_manager, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.allOf=component.igpio_adapter, driver.igpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.igpio_adapter, driver.igpio.internal_condition'>
      <accept condition='allOf.component.igpio_adapter, driver.igpio.internal_condition'/>
    </condition>
    <condition id='allOf.component.igpio_adapter, driver.igpio.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='igpio_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.codec_adapters.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.codec_i2c.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.cs42448_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.cs42888_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.da7212_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.eth_phy_common.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.exception_handling_cm7.condition_id'>
      <require condition='allOf.utility.assert, utility.debug_console, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.utility.assert, utility.debug_console, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='device.MIMXRT1165.internal_condition'>
      <accept Dname='MIMXRT1165CVM5A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1165DVM6A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1165XVM5A' Dvendor='NXP:11'/>
    </condition>
    <condition id='component.flash_adapter.condition_id'>
      <require condition='allOf.anyOf=allOf=component.mcxw_flash_adapter, driver.romapi, allOf=board=evkmimxrt1160, component.flexspi_nor_flash_adapter_rt1160, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.mcxw_flash_adapter, driver.romapi, allOf=board=evkmimxrt1160, component.flexspi_nor_flash_adapter_rt1160, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.allOf=component.mcxw_flash_adapter, driver.romapi, allOf=board=evkmimxrt1160, component.flexspi_nor_flash_adapter_rt1160.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.mcxw_flash_adapter, driver.romapi, allOf=board=evkmimxrt1160, component.flexspi_nor_flash_adapter_rt1160.internal_condition'>
      <accept condition='allOf.component.mcxw_flash_adapter, driver.romapi.internal_condition'/>
      <accept condition='allOf.board=evkmimxrt1160, component.flexspi_nor_flash_adapter_rt1160.internal_condition'/>
    </condition>
    <condition id='allOf.component.mcxw_flash_adapter, driver.romapi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mcxw_flash_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
    </condition>
    <condition id='allOf.board=evkmimxrt1160, component.flexspi_nor_flash_adapter_rt1160.internal_condition'>
      <require condition='board.evkmimxrt1160.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='adapter_rt1160'/>
    </condition>
    <condition id='board.evkmimxrt1160.internal_condition'>
      <accept condition='device.MIMXRT1165.internal_condition'/>
      <accept condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.flash_nand_flexspi.condition_id'>
      <require condition='allOf.driver.flexspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexspi, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.flash_nand_semc.condition_id'>
      <require condition='allOf.driver.semc, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.semc, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='semc'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.flash_nor_flexspi.condition_id'>
      <require condition='allOf.driver.flexspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.flash_nor_lpspi.condition_id'>
      <require condition='allOf.driver.lpspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpspi, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.flexspi_nor_flash_adapter_rt1160.condition_id'>
      <require condition='allOf.board=evkmimxrt1160, driver.flexspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.board=evkmimxrt1160, driver.flexspi, device=MIMXRT1166.internal_condition'>
      <require condition='board.evkmimxrt1160.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.gpt_adapter.condition_id'>
      <require condition='allOf.driver.gpt, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.gpt, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpt'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=anyOf=driver.lpi2c, component.lpi2c_adapter, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=anyOf=driver.lpi2c, component.lpi2c_adapter, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.allOf=anyOf=driver.lpi2c, component.lpi2c_adapter.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=anyOf=driver.lpi2c, component.lpi2c_adapter.internal_condition'>
      <accept condition='allOf.anyOf=driver.lpi2c, component.lpi2c_adapter.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, component.lpi2c_adapter.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpi2c_adapter'/>
    </condition>
    <condition id='anyOf.driver.lpi2c.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.igpio_adapter.condition_id'>
      <require condition='allOf.driver.igpio, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.igpio, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.led.condition_id'>
      <require condition='allOf.anyOf=allOf=component.igpio_adapter, driver.igpio, component.timer_manager, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.igpio_adapter, driver.igpio, component.timer_manager, driver.common, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.allOf=component.igpio_adapter, driver.igpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.log.condition_id'>
      <require condition='allOf.driver.common, utility.str, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.str, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console_lite.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console_lite, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console_lite, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.log.backend.ringbuffer.condition_id'>
      <require condition='allOf.component.log, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.lpi2c_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, component.i2c_adapter_interface, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, component.i2c_adapter_interface, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.lpspi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.lpspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.lpspi, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.lpuart_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.lpuart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
    </condition>
    <condition id='component.lpuart_dma_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.edma, component.lpuart_adapter, component.timer_manager, driver.lpuart, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, component.lpuart_adapter, component.timer_manager, driver.lpuart, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.edma.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='edma'/>
    </condition>
    <condition id='component.mcxw_flash_adapter.condition_id'>
      <require condition='allOf.driver.romapi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.romapi, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.mflash_offchip.condition_id'>
      <require condition='allOf.anyOf=driver.cache_armv7_m7, driver.cache_lmem, driver.flexspi, driver.romapi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.cache_armv7_m7, driver.cache_lmem, driver.flexspi, driver.romapi, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.cache_armv7_m7, driver.cache_lmem, driver.flexspi, driver.romapi.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.cache_armv7_m7, driver.cache_lmem, driver.flexspi, driver.romapi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_armv7_m7'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_lmem'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
    </condition>
    <condition id='component.mflash_onchip.condition_id'>
      <require condition='allOf.anyOf=driver.cache_armv7_m7, driver.cache_lmem, driver.romapi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.cache_armv7_m7, driver.cache_lmem, driver.romapi, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.cache_armv7_m7, driver.cache_lmem, driver.romapi.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.cache_armv7_m7, driver.cache_lmem, driver.romapi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_armv7_m7'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_lmem'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
    </condition>
    <condition id='component.mflash_onchip_fmu.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.pcm186x_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.pcm512x_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyaqr113c.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='phy-common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyar8031.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phydp8384x.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phygpy215.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyksz8041.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyksz8081.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phylan8720a.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phylan8741.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyrtl8201.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyrtl8211f.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phytja1100.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phytja1120.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyvsc8541.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.phyyt8521.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.pit_adapter.condition_id'>
      <require condition='allOf.driver.pit, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.pit, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.power_manager_framework.condition_id'>
      <require condition='allOf.anyOf=driver.common, driver.pgmc, driver.ssarc, component.lists, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.common, driver.pgmc, driver.ssarc, component.lists, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.common, driver.pgmc, driver.ssarc.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.common, driver.pgmc, driver.ssarc.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='pgmc'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='ssarc'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=component.software_rng_adapter, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.software_rng_adapter, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.software_rng_adapter.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.software_rng_adapter.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpspi_adapter, driver.lpspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpspi_adapter, driver.lpspi, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.allOf=component.lpspi_adapter, driver.lpspi.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpspi_adapter, driver.lpspi.internal_condition'>
      <accept condition='allOf.component.lpspi_adapter, driver.lpspi.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpspi_adapter, driver.lpspi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.sgtl_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='device_id.MIMXRT1166xxxxx.internal_condition'>
      <accept Dname='MIMXRT1166CVM5A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1166DVM6A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1166XVM5A' Dvendor='NXP:11'/>
    </condition>
    <condition id='component.silicon_id.condition_id'>
      <require condition='allOf.anyOf=allOf=anyOf=device_id=MIMXRT1166xxxxx, component.silicon_id_imxrt1170, driver.common.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=anyOf=device_id=MIMXRT1166xxxxx, component.silicon_id_imxrt1170, driver.common.internal_condition'>
      <require condition='anyOf.allOf=anyOf=device_id=MIMXRT1166xxxxx, component.silicon_id_imxrt1170.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
    </condition>
    <condition id='anyOf.allOf=anyOf=device_id=MIMXRT1166xxxxx, component.silicon_id_imxrt1170.internal_condition'>
      <accept condition='allOf.anyOf=device_id=MIMXRT1166xxxxx, component.silicon_id_imxrt1170.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=device_id=MIMXRT1166xxxxx, component.silicon_id_imxrt1170.internal_condition'>
      <require condition='anyOf.device_id=MIMXRT1166xxxxx.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt1170'/>
    </condition>
    <condition id='anyOf.device_id=MIMXRT1166xxxxx.internal_condition'>
      <accept condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imx8.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imx93.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imxrt10xx.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imxrt1170.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imxrt1180.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_mcxn.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_rw610.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_scfw.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.tfa9896_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.tfa9xxx_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.gpt_adapter, driver.gpt, allOf=component.pit_adapter, driver.pit, component.lists, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.gpt_adapter, driver.gpt, allOf=component.pit_adapter, driver.pit, component.lists, driver.common, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.allOf=component.gpt_adapter, driver.gpt, allOf=component.pit_adapter, driver.pit.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.gpt_adapter, driver.gpt, allOf=component.pit_adapter, driver.pit.internal_condition'>
      <accept condition='allOf.component.gpt_adapter, driver.gpt.internal_condition'/>
      <accept condition='allOf.component.pit_adapter, driver.pit.internal_condition'/>
    </condition>
    <condition id='allOf.component.gpt_adapter, driver.gpt.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpt_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpt'/>
    </condition>
    <condition id='allOf.component.pit_adapter, driver.pit.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit'/>
    </condition>
    <condition id='component.wifi_bt_module.88W8987.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358ma'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w2_usd'/>
    </condition>
    <condition id='component.wifi_bt_module.IW416.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457ma'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_arduino'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510ma'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_maya_w1_usd'/>
    </condition>
    <condition id='component.wifi_bt_module.IW61X.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ll_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_usd'/>
    </condition>
    <condition id='component.wifi_bt_module.RW61X.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_frdm_rw61x'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rd_rw61x'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am457_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw416'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am457ma.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am510_arduino.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am510_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am510ma.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_cm358_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_88w8987'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_cm358ma.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_nm191_usd.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_nm191ma.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_frdm_rw61x.condition_id'>
      <require condition='allOf.component.wifi_bt_module.RW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.RW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rw61x'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_k32w061_transceiver.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1xk_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1xk_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1zm_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1zm_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2dl_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw61x'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2dl_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2ds_m2.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2ds_usd.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2el_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2el_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2ll_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_rd_rw61x.condition_id'>
      <require condition='allOf.component.wifi_bt_module.RW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_jody_w2_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_jody_w5_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_jody_w5_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_lily_w1_usd.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_maya_w1_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.bt_only_fw.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.config.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_88w8987'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw416'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw61x'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rw61x'/>
    </condition>
    <condition id='component.wifi_bt_module.tx_pwr_limits.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.wifi_bt_combo_fw.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wm8524_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wm8904_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wm8960_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='component.wm8962_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=MIMXRT1166.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.core_ids=cm4.condition_id'>
      <require condition='core_ids.cm4.internal_condition'/>
    </condition>
    <condition id='core_ids.cm4.internal_condition'>
      <accept Pname='cm4' Dname='*'/>
    </condition>
    <condition id='allOf.core_ids=cm7.condition_id'>
      <require condition='core_ids.cm7.internal_condition'/>
    </condition>
    <condition id='core_ids.cm7.internal_condition'>
      <accept Pname='cm7' Dname='*'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='device.boot_header.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MIMXRT1166xxxxx.internal_condition'>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='device_ids.MIMXRT1166xxxxx.internal_condition'>
      <accept Dname='MIMXRT1166CVM5A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1166DVM6A' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT1166XVM5A' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='cores.cm4f.internal_condition'/>
      <require condition='device_ids.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='cores.cm4f.internal_condition'>
      <accept Dcore='Cortex-M4'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='cores.cm4f.internal_condition'/>
      <require condition='device_ids.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='cores.cm4f.internal_condition'/>
      <require condition='device_ids.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='allOf.toolchains=mdk, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='cores.cm7f.internal_condition'/>
      <require condition='device_ids.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='cores.cm7f.internal_condition'>
      <accept Dcore='Cortex-M7'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='cores.cm7f.internal_condition'/>
      <require condition='device_ids.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='cores.cm7f.internal_condition'/>
      <require condition='device_ids.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.device_id=MIMXRT1166xxxxx, CMSIS_Include_core_cm, device.startup, driver.common, driver.clock, driver.igpio, driver.iomuxc, driver.anadig_pmu, driver.anatop_ai, driver.memory, driver.dcdc_2, driver.gpc_xxx_ctrl, driver.pgmc, driver.src_2, driver.nic301, device.boot_header, component.lpuart_adapter, utility.debug_console_lite, utility.assert_lite, utility.str.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MIMXRT1166xxxxx, CMSIS_Include_core_cm, device.startup, driver.common, driver.clock, driver.igpio, driver.iomuxc, driver.anadig_pmu, driver.anatop_ai, driver.memory, driver.dcdc_2, driver.gpc_xxx_ctrl, driver.pgmc, driver.src_2, driver.nic301, device.boot_header, component.lpuart_adapter, utility.debug_console_lite, utility.assert_lite, utility.str.internal_condition'>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='iomuxc'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pmu_1'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='anatop_ai'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='memory'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dcdc_soc'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpc_3'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pgmc'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='soc_src'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='nic301'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='xip_device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT1166_system'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, core_ids=cm4.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='core_ids.cm4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, core_ids=cm4.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='core_ids.cm4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk, core_ids=cm4.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='core_ids.cm4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, core_ids=cm7.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='core_ids.cm7.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, core_ids=cm7.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='core_ids.cm7.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk, core_ids=cm7.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='core_ids.cm7.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT1166_header'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.acmp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.adc_etc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.ak4497.condition_id'>
      <require condition='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.anadig_pmu.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.anatop_ai.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.aoi.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.asrc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.asrc_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.asrc, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.asrc, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='asrc'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.caam.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.cache_armv7_m7.condition_id'>
      <require condition='allOf.core=cm7f, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.core=cm7f, device=MIMXRT1166.internal_condition'>
      <require condition='core.cm7f.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='core.cm7f.internal_condition'>
      <accept Dcore='Cortex-M7'/>
    </condition>
    <condition id='driver.cache_lmem.condition_id'>
      <require condition='allOf.core=cm4f, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.core=cm4f, device=MIMXRT1166.internal_condition'>
      <require condition='core.cm4f.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='core.cm4f.internal_condition'>
      <accept Dcore='Cortex-M4'/>
    </condition>
    <condition id='driver.camera-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.video-common, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ap1302.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-common, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-max9286.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-mt9m114.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov5640.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7670.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7725.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-sccb.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-receiver-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.camera-receiver-csi.condition_id'>
      <require condition='allOf.driver.camera-receiver-common, driver.csi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-receiver-common, driver.csi, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='csi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.cdog.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_enet.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.Ethernet, CMSIS_Driver_Include.Ethernet_MAC, CMSIS_Driver_Include.Ethernet_PHY, component.eth_phy_common, device.RTE, driver.enet, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.Ethernet, CMSIS_Driver_Include.Ethernet_MAC, CMSIS_Driver_Include.Ethernet_PHY, component.eth_phy_common, device.RTE, driver.enet, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='Ethernet' Capiversion='2.2.0'/>
      <require Cclass='CMSIS Driver' Cgroup='Ethernet MAC' Capiversion='2.2.0'/>
      <require Cclass='CMSIS Driver' Cgroup='Ethernet PHY' Capiversion='2.2.0'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='phy-common'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='enet'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpi2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, anyOf=allOf=driver.lpi2c, device_id=MIMXRT1166xxxxx, device.RTE, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, anyOf=allOf=driver.lpi2c, device_id=MIMXRT1166xxxxx, device.RTE, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpi2c, device_id=MIMXRT1166xxxxx.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpi2c, device_id=MIMXRT1166xxxxx.internal_condition'>
      <accept condition='allOf.driver.lpi2c, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpi2c, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpspi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, anyOf=allOf=driver.lpspi, device_id=MIMXRT1166xxxxx, device.RTE, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, anyOf=allOf=driver.lpspi, device_id=MIMXRT1166xxxxx, device.RTE, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require condition='anyOf.allOf=driver.lpspi, device_id=MIMXRT1166xxxxx.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpspi, device_id=MIMXRT1166xxxxx.internal_condition'>
      <accept condition='allOf.driver.lpspi, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpspi, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpuart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MIMXRT1166xxxxx, device.RTE, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MIMXRT1166xxxxx, device.RTE, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpuart, device_id=MIMXRT1166xxxxx.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpuart, device_id=MIMXRT1166xxxxx.internal_condition'>
      <accept condition='allOf.driver.lpuart, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpuart, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.codec.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm4f, cm7f.condition_id'>
      <require condition='cores.cm4f, cm7f.internal_condition'/>
    </condition>
    <condition id='cores.cm4f, cm7f.internal_condition'>
      <accept Dcore='Cortex-M4'/>
      <accept Dcore='Cortex-M7'/>
    </condition>
    <condition id='driver.cs42448.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.cs42888.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.csi.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.dac12.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.dbi.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dbi.condition_id'>
      <require condition='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dsi-cmd.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-mipi-dsi-cmd'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-elcdif.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.elcdif, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.elcdif, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='elcdif'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-lcdifv2.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.lcdifv2, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.lcdifv2, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lcdifv2'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-ssd1963.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dcdc_2.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dcic.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.dialog7212.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-adv7535.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.video-i2c, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-co5300.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-mipi-dsi-cmd'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-hx8394.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-it6161.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-it6263.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-mipi-dsi-cmd.condition_id'>
      <require condition='allOf.anyOf=driver.mipi_dsi_split, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.mipi_dsi_split, driver.common, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.mipi_dsi_split.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.mipi_dsi_split.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_dsi_split'/>
    </condition>
    <condition id='driver.display-rm67162.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-rm67191.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-rm68191.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-rm68200.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-rm692c9.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-rpi.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.display-sn65dsi83.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.dmamux.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.edma.condition_id'>
      <require condition='allOf.driver.common, driver.dmamux, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dmamux, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dmamux'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.ektf2k.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.elcdif.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.enc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.enet.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.ewm.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexcan.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexcan_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.flexcan, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.flexcan, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.flexio.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2c_master.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexio, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2s.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2s_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.flexio_i2s, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.flexio_i2s, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2s'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.flexio_spi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.flexio_spi, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.flexio_uart, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.flexio_uart, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.flexram.condition_id'>
      <require condition='allOf.driver.common, driver.flexram_allocate, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.flexram_allocate, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='soc_flexram_allocate'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexram_allocate.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.flexspi.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.flexspi_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.flexspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.flexspi, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.ft3267.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.ft5406.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.ft5406_rt.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.ft6x06.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device=MIMXRT1166.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.fxas21002cq.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.fxls8974cf.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.fxos8700cq.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.gpc_xxx_ctrl.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.gpt.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.gt911.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.htu21d.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.icm42688p.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.iee.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.iee_apc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.igpio.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.ili9341.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.iomuxc.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.key_manager.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.kpp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.lcdifv2.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.lpadc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.lpi2c.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.lpi2c_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.lpi2c, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.lpi2c, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.lpspi.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.lpspi_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.lpspi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.lpspi, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.lpuart.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.lpuart_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.lpuart, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.lpuart, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.lsm6dso.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.max30101.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.mecc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.memory.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.mipi_csi2rx.condition_id'>
      <require condition='allOf.driver.common, driver.mipi_csi2rx_soc, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.mipi_csi2rx_soc, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_csi2rx'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.mipi_csi2rx_soc.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.mipi_dsi_soc.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.mipi_dsi_split.condition_id'>
      <require condition='allOf.driver.common, driver.mipi_csi2rx_soc, driver.mipi_dsi_soc, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.mipi_csi2rx_soc, driver.mipi_dsi_soc, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_csi2rx'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_dsi'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.mma8451q.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.mma8652fc.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.mu.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.mx25r_flash.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.nic301.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.nmh1000.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.ocotp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.p3t1755.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pcm186x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pcm512x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pdm.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.pdm_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.pdm, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.pdm, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pdm'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pf1550.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pf3000.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pf5020.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pgmc.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.pit.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.psp27801.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.puf.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.pwm.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.pxp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.qtmr_1.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.rdc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.rdc_sema42.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.romapi.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.rtwdog.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.sai.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.sai_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.sai, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.sai, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='sai'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.sema4.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.semc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.sgtl5000.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.smartcard_emvsim.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.smartcard_phy_emvsim.condition_id'>
      <require condition='allOf.driver.common, driver.smartcard_emvsim, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.smartcard_emvsim, device_id=MIMXRT1166xxxxx.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='smartcard_emvsim'/>
      <require condition='device_id.MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.smartcard_uart.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.snvs_hp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.snvs_lp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.spdif.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.spdif_edma.condition_id'>
      <require condition='allOf.anyOf=driver.edma, driver.spdif, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.edma, driver.spdif, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.edma.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spdif'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.src_2.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.ssarc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.ssd1963.condition_id'>
      <require condition='allOf.driver.dbi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.st7796s.condition_id'>
      <require condition='allOf.driver.dbi, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.tempsensor.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.tfa9896.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx_hal.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.tma525b.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.tsl2561.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.usdhc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.video-common.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.video-i2c.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.wdog01.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.wm8524.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.wm8904.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.wm8960.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.wm8962.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='driver.xbara.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.xbarb.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.xecc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='driver.xrdc2.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT1166xxxxx.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk, cores=cm4f, cm7f.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
      <require condition='cores.cm4f, cm7f.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, driver.common, not=utility.debug_console, utility.str, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, driver.common, not=utility.debug_console, utility.str, device=MIMXRT1166.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.debug_console.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='not.utility.debug_console.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='utility.notifier.condition_id'>
      <require condition='allOf.device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=MIMXRT1166.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT1166.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT1166.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter' Cversion='2.2.0' condition='component.ak4497_adapter.condition_id'>
      <description>Component ak4497 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.h' projectpath='codec/port/ak4497'/>
        <file category='sourceC' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.c' projectpath='codec/port/ak4497'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/ak4497/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='audio_sai_edma_adapter' Cversion='1.0.0' condition='component.audio_sai_edma_adapter.condition_id'>
      <description>Component sai_edma_adapter</description>
      <files>
        <file category='header' name='components/audio/fsl_adapter_audio.h' projectpath='component/audio'/>
        <file category='sourceC' name='components/audio/fsl_adapter_sai.c' projectpath='component/audio'/>
        <file category='include' name='components/audio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='button' Cversion='1.0.0' condition='component.button.condition_id'>
      <description>Component button</description>
      <files>
        <file category='header' name='components/button/fsl_component_button.h' projectpath='component/button'/>
        <file category='sourceC' name='components/button/fsl_component_button.c' projectpath='component/button'/>
        <file category='include' name='components/button/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters' Cversion='2.2.0' condition='component.codec_adapters.condition_id'>
      <description>Component codec adapters for multi codec</description>
      <RTE_Components_h>
#ifndef CODEC_MULTI_ADAPTERS
#define CODEC_MULTI_ADAPTERS 1
#endif
</RTE_Components_h>
      <files>
        <file category='sourceC' name='components/codec/port/fsl_codec_adapter.c' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c' Cversion='2.1.0' condition='component.codec_i2c.condition_id'>
      <description>Component codec_i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/i2c/fsl_codec_i2c.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/i2c/fsl_codec_i2c.c' projectpath='codec'/>
        <file category='include' name='components/codec/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter' Cversion='2.2.1' condition='component.cs42448_adapter.condition_id'>
      <description>Component cs42448 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.h' projectpath='codec/port/cs42448'/>
        <file category='sourceC' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.c' projectpath='codec/port/cs42448'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42448/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter' Cversion='2.2.1' condition='component.cs42888_adapter.condition_id'>
      <description>Component cs42888 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.h' projectpath='codec/port/cs42888'/>
        <file category='sourceC' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.c' projectpath='codec/port/cs42888'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42888/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter' Cversion='2.2.0' condition='component.da7212_adapter.condition_id'>
      <description>Component da7212 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/da7212/fsl_codec_da7212_adapter.h' projectpath='codec/port/da7212'/>
        <file category='sourceC' name='components/codec/port/da7212/fsl_codec_da7212_adapter.c' projectpath='codec/port/da7212'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/da7212/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-common' Cversion='2.0.0' condition='component.eth_phy_common.condition_id'>
      <description>Driver phy-common</description>
      <files>
        <file category='header' name='components/phy/fsl_phy.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='exception_handling_cm7' Cversion='1.0.0' condition='component.exception_handling_cm7.condition_id'>
      <description>Component exception_handling_cm7</description>
      <files>
        <file category='header' name='components/exception_handling/fsl_component_exception_handling.h' projectpath='component/exception_handling'/>
        <file category='sourceC' name='components/exception_handling/cm7/fsl_component_exception_handling.c' projectpath='component/exception_handling/cm7'/>
        <file category='include' name='components/exception_handling/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_adapter' Cversion='1.0.0' condition='component.flash_adapter.condition_id'>
      <description>Component flash_adapter</description>
      <files>
        <file category='doc' name='components/internal_flash/component.flash_adapter_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nand_flexspi' Cversion='1.0.0' condition='component.flash_nand_flexspi.condition_id'>
      <description>Component.flash_nand_flexspi</description>
      <files>
        <file category='header' name='components/flash/nand/fsl_nand_flash.h' projectpath='nand_flash/nand'/>
        <file category='sourceC' name='components/flash/nand/flexspi/fsl_flexspi_nand_flash.c' projectpath='nand_flash/nand/flexspi'/>
        <file category='header' name='components/flash/nand/flexspi/fsl_flexspi_nand_flash.h' projectpath='nand_flash/nand/flexspi'/>
        <file category='include' name='components/flash/nand/'/>
        <file category='include' name='components/flash/nand/flexspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nand_semc' Cversion='1.0.0' condition='component.flash_nand_semc.condition_id'>
      <description>Component flash_nand_semc</description>
      <files>
        <file category='header' name='components/flash/nand/fsl_nand_flash.h' projectpath='nand_flash/nand'/>
        <file category='sourceC' name='components/flash/nand/semc/fsl_semc_nand_flash.c' projectpath='nand_flash/nand/semc'/>
        <file category='header' name='components/flash/nand/semc/fsl_semc_nand_flash.h' projectpath='nand_flash/nand/semc'/>
        <file category='include' name='components/flash/nand/'/>
        <file category='include' name='components/flash/nand/semc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nor_flexspi' Cversion='1.0.0' condition='component.flash_nor_flexspi.condition_id'>
      <description>Component flash_nor_flexspi</description>
      <files>
        <file category='header' name='components/flash/nor/fsl_nor_flash.h' projectpath='nor_flash/nor'/>
        <file category='sourceC' name='components/flash/nor/flexspi/fsl_flexspi_nor_flash.c' projectpath='nor_flash/nor/flexspi'/>
        <file category='header' name='components/flash/nor/flexspi/fsl_flexspi_nor_flash.h' projectpath='nor_flash/nor/flexspi'/>
        <file category='include' name='components/flash/nor/'/>
        <file category='include' name='components/flash/nor/flexspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nor_lpspi' Cversion='1.0.0' condition='component.flash_nor_lpspi.condition_id'>
      <description>Component flash_nor_lpspi</description>
      <files>
        <file category='header' name='components/flash/nor/fsl_nor_flash.h' projectpath='nor_flash/nor'/>
        <file category='sourceC' name='components/flash/nor/lpspi/fsl_lpspi_nor_flash.c' projectpath='nor_flash/nor/lpspi'/>
        <file category='header' name='components/flash/nor/lpspi/fsl_lpspi_nor_flash.h' projectpath='nor_flash/nor/lpspi'/>
        <file category='sourceC' name='components/flash/nor/lpspi/fsl_lpspi_mem_adapter.c' projectpath='nor_flash/nor/lpspi'/>
        <file category='header' name='components/flash/nor/lpspi/fsl_lpspi_mem_adapter.h' projectpath='nor_flash/nor/lpspi'/>
        <file category='include' name='components/flash/nor/'/>
        <file category='include' name='components/flash/nor/lpspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adapter_rt1160' Cversion='1.0.0' condition='component.flexspi_nor_flash_adapter_rt1160.condition_id'>
      <description>Component flexspi_nor_flash_adapter_rt1160</description>
      <files>
        <file category='sourceC' name='components/internal_flash/mimxrt1160/fsl_adapter_flexspi_nor_flash.c' projectpath='component/internal_flash/mimxrt1160'/>
        <file category='header' name='components/internal_flash/fsl_adapter_flash.h' projectpath='component/internal_flash'/>
        <file category='include' name='components/internal_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpt_adapter' Cversion='1.0.0' condition='component.gpt_adapter.condition_id'>
      <description>Component gpt_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_GPT
#define TIMER_PORT_TYPE_GPT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_gpt.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='igpio_adapter' Cversion='1.0.1' condition='component.igpio_adapter.condition_id'>
      <description>Component igpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_igpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='led' Cversion='1.0.0' condition='component.led.condition_id'>
      <description>Component led</description>
      <files>
        <file category='header' name='components/led/fsl_component_led.h' projectpath='component/led'/>
        <file category='sourceC' name='components/led/fsl_component_led.c' projectpath='component/led'/>
        <file category='include' name='components/led/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='log' Cversion='1.0.0' condition='component.log.condition_id'>
      <description>Component log</description>
      <files>
        <file category='header' name='components/log/fsl_component_log.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log.c' projectpath='component/log'/>
        <file category='header' name='components/log/fsl_component_log_config.h' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole' Cversion='1.0.0' condition='component.log.backend.debug_console.condition_id'>
      <description>Component log backend debug console</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole_lite' Cversion='1.0.0' condition='component.log.backend.debug_console_lite.condition_id'>
      <description>Component log backend debug console lite</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ringbuffer' Cversion='1.0.0' condition='component.log.backend.ringbuffer.condition_id'>
      <description>Component log backend ring buffer</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_ringbuffer.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_ringbuffer.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpi2c_adapter' Cversion='1.0.0' condition='component.lpi2c_adapter.condition_id'>
      <description>Component lpi2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_lpi2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_adapter' Cversion='1.0.0' condition='component.lpspi_adapter.condition_id'>
      <description>Component lpspi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_lpspi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter' Cversion='1.0.0' condition='component.lpuart_adapter.condition_id'>
      <description>Component lpuart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_lpuart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_dma_adapter' Cversion='1.0.0' condition='component.lpuart_dma_adapter.condition_id'>
      <description>Component lpuart_dma_adapter</description>
      <RTE_Components_h>
#ifndef HAL_UART_DMA_ENABLE
#define HAL_UART_DMA_ENABLE 0
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_lpuart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mcxw_flash_adapter' Cversion='1.0.0' condition='component.mcxw_flash_adapter.condition_id'>
      <description>Component mcxw_flash_adapter</description>
      <files>
        <file category='header' name='components/internal_flash/fsl_adapter_flash.h' projectpath='component/internal_flash'/>
        <file category='sourceC' name='components/internal_flash/fsl_adapter_mcxw_flash.c' projectpath='component/internal_flash'/>
        <file category='include' name='components/internal_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_offchip' Cversion='1.0.0' condition='component.mflash_offchip.condition_id'>
      <description>mflash offchip</description>
      <RTE_Components_h>
#ifndef MFLASH_FILE_BASEADDR
#define MFLASH_FILE_BASEADDR 14221312
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/flash/mflash/mflash_common.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mflash_file.c' projectpath='flash/mflash'/>
        <file category='header' name='components/flash/mflash/mflash_file.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mimxrt1160/mflash_drv.c' projectpath='flash/mflash/mimxrt1160'/>
        <file category='header' name='components/flash/mflash/mimxrt1160/mflash_drv.h' projectpath='flash/mflash/mimxrt1160'/>
        <file category='include' name='components/flash/mflash/'/>
        <file category='include' name='components/flash/mflash/mimxrt1160/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip' Cversion='1.0.0' condition='component.mflash_onchip.condition_id'>
      <description>mflash onchip</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip_fmu' Cversion='1.0.0' condition='component.mflash_onchip_fmu.condition_id'>
      <description>mflash onchip_fmu</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_fmu_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter' Cversion='2.0.0' condition='component.pcm186x_adapter.condition_id'>
      <description>Component pcm186x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.h' projectpath='codec/port/pcm186x'/>
        <file category='sourceC' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.c' projectpath='codec/port/pcm186x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm186x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter' Cversion='2.0.0' condition='component.pcm512x_adapter.condition_id'>
      <description>Component pcm512x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.h' projectpath='codec/port/pcm512x'/>
        <file category='sourceC' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.c' projectpath='codec/port/pcm512x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm512x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-aqr113c' Cversion='2.0.0' condition='component.phyaqr113c.condition_id'>
      <description>Driver phy-device-aqr113c</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyaqr113c/fsl_phyaqr113c.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyaqr113c/fsl_phyaqr113c.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyaqr113c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-ar8031' Cversion='2.0.0' condition='component.phyar8031.condition_id'>
      <description>Driver phy-device-ar8031</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyar8031/fsl_phyar8031.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyar8031/fsl_phyar8031.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyar8031/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-dp8384x' Cversion='2.0.0' condition='component.phydp8384x.condition_id'>
      <description>Driver phy-device-dp8384x</description>
      <files>
        <file category='sourceC' name='components/phy/device/phydp8384x/fsl_phydp8384x.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phydp8384x/fsl_phydp8384x.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phydp8384x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-gpy215' Cversion='2.0.0' condition='component.phygpy215.condition_id'>
      <description>Driver phy-device-gpy215</description>
      <files>
        <file category='sourceC' name='components/phy/device/phygpy215/fsl_phygpy215.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phygpy215/fsl_phygpy215.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phygpy215/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-ksz8041' Cversion='2.0.0' condition='component.phyksz8041.condition_id'>
      <description>Driver phy-device-ksz8041</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyksz8041/fsl_phyksz8041.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyksz8041/fsl_phyksz8041.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyksz8041/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-ksz8081' Cversion='2.0.0' condition='component.phyksz8081.condition_id'>
      <description>Driver phy-device-ksz8081</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyksz8081/fsl_phyksz8081.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyksz8081/fsl_phyksz8081.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyksz8081/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-lan8720a' Cversion='2.0.0' condition='component.phylan8720a.condition_id'>
      <description>Driver phy-device-lan8720a</description>
      <files>
        <file category='sourceC' name='components/phy/device/phylan8720a/fsl_phylan8720a.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phylan8720a/fsl_phylan8720a.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phylan8720a/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-lan8741' Cversion='2.0.0' condition='component.phylan8741.condition_id'>
      <description>Driver phy-device-lan8741</description>
      <files>
        <file category='sourceC' name='components/phy/device/phylan8741/fsl_phylan8741.c' projectpath='phy'/>
        <file category='header' name='components/phy/device/phylan8741/fsl_phylan8741.h' projectpath='phy'/>
        <file category='include' name='components/phy/device/phylan8741/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-rtl8201' Cversion='2.0.0' condition='component.phyrtl8201.condition_id'>
      <description>Driver phy-device-rtl8201</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyrtl8201/fsl_phyrtl8201.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyrtl8201/fsl_phyrtl8201.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyrtl8201/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-rtl8211f' Cversion='2.0.0' condition='component.phyrtl8211f.condition_id'>
      <description>Driver phy-device-rtl8211f</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyrtl8211f/fsl_phyrtl8211f.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyrtl8211f/fsl_phyrtl8211f.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyrtl8211f/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-tja1100' Cversion='2.0.0' condition='component.phytja1100.condition_id'>
      <description>Driver phy-device-tja1100</description>
      <files>
        <file category='sourceC' name='components/phy/device/phytja1100/fsl_phytja1100.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phytja1100/fsl_phytja1100.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phytja1100/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-tja1120' Cversion='2.0.0' condition='component.phytja1120.condition_id'>
      <description>Driver phy-device-tja1120</description>
      <files>
        <file category='sourceC' name='components/phy/device/phytja1120/fsl_phytja1120.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phytja1120/fsl_phytja1120.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phytja1120/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-vsc8541' Cversion='2.0.0' condition='component.phyvsc8541.condition_id'>
      <description>Driver phy-device-vsc8541</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyvsc8541/fsl_phyvsc8541.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyvsc8541/fsl_phyvsc8541.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyvsc8541/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-yt8521' Cversion='2.0.0' condition='component.phyyt8521.condition_id'>
      <description>Driver phy-device-yt8521</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyyt8521/fsl_phyyt8521.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyyt8521/fsl_phyyt8521.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyyt8521/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pit_adapter' Cversion='1.0.0' condition='component.pit_adapter.condition_id'>
      <description>Component pit_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_PIT
#define TIMER_PORT_TYPE_PIT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_pit.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='power_manager_core' Cversion='2.0.0' condition='component.power_manager_framework.condition_id'>
      <description>Component power manager core level</description>
      <RTE_Components_h>
#ifndef GENERIC_LIST_LIGHT
#define GENERIC_LIST_LIGHT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/power_manager/core/fsl_pm_core.h' projectpath='component/power_manager'/>
        <file category='sourceC' name='components/power_manager/core/fsl_pm_core.c' projectpath='component/power_manager'/>
        <file category='header' name='components/power_manager/core/fsl_pm_config.h' projectpath='component/power_manager'/>
        <file category='include' name='components/power_manager/core/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter' Cversion='2.2.0' condition='component.sgtl_adapter.condition_id'>
      <description>Component sgtl5000 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.h' projectpath='codec/port/sgtl5000'/>
        <file category='sourceC' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.c' projectpath='codec/port/sgtl5000'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/sgtl5000/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id' Cversion='2.0.1' condition='component.silicon_id.condition_id'>
      <description>Driver silicon_id</description>
      <files>
        <file category='header' name='components/silicon_id/fsl_silicon_id.h' projectpath='component/silicon_id'/>
        <file category='sourceC' name='components/silicon_id/fsl_silicon_id.c' projectpath='component/silicon_id'/>
        <file category='include' name='components/silicon_id/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_imx8' Cversion='2.0.0' condition='component.silicon_id_imx8.condition_id'>
      <description>Driver silicon_id imx8</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imx8/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_imx93' Cversion='2.0.0' condition='component.silicon_id_imx93.condition_id'>
      <description>Driver silicon_id imx93</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imx93/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt10xx' Cversion='2.0.0' condition='component.silicon_id_imxrt10xx.condition_id'>
      <description>Driver silicon_id rt10xx</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/rt10xx/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt1170' Cversion='2.0.0' condition='component.silicon_id_imxrt1170.condition_id'>
      <description>Driver silicon_id imxrt1170</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imxrt1170/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt1180' Cversion='2.0.0' condition='component.silicon_id_imxrt1180.condition_id'>
      <description>Driver silicon_id imxrt1180</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imxrt1180/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_mcxn' Cversion='2.0.0' condition='component.silicon_id_mcxn.condition_id'>
      <description>Driver silicon_id mcxn</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/mcxn/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rw610' Cversion='2.0.0' condition='component.silicon_id_rw610.condition_id'>
      <description>Driver silicon_id rw610</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/rw610/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_scfw' Cversion='2.0.0' condition='component.silicon_id_scfw.condition_id'>
      <description>Driver silicon_id scfw</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/scfw/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter' Cversion='2.2.0' condition='component.tfa9896_adapter.condition_id'>
      <description>Component tfa9896 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.h' projectpath='codec/port/tfa9896'/>
        <file category='sourceC' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.c' projectpath='codec/port/tfa9896'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9896/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter' Cversion='2.2.0' condition='component.tfa9xxx_adapter.condition_id'>
      <description>Component tfa9xxx adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.h' projectpath='codec/port/tfa9xxx'/>
        <file category='sourceC' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.c' projectpath='codec/port/tfa9xxx'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9xxx/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_88w8987' Cversion='1.0.0' condition='component.wifi_bt_module.88W8987.condition_id'>
      <description>88W8987 Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.88W8987_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw416' Cversion='1.0.0' condition='component.wifi_bt_module.IW416.condition_id'>
      <description>IW416 Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.IW416_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw61x' Cversion='1.0.0' condition='component.wifi_bt_module.IW61X.condition_id'>
      <description>IW61X Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.IW61X_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rw61x' Cversion='1.0.0' condition='component.wifi_bt_module.RW61X.condition_id'>
      <description>RW61X Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.RW61X_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am457_usd.condition_id'>
      <description>IW416-AW-AM457-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM457_USD
        #define WIFI_IW416_BOARD_AW_AM457_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am457_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am457ma.condition_id'>
      <description>IW416-AW-AM457MA-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM457MA
        #define WIFI_IW416_BOARD_AW_AM457MA
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am457ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_arduino' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am510_arduino.condition_id'>
      <description>FRDM-IW416-AW-AM510 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM510_ARDUINO
        #define WIFI_IW416_BOARD_AW_AM510_ARDUINO
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am510_arduino_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am510_usd.condition_id'>
      <description>IW416-AW-AM510-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM510_USD
        #define WIFI_IW416_BOARD_AW_AM510_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am510_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am510ma.condition_id'>
      <description>IW416-AW-AM510-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM510MA
        #define WIFI_IW416_BOARD_AW_AM510MA
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am510ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_cm358_usd.condition_id'>
      <description>AW-CM358-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_AW_CM358_USD
        #define WIFI_88W8987_BOARD_AW_CM358_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_cm358_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_cm358ma.condition_id'>
      <description>AW-CM358MA-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_AW_CM358MA
        #define WIFI_88W8987_BOARD_AW_CM358MA
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_cm358ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_nm191_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_nm191_usd.condition_id'>
      <description>AW-NM191-USD Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_nm191_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_nm191ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_nm191ma.condition_id'>
      <description>AW-NM191MA-M2 Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_nm191ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_frdm_rw61x' Cversion='1.0.0' condition='component.wifi_bt_module.board_frdm_rw61x.condition_id'>
      <description>FRDM-RW61X Wi-Fi board</description>
      <RTE_Components_h>
#ifndef WIFI_BOARD_FRDM_RW61X
#define WIFI_BOARD_FRDM_RW61X 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_frdm_rw61x_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_k32w061_trnscver' Cversion='1.0.0' condition='component.wifi_bt_module.board_k32w061_transceiver.condition_id'>
      <description>IK32W061-TRANSCEIVER Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_k32w061_transceiver_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1xk_m2.condition_id'>
      <description>IW416-MURATA-1XK-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_MURATA_1XK_M2
        #define WIFI_IW416_BOARD_MURATA_1XK_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1xk_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1xk_usd.condition_id'>
      <description>IW416-MURATA-1XK-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_MURATA_1XK_USD
        #define WIFI_IW416_BOARD_MURATA_1XK_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1xk_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1zm_m2.condition_id'>
      <description>MURATA-1ZM-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_MURATA_1ZM_M2
        #define WIFI_88W8987_BOARD_MURATA_1ZM_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1zm_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1zm_usd.condition_id'>
      <description>MURATA-1ZM-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_MURATA_1ZM_USD
        #define WIFI_88W8987_BOARD_MURATA_1ZM_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1zm_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2dl_m2.condition_id'>
      <description>IW611-MURATA-2DL-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW611_BOARD_MURATA_2DL_M2
        #define WIFI_IW611_BOARD_MURATA_2DL_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2dl_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2dl_usd.condition_id'>
      <description>IW611-MURATA-2DL-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW611_BOARD_MURATA_2DL_USD
        #define WIFI_IW611_BOARD_MURATA_2DL_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2dl_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ds_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2ds_m2.condition_id'>
      <description>MURATA-2DS-M2 Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2ds_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ds_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2ds_usd.condition_id'>
      <description>MURATA-2DS-USD Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2ds_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2el_m2.condition_id'>
      <description>IW612-MURATA-2EL-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW612_BOARD_MURATA_2EL_M2
        #define WIFI_IW612_BOARD_MURATA_2EL_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2el_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2el_usd.condition_id'>
      <description>IW612-MURATA-2EL-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW612_BOARD_MURATA_2EL_USD
        #define WIFI_IW612_BOARD_MURATA_2EL_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2el_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ll_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2ll_m2.condition_id'>
      <description>IW610-MURATA-2LL-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW610_BOARD_MURATA_2LL_M2
        #define WIFI_IW610_BOARD_MURATA_2LL_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2ll_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rd_rw61x' Cversion='1.0.0' condition='component.wifi_bt_module.board_rd_rw61x.condition_id'>
      <description>RD-RW61X Wi-Fi board</description>
      <RTE_Components_h>
#ifndef WIFI_BOARD_RW610
#define WIFI_BOARD_RW610 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_rd_rw61x_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w2_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_jody_w2_usd.condition_id'>
      <description>UBX-JODY-W2-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_UBX_JODY_W2_USD
        #define WIFI_88W8987_BOARD_UBX_JODY_W2_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_jody_w2_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_jody_w5_m2.condition_id'>
      <description>UBX-JODY-W5-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_AW611_BOARD_UBX_JODY_W5_M2
        #define WIFI_AW611_BOARD_UBX_JODY_W5_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_jody_w5_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_jody_w5_usd.condition_id'>
      <description>UBX-JODY-W5-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_AW611_BOARD_UBX_JODY_W5_USD
        #define WIFI_AW611_BOARD_UBX_JODY_W5_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_jody_w5_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_lily_w1_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_lily_w1_usd.condition_id'>
      <description>UBX-LILY-W1-USD Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_lily_w1_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_maya_w1_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_maya_w1_usd.condition_id'>
      <description>UBX-MAYA-W1-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_UBX_MAYA_W1_USD
        #define WIFI_IW416_BOARD_UBX_MAYA_W1_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_maya_w1_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='bt_only_fw' Cversion='1.0.0' condition='component.wifi_bt_module.bt_only_fw.condition_id'>
      <description>BT only firmware</description>
      <RTE_Components_h>
        #ifndef CONFIG_BT_ONLY_DNLD
        #define CONFIG_BT_ONLY_DNLD
        #endif
        #ifndef CONFIG_BT_IND_DNLD
        #define CONFIG_BT_IND_DNLD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.bt_only_fw_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config' Cversion='1.0.0' condition='component.wifi_bt_module.config.condition_id'>
      <description>Wi-Fi and BT module configs</description>
      <files>
        <file category='header' name='components/wifi_bt_module/incl/wifi_bt_module_config.h' projectpath='component/wifi_bt_module/incl'/>
        <file category='include' name='components/wifi_bt_module/incl/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tx_pwr_limits' Cversion='1.0.0' condition='component.wifi_bt_module.tx_pwr_limits.condition_id'>
      <description>Wi-Fi module Tx power limits</description>
      <files>
        <file category='header' name='components/wifi_bt_module/AzureWave/tx_pwr_limits/wlan_txpwrlimit_cfg_WW.h' projectpath='component/wifi_bt_module/AzureWave/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/AzureWave/tx_pwr_limits/wlan_txpwrlimit_cfg_WW_rw610.h' projectpath='component/wifi_bt_module/AzureWave/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_1XK_WW.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_1ZM_WW.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_2EL_WW.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_NH.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/u-blox/tx_pwr_limits/wlan_txpwrlimit_cfg_jody_w5_WW.h' projectpath='component/wifi_bt_module/u-blox/tx_pwr_limits'/>
        <file category='include' name='components/wifi_bt_module/AzureWave/tx_pwr_limits/'/>
        <file category='include' name='components/wifi_bt_module/Murata/tx_pwr_limits/'/>
        <file category='include' name='components/wifi_bt_module/u-blox/tx_pwr_limits/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_combo_fw' Cversion='1.0.0' condition='component.wifi_bt_module.wifi_bt_combo_fw.condition_id'>
      <description>Wi-Fi/BT combo firmware</description>
      <RTE_Components_h>
        #ifndef CONFIG_WIFI_BT_CMOBO_DNLD
        #define CONFIG_WIFI_BT_CMOBO_DNLD
        #endif
        #ifndef CONFIG_WIFI_IND_DNLD
        #define CONFIG_WIFI_IND_DNLD 0
        #endif
        #ifndef CONFIG_WIFI_IND_RESET
        #define CONFIG_WIFI_IND_RESET 0
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.wifi_bt_combo_fw_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter' Cversion='2.2.0' condition='component.wm8524_adapter.condition_id'>
      <description>Component wm8524 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.h' projectpath='codec/port/wm8524'/>
        <file category='sourceC' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.c' projectpath='codec/port/wm8524'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8524/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter' Cversion='2.2.0' condition='component.wm8904_adapter.condition_id'>
      <description>Component wm8904 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.h' projectpath='codec/port/wm8904'/>
        <file category='sourceC' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.c' projectpath='codec/port/wm8904'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8904/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter' Cversion='2.2.0' condition='component.wm8960_adapter.condition_id'>
      <description>Component wm8960 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.h' projectpath='codec/port/wm8960'/>
        <file category='sourceC' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.c' projectpath='codec/port/wm8960'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8960/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter' Cversion='2.2.0' condition='component.wm8962_adapter.condition_id'>
      <description>Component wm8962 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.h' projectpath='codec/port/wm8962'/>
        <file category='sourceC' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.c' projectpath='codec/port/wm8962'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8962/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT1166_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device MIMXRT1166_cmsis</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/fsl_device_registers.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm4.condition_id' category='header' name='devices/MIMXRT1166/MIMXRT1166_cm4.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm4.condition_id' category='header' name='devices/MIMXRT1166/MIMXRT1166_cm4_COMMON.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm4.condition_id' category='header' name='devices/MIMXRT1166/MIMXRT1166_cm4_features.h' projectpath='device'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ADC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ADC_ETC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_LDO_SNVS.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_LDO_SNVS_DIG.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_MISC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_OSC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_PLL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_PMU.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ANADIG_TEMPSENSOR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_AOI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ASRC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_AUDIO_PLL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CAAM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CAN.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CAN_WRAPPER.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CCM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CCM_OBS.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CDOG.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CMP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_CSI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DAC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DCDC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DCIC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DMA.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DMAMUX.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DSI_HOST.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DSI_HOST_APB_PKT_IF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DSI_HOST_DPI_INTFC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_DSI_HOST_NXP_FDSOI28_DPHY_INTFC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_EMVSIM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ENC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ENET.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_ETHERNET_PLL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_EWM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_FLEXIO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_FLEXRAM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_FLEXSPI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_GPC_CPU_MODE_CTRL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_GPC_SET_POINT_CTRL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_GPC_STBY_CTRL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_GPIO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_GPT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_I2S.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IEE.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IEE_APC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IOMUXC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IOMUXC_GPR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IOMUXC_LPSR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IOMUXC_LPSR_GPR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IOMUXC_SNVS.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IOMUXC_SNVS_GPR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_IPS_DOMAIN.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_KEY_MANAGER.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_KPP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_LCDIF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_LCDIFV2.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_LMEM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_LPI2C.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_LPSPI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_LPUART.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_MCM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_MECC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_MIPI_CSI2RX.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_MMCAU.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_MU.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_OCOTP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_OSC_RC_400M.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_OTFAD.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PDM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PGMC_BPC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PGMC_CPC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PGMC_MIF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PGMC_PPC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PHY_LDO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PIT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PUF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PWM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_PXP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_RDC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_RDC_SEMAPHORE.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_RTWDOG.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SEMA4.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SEMC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SNVS.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SPDIF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SRAM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SRC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SSARC_HP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_SSARC_LP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_TMPSNS.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_TMR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_USB.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_USBHSDCD.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_USBNC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_USBPHY.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_USDHC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_VIDEO_MUX.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_VIDEO_PLL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_VMBANDGAP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_WDOG.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_XBARA.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_XBARB.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_XECC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT1166/periph/PERI_XRDC2.h' projectpath='device/periph'/>
        <file condition='allOf.core_ids=cm7.condition_id' category='header' name='devices/MIMXRT1166/MIMXRT1166_cm7.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm7.condition_id' category='header' name='devices/MIMXRT1166/MIMXRT1166_cm7_COMMON.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm7.condition_id' category='header' name='devices/MIMXRT1166/MIMXRT1166_cm7_features.h' projectpath='device'/>
        <file category='include' name='devices/MIMXRT1166/'/>
        <file category='include' name='devices/MIMXRT1166/periph/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='header' attr='config' name='devices/MIMXRT1166/template/RTE_Device.h' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MIMXRT1166/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xip_device' Cversion='1.0.0' condition='device.boot_header.condition_id'>
      <description>Device Boot Header</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/xip/fsl_flexspi_nor_boot.c' projectpath='xip'/>
        <file category='header' name='devices/MIMXRT1166/xip/fsl_flexspi_nor_boot.h' projectpath='xip'/>
        <file category='include' name='devices/MIMXRT1166/xip/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='MIMXRT1166_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device MIMXRT1166_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm4_flexspi_nor.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm4_flexspi_nor_sdram.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm4_ram.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm4_sdram.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm4_sdram_txt.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=armgcc, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm4_flexspi_nor.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm4_flexspi_nor_sdram.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm4_ram.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm4_sdram.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm4_sdram_txt.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=iar, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm4_flexspi_nor.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm4_flexspi_nor_sdram.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm4_ram.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm4_sdram.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm4f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm4_sdram_txt.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=mdk, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm7_flexspi_nor.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm7_flexspi_nor_sdram.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm7_ram.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm7_sdram.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/arm/MIMXRT1166xxxxx_cm7_sdram_txt.scf' version='1.0.0' projectpath='MIMXRT1166/arm'/>
        <file condition='allOf.toolchains=armgcc, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm7_flexspi_nor.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm7_flexspi_nor_sdram.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm7_ram.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm7_sdram.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/gcc/MIMXRT1166xxxxx_cm7_sdram_txt.ld' version='1.0.0' projectpath='MIMXRT1166/gcc'/>
        <file condition='allOf.toolchains=iar, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm7_flexspi_nor.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm7_flexspi_nor_sdram.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm7_ram.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm7_sdram.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm7f, device_ids=MIMXRT1166xxxxx.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT1166/iar/MIMXRT1166xxxxx_cm7_sdram_txt.icf' version='1.0.0' projectpath='MIMXRT1166/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='MIMXRT1166' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template MIMXRT1166</description>
      <files>
        <file category='header' attr='config' name='devices/MIMXRT1166/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT1166/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MIMXRT1166/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT1166/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MIMXRT1166/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT1166/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MIMXRT1166/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT1166/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MIMXRT1166/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device MIMXRT1166_startup</description>
      <files>
        <file condition='allOf.toolchains=iar, core_ids=cm4.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT1166/iar/startup_MIMXRT1166_cm4.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc, core_ids=cm4.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT1166/gcc/startup_MIMXRT1166_cm4.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk, core_ids=cm4.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT1166/arm/startup_MIMXRT1166_cm4.S' version='1.0.0' projectpath='startup/arm'/>
        <file condition='allOf.toolchains=iar, core_ids=cm7.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT1166/iar/startup_MIMXRT1166_cm7.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc, core_ids=cm7.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT1166/gcc/startup_MIMXRT1166_cm7.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk, core_ids=cm7.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT1166/arm/startup_MIMXRT1166_cm7.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT1166_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device MIMXRT1166_system</description>
      <files>
        <file condition='allOf.core_ids=cm4.condition_id' category='sourceC' name='devices/MIMXRT1166/system_MIMXRT1166_cm4.c' projectpath='device'/>
        <file condition='allOf.core_ids=cm4.condition_id' category='header' name='devices/MIMXRT1166/system_MIMXRT1166_cm4.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm7.condition_id' category='sourceC' name='devices/MIMXRT1166/system_MIMXRT1166_cm7.c' projectpath='device'/>
        <file condition='allOf.core_ids=cm7.condition_id' category='header' name='devices/MIMXRT1166/system_MIMXRT1166_cm7.h' projectpath='device'/>
        <file category='include' name='devices/MIMXRT1166/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='acmp' Cversion='2.3.0' condition='driver.acmp.condition_id'>
      <description>ACMP Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_acmp.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_acmp.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adc_etc' Cversion='2.3.2' condition='driver.adc_etc.condition_id'>
      <description>ADC_ETC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_adc_etc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_adc_etc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497' Cversion='2.1.2' condition='driver.ak4497.condition_id'>
      <description>Driver ak4497</description>
      <RTE_Components_h>
#ifndef CODEC_AK4497_ENABLE
#define CODEC_AK4497_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/ak4497/fsl_ak4497.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/ak4497/fsl_ak4497.c' projectpath='codec'/>
        <file category='include' name='components/codec/ak4497/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pmu_1' Cversion='2.1.2' condition='driver.anadig_pmu.condition_id'>
      <description>PMU Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pmu.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pmu.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='anatop_ai' Cversion='1.0.0' condition='driver.anatop_ai.condition_id'>
      <description>ANATOP AI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_anatop_ai.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_anatop_ai.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='aoi' Cversion='2.0.2' condition='driver.aoi.condition_id'>
      <description>AOI Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_aoi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_aoi.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='asrc' Cversion='2.1.3' condition='driver.asrc.condition_id'>
      <description>asrc Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_asrc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_asrc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='asrc_edma' Cversion='2.2.0' condition='driver.asrc_edma.condition_id'>
      <description>asrc_edma Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_asrc_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_asrc_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='caam' Cversion='2.4.0' condition='driver.caam.condition_id'>
      <description>CAAM Driver</description>
      <RTE_Components_h>
        #ifndef CRYPTO_USE_DRIVER_CAAM
        #define CRYPTO_USE_DRIVER_CAAM
        #endif
        #ifndef CACHE_MODE_WRITE_THROUGH
        #define CACHE_MODE_WRITE_THROUGH 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_caam.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_caam.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cache_armv7_m7' Cversion='2.0.4' condition='driver.cache_armv7_m7.condition_id'>
      <description>CACHE Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/cm7/fsl_cache.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/cm7/fsl_cache.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/cm7/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cache_lmem' Cversion='2.1.0' condition='driver.cache_lmem.condition_id'>
      <description>CACHE LMEM Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/cm4/fsl_cache.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/cm4/fsl_cache.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/cm4/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common' Cversion='1.0.0' condition='driver.camera-common.condition_id'>
      <description>Driver camera-common</description>
      <files>
        <file category='header' name='components/video/camera/fsl_camera.h' projectpath='video'/>
        <file category='include' name='components/video/camera/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ap1302' Cversion='1.0.1' condition='driver.camera-device-ap1302.condition_id'>
      <description>Driver camera-device-ap1302</description>
      <files>
        <file category='header' name='components/video/camera/device/ap1302/fsl_ap1302.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ap1302/fsl_ap1302.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ap1302/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common' Cversion='1.0.0' condition='driver.camera-device-common.condition_id'>
      <description>Driver camera-device-common</description>
      <files>
        <file category='header' name='components/video/camera/device/fsl_camera_device.h' projectpath='video'/>
        <file category='include' name='components/video/camera/device/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-max9286' Cversion='1.0.2' condition='driver.camera-device-max9286.condition_id'>
      <description>Driver camera-device-max9286</description>
      <files>
        <file category='header' name='components/video/camera/device/max9286/fsl_max9286.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/max9286/fsl_max9286.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/max9286/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-mt9m114' Cversion='1.0.2' condition='driver.camera-device-mt9m114.condition_id'>
      <description>Driver camera-device-mt9m114</description>
      <files>
        <file category='header' name='components/video/camera/device/mt9m114/fsl_mt9m114.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/mt9m114/fsl_mt9m114.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/mt9m114/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov5640' Cversion='1.0.1' condition='driver.camera-device-ov5640.condition_id'>
      <description>Driver camera-device-ov5640</description>
      <files>
        <file category='header' name='components/video/camera/device/ov5640/fsl_ov5640.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov5640/fsl_ov5640.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov5640/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7670' Cversion='1.0.2' condition='driver.camera-device-ov7670.condition_id'>
      <description>Driver camera-device-ov7670</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7670/fsl_ov7670.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7670/fsl_ov7670.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7670/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7725' Cversion='1.0.1' condition='driver.camera-device-ov7725.condition_id'>
      <description>Driver camera-device-ov7725</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7725/fsl_ov7725.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7725/fsl_ov7725.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7725/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb' Cversion='1.0.1' condition='driver.camera-device-sccb.condition_id'>
      <description>Driver camera-device-sccb</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/camera/device/sccb/fsl_sccb.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/sccb/fsl_sccb.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/sccb/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-common' Cversion='1.0.0' condition='driver.camera-receiver-common.condition_id'>
      <description>Driver camera-receiver-common</description>
      <files>
        <file category='header' name='components/video/camera/receiver/fsl_camera_receiver.h' projectpath='video'/>
        <file category='include' name='components/video/camera/receiver/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-csi' Cversion='1.0.2' condition='driver.camera-receiver-csi.condition_id'>
      <description>Driver camera-receiver-csi</description>
      <files>
        <file category='header' name='components/video/camera/receiver/csi/fsl_csi_camera_adapter.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/receiver/csi/fsl_csi_camera_adapter.c' projectpath='video'/>
        <file category='include' name='components/video/camera/receiver/csi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cdog' Cversion='2.1.3' condition='driver.cdog.condition_id'>
      <description>cdog Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_cdog.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_cdog.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.1.6' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='Ethernet' Csub='enet_cmsis' Cversion='2.4.0' Capiversion='2.4.0' condition='driver.cmsis_enet.condition_id'>
      <description>ENET CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/cmsis_drivers/fsl_enet_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/cmsis_drivers/fsl_enet_cmsis.c' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/cmsis_drivers/fsl_enet_phy_cmsis.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/cmsis_drivers/fsl_enet_phy_cmsis.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='lpi2c_cmsis' Cversion='2.6.0' Capiversion='2.3.0' condition='driver.cmsis_lpi2c.condition_id'>
      <description>LPI2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/cmsis_drivers/fsl_lpi2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/cmsis_drivers/fsl_lpi2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='lpspi_cmsis' Cversion='2.12.0' Capiversion='2.2.0' condition='driver.cmsis_lpspi.condition_id'>
      <description>LPSPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/cmsis_drivers/fsl_lpspi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/cmsis_drivers/fsl_lpspi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='lpuart_cmsis' Cversion='2.7.0' Capiversion='2.3.0' condition='driver.cmsis_lpuart.condition_id'>
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/cmsis_drivers/fsl_lpuart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/cmsis_drivers/fsl_lpuart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec' Cversion='2.3.1' condition='driver.codec.condition_id'>
      <description>Driver codec</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/fsl_codec_common.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/fsl_codec_common.c' projectpath='codec'/>
        <file category='include' name='components/codec/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm4f, cm7f.condition_id' category='sourceC' name='devices/MIMXRT1166/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm4f, cm7f.condition_id' category='header' name='devices/MIMXRT1166/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448' Cversion='2.0.1' condition='driver.cs42448.condition_id'>
      <description>Driver cs42448</description>
      <RTE_Components_h>
#ifndef CODEC_CS42448_ENABLE
#define CODEC_CS42448_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42448/fsl_cs42448.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42448/fsl_cs42448.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42448/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888' Cversion='2.1.3' condition='driver.cs42888.condition_id'>
      <description>Driver cs42888</description>
      <RTE_Components_h>
#ifndef CODEC_CS42888_ENABLE
#define CODEC_CS42888_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42888/fsl_cs42888.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42888/fsl_cs42888.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42888/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='csi' Cversion='2.2.0' condition='driver.csi.condition_id'>
      <description>CSI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_csi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_csi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dac' Cversion='2.1.1' condition='driver.dac12.condition_id'>
      <description>DAC12 Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_dac12.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_dac12.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi' Cversion='1.0.0' condition='driver.dbi.condition_id'>
      <description>Driver dbi</description>
      <files>
        <file category='header' name='components/video/display/dbi/fsl_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/fsl_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common' Cversion='1.0.0' condition='driver.dc-fb-common.condition_id'>
      <description>Driver dc-fb-common</description>
      <files>
        <file category='header' name='components/video/display/dc/fsl_dc_fb.h' projectpath='video'/>
        <file category='include' name='components/video/display/dc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dbi' Cversion='1.0.0' condition='driver.dc-fb-dbi.condition_id'>
      <description>Driver dc-fb-dbi</description>
      <RTE_Components_h>
#ifndef MCUX_DBI_LEGACY
#define MCUX_DBI_LEGACY 0
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dsi-cmd' Cversion='1.1.1' condition='driver.dc-fb-dsi-cmd.condition_id'>
      <description>Driver dc-fb-dsi-cmd</description>
      <files>
        <file category='header' name='components/video/display/dc/dsi_cmd/fsl_dc_fb_dsi_cmd.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dsi_cmd/fsl_dc_fb_dsi_cmd.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dsi_cmd/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-elcdif' Cversion='1.0.1' condition='driver.dc-fb-elcdif.condition_id'>
      <description>Driver dc-fb-elcdif</description>
      <files>
        <file category='header' name='components/video/display/dc/elcdif/fsl_dc_fb_elcdif.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/elcdif/fsl_dc_fb_elcdif.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/elcdif/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-lcdifv2' Cversion='1.0.2' condition='driver.dc-fb-lcdifv2.condition_id'>
      <description>Driver dc-fb-lcdifv2</description>
      <files>
        <file category='header' name='components/video/display/dc/lcdifv2/fsl_dc_fb_lcdifv2.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/lcdifv2/fsl_dc_fb_lcdifv2.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/lcdifv2/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-ssd1963' Cversion='1.0.2' condition='driver.dc-fb-ssd1963.condition_id'>
      <description>Driver dc-fb-ssd1963</description>
      <files>
        <file category='header' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dcdc_soc' Cversion='2.1.2' condition='driver.dcdc_2.condition_id'>
      <description>DCDC SOC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_dcdc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_dcdc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dcic' Cversion='2.0.2' condition='driver.dcic.condition_id'>
      <description>DCIC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_dcic.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_dcic.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dialog7212' Cversion='2.3.1' condition='driver.dialog7212.condition_id'>
      <description>Driver dialog7212</description>
      <RTE_Components_h>
#ifndef CODEC_DA7212_ENABLE
#define CODEC_DA7212_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/da7212/fsl_dialog7212.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/da7212/fsl_dialog7212.c' projectpath='codec'/>
        <file category='include' name='components/codec/da7212/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-adv7535' Cversion='1.0.1' condition='driver.display-adv7535.condition_id'>
      <description>Driver display-adv7535</description>
      <files>
        <file category='header' name='components/video/display/adv7535/fsl_adv7535.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/adv7535/fsl_adv7535.c' projectpath='video'/>
        <file category='include' name='components/video/display/adv7535/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-co5300' Cversion='1.0.0' condition='driver.display-co5300.condition_id'>
      <description>Driver display-co5300</description>
      <files>
        <file category='header' name='components/video/display/co5300/fsl_co5300.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/co5300/fsl_co5300.c' projectpath='video'/>
        <file category='include' name='components/video/display/co5300/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-common' Cversion='1.0.0' condition='driver.display-common.condition_id'>
      <description>Driver display-common</description>
      <files>
        <file category='header' name='components/video/display/fsl_display.h' projectpath='video'/>
        <file category='include' name='components/video/display/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-hx8394' Cversion='1.0.0' condition='driver.display-hx8394.condition_id'>
      <description>Driver display-hx8394</description>
      <files>
        <file category='header' name='components/video/display/hx8394/fsl_hx8394.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/hx8394/fsl_hx8394.c' projectpath='video'/>
        <file category='include' name='components/video/display/hx8394/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6161' Cversion='1.0.0' condition='driver.display-it6161.condition_id'>
      <description>Driver display-it6161</description>
      <files>
        <file category='header' name='components/video/display/it6161/fsl_it6161.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/fsl_it6161.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/hdmi_tx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/hdmi_tx.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/mipi_rx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/mipi_rx.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6161/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6263' Cversion='1.0.1' condition='driver.display-it6263.condition_id'>
      <description>Driver display-it6263</description>
      <files>
        <file category='header' name='components/video/display/it6263/fsl_it6263.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6263/fsl_it6263.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6263/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-mipi-dsi-cmd' Cversion='1.0.2' condition='driver.display-mipi-dsi-cmd.condition_id'>
      <description>Driver display-mipi-dsi-cmd</description>
      <files>
        <file category='header' name='components/video/display/mipi_dsi_cmd/fsl_mipi_dsi_cmd.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/mipi_dsi_cmd/fsl_mipi_dsi_cmd.c' projectpath='video'/>
        <file category='include' name='components/video/display/mipi_dsi_cmd/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm67162' Cversion='1.0.2' condition='driver.display-rm67162.condition_id'>
      <description>Driver display-rm67162</description>
      <files>
        <file category='header' name='components/video/display/rm67162/fsl_rm67162.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm67162/fsl_rm67162.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm67162/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm67191' Cversion='1.1.0' condition='driver.display-rm67191.condition_id'>
      <description>Driver display-rm67191</description>
      <files>
        <file category='header' name='components/video/display/rm67191/fsl_rm67191.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm67191/fsl_rm67191.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm67191/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm68191' Cversion='1.1.0' condition='driver.display-rm68191.condition_id'>
      <description>Driver display-rm68191</description>
      <files>
        <file category='header' name='components/video/display/rm68191/fsl_rm68191.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm68191/fsl_rm68191.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm68191/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm68200' Cversion='1.1.1' condition='driver.display-rm68200.condition_id'>
      <description>Driver display-rm68200</description>
      <files>
        <file category='header' name='components/video/display/rm68200/fsl_rm68200.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm68200/fsl_rm68200.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm68200/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm692c9' Cversion='1.1.0' condition='driver.display-rm692c9.condition_id'>
      <description>Driver display-rm692c9</description>
      <files>
        <file category='header' name='components/video/display/rm692c9/fsl_rm692c9.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm692c9/fsl_rm692c9.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm692c9/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rpi' Cversion='1.0.0' condition='driver.display-rpi.condition_id'>
      <description>Driver display-rpi</description>
      <files>
        <file category='header' name='components/video/display/rpi/fsl_rpi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rpi/fsl_rpi.c' projectpath='video'/>
        <file category='include' name='components/video/display/rpi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-sn65dsi83' Cversion='1.0.0' condition='driver.display-sn65dsi83.condition_id'>
      <description>Driver display-sn65dsi83</description>
      <files>
        <file category='header' name='components/video/display/sn65dsi83/fsl_sn65dsi83.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/sn65dsi83/fsl_sn65dsi83.c' projectpath='video'/>
        <file category='include' name='components/video/display/sn65dsi83/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmamux' Cversion='2.1.2' condition='driver.dmamux.condition_id'>
      <description>DMAMUX Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_dmamux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_dmamux.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='edma' Cversion='2.4.5' condition='driver.edma.condition_id'>
      <description>EDMA Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ektf2k' Cversion='1.0.0' condition='driver.ektf2k.condition_id'>
      <description>Driver ektf2k</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/ektf2k/fsl_ektf2k.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ektf2k/fsl_ektf2k.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ektf2k/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='elcdif' Cversion='2.1.0' condition='driver.elcdif.condition_id'>
      <description>ELCDIF Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_elcdif.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_elcdif.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enc' Cversion='2.2.1' condition='driver.enc.condition_id'>
      <description>ENC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_enc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_enc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enet' Cversion='2.9.2' condition='driver.enet.condition_id'>
      <description>ENET Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_enet.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_enet.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ewm' Cversion='2.0.4' condition='driver.ewm.condition_id'>
      <description>EWM Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_ewm.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_ewm.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan' Cversion='2.14.1' condition='driver.flexcan.condition_id'>
      <description>FLEXCAN Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexcan.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexcan.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan_edma' Cversion='2.12.0' condition='driver.flexcan_edma.condition_id'>
      <description>FLEXCAN Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexcan_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexcan_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio' Cversion='2.3.0' condition='driver.flexio.condition_id'>
      <description>FLEXIO Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2c_master' Cversion='2.6.1' condition='driver.flexio_i2c_master.condition_id'>
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_i2c_master.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_i2c_master.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2s' Cversion='2.2.1' condition='driver.flexio_i2s.condition_id'>
      <description>FLEXIO I2S Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_i2s.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_i2s.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2s_edma' Cversion='2.1.8' condition='driver.flexio_i2s_edma.condition_id'>
      <description>FLEXIO I2S EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_i2s_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_i2s_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi' Cversion='2.4.2' condition='driver.flexio_spi.condition_id'>
      <description>FLEXIO SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_spi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_spi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi_edma' Cversion='2.3.0' condition='driver.flexio_spi_edma.condition_id'>
      <description>FLEXIO SPI EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_spi_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_spi_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart' Cversion='2.6.2' condition='driver.flexio_uart.condition_id'>
      <description>FLEXIO UART Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_uart.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_uart.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart_edma' Cversion='2.4.1' condition='driver.flexio_uart_edma.condition_id'>
      <description>FLEXIO UART EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexio_uart_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexio_uart_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexram' Cversion='2.3.0' condition='driver.flexram.condition_id'>
      <description>FLEXRAM Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexram.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexram.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='soc_flexram_allocate' Cversion='2.0.2' condition='driver.flexram_allocate.condition_id'>
      <description>SOC FLEXRAM ALLOCATE Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexram_allocate.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexram_allocate.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi' Cversion='2.7.0' condition='driver.flexspi.condition_id'>
      <description>FLEXSPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexspi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexspi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi_edma' Cversion='2.3.3' condition='driver.flexspi_edma.condition_id'>
      <description>FLEXSPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_flexspi_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_flexspi_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft3267' Cversion='1.0.1' condition='driver.ft3267.condition_id'>
      <description>Touch panel controller FT3267 driver</description>
      <files>
        <file category='header' name='components/touch/ft3267/fsl_ft3267.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft3267/fsl_ft3267.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft3267/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406' Cversion='1.0.0' condition='driver.ft5406.condition_id'>
      <description>Driver ft5406</description>
      <files>
        <file category='header' name='components/touch/ft5406/fsl_ft5406.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406/fsl_ft5406.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406_rt' Cversion='1.0.0' condition='driver.ft5406_rt.condition_id'>
      <description>Driver ft5406_rt</description>
      <files>
        <file category='header' name='components/touch/ft5406_rt/fsl_ft5406_rt.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406_rt/fsl_ft5406_rt.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406_rt/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft6x06' Cversion='1.0.0' condition='driver.ft6x06.condition_id'>
      <description>Driver ft6x06</description>
      <files>
        <file category='header' name='components/touch/ft6x06/fsl_ft6x06.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft6x06/fsl_ft6x06.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft6x06/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxas21002cq' Cversion='1.0.0' condition='driver.fxas21002cq.condition_id'>
      <description>Driver fxas21002cq</description>
      <files>
        <file category='header' name='components/sensor/fxas21002cq/fsl_fxas.h' projectpath='gyroscope'/>
        <file category='sourceC' name='components/sensor/fxas21002cq/fsl_fxas.c' projectpath='gyroscope'/>
        <file category='include' name='components/sensor/fxas21002cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxls8974cf' Cversion='1.0.0' condition='driver.fxls8974cf.condition_id'>
      <description>Driver fxls8974cf</description>
      <files>
        <file category='header' name='components/sensor/fxls8974cf/fsl_fxls.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxls8974cf/fsl_fxls.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxls8974cf/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxos8700cq' Cversion='1.0.0' condition='driver.fxos8700cq.condition_id'>
      <description>Driver fxos8700cq</description>
      <files>
        <file category='header' name='components/sensor/fxos8700cq/fsl_fxos.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxos8700cq/fsl_fxos.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxos8700cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpc_3' Cversion='2.5.0' condition='driver.gpc_xxx_ctrl.condition_id'>
      <description>GPC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_gpc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_gpc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpt' Cversion='2.0.5' condition='driver.gpt.condition_id'>
      <description>GPT Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_gpt.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_gpt.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gt911' Cversion='1.0.4' condition='driver.gt911.condition_id'>
      <description>Driver gt911</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/gt911/fsl_gt911.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/gt911/fsl_gt911.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/gt911/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='htu21d' Cversion='1.0.0' condition='driver.htu21d.condition_id'>
      <description>Driver htu21d</description>
      <files>
        <file category='header' name='components/sensor/htu21d/fsl_htu21d.h' projectpath='htu'/>
        <file category='sourceC' name='components/sensor/htu21d/fsl_htu21d.c' projectpath='htu'/>
        <file category='include' name='components/sensor/htu21d/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='icm42688p' Cversion='1.0.0' condition='driver.icm42688p.condition_id'>
      <description>Driver icm42688p</description>
      <files>
        <file category='header' name='components/sensor/icm42688p/fsl_icm42688p.h' projectpath='icm42688p'/>
        <file category='sourceC' name='components/sensor/icm42688p/fsl_icm42688p.c' projectpath='icm42688p'/>
        <file category='include' name='components/sensor/icm42688p/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iee' Cversion='2.1.1' condition='driver.iee.condition_id'>
      <description>IEE Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_iee.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_iee.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iee_apc' Cversion='2.0.2' condition='driver.iee_apc.condition_id'>
      <description>IEE APC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_iee_apc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_iee_apc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.0.6' condition='driver.igpio.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ili9341' Cversion='1.0.2' condition='driver.ili9341.condition_id'>
      <description>Driver ili9341</description>
      <files>
        <file category='header' name='components/display/ili9341/fsl_ili9341.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ili9341/fsl_ili9341.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ili9341/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iomuxc' Cversion='2.0.0' condition='driver.iomuxc.condition_id'>
      <description>IOMUXC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_iomuxc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='key_manager' Cversion='2.0.2' condition='driver.key_manager.condition_id'>
      <description>Key Manager Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_key_manager.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_key_manager.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='kpp' Cversion='2.0.1' condition='driver.kpp.condition_id'>
      <description>KPP Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_kpp.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_kpp.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lcdifv2' Cversion='2.3.3' condition='driver.lcdifv2.condition_id'>
      <description>LCDIFV2 Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lcdifv2.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lcdifv2.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpadc' Cversion='2.9.3' condition='driver.lpadc.condition_id'>
      <description>LPADC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpadc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpadc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.6.1' condition='driver.lpi2c.condition_id'>
      <description>LPI2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpi2c.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpi2c.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma' Cversion='2.4.4' condition='driver.lpi2c_edma.condition_id'>
      <description>LPI2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpi2c_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpi2c_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi' Cversion='2.7.1' condition='driver.lpspi.condition_id'>
      <description>LPSPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpspi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpspi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_edma' Cversion='2.4.6' condition='driver.lpspi_edma.condition_id'>
      <description>LPSPI Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpspi_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpspi_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart' Cversion='2.9.1' condition='driver.lpuart.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpuart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpuart.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_edma' Cversion='2.6.0' condition='driver.lpuart_edma.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_lpuart_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_lpuart_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lsm6dso' Cversion='1.0.0' condition='driver.lsm6dso.condition_id'>
      <description>Driver lsm6dso</description>
      <files>
        <file category='header' name='components/sensor/lsm6dso/fsl_lsm.h' projectpath='tilt_pedo'/>
        <file category='sourceC' name='components/sensor/lsm6dso/fsl_lsm.c' projectpath='tilt_pedo'/>
        <file category='include' name='components/sensor/lsm6dso/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='max30101' Cversion='1.0.0' condition='driver.max30101.condition_id'>
      <description>Driver max30101</description>
      <files>
        <file category='header' name='components/sensor/max30101/fsl_max.h' projectpath='heartrate'/>
        <file category='sourceC' name='components/sensor/max30101/fsl_max.c' projectpath='heartrate'/>
        <file category='include' name='components/sensor/max30101/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mecc' Cversion='2.1.1' condition='driver.mecc.condition_id'>
      <description>MECC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_mecc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_mecc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='memory' Cversion='2.0.0' condition='driver.memory.condition_id'>
      <description>MEMORY Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_memory.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_csi2rx' Cversion='2.0.4' condition='driver.mipi_csi2rx.condition_id'>
      <description>MIPI CSI2RX Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_mipi_csi2rx.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_mipi_csi2rx.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_csi2rx' Cversion='2.0.2' condition='driver.mipi_csi2rx_soc.condition_id'>
      <description>SOC MIPI CSI2RX Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_soc_mipi_csi2rx.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_soc_mipi_csi2rx.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_dsi' Cversion='2.0.0' condition='driver.mipi_dsi_soc.condition_id'>
      <description>SOC MIPI DSI Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_soc_mipi_dsi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_dsi_split' Cversion='2.2.5' condition='driver.mipi_dsi_split.condition_id'>
      <description>MIPI DSI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_mipi_dsi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_mipi_dsi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8451q' Cversion='1.0.0' condition='driver.mma8451q.condition_id'>
      <description>Driver mma8451q</description>
      <files>
        <file category='header' name='components/sensor/mma8451q/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8451q/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8451q/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8652fc' Cversion='1.0.0' condition='driver.mma8652fc.condition_id'>
      <description>Driver mma8652fc</description>
      <files>
        <file category='header' name='components/sensor/mma8652fc/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8652fc/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8652fc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mu' Cversion='2.2.0' condition='driver.mu.condition_id'>
      <description>MU Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_mu.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_mu.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mx25r_flash' Cversion='2.0.0' condition='driver.mx25r_flash.condition_id'>
      <description>Driver mx25r_flash</description>
      <files>
        <file category='header' name='components/mx25r_flash/mx25r_flash.h' projectpath='source'/>
        <file category='sourceC' name='components/mx25r_flash/mx25r_flash.c' projectpath='source'/>
        <file category='include' name='components/mx25r_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nic301' Cversion='2.0.1' condition='driver.nic301.condition_id'>
      <description>NIC301 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_nic301.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nmh1000' Cversion='1.0.0' condition='driver.nmh1000.condition_id'>
      <description>Driver nmh1000</description>
      <files>
        <file category='header' name='components/sensor/nmh1000/fsl_nmh1000.h' projectpath='nmh1000'/>
        <file category='sourceC' name='components/sensor/nmh1000/fsl_nmh1000.c' projectpath='nmh1000'/>
        <file category='include' name='components/sensor/nmh1000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ocotp' Cversion='2.1.4' condition='driver.ocotp.condition_id'>
      <description>OCOTP Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_ocotp.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_ocotp.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='p3t1755' Cversion='2.0.0' condition='driver.p3t1755.condition_id'>
      <description>Driver p3t1755</description>
      <files>
        <file category='header' name='components/sensor/p3t1755/fsl_p3t1755.h' projectpath='component/p3t1755'/>
        <file category='sourceC' name='components/sensor/p3t1755/fsl_p3t1755.c' projectpath='component/p3t1755'/>
        <file category='include' name='components/sensor/p3t1755/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x' Cversion='2.0.1' condition='driver.pcm186x.condition_id'>
      <description>Driver pcm186x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM186X_ENABLE
#define CODEC_PCM186X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm186x/fsl_pcm186x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm186x/fsl_pcm186x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm186x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x' Cversion='2.0.1' condition='driver.pcm512x.condition_id'>
      <description>Driver pcm512x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM512X_ENABLE
#define CODEC_PCM512X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm512x/fsl_pcm512x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm512x/fsl_pcm512x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm512x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pdm' Cversion='2.9.1' condition='driver.pdm.condition_id'>
      <description>PDM Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pdm.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pdm.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pdm_edma' Cversion='2.6.4' condition='driver.pdm_edma.condition_id'>
      <description>PDM EDMA Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pdm_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pdm_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf1550' Cversion='1.0.0' condition='driver.pf1550.condition_id'>
      <description>Driver pf1550</description>
      <files>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550.h' projectpath='component/pmic/pf1550'/>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550_charger.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550_charger.h' projectpath='component/pmic/pf1550'/>
        <file category='include' name='components/pmic/pf1550/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf3000' Cversion='1.0.0' condition='driver.pf3000.condition_id'>
      <description>Driver pf3000</description>
      <files>
        <file category='sourceC' name='components/pmic/pf3000/fsl_pf3000.c' projectpath='component/pmic/pf3000'/>
        <file category='header' name='components/pmic/pf3000/fsl_pf3000.h' projectpath='component/pmic/pf3000'/>
        <file category='include' name='components/pmic/pf3000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf5020' Cversion='2.0.0' condition='driver.pf5020.condition_id'>
      <description>Driver pf5020</description>
      <files>
        <file category='sourceC' name='components/pmic/pf5020/fsl_pf5020.c' projectpath='component/pmic/pf5020'/>
        <file category='header' name='components/pmic/pf5020/fsl_pf5020.h' projectpath='component/pmic/pf5020'/>
        <file category='include' name='components/pmic/pf5020/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pgmc' Cversion='2.1.2' condition='driver.pgmc.condition_id'>
      <description>PGMC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pgmc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pgmc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pit' Cversion='2.2.0' condition='driver.pit.condition_id'>
      <description>PIT Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pit.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pit.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='psp27801' Cversion='1.0.0' condition='driver.psp27801.condition_id'>
      <description>Driver psp27801</description>
      <files>
        <file category='header' name='components/display/psp27801/fsl_psp27801.h' projectpath='oled'/>
        <file category='sourceC' name='components/display/psp27801/fsl_psp27801.c' projectpath='oled'/>
        <file category='include' name='components/display/psp27801/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='PUF' Cversion='2.2.0' condition='driver.puf.condition_id'>
      <description>PUF Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_puf.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_puf.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm' Cversion='2.9.0' condition='driver.pwm.condition_id'>
      <description>PWM Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pwm.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pwm.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pxp' Cversion='2.7.0' condition='driver.pxp.condition_id'>
      <description>PXP Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_pxp.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_pxp.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='qtmr' Cversion='2.2.2' condition='driver.qtmr_1.condition_id'>
      <description>QTMR Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_qtmr.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_qtmr.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rdc' Cversion='2.2.0' condition='driver.rdc.condition_id'>
      <description>RDC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_rdc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_rdc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rdc_sema42' Cversion='2.0.4' condition='driver.rdc_sema42.condition_id'>
      <description>RDC SEMA42 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_rdc_sema42.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_rdc_sema42.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='romapi' Cversion='1.1.1' condition='driver.romapi.condition_id'>
      <description>ROMAPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_romapi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_romapi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtwdog' Cversion='2.1.4' condition='driver.rtwdog.condition_id'>
      <description>RTWDOG Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_rtwdog.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_rtwdog.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sai' Cversion='2.4.7' condition='driver.sai.condition_id'>
      <description>SAI Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_sai.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_sai.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sai_edma' Cversion='2.7.3' condition='driver.sai_edma.condition_id'>
      <description>SAI EDMA Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_sai_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_sai_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sema4' Cversion='2.2.0' condition='driver.sema4.condition_id'>
      <description>SEMA4 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_sema4.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_sema4.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='semc' Cversion='2.7.1' condition='driver.semc.condition_id'>
      <description>SEMC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_semc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_semc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl5000' Cversion='2.1.1' condition='driver.sgtl5000.condition_id'>
      <description>Driver sgtl5000</description>
      <RTE_Components_h>
#ifndef CODEC_SGTL5000_ENABLE
#define CODEC_SGTL5000_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/sgtl5000/fsl_sgtl5000.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/sgtl5000/fsl_sgtl5000.c' projectpath='codec'/>
        <file category='include' name='components/codec/sgtl5000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='smartcard_emvsim' Cversion='2.3.0' condition='driver.smartcard_emvsim.condition_id'>
      <description>SMARTCARD EMVSIM Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_smartcard.h' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_smartcard_emvsim.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_smartcard_emvsim.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='smartcard_phy_emvsim' Cversion='2.3.0' condition='driver.smartcard_phy_emvsim.condition_id'>
      <description>SMARTCARD PHY EMVSIM, use only one SMARTCARD PHY in the project</description>
      <RTE_Components_h>
#ifndef USING_PHY_EMVSIM
#define USING_PHY_EMVSIM 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_smartcard_phy.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_smartcard_phy_emvsim.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='smartcard_uart' Cversion='2.3.0' condition='driver.smartcard_uart.condition_id'>
      <description>SMARTCARD UART Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_smartcard.h' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_smartcard_uart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_smartcard_uart.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='snvs_hp' Cversion='2.3.2' condition='driver.snvs_hp.condition_id'>
      <description>SNVS HP Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_snvs_hp.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_snvs_hp.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='snvs_lp' Cversion='2.4.6' condition='driver.snvs_lp.condition_id'>
      <description>SNVS LP Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_snvs_lp.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_snvs_lp.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spdif' Cversion='2.0.7' condition='driver.spdif.condition_id'>
      <description>SPDIF Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_spdif.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_spdif.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spdif_edma' Cversion='2.0.8' condition='driver.spdif_edma.condition_id'>
      <description>SPDIF EDMA Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_spdif_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_spdif_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='soc_src' Cversion='2.1.1' condition='driver.src_2.condition_id'>
      <description>SOC SRC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_soc_src.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_soc_src.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ssarc' Cversion='2.1.0' condition='driver.ssarc.condition_id'>
      <description>SSARC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_ssarc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_ssarc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963' Cversion='1.2.0' condition='driver.ssd1963.condition_id'>
      <description>Driver ssd1963</description>
      <files>
        <file category='header' name='components/display/ssd1963/fsl_ssd1963.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ssd1963/fsl_ssd1963.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='st7796s' Cversion='1.0.0' condition='driver.st7796s.condition_id'>
      <description>Driver st7796s</description>
      <files>
        <file category='header' name='components/display/st7796s/fsl_st7796s.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/st7796s/fsl_st7796s.c' projectpath='lcdc'/>
        <file category='include' name='components/display/st7796s/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tempsensor' Cversion='2.1.2' condition='driver.tempsensor.condition_id'>
      <description>TEMPSENSOR Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_tempsensor.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_tempsensor.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896' Cversion='6.0.2' condition='driver.tfa9896.condition_id'>
      <description>Driver tfa9896</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9896_ENABLE
#define CODEC_TFA9896_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896_buffer.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_hal_registers.c' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_tfa9896.c' projectpath='codec'/>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896.h' projectpath='codec'/>
        <file category='doc' name='components/codec/tfa9896/MIMXRT595595-EVK_TFA9896_SW.pdf' projectpath='codec'/>
        <file category='include' name='components/codec/tfa9896/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx' Cversion='2.1.0' condition='driver.tfa9xxx.condition_id'>
      <description>Driver tfa9xxx</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9XXX_ENABLE
#define CODEC_TFA9XXX_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9892N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N2.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx.c' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/fsl_tfa9xxx.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx_IMX.c' projectpath='codec/tfa9xxx'/>
        <file category='doc' name='components/codec/tfa9xxx/README.md' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/config.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dsp_fw.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_init.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa9xxx_parameters.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_container_crc32.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_haptic_fw_defs.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
        <file category='include' name='components/codec/tfa9xxx/vas_tfa_drv/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_hal' Cversion='2.1.0' condition='driver.tfa9xxx_hal.condition_id'>
      <description>Driver tfa9xxx_hal</description>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_device_hal.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/tfa_device_hal.c' projectpath='codec/tfa9xxx'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tma525b' Cversion='1.0.0' condition='driver.tma525b.condition_id'>
      <description>Driver tma525b</description>
      <files>
        <file category='header' name='components/touch/tma525b/fsl_tma525b.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/tma525b/fsl_tma525b.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/tma525b/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tsl2561' Cversion='1.0.0' condition='driver.tsl2561.condition_id'>
      <description>Driver tsl2561</description>
      <files>
        <file category='header' name='components/sensor/tsl2561/fsl_tsl2561.h' projectpath='tsl'/>
        <file category='sourceC' name='components/sensor/tsl2561/fsl_tsl2561.c' projectpath='tsl'/>
        <file category='include' name='components/sensor/tsl2561/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sdhc' Cversion='2.8.5' condition='driver.usdhc.condition_id'>
      <description>USDHC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_usdhc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_usdhc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-common' Cversion='1.1.0' condition='driver.video-common.condition_id'>
      <description>Driver video-common</description>
      <files>
        <file category='header' name='components/video/fsl_video_common.h' projectpath='video'/>
        <file category='sourceC' name='components/video/fsl_video_common.c' projectpath='video'/>
        <file category='include' name='components/video/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c' Cversion='1.0.1' condition='driver.video-i2c.condition_id'>
      <description>Driver video-i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/i2c/fsl_video_i2c.h' projectpath='video'/>
        <file category='sourceC' name='components/video/i2c/fsl_video_i2c.c' projectpath='video'/>
        <file category='include' name='components/video/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wdog' Cversion='2.2.0' condition='driver.wdog01.condition_id'>
      <description>wdog01 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_wdog.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_wdog.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524' Cversion='2.1.1' condition='driver.wm8524.condition_id'>
      <description>Driver wm8524</description>
      <RTE_Components_h>
#ifndef CODEC_WM8524_ENABLE
#define CODEC_WM8524_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8524/fsl_wm8524.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8524/fsl_wm8524.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8524/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904' Cversion='2.5.1' condition='driver.wm8904.condition_id'>
      <description>Driver wm8904</description>
      <RTE_Components_h>
#ifndef CODEC_WM8904_ENABLE
#define CODEC_WM8904_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8904/fsl_wm8904.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8904/fsl_wm8904.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8904/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960' Cversion='2.2.4' condition='driver.wm8960.condition_id'>
      <description>Driver wm8960</description>
      <RTE_Components_h>
#ifndef CODEC_WM8960_ENABLE
#define CODEC_WM8960_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8960/fsl_wm8960.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8960/fsl_wm8960.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8960/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962' Cversion='2.2.0' condition='driver.wm8962.condition_id'>
      <description>Driver wm8962</description>
      <RTE_Components_h>
#ifndef CODEC_WM8962_ENABLE
#define CODEC_WM8962_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8962/fsl_wm8962.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8962/fsl_wm8962.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8962/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xbara' Cversion='2.0.6' condition='driver.xbara.condition_id'>
      <description>XBARA Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_xbara.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_xbara.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xbarb' Cversion='2.0.2' condition='driver.xbarb.condition_id'>
      <description>XBARB Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_xbarb.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_xbarb.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xecc' Cversion='2.0.0' condition='driver.xecc.condition_id'>
      <description>XECC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_xecc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_xecc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='xrdc2' Cversion='2.0.3' condition='driver.xrdc2.condition_id'>
      <description>XRDC2 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/drivers/fsl_xrdc2.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT1166/drivers/fsl_xrdc2.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT1166/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MIMXRT1166/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MIMXRT1166/utilities/fsl_sbrk.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc, mdk, cores=cm4f, cm7f.condition_id' category='sourceAsm' name='devices/MIMXRT1166/utilities/fsl_memcpy.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MIMXRT1166/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MIMXRT1166/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/MIMXRT1166/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/MIMXRT1166/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/MIMXRT1166/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/MIMXRT1166/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='notifier' Cversion='1.0.0' condition='utility.notifier.condition_id'>
      <description>Utility notifier</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/fsl_notifier.c' projectpath='utilities/utilities'/>
        <file category='header' name='devices/MIMXRT1166/utilities/fsl_notifier.h' projectpath='utilities/utilities'/>
        <file category='include' name='devices/MIMXRT1166/utilities/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/MIMXRT1166/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/MIMXRT1166/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/MIMXRT1166/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/MIMXRT1166/utilities/str/'/>
      </files>
    </component>
  </components>
</package>