<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1040-EVK_EDGEFAST_BLUETOOTH_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware edgefast_bluetooth Examples Pack for MIMXRT1040-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1040-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MIMXRT1042_DFP" vendor="NXP" version="19.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="FATFS" vendor="NXP" version="3.0.0"/>
      <package name="EDGEFAST_BT_BLE" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="WIFI" vendor="NXP" version="3.0.0"/>
      <package name="LITTLEFS" vendor="NXP" version="3.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="a2dp_sink" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/a2dp_sink" doc="readme.md">
      <description>The Edgefast Bluetooth audio source with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/a2dp_sink.uvprojx"/>
        <environment name="csolution" load="a2dp_sink.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="a2dp_source" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/a2dp_source" doc="readme.md">
      <description>The Edgefast Bluetooth audio source with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/a2dp_source.uvprojx"/>
        <environment name="csolution" load="a2dp_source.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_hpc" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/central_hpc" doc="readme.md">
      <description>The Edgefast Bluetooth hpc example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_hpc.uvprojx"/>
        <environment name="csolution" load="central_hpc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_ht" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/central_ht" doc="readme.md">
      <description>The Edgefast Bluetooth hts example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_ht.uvprojx"/>
        <environment name="csolution" load="central_ht.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_ipsp" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/central_ipsp" doc="readme.md">
      <description>The Edgefast Bluetooth ipsp example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_ipsp.uvprojx"/>
        <environment name="csolution" load="central_ipsp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_pxm" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/central_pxm" doc="readme.md">
      <description>The Edgefast Bluetooth pxm example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_pxm.uvprojx"/>
        <environment name="csolution" load="central_pxm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="handsfree" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/handsfree" doc="readme.md">
      <description>The Edgefast Bluetooth bluetooth handsfree example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/handsfree.uvprojx"/>
        <environment name="csolution" load="handsfree.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="handsfree_ag" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/handsfree_ag" doc="readme.md">
      <description>The Edgefast Bluetooth handsfree AG example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/handsfree_ag.uvprojx"/>
        <environment name="csolution" load="handsfree_ag.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_beacon" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/peripheral_beacon" doc="readme.md">
      <description>The Edgefast Bluetooth beacon example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_beacon.uvprojx"/>
        <environment name="csolution" load="peripheral_beacon.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_hps" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/peripheral_hps" doc="readme.md">
      <description>The Edgefast Bluetooth hps example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_hps.uvprojx"/>
        <environment name="csolution" load="peripheral_hps.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_ht" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/peripheral_ht" doc="readme.md">
      <description>The Edgefast Bluetooth hts example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_ht.uvprojx"/>
        <environment name="csolution" load="peripheral_ht.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_ipsp" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/peripheral_ipsp" doc="readme.md">
      <description>The Edgefast Bluetooth ipsp example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_ipsp.uvprojx"/>
        <environment name="csolution" load="peripheral_ipsp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_pxr" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/peripheral_pxr" doc="readme.md">
      <description>The Edgefast Bluetooth pxr example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_pxr.uvprojx"/>
        <environment name="csolution" load="peripheral_pxr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spp" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/spp" doc="readme.md">
      <description>The Bluetooth BR SPP example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spp.uvprojx"/>
        <environment name="csolution" load="spp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wireless_uart" folder="boards/evkmimxrt1040/edgefast_bluetooth_examples/wireless_uart" doc="readme.md">
      <description>The Edgefast Bluetooth wireless uart example with simplified application.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wireless_uart.uvprojx"/>
        <environment name="csolution" load="wireless_uart.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
