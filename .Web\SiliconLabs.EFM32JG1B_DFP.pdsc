<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EFM32JG1B_DFP</name>
  <description>Silicon Labs EFM32JG1B Jade Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EFM32JG1B_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EFM32JG1B_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFM32JG1B</keyword>
    <keyword>EFM32</keyword>
    <keyword>Jade Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFM32JG1B Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M3" DcoreVersion="r2p1" Dfpu="0" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="40000000"/>
      <book name="Documents/cortex_m3_dgug.pdf"      title="Cortex-M3 Generic User Guide"/>
      <book name="Documents/efm32-cortex-m3-rm.pdf"  title="EFM32 Cortex-M3 Reference Manual"/>
      <book name="Documents/EFM32JG1-ReferenceManual.pdf"  title="EFM32JG1B Reference Manual"/>
      <description>
- ARM Cortex-M3 at 40 MHz&#xD;&#xA;- Ultra low energy operation:&#xD;&#xA;- 1.1 uA EM3 Stop current (CRYOTIMER running with state/RAM retention)&#xD;&#xA;- 1.4 uA EM2 DeepSleep current (RTCC running with state and RAM retention)&#xD;&#xA;- 60 uA/MHz in Energy Mode 0 (EM0)&#xD;&#xA;- Hardware cryptographic engine supports AES, ECC, and SHA&#xD;&#xA;- Integrated dc-dc converter&#xD;&#xA;- CRYOTIMER operates down to EM4&#xD;&#xA;- 5 V tolerant I/O&#xD;&#xA;&#xD;&#xA;EFM32JG1 features a powerful 32-bit ARM Cortex-M3 and a wide selection of peripherals, including a unique cryptographic hardware engine supporting AES, ECC, and SHA. These features, combined with ultra-low current active mode and short wake-up time from energy-saving modes, make EFM32JG1 microcontrollers well suited for any battery-powered application, as well as other systems requiring high performance and low-energy consumption.
      </description>

      <subFamily DsubFamily="EFM32JG1B100">
        <book         name="Documents/efm32jg1-datasheet.pdf"      title="EFM32JG1B100 Data Sheet"/>
        <book         name="Documents/efm32jg1-errata.pdf"         title="EFM32JG1B100 Errata"/>
        <!-- *************************  Device 'EFM32JG1B100F128GM32'  ***************************** -->
        <device Dname="EFM32JG1B100F128GM32">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B100F128GM32"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B100F128GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B100F256GM32'  ***************************** -->
        <device Dname="EFM32JG1B100F256GM32">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B100F256GM32"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B100F256GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B100F256IM32'  ***************************** -->
        <device Dname="EFM32JG1B100F256IM32">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B100F256IM32"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B100F256IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32JG1B200">
        <book         name="Documents/efm32jg1-datasheet.pdf"      title="EFM32JG1B200 Data Sheet"/>
        <book         name="Documents/efm32jg1-errata.pdf"         title="EFM32JG1B200 Errata"/>
        <!-- *************************  Device 'EFM32JG1B200F128GM32'  ***************************** -->
        <device Dname="EFM32JG1B200F128GM32">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B200F128GM32"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B200F128GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B200F128GM48'  ***************************** -->
        <device Dname="EFM32JG1B200F128GM48">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B200F128GM48"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B200F128GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B200F256GM32'  ***************************** -->
        <device Dname="EFM32JG1B200F256GM32">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B200F256GM32"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B200F256GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B200F256GM48'  ***************************** -->
        <device Dname="EFM32JG1B200F256GM48">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B200F256GM48"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B200F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B200F256IM32'  ***************************** -->
        <device Dname="EFM32JG1B200F256IM32">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B200F256IM32"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B200F256IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32JG1B200F256IM48'  ***************************** -->
        <device Dname="EFM32JG1B200F256IM48">
          <compile header="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"  define="EFM32JG1B200F256IM48"/>
          <debug      svd="SVD/EFM32JG1B/EFM32JG1B200F256IM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFM32JG1B">
      <description>Silicon Labs EFM32JG1B device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFM32JG1B*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EFM32JG1B">
      <description>System Startup for Silicon Labs EFM32JG1B device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFM32JG1B/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFM32JG1B/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFM32JG1B/Source/ARM/startup_efm32jg1b.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/EFM32JG1B/Source/GCC/startup_efm32jg1b.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFM32JG1B/Source/GCC/efm32jg1b.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFM32JG1B/Source/system_efm32jg1b.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
