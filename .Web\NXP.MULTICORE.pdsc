<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MULTICORE</name>
  <vendor>NXP</vendor>
  <description>Software Pack for multicore</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="11.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="10.0.0" date="1970-01-01">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="device_id.K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242.internal_condition">
      <accept Dname="K32L2B11VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MK22F12810.internal_condition">
      <accept Dname="MK22FN128VDC10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLL10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VMP10" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MK22F25612.internal_condition">
      <accept Dname="MK22FN128CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VMP12" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MK22F51212.internal_condition">
      <accept Dname="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VMP12" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MK02F12810.internal_condition">
      <accept Dname="MK02FN128VFM10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VLF10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VFM10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VLF10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VLH10" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.K32L2B11A.internal_condition">
      <accept Dname="K32L2B11VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VMP0A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.K32L2B21A.internal_condition">
      <accept Dname="K32L2B21VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VMP0A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.K32L2B31A.internal_condition">
      <accept Dname="K32L2B31VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VMP0A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.K32L3A60.internal_condition">
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S66.internal_condition">
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S69.internal_condition">
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC5534.internal_condition">
      <accept Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC5536.internal_condition">
      <accept Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S36.internal_condition">
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1165.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1166.internal_condition">
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1171.internal_condition">
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1172.internal_condition">
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1173.internal_condition">
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1175.internal_condition">
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1176.internal_condition">
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT685S.internal_condition">
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT633S.internal_condition">
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1061.internal_condition">
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1062.internal_condition">
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1181.internal_condition">
      <accept Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1182.internal_condition">
      <accept Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1187.internal_condition">
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1189.internal_condition">
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN946.internal_condition">
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN947.internal_condition">
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN546.internal_condition">
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN547.internal_condition">
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN235.internal_condition">
      <accept Dname="MCXN235VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VNL" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN236.internal_condition">
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC143.internal_condition">
      <accept Dname="MCXC143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC144.internal_condition">
      <accept Dname="MCXC144VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC243.internal_condition">
      <accept Dname="MCXC243VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC244.internal_condition">
      <accept Dname="MCXC244VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC443.internal_condition">
      <accept Dname="MCXC443VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VMP" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC444.internal_condition">
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC142.internal_condition">
      <accept Dname="MCXC142VFM" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC242.internal_condition">
      <accept Dname="MCXC242VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC141.internal_condition">
      <accept Dname="MCXC141VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.multicore.erpc.condition_id">
      <require condition="allOf.anyOf=device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition">
      <require condition="anyOf.device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="anyOf.device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition">
      <accept condition="device_id.K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242.internal_condition"/>
      <accept condition="board.frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="board.frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition">
      <accept condition="device.MK22F12810.internal_condition"/>
      <accept condition="device.MK22F25612.internal_condition"/>
      <accept condition="device.MK22F51212.internal_condition"/>
      <accept condition="device.MK02F12810.internal_condition"/>
      <accept condition="device.K32L2B11A.internal_condition"/>
      <accept condition="device.K32L2B21A.internal_condition"/>
      <accept condition="device.K32L2B31A.internal_condition"/>
      <accept condition="device.K32L3A60.internal_condition"/>
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
      <accept condition="device.LPC5534.internal_condition"/>
      <accept condition="device.LPC5536.internal_condition"/>
      <accept condition="device.LPC55S36.internal_condition"/>
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1181.internal_condition"/>
      <accept condition="device.MIMXRT1182.internal_condition"/>
      <accept condition="device.MIMXRT1187.internal_condition"/>
      <accept condition="device.MIMXRT1189.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
      <accept condition="device.MCXN546.internal_condition"/>
      <accept condition="device.MCXN547.internal_condition"/>
      <accept condition="device.MCXN235.internal_condition"/>
      <accept condition="device.MCXN236.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
      <accept condition="device.MCXC143.internal_condition"/>
      <accept condition="device.MCXC144.internal_condition"/>
      <accept condition="device.MCXC243.internal_condition"/>
      <accept condition="device.MCXC244.internal_condition"/>
      <accept condition="device.MCXC443.internal_condition"/>
      <accept condition="device.MCXC444.internal_condition"/>
      <accept condition="device.MCXC142.internal_condition"/>
      <accept condition="device.MCXC242.internal_condition"/>
      <accept condition="device.MCXC141.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.common.condition_id">
      <require condition="allOf.anyOf=device_id=K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC5534, LPC5536, LPC55S36, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN236, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.doc.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.erpc.common.internal_condition">
      <require Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC common"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_arbitrator.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_client.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_dspi_master_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_dspi_master_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_dspi_slave_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_dspi_slave_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_i2c_slave_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_i2c_slave_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_lpi2c_slave_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_lpi2c_slave_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_lpspi_slave_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_lpspi_slave_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_mu_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_mu_rtos_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_mu_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_port_freertos.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_port_stdlib.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_lite_master_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_lite_remote_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_lite_rtos_master_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_lite_rtos_remote_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_lite_rtos_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_lite_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_tty_rtos_remote_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_rpmsg_tty_rtos_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_server.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_spi_master_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_spi_master_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_spi_slave_c_wrapper.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_spi_slave_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.eRPC_uart_cmsis_transport.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.examples.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.java.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc.zephyr.condition_id">
      <require condition="allOf.middleware.multicore.erpc.common.internal_condition"/>
    </condition>
    <condition id="device_id.MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_multicore.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <require condition="anyOf.device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="anyOf.device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device_id.MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547.internal_condition"/>
      <accept condition="board.frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="board.frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device.MK22F12810.internal_condition"/>
      <accept condition="device.MK22F25612.internal_condition"/>
      <accept condition="device.MK22F51212.internal_condition"/>
      <accept condition="device.MK02F12810.internal_condition"/>
      <accept condition="device.K32L2B11A.internal_condition"/>
      <accept condition="device.K32L2B21A.internal_condition"/>
      <accept condition="device.K32L2B31A.internal_condition"/>
      <accept condition="device.K32L3A60.internal_condition"/>
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
      <accept condition="device.LPC5534.internal_condition"/>
      <accept condition="device.LPC5536.internal_condition"/>
      <accept condition="device.LPC55S36.internal_condition"/>
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1181.internal_condition"/>
      <accept condition="device.MIMXRT1182.internal_condition"/>
      <accept condition="device.MIMXRT1187.internal_condition"/>
      <accept condition="device.MIMXRT1189.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
      <accept condition="device.MCXN546.internal_condition"/>
      <accept condition="device.MCXN547.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_multicore_matrix_multiply_client.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_multicore_matrix_multiply_server.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="device_id.MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_multiprocessor.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition">
      <require condition="anyOf.device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="anyOf.device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition">
      <accept condition="device_id.MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242.internal_condition"/>
      <accept condition="board.frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_multiprocessor_matrix_multiply_client.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_multiprocessor_matrix_multiply_server.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN236, MCXN947, MCXN547, MCXC444, MCXC242, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn236, frdmmcxn947, frdmmcxc444, frdmmcxc242.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_two_way_rpc_core0.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.erpc_common_two_way_rpc_core1.condition_id">
      <require condition="allOf.anyOf=device_id=MIMXRT1165xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1189xxxxx, MIMXRT1187xxxxx, MCXN947, MCXN547, board=frdmk22f, frdmk32l2b, frdmk32l3a6, lpcxpresso55s69, lpcxpresso55s36, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt685, evkmimxrt1060, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="device_id.K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547.internal_condition">
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.condition_id">
      <require condition="allOf.anyOf=device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, board=frdmk32l3a6, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, board=frdmk32l3a6, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <require condition="anyOf.device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, board=frdmk32l3a6, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="anyOf.device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, board=frdmk32l3a6, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device_id.K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547.internal_condition"/>
      <accept condition="board.frdmk32l3a6, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="board.frdmk32l3a6, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, evkmimxrt1180, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device.K32L3A60.internal_condition"/>
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1181.internal_condition"/>
      <accept condition="device.MIMXRT1182.internal_condition"/>
      <accept condition="device.MIMXRT1187.internal_condition"/>
      <accept condition="device.MIMXRT1189.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
      <accept condition="device.MCXN546.internal_condition"/>
      <accept condition="device.MCXN547.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.imxrt1160.condition_id">
      <require condition="allOf.middleware.multicore.mcmgr, driver.mu, board=evkmimxrt1160.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.mcmgr, driver.mu, board=evkmimxrt1160.internal_condition">
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mu"/>
      <require condition="board.evkmimxrt1160.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt1160.internal_condition">
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.imxrt1170.condition_id">
      <require condition="allOf.middleware.multicore.mcmgr, driver.mu, board=evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.mcmgr, driver.mu, board=evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mu"/>
      <require condition="board.evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.imxrt1180.condition_id">
      <require condition="allOf.middleware.multicore.mcmgr, driver.mu1, board=evkmimxrt1180.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.mcmgr, driver.mu1, board=evkmimxrt1180.internal_condition">
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mu"/>
      <require condition="board.evkmimxrt1180.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt1180.internal_condition">
      <accept condition="device.MIMXRT1181.internal_condition"/>
      <accept condition="device.MIMXRT1182.internal_condition"/>
      <accept condition="device.MIMXRT1187.internal_condition"/>
      <accept condition="device.MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.k32l3a6.condition_id">
      <require condition="allOf.middleware.multicore.mcmgr, driver.mu, board=frdmk32l3a6.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.mcmgr, driver.mu, board=frdmk32l3a6.internal_condition">
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mu"/>
      <require condition="board.frdmk32l3a6.internal_condition"/>
    </condition>
    <condition id="board.frdmk32l3a6.internal_condition">
      <accept condition="device.K32L3A60.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.lpc55s69.condition_id">
      <require condition="allOf.middleware.multicore.mcmgr, driver.mailbox.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.mcmgr, driver.mailbox.internal_condition">
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mailbox"/>
    </condition>
    <condition id="middleware.multicore.mcmgr.mcxnx4x.condition_id">
      <require condition="allOf.middleware.multicore.mcmgr, driver.mailbox, board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.mcmgr, driver.mailbox, board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mailbox"/>
      <require condition="board.mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="board.mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
      <accept condition="device.MCXN546.internal_condition"/>
      <accept condition="device.MCXN547.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
    </condition>
    <condition id="device_id.K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition">
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT533S.internal_condition">
      <accept Dname="MIMXRT533SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFFOC" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT555S.internal_condition">
      <accept Dname="MIMXRT555SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFFOC" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT595S.internal_condition">
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.condition_id">
      <require condition="allOf.anyOf=device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, board=frdmk32l3a6, evkmimxrt685, mimxrt685audevk, evkmimxrt595, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkmimxrt1180, evkbmimxrt1170, mcxn9xxevk, mcxn5xxevk, frdmmcxn947, anyOf=middleware.multicore.rpmsg_lite.bm, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.rpmsg_lite.xos, middleware.multicore.rpmsg_lite.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, board=frdmk32l3a6, evkmimxrt685, mimxrt685audevk, evkmimxrt595, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkmimxrt1180, evkbmimxrt1170, mcxn9xxevk, mcxn5xxevk, frdmmcxn947, anyOf=middleware.multicore.rpmsg_lite.bm, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.rpmsg_lite.xos, middleware.multicore.rpmsg_lite.azurertos.internal_condition">
      <require condition="anyOf.device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, board=frdmk32l3a6, evkmimxrt685, mimxrt685audevk, evkmimxrt595, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkmimxrt1180, evkbmimxrt1170, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
      <require condition="anyOf.middleware.multicore.rpmsg_lite.bm, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.rpmsg_lite.xos, middleware.multicore.rpmsg_lite.azurertos.internal_condition"/>
    </condition>
    <condition id="anyOf.device_id=K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S, board=frdmk32l3a6, evkmimxrt685, mimxrt685audevk, evkmimxrt595, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkmimxrt1180, evkbmimxrt1170, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device_id.K32L3A60xxx, LPC55S69, LPC55S66, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MCXN946, MCXN947, MCXN546, MCXN547, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
      <accept condition="board.frdmk32l3a6, evkmimxrt685, mimxrt685audevk, evkmimxrt595, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkmimxrt1180, evkbmimxrt1170, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="board.frdmk32l3a6, evkmimxrt685, mimxrt685audevk, evkmimxrt595, lpcxpresso55s69, evkmimxrt1160, evkmimxrt1170, evkmimxrt1180, evkbmimxrt1170, mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <accept condition="device.K32L3A60.internal_condition"/>
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
      <accept condition="device.MIMXRT533S.internal_condition"/>
      <accept condition="device.MIMXRT555S.internal_condition"/>
      <accept condition="device.MIMXRT595S.internal_condition"/>
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1181.internal_condition"/>
      <accept condition="device.MIMXRT1182.internal_condition"/>
      <accept condition="device.MIMXRT1187.internal_condition"/>
      <accept condition="device.MIMXRT1189.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
      <accept condition="device.MCXN546.internal_condition"/>
      <accept condition="device.MCXN547.internal_condition"/>
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.multicore.rpmsg_lite.bm, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.rpmsg_lite.xos, middleware.multicore.rpmsg_lite.azurertos.internal_condition">
      <accept Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <accept Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <accept Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite xos env layer"/>
      <accept Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite Azure RTOS env layer"/>
    </condition>
    <condition id="allOf.components=middleware.baremetal.condition_id">
      <require condition="components.middleware.baremetal.internal_condition"/>
    </condition>
    <condition id="components.middleware.baremetal.internal_condition">
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="baremetal"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.bm.config.imxrt1160.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.imxrt1160, board=evkmimxrt1160, middleware.multicore.rpmsg_lite.imxrt1160_bm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.imxrt1160, board=evkmimxrt1160, middleware.multicore.rpmsg_lite.imxrt1160_bm.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1160 MCMGR"/>
      <require condition="board.evkmimxrt1160.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1160 rpmsg_lite bm app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.bm.config.imxrt1170.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.imxrt1170, board=evkmimxrt1170, evkbmimxrt1170, middleware.multicore.rpmsg_lite.imxrt1170_bm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.imxrt1170, board=evkmimxrt1170, evkbmimxrt1170, middleware.multicore.rpmsg_lite.imxrt1170_bm.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1170 MCMGR"/>
      <require condition="board.evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1170 rpmsg_lite bm app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.bm.config.imxrt1180.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.imxrt1180, board=evkmimxrt1180, middleware.multicore.rpmsg_lite.imxrt1180_bm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.imxrt1180, board=evkmimxrt1180, middleware.multicore.rpmsg_lite.imxrt1180_bm.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1180 MCMGR"/>
      <require condition="board.evkmimxrt1180.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1180 rpmsg_lite bm app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.bm.config.k32l3a6.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.k32l3a6, board=frdmk32l3a6, middleware.multicore.rpmsg_lite.k32l3a6_bm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.k32l3a6, board=frdmk32l3a6, middleware.multicore.rpmsg_lite.k32l3a6_bm.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="k32l3a6 MCMGR"/>
      <require condition="board.frdmk32l3a6.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="k32l3a6 rpmsg_lite bm app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.bm.config.lpcxpresso55s69.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.lpc55s69, board=lpcxpresso55s69, middleware.multicore.rpmsg_lite.lpcxpresso55s69_bm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.lpc55s69, board=lpcxpresso55s69, middleware.multicore.rpmsg_lite.lpcxpresso55s69_bm.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="lpc55s69 MCMGR"/>
      <require condition="board.lpcxpresso55s69.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="lpc55s69 rpmsg_lite bm app"/>
    </condition>
    <condition id="board.lpcxpresso55s69.internal_condition">
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.bm.config.mcxnx4x.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.mcxnx4x, board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947, middleware.multicore.rpmsg_lite.mcxnx4x_bm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.bm, middleware.multicore.mcmgr.mcxnx4x, board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947, middleware.multicore.rpmsg_lite.mcxnx4x_bm.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcxnx4x MCMGR"/>
      <require condition="board.mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="mcxnx10 rpmsg_lite bm app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.evkmimxrt595_bm.condition_id">
      <require condition="allOf.board=evkmimxrt595.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt595.internal_condition">
      <require condition="board.evkmimxrt595.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt595.internal_condition">
      <accept condition="device.MIMXRT533S.internal_condition"/>
      <accept condition="device.MIMXRT555S.internal_condition"/>
      <accept condition="device.MIMXRT595S.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.evkmimxrt595_freertos.condition_id">
      <require condition="allOf.board=evkmimxrt595, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt595, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.evkmimxrt595.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.evkmimxrt595_fusionf1_bm.condition_id">
      <require condition="allOf.board=evkmimxrt595.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.evkmimxrt685_bm.condition_id">
      <require condition="allOf.board=evkmimxrt685.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt685.internal_condition">
      <require condition="board.evkmimxrt685.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt685.internal_condition">
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.evkmimxrt685_freertos.condition_id">
      <require condition="allOf.board=evkmimxrt685, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt685, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.evkmimxrt685.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.evkmimxrt685_hifi4_bm.condition_id">
      <require condition="allOf.board=evkmimxrt685.internal_condition"/>
    </condition>
    <condition id="allOf.components=middleware.freertos-kernel.condition_id">
      <require condition="components.middleware.freertos-kernel.internal_condition"/>
    </condition>
    <condition id="components.middleware.freertos-kernel.internal_condition">
      <accept Cclass="RTOS" Cgroup="Core"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.freertos.config.imxrt1160.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.imxrt1160, board=evkmimxrt1160, middleware.multicore.rpmsg_lite.imxrt1160_freertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.imxrt1160, board=evkmimxrt1160, middleware.multicore.rpmsg_lite.imxrt1160_freertos.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1160 MCMGR"/>
      <require condition="board.evkmimxrt1160.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1160 rpmsg_lite RTOS app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.freertos.config.imxrt1170.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.imxrt1170, board=evkmimxrt1170, evkbmimxrt1170, middleware.multicore.rpmsg_lite.imxrt1170_freertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.imxrt1170, board=evkmimxrt1170, evkbmimxrt1170, middleware.multicore.rpmsg_lite.imxrt1170_freertos.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1170 MCMGR"/>
      <require condition="board.evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1170 rpmsg_lite RTOS app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.freertos.config.imxrt1180.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.imxrt1180, board=evkmimxrt1180, middleware.multicore.rpmsg_lite.imxrt1180_freertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.imxrt1180, board=evkmimxrt1180, middleware.multicore.rpmsg_lite.imxrt1180_freertos.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1180 MCMGR"/>
      <require condition="board.evkmimxrt1180.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1180 rpmsg_lite RTOS app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.freertos.config.k32l3a6.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.k32l3a6, board=frdmk32l3a6, middleware.multicore.rpmsg_lite.k32l3a6_freertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.k32l3a6, board=frdmk32l3a6, middleware.multicore.rpmsg_lite.k32l3a6_freertos.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="k32l3a6 MCMGR"/>
      <require condition="board.frdmk32l3a6.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="k32l3a6 rpmsg_lite RTOS app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.freertos.config.lpcxpresso55s69.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.lpc55s69, board=lpcxpresso55s69, middleware.multicore.rpmsg_lite.lpcxpresso55s69_freertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.lpc55s69, board=lpcxpresso55s69, middleware.multicore.rpmsg_lite.lpcxpresso55s69_freertos.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="lpc55s69 MCMGR"/>
      <require condition="board.lpcxpresso55s69.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="lpc55s69 rpmsg_lite RTOS app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.freertos.config.mcxnx4x.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.mcxnx4x, board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947, middleware.multicore.rpmsg_lite.mcxnx4x_freertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite, middleware.multicore.rpmsg_lite.freertos, middleware.multicore.mcmgr.mcxnx4x, board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947, middleware.multicore.rpmsg_lite.mcxnx4x_freertos.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer"/>
      <require Cclass="Multicore" Cgroup="Core Management" Csub="mcxnx4x MCMGR"/>
      <require condition="board.mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="mcxnx10 rpmsg_lite RTOS app"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.imxrt1160_bm.condition_id">
      <require condition="allOf.board=evkmimxrt1160.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1160.internal_condition">
      <require condition="board.evkmimxrt1160.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.imxrt1160_freertos.condition_id">
      <require condition="allOf.board=evkmimxrt1160, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1160, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.evkmimxrt1160.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.imxrt1170_bm.condition_id">
      <require condition="allOf.board=evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <require condition="board.evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.imxrt1170_freertos.condition_id">
      <require condition="allOf.board=evkmimxrt1170, evkbmimxrt1170, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1170, evkbmimxrt1170, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.imxrt1180_bm.condition_id">
      <require condition="allOf.board=evkmimxrt1180.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1180.internal_condition">
      <require condition="board.evkmimxrt1180.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.imxrt1180_freertos.condition_id">
      <require condition="allOf.board=evkmimxrt1180, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1180, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.evkmimxrt1180.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.k32l3a6_bm.condition_id">
      <require condition="allOf.board=frdmk32l3a6.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmk32l3a6.internal_condition">
      <require condition="board.frdmk32l3a6.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.k32l3a6_freertos.condition_id">
      <require condition="allOf.board=frdmk32l3a6, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmk32l3a6, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.frdmk32l3a6.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.lpcxpresso55s69_bm.condition_id">
      <require condition="allOf.board=lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpcxpresso55s69.internal_condition">
      <require condition="board.lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.lpcxpresso55s69_freertos.condition_id">
      <require condition="allOf.board=lpcxpresso55s69, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpcxpresso55s69, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.lpcxpresso55s69.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.mcxnx4x_bm.condition_id">
      <require condition="allOf.board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="allOf.board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition">
      <require condition="board.mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.mcxnx4x_freertos.condition_id">
      <require condition="allOf.board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=mcxn9xxevk, mcxn5xxevk, frdmmcxn947, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.mcxn9xxevk, mcxn5xxevk, frdmmcxn947.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.mimxrt685audevk_bm.condition_id">
      <require condition="allOf.board=mimxrt685audevk.internal_condition"/>
    </condition>
    <condition id="allOf.board=mimxrt685audevk.internal_condition">
      <require condition="board.mimxrt685audevk.internal_condition"/>
    </condition>
    <condition id="board.mimxrt685audevk.internal_condition">
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.mimxrt685audevk_freertos.condition_id">
      <require condition="allOf.board=mimxrt685audevk, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition"/>
    </condition>
    <condition id="allOf.board=mimxrt685audevk, middleware.freertos-kernel, middleware.freertos-kernel.heap_4.internal_condition">
      <require condition="board.mimxrt685audevk.internal_condition"/>
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.mimxrt685audevk_hifi4_bm.condition_id">
      <require condition="allOf.board=mimxrt685audevk.internal_condition"/>
    </condition>
    <condition id="middleware.multicore.rpmsg_lite.zephyr.condition_id">
      <require condition="allOf.middleware.multicore.rpmsg_lite.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.multicore.rpmsg_lite.internal_condition">
      <require Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Multicore" Cgroup="MulticoreSDK" Cversion="2.16.0">
      <description>Multicore SDK</description>
      <files>
        <file category="doc" name="middleware/multicore/readme.txt" projectpath="multicore"/>
        <file category="doc" name="middleware/multicore/ChangeLogKSDK.txt" projectpath="multicore"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC" Cversion="1.13.0" condition="middleware.multicore.erpc.condition_id">
      <description>eRPC</description>
      <files>
        <file category="doc" name="middleware/multicore/erpc/README.md" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/SW-Content-Register.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/annotated.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/class_dynamic_message_buffer_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/class_dynamic_message_buffer_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/class_r_p_msg_message_buffer_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/class_r_p_msg_message_buffer_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/class_static_message_buffer_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/class_static_message_buffer_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_arbitrated_client_manager-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_arbitrated_client_manager.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_basic_codec-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_basic_codec.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_basic_codec_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_basic_codec_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_client_manager-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_client_manager.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_client_server_common-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_client_server_common.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_codec-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_codec.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_codec_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_codec_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_crc16-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_crc16.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_cursor-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_cursor.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_dspi_master_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_dspi_master_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_dspi_slave_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_dspi_slave_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_framed_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_framed_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_i2c_slave_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_i2c_slave_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_inter_thread_buffer_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_inter_thread_buffer_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_l_p_i2c_slave_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_l_p_i2c_slave_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_l_p_spi_slave_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_l_p_spi_slave_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_m_b_o_x_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_m_b_o_x_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_m_u_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_m_u_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_manually_constructed-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_manually_constructed.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_buffer-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_buffer.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_buffer_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_buffer_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_logger-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_logger.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_loggers-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_message_loggers.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_mutex-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_mutex.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_mutex_1_1_guard-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_mutex_1_1_guard.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_pre_post_action-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_pre_post_action.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_base-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_base.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_linux_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_linux_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_r_t_o_s_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_r_t_o_s_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_t_t_y_r_t_o_s_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_t_t_y_r_t_o_s_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_r_p_msg_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_request_context-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_request_context.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_semaphore-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_semaphore.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_serial_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_serial_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_server-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_server.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_service-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_service.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_simple_server-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_simple_server.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_spi_master_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_spi_master_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_spi_slave_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_spi_slave_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_spidev_master_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_spidev_master_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_static_queue-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_static_queue.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_t_c_p_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_t_c_p_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_thread-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_thread.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_transport_arbitrator-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_transport_arbitrator.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_transport_factory-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_transport_factory.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_uart_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_uart_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_usb_cdc_transport-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classerpc_1_1_usb_cdc_transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/classes.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/customdoxygen.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000001_000004.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000002_000001.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000002_000004.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000002_000005.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000004_000001.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000004_000003.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000005_000001.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_000005_000004.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_318b660cd76dff0271d6acfa47597bac.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_4c1986dc9092ab2d1c997193a3634562.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_52949f44d4caf8ec53681e2fdb42569d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_62e9b24ece508051f26d84717c79c815.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_65f3d2eaa0376abba9fa4e8a5e7d2f88.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/dir_ecce72d14eab190b8175cc3cd0696eeb.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/doxygen.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__arbitrated__client__manager_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__arbitrated__client__manager_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__arbitrated__client__setup_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__arbitrated__client__setup_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__basic__codec_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__basic__codec_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__client__manager_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__client__manager_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__client__server__common_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__client__server__common_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__client__setup_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__client__setup_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__codec_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__codec_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__common_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__config_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__config_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__config__internal_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__crc16_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__crc16_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__dspi__master__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__dspi__master__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__dspi__slave__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__dspi__slave__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__endianness__agnostic__example_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__endianness__undefined_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__framed__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__framed__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__i2c__slave__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__i2c__slave__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__inter__thread__buffer__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__inter__thread__buffer__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__lpi2c__slave__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__lpi2c__slave__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__lpspi__slave__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__lpspi__slave__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__manually__constructed_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__manually__constructed_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__mbf__setup_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__mbf__setup_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__mbox__zephyr__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__mbox__zephyr__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__message__buffer_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__message__buffer_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__message__loggers_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__message__loggers_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__mu__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__mu__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__port_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__port_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__pre__post__action_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__pre__post__action_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__linux__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__lite__base_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__lite__base_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__lite__rtos__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__lite__rtos__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__lite__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__lite__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__tty__rtos__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__rpmsg__tty__rtos__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__serial_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__serial_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__serial__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__serial__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__server_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__server_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__server__setup_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__server__setup_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__setup__extensions_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__setup__extensions_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__simple__server_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__simple__server_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spi__master__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spi__master__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spi__slave__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spi__slave__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spidev_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spidev__master__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__spidev__master__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__static__queue_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__static__queue_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__sysgpio_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__tcp__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__tcp__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__threading_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__threading_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__transport__arbitrator_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__transport__arbitrator_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__transport__setup_8h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__transport__setup_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__uart__cmsis__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__uart__cmsis__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__uart__zephyr__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__uart__zephyr__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__usb__cdc__transport_8hpp.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__usb__cdc__transport_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__utils_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/erpc__version_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/files.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_0x7e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_0x7e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_g.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_i.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_l.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_m.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_o.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_p.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_r.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_s.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_t.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_u.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_v.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_func_w.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_g.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_i.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_k.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_l.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_m.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_o.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_p.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_r.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_s.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_t.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_u.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_v.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_vars.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/functions_w.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/globals.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/globals_defs.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/globals_func.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/globals_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/graph_legend.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group___u_s_b___c_d_c__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__client__setup.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__config.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__dspi__master__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__dspi__slave__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__i2c__slave__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__infra.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__infra__client.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__infra__codec.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__infra__server.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__infra__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__infra__utility.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__itbp__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__lpi2c__slave__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__lpspi__slave__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__mbox__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__message__buffer__factory__setup.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__mu__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port__mem.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port__serial.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port__setup__extensions.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port__spidev.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port__sysgpio.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__port__threads.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__rpmsg__linux__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__rpmsg__lite__rtos__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__rpmsg__lite__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__rpmsg__tty__rtos__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__serial__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__server__setup.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__setup.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__spi__master__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__spi__slave__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__spidev__master__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__tcp__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__transport__setup.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__transports.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/group__uart__transport.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/hierarchy.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/index.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/inherits.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/modules.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/navtree.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/struct_i2_c___c_l_b___u_s_e_r___d_a_t_a-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/struct_i2_c___c_l_b___u_s_e_r___d_a_t_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/struct_l_p_i2_c___c_l_b___u_s_e_r___d_a_t_a-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/struct_l_p_i2_c___c_l_b___u_s_e_r___d_a_t_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/structerpc_1_1_transport_arbitrator_1_1_pending_client_info-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/structerpc_1_1_transport_arbitrator_1_1_pending_client_info.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/tabs.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_10.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_11.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_12.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_13.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_14.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_15.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_16.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/all_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/classes_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/enums_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/enums_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/enums_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/enumvalues_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/files_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_10.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_11.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_12.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_13.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/functions_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/groups_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/nomatches.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/pages_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/search.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/typedefs_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/typedefs_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/typedefs_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/typedefs_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/variables_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/variables_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/eRPC/search/variables_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_alias_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_annotation_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_array_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_ast_node_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_ast_walker_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_builtin_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_c_generator_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_const_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_data_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_enum_member_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_enum_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_erpc_lexer_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_function_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_function_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_generator_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_group_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_hex_values_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_interface_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_interface_definition_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_java_generator_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_list_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_logging_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_parse_errors_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_program_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_python_generator_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_search_path_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_struct_member_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_struct_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_symbol_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_symbol_scanner_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_symbol_scope_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_token_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_union_case_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_union_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_unique_id_checker_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_utils_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_value_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/_void_type_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/annotated.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/annotations_8h_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_float_value-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_float_value.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_integer_value-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_integer_value.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_log-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_log.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_log_1_1_set_output_level-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_log_1_1_set_output_level.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_logger-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_logger.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_argv_iter-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_argv_iter.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_istream_iter-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_istream_iter.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_iter-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_iter.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_iter_rwd-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_iter_rwd.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_str_tok_iter-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_opt_str_tok_iter.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_option_spec-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_option_spec.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_options-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_options.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_path_searcher-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_path_searcher.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_stdout_logger-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_stdout_logger.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_string_value-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_string_value.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_value-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/class_value.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_alias_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_alias_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_annotation-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_annotation.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_array_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_array_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_ast_node-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_ast_node.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_ast_printer-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_ast_printer.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_ast_walker-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_ast_walker.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_builtin_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_builtin_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_c_generator-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_c_generator.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_const_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_const_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_current_file_info-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_current_file_info.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_data_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_data_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_enum_member-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_enum_member.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_enum_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_enum_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_erpc_lexer-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_erpc_lexer.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_function-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_function.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_function_base-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_function_base.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_function_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_function_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_generator-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_generator.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_group-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_group.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_interface-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_interface.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_interface_definition-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_interface_definition.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_java_generator-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_java_generator.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_list_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_list_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_program-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_program.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_python_generator-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_python_generator.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_struct_member-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_struct_member.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_struct_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_struct_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol_scanner-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol_scanner.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol_scope-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol_scope.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol_scope_1_1typed__iterator-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_symbol_scope_1_1typed__iterator.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_token-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_token.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_union_case-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_union_case.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_union_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_union_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_unique_id_checker-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_unique_id_checker.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_void_type-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1_void_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1erpc__error-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1erpc__error.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1erpcgen_tool-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1erpcgen_tool.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1internal__error-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1internal__error.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1lexical__error-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1lexical__error.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1semantic__error-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1semantic__error.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1syntax__error-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1syntax__error.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1syntax__error2-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classerpcgen_1_1syntax__error2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classes.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classsmart__ptr-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/classsmart__ptr.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/customdoxygen.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/dir_61c3f993326311b905af09f643fec799.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/dir_acfaa839b8ffb0f73c7c3063e82240f2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/dir_b0461de83f30261a836ec46318e22617.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/doxygen.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/files.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/format__string_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_0x7e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_enum.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_0x7e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_g.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_i.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_j.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_l.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_m.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_n.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_o.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_p.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_r.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_s.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_t.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_u.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_v.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_w.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_func_y.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_g.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_h.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_i.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_j.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_l.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_m.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_n.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_o.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_p.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_r.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_s.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_t.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_type.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_u.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_v.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_vars.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_w.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/functions_y.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/hierarchy.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/index.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/navtree.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/options_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/os__config_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/smart__ptr_8hpp_source.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structerpcgen_1_1token__loc__t-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structerpcgen_1_1token__loc__t.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structsmart__ptr__delete-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structsmart__ptr__delete.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structsmart__ptr__delete__array-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structsmart__ptr__delete__array.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structsmart__ptr__free-members.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/structsmart__ptr__free.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/tabs.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_10.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_11.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_12.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_13.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_14.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_15.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_16.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_17.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_18.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_19.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/all_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/classes_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enums_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/enumvalues_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_10.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_11.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_12.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_13.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_14.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_15.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_16.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_17.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_7.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_8.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_9.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_a.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_b.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_c.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_d.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_e.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/functions_f.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/nomatches.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/pages_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/search.css" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_4.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_5.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/typedefs_6.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/variables_0.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/variables_1.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/variables_2.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/erpcgen/search/variables_3.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC_infrastructure/index.html" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/erpc_c/readme.txt" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/erpc_python/readme.md" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/erpc_python/README_Pypi.md" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/erpcgen/readme.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/erpcgen/src/cpptemplate/LICENSE.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/erpcgen/VisualStudio_v14/readme_erpcgen.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/erpcsniffer/readme.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/utilities/README.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/utilities/styles/README.txt" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/utilities/styles/VSC/CHANGELOG.md" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/utilities/styles/VSC/README.md" projectpath="erpc/erpc_aux"/>
        <file category="doc" name="middleware/multicore/erpc/utilities/styles/VSC/vsc-extension-quickstart.md" projectpath="erpc/erpc_aux"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC common" Cversion="1.13.0" condition="middleware.multicore.erpc.common.condition_id">
      <description>eRPC</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_version.h" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_codec.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_basic_codec.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_transport.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_message_buffer.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_message_loggers.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_common.h" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/setup/erpc_transport_setup.h" projectpath="erpc/setup"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/setup/erpc_mbf_setup.h" projectpath="erpc/setup"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_manually_constructed.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_crc16.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_basic_codec.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_message_buffer.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_message_loggers.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_crc16.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_client_server_common.hpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_pre_post_action.h" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_pre_post_action.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_utils.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_utils.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_setup_extensions.h" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_endianness_agnostic_example.h" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_endianness_undefined.h" projectpath="erpc/port"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/setup/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/port/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC documentation" Cversion="1.13.0" condition="middleware.multicore.erpc.doc.condition_id">
      <description>erpc_doc</description>
      <files>
        <file category="doc" name="middleware/multicore/erpc/docs/erpcgen.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC-infrastructure.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/erpcsniffer.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/eRPC-footprint.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/Home.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/IDL-Reference.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/Porting-Guide.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/Contributing.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/Getting-Started.md" projectpath="erpc/doc"/>
        <file category="doc" name="middleware/multicore/erpc/docs/_Sidebar.md" projectpath="erpc/doc"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC arbitrator" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_arbitrator.condition_id">
      <description>eRPC_arbitrator</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_arbitrated_client_manager.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_arbitrated_client_manager.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_client_manager.h" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_client_manager.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/setup/erpc_arbitrated_client_setup.h" projectpath="erpc/setup"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_arbitrated_client_setup.cpp" projectpath="erpc/setup"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_simple_server.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_simple_server.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_server.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_server.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/setup/erpc_server_setup.h" projectpath="erpc/setup"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_server_setup.cpp" projectpath="erpc/setup"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_transport_arbitrator.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_transport_arbitrator.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_threading.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_threading_freertos.cpp" projectpath="erpc/port"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/setup/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/port/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC client" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_client.condition_id">
      <description>eRPC_client</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_client_manager.h" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_client_manager.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/setup/erpc_client_setup.h" projectpath="erpc/setup"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_client_setup.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/setup/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_dspi_master_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_dspi_master_c_wrapper.condition_id">
      <description>eRPC_dspi_master_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_dspi_master.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_dspi_master_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_dspi_master_transport.condition_id">
      <description>eRPC_dspi_master_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_dspi_master_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_dspi_master_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_dspi_slave_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_dspi_slave_c_wrapper.condition_id">
      <description>eRPC_dspi_slave_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_dspi_slave.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_dspi_slave_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_dspi_slave_transport.condition_id">
      <description>eRPC_dspi_slave_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_dspi_slave_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_dspi_slave_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_i2c_slave_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_i2c_slave_c_wrapper.condition_id">
      <description>eRPC_i2c_slave_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_i2c_slave.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_i2c_slave_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_i2c_slave_transport.condition_id">
      <description>eRPC_i2c_slave_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_i2c_slave_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_i2c_slave_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_lpi2c_slave_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_lpi2c_slave_c_wrapper.condition_id">
      <description>eRPC_lpi2c_slave_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_lpi2c_slave.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_lpi2c_slave_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_lpi2c_slave_transport.condition_id">
      <description>eRPC_lpi2c_slave_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_lpi2c_slave_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_lpi2c_slave_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_lpspi_slave_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_lpspi_slave_c_wrapper.condition_id">
      <description>eRPC_lpspi_slave_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_lpspi_slave.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_lpspi_slave_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_lpspi_slave_transport.condition_id">
      <description>eRPC_lpspi_slave_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_lpspi_slave_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_lpspi_slave_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_mu_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_mu_c_wrapper.condition_id">
      <description>eRPC_mu_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mu.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_mu_rtos_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_mu_rtos_transport.condition_id">
      <description>eRPC_mu_rtos_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_mu_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_mu_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_threading.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_threading_freertos.cpp" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/port/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_mu_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_mu_transport.condition_id">
      <description>eRPC_mu_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_mu_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_mu_transport.cpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_port_freertos" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_port_freertos.condition_id">
      <description>eRPC_port_freertos</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_port.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_port_freertos.cpp" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_config_internal.h" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_threading.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_threading_freertos.cpp" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_setup_extensions.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_setup_extensions_freertos.cpp" projectpath="erpc/port"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/port/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_port_stdlib" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_port_stdlib.condition_id">
      <description>eRPC_port_stdlib</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_port.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_port_stdlib.cpp" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_config_internal.h" projectpath="erpc/port"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/port/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_rpmsg_lite_master_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_lite_master_c_wrapper.condition_id">
      <description>eRPC_rpmsg_lite_master_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_rpmsg_lite_master.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_rpmsg_lite_remote_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_lite_remote_c_wrapper.condition_id">
      <description>eRPC_rpmsg_lite_remote_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_rpmsg_lite_remote.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPCrpmsglite_rtos_master_c_wrap" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_lite_rtos_master_c_wrapper.condition_id">
      <description>eRPC_rpmsg_lite_rtos_master_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_rpmsg_lite_rtos_master.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPCrpmsglite_rtos_remote_c_wrap" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_lite_rtos_remote_c_wrapper.condition_id">
      <description>eRPC_rpmsg_lite_rtos_remote_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_rpmsg_lite_rtos_remote.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_rpmsg_lite_rtos_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_lite_rtos_transport.condition_id">
      <description>eRPC_rpmsg_lite_rtos_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_base.hpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_rtos_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_rtos_transport.cpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_rpmsg.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_rpmsg_lite_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_lite_transport.condition_id">
      <description>eRPC_rpmsg_lite_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_base.hpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_static_queue.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_rpmsg.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPCrpmsg_tty_rtos_remote_c_wrap" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_tty_rtos_remote_c_wrapper.condition_id">
      <description>eRPC_rpmsg_tty_rtos_remote_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_rpmsg_tty_rtos_remote.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_rpmsg_tty_rtos_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_rpmsg_tty_rtos_transport.condition_id">
      <description>eRPC_rpmsg_tty_rtos_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_lite_base.hpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_tty_rtos_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_rpmsg_tty_rtos_transport.cpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_rpmsg.cpp" projectpath="erpc/setup"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/port/erpc_threading.h" projectpath="erpc/port"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/port/erpc_threading_freertos.cpp" projectpath="erpc/port"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/port/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC server" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_server.condition_id">
      <description>eRPC_server</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_simple_server.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_simple_server.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_server.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_server.cpp" projectpath="erpc/infra"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/setup/erpc_server_setup.h" projectpath="erpc/setup"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_server_setup.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/setup/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_spi_master_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_spi_master_c_wrapper.condition_id">
      <description>eRPC_spi_master_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_spi_master.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_spi_master_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_spi_master_transport.condition_id">
      <description>eRPC_spi_master_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_spi_master_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_spi_master_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_spi_slave_c_wrapper" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_spi_slave_c_wrapper.condition_id">
      <description>eRPC_spi_slave_c_wrapper</description>
      <files>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_spi_slave.cpp" projectpath="erpc/setup"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_spi_slave_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_spi_slave_transport.condition_id">
      <description>eRPC_spi_slave_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_spi_slave_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_spi_slave_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC_uart_cmsis_transport" Cversion="1.13.0" condition="middleware.multicore.erpc.eRPC_uart_cmsis_transport.condition_id">
      <description>eRPC_uart_cmsis_transport</description>
      <files>
        <file category="header" name="middleware/multicore/erpc/erpc_c/transports/erpc_uart_cmsis_transport.hpp" projectpath="erpc/transports"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/transports/erpc_uart_cmsis_transport.cpp" projectpath="erpc/transports"/>
        <file category="header" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.hpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/infra/erpc_framed_transport.cpp" projectpath="erpc/infra"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_uart_cmsis.cpp" projectpath="erpc/setup"/>
        <file category="sourceCpp" name="middleware/multicore/erpc/erpc_c/setup/erpc_setup_mbf_dynamic.cpp" projectpath="erpc/setup"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/transports/"/>
        <file category="include" name="middleware/multicore/erpc/erpc_c/infra/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC examples" Cversion="1.13.0" condition="middleware.multicore.erpc.examples.condition_id">
      <description>erpc_examples</description>
      <files>
        <file category="doc" name="middleware/multicore/erpc/examples/README.md" projectpath="erpc/examples"/>
        <file category="doc" name="middleware/multicore/erpc/examples/matrix_multiply_java/readme.md" projectpath="erpc/examples/matrix_multiply_java"/>
        <file category="doc" name="middleware/multicore/erpc/examples/matrix_multiply_tcp_python/readme.md" projectpath="erpc/examples/matrix_multiply_tcp_python"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC Java sources" Cversion="1.13.0" condition="middleware.multicore.erpc.java.condition_id">
      <description>erpc_java</description>
      <files>
        <file category="doc" name="middleware/multicore/erpc/erpc_java/readme.md" projectpath="erpc/erpc_java"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="eRPC Zephyr sources" Cversion="1.13.0" condition="middleware.multicore.erpc.zephyr.condition_id">
      <description>erpc_zephyr</description>
      <files>
        <file category="doc" name="middleware/multicore/erpc/zephyr/CMakeLists.txt" projectpath="erpc/erpc/zephyr"/>
        <file category="other" name="middleware/multicore/erpc/zephyr/module.yml" projectpath="erpc/erpc/zephyr"/>
        <file category="doc" name="middleware/multicore/erpc/zephyr/readme.md" projectpath="erpc/erpc/zephyr"/>
        <file category="doc" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/CMakeLists.txt" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/Kconfig.sysbuild" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/README.rst" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="header" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/erpc_config.h" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/prj.conf" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/sample.yaml" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/sysbuild.cmake" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_mbox"/>
        <file category="doc" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_mbox/remote/CMakeLists.txt" projectpath="erpc/zephyr/examples/matrix_multiply_mbox/remote"/>
        <file category="doc" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/CMakeLists.txt" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/Kconfig.sysbuild" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/README.rst" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="header" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/erpc_config.h" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/prj.conf" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="header" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/rpmsg_config.h" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/sample.yaml" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="other" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/sysbuild.cmake" projectpath="erpc/erpc/examples/zephyr/matrix_multiply_rpmsglite"/>
        <file category="doc" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_rpmsglite/remote/CMakeLists.txt" projectpath="erpc/zephyr/examples/matrix_multiply_rpmsglite/remote"/>
        <file category="doc" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_tcp/CMakeLists.txt" projectpath="erpc/zephyr/examples/matrix_multiply_tcp"/>
        <file category="doc" name="middleware/multicore/erpc/examples/zephyr/matrix_multiply_uart/CMakeLists.txt" projectpath="erpc/zephyr/examples/matrix_multiply_uart"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multicore_common" Cversion="1.0.0" condition="middleware.multicore.erpc_common_multicore.condition_id">
      <description>erpc examples common files</description>
      <files>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_error_handler.h" projectpath="source"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_error_handler.cpp" projectpath="source"/>
        <file category="include" name="middleware/multicore/example/multicore_examples/erpc_common/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multicore_mm_client" Cversion="1.0.0" condition="middleware.multicore.erpc_common_multicore_matrix_multiply_client.condition_id">
      <description>erpc matrix_multiply_client examples common files</description>
      <files>
        <file category="other" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply.erpc" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_client.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_client.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_client.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_client.cpp" projectpath="erpc/service"/>
        <file category="include" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multicore_mm_server" Cversion="1.0.0" condition="middleware.multicore.erpc_common_multicore_matrix_multiply_server.condition_id">
      <description>erpc matrix_multiply_server examples common files</description>
      <files>
        <file category="other" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply.erpc" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_server.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_server.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_server.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_server.cpp" projectpath="erpc/service"/>
        <file category="include" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_matrix_multiply/service/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multiproc_common" Cversion="1.0.0" condition="middleware.multicore.erpc_common_multiprocessor.condition_id">
      <description>erpc multiprocessor examples common files</description>
      <files>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_error_handler.h" projectpath="source"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_error_handler.cpp" projectpath="source"/>
        <file category="include" name="middleware/multicore/example/multiprocessor_examples/erpc_common/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multiproc_mm_client" Cversion="1.0.0" condition="middleware.multicore.erpc_common_multiprocessor_matrix_multiply_client.condition_id">
      <description>erpc multiprocessor matrix_multiply_client examples common files</description>
      <files>
        <file category="other" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply.erpc" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_client.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_client.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_client.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_client.cpp" projectpath="erpc/service"/>
        <file category="include" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multiproc_mm_server" Cversion="1.0.0" condition="middleware.multicore.erpc_common_multiprocessor_matrix_multiply_server.condition_id">
      <description>erpc multiprocessor matrix_multiply_server examples common files</description>
      <files>
        <file category="other" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply.erpc" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_server.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_server.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/erpc_matrix_multiply_interface.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_server.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/c_erpc_matrix_multiply_server.cpp" projectpath="erpc/service"/>
        <file category="include" name="middleware/multicore/example/multiprocessor_examples/erpc_common/erpc_matrix_multiply/service/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multicore_two_way_core0" Cversion="1.0.0" condition="middleware.multicore.erpc_common_two_way_rpc_core0.condition_id">
      <description>erpc erpc_two_way_rpc_core0 examples common files</description>
      <files>
        <file category="other" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc.erpc" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_client.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_client.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core0Interface_client.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core0Interface_client.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_interface.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_server.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_server.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core1Interface_server.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core1Interface_server.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_interface.cpp" projectpath="erpc/service"/>
        <file category="include" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Remote Procedure Call" Csub="examples_multicore_two_way_core1" Cversion="1.0.0" condition="middleware.multicore.erpc_common_two_way_rpc_core1.condition_id">
      <description>erpc erpc_two_way_rpc_core1 examples common files</description>
      <files>
        <file category="other" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc.erpc" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_client.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_client.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core1Interface_client.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core1Interface_client.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core1Interface_interface.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_common.h" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_common.hpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_server.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_server.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core0Interface_server.h" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/c_erpc_two_way_rpc_Core0Interface_server.cpp" projectpath="erpc/service"/>
        <file category="header" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_interface.hpp" projectpath="erpc/service"/>
        <file category="sourceCpp" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/erpc_two_way_rpc_Core0Interface_interface.cpp" projectpath="erpc/service"/>
        <file category="include" name="middleware/multicore/example/multicore_examples/erpc_common/erpc_two_way_rpc/service/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="mcmgr" Cversion="4.1.5" condition="middleware.multicore.mcmgr.condition_id">
      <description>MCMgr</description>
      <Pre_Include_Global_h>
#ifndef MULTICORE_APP
#define MULTICORE_APP 1
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/multicore/mcmgr/src/mcmgr.h" projectpath="mcmgr"/>
        <file category="header" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api.h" projectpath="mcmgr"/>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr.c" projectpath="mcmgr"/>
        <file category="doc" name="middleware/multicore/mcmgr/readme.txt" projectpath="mcmgr"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/customdoxygen.css" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/dir_68267d1309a1af8e8297ef4c3efbcdba.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/doxygen.css" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/group__mcmgr.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/index.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/mcmgr_8h_source.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/modules.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/navtree.css" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/tabs.css" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/all_0.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/all_1.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/all_2.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/enums_0.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/enums_1.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/enumvalues_0.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/functions_0.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/groups_0.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/nomatches.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/pages_0.html" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/search.css" projectpath="mcmgr/doc"/>
        <file category="doc" name="middleware/multicore/mcmgr/doc/search/typedefs_0.html" projectpath="mcmgr/doc"/>
        <file category="include" name="middleware/multicore/mcmgr/src/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1160 MCMGR" Cversion="4.1.5" condition="middleware.multicore.mcmgr.imxrt1160.condition_id">
      <description>Multicore Manager for evkmimxrt1160 board</description>
      <files>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api_imxrt1160.c" projectpath="mcmgr"/>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_mu_internal.c" projectpath="mcmgr"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1170 MCMGR" Cversion="4.1.5" condition="middleware.multicore.mcmgr.imxrt1170.condition_id">
      <description>Multicore Manager for evkmimxrt1170 board</description>
      <files>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api_imxrt1170.c" projectpath="mcmgr"/>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_mu_internal.c" projectpath="mcmgr"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="imxrt1180 MCMGR" Cversion="4.1.5" condition="middleware.multicore.mcmgr.imxrt1180.condition_id">
      <description>Multicore Manager for evkmimxrt1180 board</description>
      <files>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api_imxrt1180.c" projectpath="mcmgr"/>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_mu_internal.c" projectpath="mcmgr"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="k32l3a6 MCMGR" Cversion="4.1.5" condition="middleware.multicore.mcmgr.k32l3a6.condition_id">
      <description>Multicore Manager for frdmk32l3a6 board</description>
      <files>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api_k32l3a6.c" projectpath="mcmgr"/>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_mu_internal.c" projectpath="mcmgr"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="lpc55s69 MCMGR" Cversion="4.1.5" condition="middleware.multicore.mcmgr.lpc55s69.condition_id">
      <description>Multicore Manager for fpga_niobe4 board</description>
      <files>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api_lpc55s69.c" projectpath="mcmgr"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Core Management" Csub="mcxnx4x MCMGR" Cversion="4.1.5" condition="middleware.multicore.mcmgr.mcxnx4x.condition_id">
      <description>Multicore Manager for MCX NX4X board</description>
      <files>
        <file category="sourceC" name="middleware/multicore/mcmgr/src/mcmgr_internal_core_api_mcxnx4x.c" projectpath="mcmgr"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.condition_id">
      <description>RPMsg-Lite</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_lite.h" projectpath="rpmsg_lite/include"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_ns.h" projectpath="rpmsg_lite/include"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/llist.h" projectpath="rpmsg_lite/include"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_default_config.h" projectpath="rpmsg_lite/include"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/virtio_ring.h" projectpath="rpmsg_lite/include"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/virtqueue.h" projectpath="rpmsg_lite/include"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_compiler.h" projectpath="rpmsg_lite/include"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/common/llist.c" projectpath="rpmsg_lite/common"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/rpmsg_lite.c" projectpath="rpmsg_lite/rpmsg_lite"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/rpmsg_ns.c" projectpath="rpmsg_lite/rpmsg_lite"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/virtio/virtqueue.c" projectpath="rpmsg_lite/virtio"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_env.h" projectpath="rpmsg_lite/include"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/readme.txt" projectpath="rpmsg_lite"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/customdoxygen.css" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/dir_5a30104352ef4255dc24354b02eb2d20.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/dir_97aefd0d527b934f1d99a682da8fe6a9.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/doxygen.css" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/group__config.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/group__rpmsg__lite.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/group__rpmsg__ns.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/group__rpmsg__queue.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/index.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/modules.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/navtree.css" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/rpmsg__default__config_8h.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/rpmsg__default__config_8h_source.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/rpmsg__lite_8h_source.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/rpmsg__ns_8h_source.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/rpmsg__queue_8h_source.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/tabs.css" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_1.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_2.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_3.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_4.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_5.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_6.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_7.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_8.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_9.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/all_a.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/classes_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/files_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/functions_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/groups_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/nomatches.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/pages_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/search.css" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/typedefs_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_0.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_1.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_2.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_3.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_4.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_5.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_6.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_7.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_8.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_9.html" projectpath="rpmsg_lite/doc"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/doc/search/variables_a.html" projectpath="rpmsg_lite/doc"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite Azure RTOS env layer" Cversion="5.1.2">
      <description>RPMsg-Lite Azure RTOS environment sources</description>
      <Pre_Include_Global_h>
#ifndef FSL_RTOS_THREADX
#define FSL_RTOS_THREADX 1
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/environment/rpmsg_env_threadx.c" projectpath="rpmsg_lite/rpmsg_lite/porting/environment"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_queue.h" projectpath="rpmsg_lite/include"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/rpmsg_queue.c" projectpath="rpmsg_lite/rpmsg_lite"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/environment/threadx/rpmsg_env_specific.h" projectpath="rpmsg_lite/include/environment/threadx"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/environment/threadx/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite baremetal env layer" Cversion="5.1.2">
      <description>RPMsg-Lite BM environment sources</description>
      <files>
        <file condition="allOf.components=middleware.baremetal.condition_id" category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/environment/rpmsg_env_bm.c" projectpath="rpmsg_lite/rpmsg_lite/porting/environment"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/environment/bm/rpmsg_env_specific.h" projectpath="rpmsg_lite/include/environment/bm"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/environment/bm/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1160 rpmsg_lite bm config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.bm.config.imxrt1160.condition_id">
      <description>RPMsg-Lite baremetal for evkmimxrt1160 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1170 rpmsg_lite bm config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.bm.config.imxrt1170.condition_id">
      <description>RPMsg-Lite baremetal for evkmimxrt1170 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1180 rpmsg_lite bm config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.bm.config.imxrt1180.condition_id">
      <description>RPMsg-Lite baremetal for evkmimxrt1180 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="k32l3a6 rpmsg_lite bm config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.bm.config.k32l3a6.condition_id">
      <description>RPMsg-Lite baremetal for frdmk32l3a6 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="lpc55s69 rpmsg_lite bm config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.bm.config.lpcxpresso55s69.condition_id">
      <description>RPMsg-Lite baremetal for lpcxpresso55s69 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="mcxnx4x rpmsg_lite bm config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.bm.config.mcxnx4x.condition_id">
      <description>RPMsg-Lite baremetal for mcxnx10 boards</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="evkmimxrt595 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.evkmimxrt595_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt595 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt500_m33/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt500_m33"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt500_m33/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt500_m33"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt500_m33/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="evkmimxrt595 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.evkmimxrt595_freertos.condition_id">
      <description>RPMsg-Lite for evkmimxrt595 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt500_m33/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt500_m33"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt500_m33/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt500_m33"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt500_m33/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="595 rpmsg_lite bm fusion app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.evkmimxrt595_fusionf1_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt595 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt500_fusionf1/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt500_fusionf1"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt500_fusionf1/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt500_fusionf1"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt500_fusionf1/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="evkmimxrt685 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.evkmimxrt685_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt685 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt600_m33"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt600_m33/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt600_m33"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="evkmimxrt685 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.evkmimxrt685_freertos.condition_id">
      <description>RPMsg-Lite for evkmimxrt685 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt600_m33"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt600_m33/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt600_m33"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="685 rpmsg_lite bm hifi4 app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.evkmimxrt685_hifi4_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt685 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_hifi4/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt600_hifi4"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt600_hifi4/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt600_hifi4"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_hifi4/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite freertos env layer" Cversion="5.1.2">
      <description>RPMsg-Lite FreeRTOS environment sources</description>
      <files>
        <file condition="allOf.components=middleware.freertos-kernel.condition_id" category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/environment/rpmsg_env_freertos.c" projectpath="rpmsg_lite/rpmsg_lite/porting/environment"/>
        <file condition="allOf.components=middleware.freertos-kernel.condition_id" category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_queue.h" projectpath="rpmsg_lite/include"/>
        <file condition="allOf.components=middleware.freertos-kernel.condition_id" category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/rpmsg_queue.c" projectpath="rpmsg_lite/rpmsg_lite"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/environment/freertos/rpmsg_env_specific.h" projectpath="rpmsg_lite/include/environment/freertos"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/environment/freertos/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1160 rpmsg_lite RTOS config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.freertos.config.imxrt1160.condition_id">
      <description>RPMsg-Lite FreeRTOS for evkmimxrt1160 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1170 rpmsg_lite RTOS config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.freertos.config.imxrt1170.condition_id">
      <description>RPMsg-Lite FreeRTOS for evkmimxrt1170 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1180 rpmsg_lite RTOS config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.freertos.config.imxrt1180.condition_id">
      <description>RPMsg-Lite FreeRTOS for evkmimxrt1180 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="k32l3a6 rpmsg_lite RTOS config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.freertos.config.k32l3a6.condition_id">
      <description>RPMsg-Lite FreeRTOS for frdmk32l3a6 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="lpc55s69 rpmsg_lite RTOS config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.freertos.config.lpcxpresso55s69.condition_id">
      <description>RPMsg-Lite FreeRTOS for lpcxpresso55s69 board</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="mcxnx4x rpmsg_lite RTOS config" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.freertos.config.mcxnx4x.condition_id">
      <description>RPMsg-Lite FreeRTOS for mcxnx10 boards</description>
      <files>
        <file category="header" attr="config" name="middleware/multicore/rpmsg_lite/template_application/rpmsg_config.h" version="5.1.2" projectpath="source"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1160 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.imxrt1160_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt1160 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1160/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt1160"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt1160/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt1160"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1160/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1160 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.imxrt1160_freertos.condition_id">
      <description>RPMsg-Lite for evkmimxrt1160 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1160/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt1160"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt1160/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt1160"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1160/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1170 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.imxrt1170_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt1170 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1170/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt1170"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt1170/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt1170"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1170/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1170 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.imxrt1170_freertos.condition_id">
      <description>RPMsg-Lite for evkmimxrt1170 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1170/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt1170"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt1170/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt1170"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1170/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1180 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.imxrt1180_bm.condition_id">
      <description>RPMsg-Lite for evkmimxrt1180 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1180/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt1180"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt1180/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt1180"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1180/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="imxrt1180 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.imxrt1180_freertos.condition_id">
      <description>RPMsg-Lite for evkmimxrt1180 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1180/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt1180"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt1180/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt1180"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt1180/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="k32l3a6 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.k32l3a6_bm.condition_id">
      <description>RPMsg-Lite for frdmk32l3a6 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/k32l3a6/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/k32l3a6"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/k32l3a6/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/k32l3a6"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/k32l3a6/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="k32l3a6 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.k32l3a6_freertos.condition_id">
      <description>RPMsg-Lite for frdmk32l3a6 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/k32l3a6/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/k32l3a6"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/k32l3a6/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/k32l3a6"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/k32l3a6/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="lpc55s69 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.lpcxpresso55s69_bm.condition_id">
      <description>RPMsg-Lite for lpcxpresso55s69 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/lpc55s69/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/lpc55s69"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/lpc55s69/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/lpc55s69"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/lpc55s69/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="lpc55s69 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.lpcxpresso55s69_freertos.condition_id">
      <description>RPMsg-Lite for lpcxpresso55s69 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/lpc55s69/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/lpc55s69"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/lpc55s69/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/lpc55s69"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/lpc55s69/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="mcxnx10 rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.mcxnx4x_bm.condition_id">
      <description>RPMsg-Lite for mcxnx10 baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/mcxnx4x/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/mcxnx4x"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/mcxnx4x/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/mcxnx4x"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/mcxnx4x/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="mcxnx10 rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.mcxnx4x_freertos.condition_id">
      <description>RPMsg-Lite for mcxnx10 FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/mcxnx4x/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/mcxnx4x"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/mcxnx4x/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/mcxnx4x"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/mcxnx4x/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="685audevk rpmsg_lite bm app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.mimxrt685audevk_bm.condition_id">
      <description>RPMsg-Lite for MIMXRT685-AUD-EVK baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt600_m33"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt600_m33/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt600_m33"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="685audevk rpmsg_lite RTOS app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.mimxrt685audevk_freertos.condition_id">
      <description>RPMsg-Lite for MIMXRT685-AUD-EVK FreeRTOS application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt600_m33"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt600_m33/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt600_m33"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_m33/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="685audevk rpmsg_lite bmhifi app" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.mimxrt685audevk_hifi4_bm.condition_id">
      <description>RPMsg-Lite for MIMXRT685-AUD-EVK baremetal application</description>
      <files>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_hifi4/rpmsg_platform.h" projectpath="rpmsg_lite/include/platform/imxrt600_hifi4"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/platform/imxrt600_hifi4/rpmsg_platform.c" projectpath="rpmsg_lite/rpmsg_lite/porting/platform/imxrt600_hifi4"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/platform/imxrt600_hifi4/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite xos env layer" Cversion="5.1.2">
      <description>RPMsg-Lite XOS environment layer sources</description>
      <Pre_Include_Global_h>
#ifndef FSL_RTOS_XOS
#define FSL_RTOS_XOS 1
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/environment/rpmsg_env_xos.c" projectpath="rpmsg_lite/env/xos"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_queue.h" projectpath="rpmsg_lite"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/rpmsg_queue.c" projectpath="rpmsg_lite/env/xos"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/environment/xos/rpmsg_env_specific.h" projectpath="rpmsg_lite/include/environment/xos"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/environment/xos/"/>
      </files>
    </component>
    <component Cclass="Multicore" Cgroup="Inter-Core Messaging System" Csub="rpmsg_lite Zephyr env layer" Cversion="5.1.2" condition="middleware.multicore.rpmsg_lite.zephyr.condition_id">
      <description>RPMsg-Lite Zephyr environment sources</description>
      <files>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/porting/environment/rpmsg_env_zephyr.c" projectpath="rpmsg_lite/rpmsg_lite/porting/environment"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/rpmsg_queue.h" projectpath="rpmsg_lite/include"/>
        <file category="sourceC" name="middleware/multicore/rpmsg_lite/lib/rpmsg_lite/rpmsg_queue.c" projectpath="rpmsg_lite/rpmsg_lite"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/lib/include/environment/zephyr/rpmsg_env_specific.h" projectpath="rpmsg_lite/include/environment/zephyr"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/zephyr/CMakeLists.txt" projectpath="rpmsg_lite/rpmsg_lite/zephyr"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/zephyr/README.md" projectpath="rpmsg_lite/rpmsg_lite/zephyr"/>
        <file category="other" name="middleware/multicore/rpmsg_lite/zephyr/module.yml" projectpath="rpmsg_lite/rpmsg_lite/zephyr"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/CMakeLists.txt" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="other" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/Kconfig.sysbuild" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="other" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/README.rst" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/common.h" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="other" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/prj.conf" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="header" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/rpmsg_config.h" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="other" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/sample.yaml" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="other" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/sysbuild.cmake" projectpath="rpmsg_lite/rpmsg_lite/zephyr/samples/rpmsglite_pingpong"/>
        <file category="doc" name="middleware/multicore/rpmsg_lite/zephyr/samples/rpmsglite_pingpong/remote/CMakeLists.txt" projectpath="rpmsg_lite/zephyr/samples/rpmsglite_pingpong/remote"/>
        <file category="include" name="middleware/multicore/rpmsg_lite/lib/include/environment/zephyr/"/>
      </files>
    </component>
  </components>
</package>
