<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1180-EVK_EDGEFAST_BLUETOOTH_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware edgefast_bluetooth Examples Pack for MIMXRT1180-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1180-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="MIMXRT1189_DFP" vendor="NXP" version="19.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="EDGEFAST_BT_BLE" vendor="NXP" version="3.0.0"/>
      <package name="FATFS" vendor="NXP" version="3.0.0"/>
      <package name="LITTLEFS" vendor="NXP" version="3.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="WIFI" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="a2dp_sink" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/a2dp_sink/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth audio source with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/a2dp_sink.uvprojx"/>
        <environment name="csolution" load="a2dp_sink.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="a2dp_source_cm33" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/a2dp_source/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth audio source with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/a2dp_source_cm33.uvprojx"/>
        <environment name="csolution" load="a2dp_source_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_hpc" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/central_hpc/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth hpc example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_hpc.uvprojx"/>
        <environment name="csolution" load="central_hpc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_ht" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/central_ht/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth hts example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_ht.uvprojx"/>
        <environment name="csolution" load="central_ht.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_ipsp" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/central_ipsp/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth ipsp example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_ipsp.uvprojx"/>
        <environment name="csolution" load="central_ipsp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="central_pxm" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/central_pxm/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth pxm example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/central_pxm.uvprojx"/>
        <environment name="csolution" load="central_pxm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edgefast_bluetooth_shell" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/shell/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth shell example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edgefast_bluetooth_shell.uvprojx"/>
        <environment name="csolution" load="edgefast_bluetooth_shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="handsfree" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/handsfree/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth bluetooth handsfree example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/handsfree.uvprojx"/>
        <environment name="csolution" load="handsfree.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="handsfree_ag" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/handsfree_ag/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth handsfree AG example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/handsfree_ag.uvprojx"/>
        <environment name="csolution" load="handsfree_ag.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_beacon" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/peripheral_beacon/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth beacon example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_beacon.uvprojx"/>
        <environment name="csolution" load="peripheral_beacon.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_hps" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/peripheral_hps/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth hps example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_hps.uvprojx"/>
        <environment name="csolution" load="peripheral_hps.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_ht" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/peripheral_ht/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth hts example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_ht.uvprojx"/>
        <environment name="csolution" load="peripheral_ht.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_ipsp" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/peripheral_ipsp/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth ipsp example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_ipsp.uvprojx"/>
        <environment name="csolution" load="peripheral_ipsp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="peripheral_pxr" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/peripheral_pxr/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth pxr example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/peripheral_pxr.uvprojx"/>
        <environment name="csolution" load="peripheral_pxr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spp" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/spp/cm33" doc="readme.md">
      <description>The Bluetooth BR SPP example.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spp.uvprojx"/>
        <environment name="csolution" load="spp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wireless_uart" folder="boards/evkmimxrt1180/edgefast_bluetooth_examples/wireless_uart/cm33" doc="readme.md">
      <description>The Edgefast Bluetooth wireless uart example with simplified application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wireless_uart.uvprojx"/>
        <environment name="csolution" load="wireless_uart.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
