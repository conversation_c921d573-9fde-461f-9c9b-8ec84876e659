<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.36" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.36/schema/PACK.xsd">
  <name>CMSIS-Driver</name>
  <description overview="Documentation/OVERVIEW.md">CMSIS Drivers for external devices</description>
  <vendor>ARM</vendor>
  <url>https://www.keil.com/pack/</url>
  <license>LICENSE</license>
  <licenseSets>
    <licenseSet id="all" default="true" gating="true">
      <license name="LICENSE" title="Apache 2.0 open-source license" spdx="Apache-2.0"/>
    </licenseSet>
  </licenseSets>

  <releases>
    <release version="2.10.0" date="2024-11-27" tag="2.10.0">
      - Added WiFi Shield layers
    </release>
    <release version="2.9.0" date="2024-10-08" tag="2.9.0">
      - Added USB Host EHCI (TT) driver
      - Added USB Host OHCI driver
    </release>
    <release version="2.8.0" date="2023-12-19" tag="2.8.0">
      - Align with CMSIS v6
    </release>
    <release version="2.7.2" date="2022-06-20" tag="2.7.2">
      - Added Renesas DA16200 WiFi driver
      - Updated Inventek ISM43362 WiFi driver (version 1.14.0):
        - Added statically allocated control block for asynchronous thread if FreeRTOS is used
    </release>
    <release version="2.7.1" date="2022-04-27" tag="2.7.1">
      - Added support for Arm Cortex-M85 processor based devices
      - Added support for Arm China Star-MC1 processor based devices
    </release>
    <release version="2.7.0" date="2022-04-06" tag="2.7.0">
      - Added PHY_LAN8740A driver
      - Added ETH_LAN91C111 driver
      - Updated ETH_KSZ8851SNL and ETH_LAN9220 drivers:
        - Corrected invalid power status in MAC_PowerControl
      - Updated ESP8266 WiFi driver:
        - Fixed Activate to use BSSID specified in SetOption with ARM_WIFI_BSSID
      - Updated ESP8266, ESP32 and WizFi360 WiFi drivers:
        - Fixed return string null terminator in GetModuleInfo
        - Fixed SocketSendTo for stream socket lengths above 2048 bytes
        - Enabled placement of USART transfer buffers in appropriate RAM by using section
          ".bss.driver.usartn" (n = USART driver instance number) in the linker scatter file
      - Updated Inventek ISM43362 WiFi driver (version 1.13.0):
        - Supports also ISM43340 Module
        - Added configuration for asynchronous thread stack size
        - Added support for 5 GHz channels on Access Point
        - Fixed socket connect operation for non-blocking mode
        - Enabled placement of SPI transfer buffers in appropriate RAM by using section
          ".bss.driver.spin" (n = SPI driver instance number) in the linker scatter file
        - Detected ISM43362 Module on STMicroelectronics B-L475E-IOT01A1 firmware limitation:
          SocketConnect does not work if first or last octet of IP address is 0
          (for example IPs 0.x.y.z or x.y.z.0 do not work)
        - Detected shield firmware limitation: SocketConnect does not work if
          certain IP address octets contain value 0 or 255
          (combinations that do not work: 0.x.y.z, x.y.z.0, 255.x.y.z)
        - Updated CMSIS Driver Validation test results
    </release>
    <release version="2.6.1" date="2020-07-13" tag="2.6.1">
      - Updated ESP8266, ESP32 and WizFi360 WiFi drivers:
        - Added auto protocol selection in SocketCreate
        - Fixed socket default timeout (zero == no time out)
        - Fixed SocketRecv/RecvFrom non blocking mode when received less than buffer length
      - Updated Inventek ISM43362 WiFi driver (version 1.9.0):
        - Corrected Initialize function failure if called shortly after reset
        - Corrected default protocol selection in SocketCreate function
        - Detected STM firmware limitation: SocketConnect does not
          work if any of IP address octets is 255 (for example IP like x.y.z.255)
    </release>
    <release version="2.6.0" date="2020-04-29" tag="2.6.0">
      - Updated ESP8266, ESP32 and WizFi360 WiFi drivers:
        - API V1.1: SocketSend/SendTo and SocketRecv/RecvFrom (support for polling)
        - Added DHCP setting before station Activate
        - Added read of DHCP assigned IPs after station activate
        - Fixed serial tx busy flag handling
        - Fixed function AT_Resp_ConnectAP for NULL argument
        - Enhanced serial communication startup procedure
      - Updated Inventek ISM43362 WiFi driver (version 1.8.0):
        - API V1.1: SocketSend/SendTo and SocketRecv/RecvFrom (support for polling)
        - Corrected GetModuleInfo return string termination
        - Corrected functionality when DATARDY line is used in polling mode
        - Corrected SocketConnect function never returning 0 in non-blocking mode
        - Corrected SocketRecv/SocketRecvFrom function polling if called without previous Bind
        - Corrected delay after module reset
        - For non-STM firmware variant only firmware version ******* is supported
      - Documented firmware update procedure for Inventek ISMART43362-E WiFi Shield
    </release>
    <release version="2.5.0" date="2019-12-02" tag="2.5.0">
      - Added Espressif ESP32 WiFi driver
      - Added Espressif ESP8266 WiFi driver
      - Added Wiznet WizFi360 WiFi driver
      - Updated Inventek ISM43362 WiFi driver (version 1.3.0):
        - Fixed SocketClose functionality
        - Fixed Activate function not to set password if OPEN security is used
        - Updated Initialization function to handle unavailable reset pin
        - Added debug of SPI traffic with Event Recorder
        - Added CMSIS Driver Validation test results
    </release>
    <release version="2.4.1" date="2019-07-11" tag="2.4.1">
      - Updated Inventek ISM43362 WiFi driver
      - Corrected PHY_LAN8742A power down bit definition
    </release>
    <release version="2.4.0" date="2019-03-15" tag="2.4.0">
      - Added CMSIS-Driver WiFi implementation for Inventek ISM43362 via SPI (CMSIS-Driver SPI)
    </release>
    <release version="2.3.0" date="2018-06-15" tag="2.3.0">
      - Updated PHY_KSZ8081RNA:
        - Added support for 50MHz reference clock
      - Updated NAND MemoryBus driver:
         - Fixed Ready/Busy configuration handling
         - Enhanced compatibility for ARM Compiler 6
      - Updated Flash Drivers:
         - Added data cache handling to memory bus based drivers
         - Fixed warnings flagged by ARM Compiler 6
    </release>
    <release version="2.2.0" date="2018-03-16" tag="2.2.0">
      - Cvendor explicitly set to "Keil" to ease project migration
      - doxygen based documentation:
        - added links to hardware parts
        - added Multi-Slave I2C and SPI usage instructions
    </release>
    <release version="2.1.0" date="2018-02-21" tag="2.1.0">
      - Added LAN9220 Ethernet MAC+PHY driver
      - Updated I2C and SPI multi-slave wrapper:
        - configuration options moved into separate file
    </release>
    <release version="2.0.0" date="2018-02-09" tag="2.0.0">
      CMSIS-Driver implementation for non-MCU devices from MDK-Middleware pack have been moved to git-hub and are now shipped as a separate pack.
    </release>
  </releases>

  <requirements>
    <packages>
      <package vendor="ARM" name="CMSIS" version="5.9.0-0"/>
    </packages>
  </requirements>

  <conditions>
    <condition id="Cortex-M Device">
      <description>Cortex-M processor based device: Cortex-M0/M0+/M1/M3/M4/M7/M23/M33/M35P/M55/M85, ARMV8MBL/ML, Star-MC1, SC000/300</description>
      <accept Dcore="Cortex-M0"/>
      <accept Dcore="Cortex-M0+"/>
      <accept Dcore="Cortex-M1"/>
      <accept Dcore="Cortex-M3"/>
      <accept Dcore="Cortex-M4"/>
      <accept Dcore="Cortex-M7"/>
      <accept Dcore="Cortex-M23"/>
      <accept Dcore="Cortex-M33"/>
      <accept Dcore="Cortex-M35P"/>
      <accept Dcore="Cortex-M55"/>
      <accept Dcore="Cortex-M85"/>
      <accept Dcore="ARMV8MBL"/>
      <accept Dcore="ARMV8MML"/>
      <accept Dcore="ARMV81MML"/>
      <accept Dcore="Star-MC1"/>
      <accept Dcore="SC000"/>
      <accept Dcore="SC300"/>
    </condition>
    <condition id="Device CMSIS_Core">
      <description>Device and CMSIS Core</description>
      <require condition="Cortex-M Device"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="Device CMSIS_Core SPI_Driver">
      <description>Device and CMSIS Core and SPI Driver</description>
      <require condition="Cortex-M Device"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI"/>
    </condition>
    <condition id="Device CMSIS_Core CMSIS_RTOS2">
      <description>Device and CMSIS Core with RTOS2</description>
      <require condition="Cortex-M Device"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="CMSIS" Cgroup="RTOS2"/>
    </condition>
    <condition id="Device CMSIS_Core CMSIS_RTOS2 SPI_Driver">
      <description>Device and CMSIS Core with RTOS2 and SPI Driver</description>
      <require condition="Cortex-M Device"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="CMSIS" Cgroup="RTOS2"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI"/>
    </condition>
    <condition id="Device CMSIS_Core CMSIS_RTOS2 USART_Driver">
      <description>Device and CMSIS Core with RTOS2 and USART Driver</description>
      <require condition="Cortex-M Device"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="CMSIS" Cgroup="RTOS2"/>
      <require Cclass="CMSIS Driver" Cgroup="USART"/>
    </condition>
  </conditions>

  <components>

    <!-- CMSIS-Driver Ethernet -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet" Csub="KSZ8851SNL" Capiversion="2.2.0" Cversion="6.8.0" condition="Device CMSIS_Core CMSIS_RTOS2 SPI_Driver">
      <description>Ethernet MAC + PHY KSZ8851SNL/SNLI Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_ETH_KSZ8851SNL      /* Driver ETH KSZ8851SNL */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/KSZ8851"/>
        <file category="source" name="Ethernet/KSZ8851SNL/ETH_KSZ8851SNL.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet" Csub="ETH_LAN91C111" Capiversion="2.2.0" Cversion="1.1.0" condition="Device CMSIS_Core">
      <description>Ethernet MAC + PHY LAN91C111 Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_ETH_LAN91C111       /* Driver ETH_LAN91C111 */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/LAN91C111"/>
        <file category="source" name="Ethernet/LAN91C111/ETH_LAN91C111.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet" Csub="LAN9220" Capiversion="2.2.0" Cversion="1.2.0" condition="Device CMSIS_Core">
      <description>Ethernet MAC + PHY LAN9220 Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_ETH_LAN9220         /* Driver ETH LAN9220 */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/LAN9220"/>
        <file category="source" name="Ethernet/LAN9220/ETH_LAN9220.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver Ethernet PHY -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="DP83848C" Capiversion="2.2.0" Cversion="6.2.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY DP83848C Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_DP83848C        /* Driver PHY DP83848C */
      </RTE_Components_h>
      <files>
        <file category="doc" name="http://www.ti.com/product/DP83848C"/>
        <file category="source" name="Ethernet_PHY/DP83848C/PHY_DP83848C.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="KSZ8061RNB" Capiversion="2.2.0" Cversion="1.3.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY KSZ8061RNB Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_KSZ8061RNB      /* Driver PHY KSZ8061RNB */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/KSZ8061"/>
        <file category="source" name="Ethernet_PHY/KSZ8061RNB/PHY_KSZ8061RNB.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="KSZ8081RNA" Capiversion="2.2.0" Cversion="6.3.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY KSZ8081RNA Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_KSZ8081RNA      /* Driver PHY KSZ8081RNA/RND */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/KSZ8081"/>
        <file category="source" name="Ethernet_PHY/KSZ8081RNA/PHY_KSZ8081RNA.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="LAN8710A" Capiversion="2.2.0" Cversion="1.0.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY LAN8710A Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_LAN8710A        /* Driver PHY LAN8710A */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/LAN8710A"/>
        <file category="source" name="Ethernet_PHY/LAN8710A/PHY_LAN8710A.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="LAN8720" Capiversion="2.2.0" Cversion="6.2.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY LAN8720 Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_LAN8720         /* Driver PHY LAN8720 */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/LAN8720A"/>
        <file category="source" name="Ethernet_PHY/LAN8720/PHY_LAN8720.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="LAN8740A" Capiversion="2.2.0" Cversion="1.0.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY LAN8740A Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_LAN8740A        /* Driver PHY LAN8740A */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/LAN8740A"/>
        <file category="source" name="Ethernet_PHY/LAN8740A/PHY_LAN8740A.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="LAN8742A" Capiversion="2.2.0" Cversion="1.3.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY LAN8742A Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_LAN8742A        /* Driver PHY LAN8742A */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/LAN8742A"/>
        <file category="source" name="Ethernet_PHY/LAN8742A/PHY_LAN8742A.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="ST802RT1" Capiversion="2.2.0" Cversion="6.2.0" condition="Device CMSIS_Core">
      <description>Ethernet PHY ST802RT1 Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_PHY_ST802RT1        /* Driver PHY ST802RT1 */
      </RTE_Components_h>
      <files>
        <file category="doc" name="http://www.st.com/content/ccc/resource/technical/document/data_brief/37/8e/14/c7/84/39/4d/61/**********.pdf/files/**********.pdf/jcr:content/translations/en.**********.pdf"/>
        <file category="source" name="Ethernet_PHY/ST802RT1/PHY_ST802RT1.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver Flash -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="AM29x800BB" Capiversion="2.3.0" Cversion="1.4.0" condition="Device CMSIS_Core">
      <description>AM29x800BB Flash (16-bit Bus)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Driver_Flash_AM29x800BB     /* Driver Flash AM29x800BB */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.microchip.com/KSZ8061"/>
        <file category="source" name="Flash/AM29x800BB/AM29x800BB.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="AT45DB641E" Capiversion="2.3.0" Cversion="1.3.0" condition="Device CMSIS_Core SPI_Driver">
      <description>AT45DB641E Flash (SPI)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_Flash_AT45DB641E    /* Driver Flash AT45DB641E */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.adestotech.com/products/data-flash/"/>
        <file category="source" name="Flash/AT45DB641E/AT45DB641E.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="AT45DB642D" Capiversion="2.3.0" Cversion="1.3.0" condition="Device CMSIS_Core SPI_Driver">
      <description>AT45DB642D Flash (SPI)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_Flash_AT45DB642D    /* Driver Flash AT45DB642D */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.adestotech.com/legacy-product-datasheets/"/>
        <file category="source" name="Flash/AT45DB642D/AT45DB642D.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="M29EW28F128" Capiversion="2.3.0" Cversion="1.4.0" condition="Device CMSIS_Core">
      <description>M29EW 128Mb Flash (16-bit Bus): JS28F128M29EW, PC28F128M29EW, RC28F128M29EW</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_Flash_M29EW28F128   /* Driver Flash M29EW28F128 */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.micron.com/parts/nor-flash/parallel-nor-flash/pc28f128m29ewhf"/>
        <file category="source" name="Flash/M29EW28F128/M29EW28F128.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="M29W640FB" Capiversion="2.3.0" Cversion="1.4.0" condition="Device CMSIS_Core">
      <description>M29W640FB Flash (16-bit Bus)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_Flash_M29W640FB     /* Driver Flash M29W640FB */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.micron.com/parts/nor-flash/parallel-nor-flash/m29w640fb70n6e"/>
        <file category="source" name="Flash/M29W640FB/M29W640FB.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="N25Q032A" Capiversion="2.3.0" Cversion="1.1.0" condition="Device CMSIS_Core SPI_Driver">
      <description>N25Q032A Flash (SPI)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_Flash_N25Q032A      /* Driver Flash N25Q032A */
      </RTE_Components_h>
      <files>
        <file category="doc" name="https://www.micron.com/parts/nor-flash/serial-nor-flash/n25q032a11ef440e"/>
        <file category="source" name="Flash/N25Q032A/N25Q032A.c"/>
      </files>
    </component>

    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="Flash" Csub="S29GL064Nx2" Capiversion="2.3.0" Cversion="1.4.0" condition="Device CMSIS_Core">
      <description>S29GL064N Dual Flash (32-bit Bus)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_Flash_S29GL064Nx2   /* Driver Flash S29GL064Nx2 */
      </RTE_Components_h>
      <files>
        <file category="doc" name="http://www.cypress.com/documentation/datasheets/s29gl064n-s29gl032n-64-mbit-32-mbit-3-v-page-mode-mirrorbit-flash"/>
        <file category="source" name="Flash/S29GL064Nx2/S29GL064Nx2.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver NAND -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="NAND" Csub="Memory Bus" Capiversion="2.4.0" Cversion="1.1.0" condition="Device CMSIS_Core">
      <description>NAND Flash connected to Memory Bus</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Driver_NAND_MemoryBus       /* Driver NAND Flash on Memory Bus */
      </RTE_Components_h>
      <files>
        <file category="header" name="Config/NAND_MemBus_Config.h" attr="config" version="1.0.0"/>
        <file category="source" name="NAND/NAND_MemBus.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver I2C -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="I2C" Csub="Multi-Slave" Capiversion="2.4.0" Cversion="1.0.1" condition="Device CMSIS_Core CMSIS_RTOS2">
      <description>I2C Master to Multi-Slave Driver Wrapper</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_I2C_MultiSlave      /* Driver I2C Multi-Slave */
      </RTE_Components_h>
      <files>
        <file category="doc"    name="Documentation/html/driver_I2C.html"/>
        <file category="header" name="Config/I2C_MultiSlave_Config.h" attr="config" version="1.0.0"/>
        <file category="source" name="I2C/I2C_MultiSlave.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver SPI -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="SPI" Csub="Multi-Slave" Capiversion="2.3.0" Cversion="1.0.1" condition="Device CMSIS_Core CMSIS_RTOS2">
      <description>SPI Master to Multi-Slave Driver Wrapper</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_SPI_MultiSlave      /* Driver SPI Multi-Slave */
      </RTE_Components_h>
      <files>
        <file category="doc"    name="Documentation/html/driver_SPI.html"/>
        <file category="header" name="Config/SPI_MultiSlave_Config.h" attr="config" version="1.0.0"/>
        <file category="header" name="SPI/SPI_MultiSlave.h"/>
        <file category="source" name="SPI/SPI_MultiSlave.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver USB Host -->

    <!-- EHCI -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="USB Host" Csub="EHCI" Cvariant="TT" Capiversion="2.4.0" Cversion="1.0.0" condition="Device CMSIS_Core CMSIS_RTOS2">
      <description>USB Host EHCI Controller Driver for customized EHCI with internal Transaction Translator (TT)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_USBH_EHCI           /* Driver USBH EHCI */
        #define RTE_Drivers_USBH_EHCI_TT        /* Driver USBH EHCI (TT) */
      </RTE_Components_h>
      <files>
        <file category="header" name="USB/EHCI/Interface/USBH_EHCI_HW.h"/>
        <file category="header" name="USB/EHCI/Config/USBH_EHCI_Config.h" attr="config"   version="1.0.0"/>
        <file category="source" name="USB/EHCI/Template/USBH_EHCI_HW.c"   attr="template" version="1.0.0" select="Implementation of Host Controller hardware specifics for EHCI"/>
        <file category="source" name="USB/EHCI/Source/USBH_EHCI_TT.c"/>
      </files>
    </component>

    <!-- OHCI -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="USB Host" Csub="OHCI" Capiversion="2.4.0" Cversion="1.0.0" condition="Device CMSIS_Core CMSIS_RTOS2">
      <description>USB Host OHCI Controller Driver</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_USBH_OHCI           /* Driver USBH OHCI */
      </RTE_Components_h>
      <files>
        <file category="header" name="USB/OHCI/Interface/USBH_OHCI_HW.h"/>
        <file category="header" name="USB/OHCI/Config/USBH_OHCI_Config.h" attr="config"   version="1.0.0"/>
        <file category="source" name="USB/OHCI/Template/USBH_OHCI_HW.c"   attr="template" version="1.0.0" select="Implementation of Host Controller hardware specifics for OHCI"/>
        <file category="source" name="USB/OHCI/Source/USBH_OHCI.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver WiFi -->

    <!-- DA16200 -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="WiFi" Csub="DA16200" Capiversion="1.1.0" Cvariant="UART" Cversion="1.0.0" condition="Device CMSIS_Core CMSIS_RTOS2 USART_Driver">
      <description>Renesas DA16200 WiFi Driver (UART)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_WiFi_DA16200_UART   /* Driver WiFi DA16200 (UART) */
      </RTE_Components_h>
      <files>
        <file category="header"  name="WiFi/DA16200/Config/WiFi_DA16200_Config.h" attr="config" version="1.0.0"/>
        <file category="source"  name="WiFi/DA16200/Config/WiFi_DA16200_HW.c"     attr="config" version="1.0.0"/>
        <file category="include" name="WiFi/DA16200/"/>
        <file category="source"  name="WiFi/DA16200/WiFi_DA16200.c"/>
        <file category="source"  name="WiFi/DA16200/WiFi_DA16200_Os.c"/>
        <file category="source"  name="WiFi/DA16200/DA16200.c"/>
        <file category="source"  name="WiFi/DA16200/DA16200_Serial.c"/>
        <file category="include" name="WiFi/DA16200/BufList/"/>
        <file category="source"  name="WiFi/DA16200/BufList/BufList.c"/>
        <file category="source"  name="WiFi/DA16200/BufList/LinkList.c"/>
      </files>
    </component>

    <!-- Inventek ISM43362 -->    
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="WiFi" Csub="ISM43362" Capiversion="1.1.0" Cvariant="SPI" Cversion="1.15.0" condition="Device CMSIS_Core CMSIS_RTOS2 SPI_Driver">
      <description>WiFi Inventek ISM43362/ISM43340 Driver (SPI)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_WiFi_ISM43362_SPI   /* Driver WiFi ISM43362 (SPI) */
      </RTE_Components_h>
      <files>
        <file category="other"   name="WiFi/ISM43362/WiFi_ISM43362.scvd"/>
        <file category="include" name="WiFi/ISM43362/"/>
        <file category="header"  name="WiFi/ISM43362/Config/WiFi_ISM43362_Config.h" attr="config" version="1.2.0"/>
        <file category="source"  name="WiFi/ISM43362/Config/WiFi_ISM43362_HW.c"     attr="config" version="1.1.0"/>
        <file category="source"  name="WiFi/ISM43362/WiFi_ISM43362.c"/>
        <file category="source"  name="WiFi/ISM43362/WiFi_ISM43362_Buf.c"/>
        <file category="source"  name="WiFi/ISM43362/WiFi_ISM43362_Mem.c"/>
      </files>
    </component>

    <!-- ESP32 -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="WiFi" Csub="ESP32" Capiversion="1.1.0" Cvariant="UART" Cversion="1.7.0" condition="Device CMSIS_Core CMSIS_RTOS2 USART_Driver">
      <description>Espressif ESP32 WiFi Driver (UART)</description>
      <RTE_Components_h>
        #define RTE_Drivers_WiFi_ESP32_UART   /* Driver WiFi ESP32 (UART) */
      </RTE_Components_h>
      <files>
        <file category="header"  name="WiFi/ESP32/Config/WiFi_ESP32_Config.h" attr="config" version="1.0.0"/>
        <file category="include" name="WiFi/ESP32/"/>
        <file category="source"  name="WiFi/ESP32/WiFi_ESP32.c"/>
        <file category="source"  name="WiFi/ESP32/WiFi_ESP32_Os.c"/>
        <file category="source"  name="WiFi/ESP32/ESP32.c"/>
        <file category="source"  name="WiFi/ESP32/ESP32_Serial.c"/>
        <file category="include" name="WiFi/ESP32/BufList/"/>
        <file category="source"  name="WiFi/ESP32/BufList/BufList.c"/>
        <file category="source"  name="WiFi/ESP32/BufList/LinkList.c"/>
      </files>
    </component>

    <!-- ESP8266 -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="WiFi" Csub="ESP8266" Capiversion="1.1.0" Cvariant="UART" Cversion="1.8.0" condition="Device CMSIS_Core CMSIS_RTOS2 USART_Driver">
      <description>Espressif ESP8266 WiFi Driver (UART)</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_WiFi_ESP8266_UART   /* Driver WiFi ESP8266 (UART) */
      </RTE_Components_h>
      <files>
        <file category="header"  name="WiFi/ESP8266/Config/WiFi_ESP8266_Config.h" attr="config" version="1.0.0"/>
        <file category="include" name="WiFi/ESP8266/"/>
        <file category="source"  name="WiFi/ESP8266/WiFi_ESP8266.c"/>
        <file category="source"  name="WiFi/ESP8266/WiFi_ESP8266_Os.c"/>
        <file category="source"  name="WiFi/ESP8266/ESP8266.c"/>
        <file category="source"  name="WiFi/ESP8266/ESP8266_Serial.c"/>
        <file category="include" name="WiFi/ESP8266/BufList/"/>
        <file category="source"  name="WiFi/ESP8266/BufList/BufList.c"/>
        <file category="source"  name="WiFi/ESP8266/BufList/LinkList.c"/>
      </files>
    </component>

    <!-- WizFi360 -->
    <component Cvendor="Keil" Cclass="CMSIS Driver" Cgroup="WiFi" Csub="WizFi360" Capiversion="1.1.0" Cvariant="UART" Cversion="1.8.0" condition="Device CMSIS_Core CMSIS_RTOS2 USART_Driver">
      <description>WIZnet WizFi360 WiFi Driver (UART)</description>
      <RTE_Components_h>
        #define RTE_Drivers_WiFi_WIZFI360_UART   /* Driver WiFi WIZFI360 (UART) */
      </RTE_Components_h>
      <files>
        <file category="header"  name="WiFi/WizFi360/Config/WiFi_WizFi360_Config.h" attr="config" version="1.0.0"/>
        <file category="include" name="WiFi/WizFi360/"/>
        <file category="source"  name="WiFi/WizFi360/WiFi_WizFi360.c"/>
        <file category="source"  name="WiFi/WizFi360/WiFi_WizFi360_Os.c"/>
        <file category="source"  name="WiFi/WizFi360/WizFi360.c"/>
        <file category="source"  name="WiFi/WizFi360/WizFi360_Serial.c"/>
        <file category="include" name="WiFi/WizFi360/BufList/"/>
        <file category="source"  name="WiFi/WizFi360/BufList/BufList.c"/>
        <file category="source"  name="WiFi/WizFi360/BufList/LinkList.c"/>
      </files>
    </component>
  </components>

  <csolution>
    <clayer type="Shield" path="Shield/WiFi/Sparkfun_ESP8266"       file="Shield.clayer.yml" copy-to="Shield/WiFi/Sparkfun_ESP8266"/>
    <clayer type="Shield" path="Shield/WiFi/WizNet_WizFi360-EVB"    file="Shield.clayer.yml" copy-to="Shield/WiFi/WizNet_WizFi360-EVB"/>
    <clayer type="Shield" path="Shield/WiFi/Inventek_ISMART43362-E" file="Shield.clayer.yml" copy-to="Shield/WiFi/Inventek_ISMART43362-E"/>
    <clayer type="Shield" path="Shield/WiFi/Sparkfun_DA16200"       file="Shield.clayer.yml" copy-to="Shield/WiFi/Sparkfun_DA16200"/>
  </csolution>

</package>
