<?xml version="1.0" encoding="utf-8"?>

<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>RealThread</vendor>
  <name>RT-Thread</name>
  <description>RT-Thread Software Components</description>
  <url>https://www.rt-thread.org/download/mdk/</url>
  <supportContact>https://www.rt-thread.org</supportContact>

  <license>License.txt</license>
  
  <releases>
    <release version="3.1.5" date="2021-06-17">
      Updated to RT-Thread 3.1.5
      Add M23/M33 core
    </release>
	<release version="3.1.3" date="2019-11-11">
      Updated to RT-Thread 3.1.3
      Add RISC-V example
    </release>
    <release version="3.1.2" date="2019-01-29">
      Updated to RT-Thread 3.1.2
    </release>
    <release version="3.0.3" date="2018-03-09">
      Updated to RT-Thread 3.0.3
      Added V2M-MPS2 example
    </release>
    <release version="2.1.1">
      first release
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>rt-thread</keyword>
    <keyword>rtthread</keyword>
    <keyword>rtt</keyword>
  </keywords>
  
  <!-- apis section (optional - for Application Programming Interface descriptions) -->
  <!-- 
  <apis>
  </apis>
  -->

  <!-- boards section (mandatory for Board Support Packs) -->
  <!-- 
  <boards>
  </boards>
  -->

  <!-- devices section (mandatory for Device Family Packs) -->
  <!--
  <devices>
  </devices>
  -->
  
  <conditions> 
    <condition id="ARM Compiler">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Cortex-M">
      <accept  Dcore="Cortex-M0"/>
      <accept  Dcore="Cortex-M0+"/>
      <accept  Dcore="Cortex-M23"/>
      <accept  Dcore="Cortex-M33"/>
      <accept  Dcore="Cortex-M3"/>
      <accept  Dcore="Cortex-M4"/>
      <accept  Dcore="Cortex-M7"/>
    </condition>
    <condition id="CM0">
      <description>Cortex-M0 based device</description>
      <require condition="ARM Compiler"/>
      <accept  Dcore="Cortex-M0"/>
      <accept  Dcore="Cortex-M0+"/>
    </condition>
    <condition id="CM23">
      <description>Cortex-M23 based device</description>
      <require condition="ARM Compiler"/>
      <accept  Dcore="Cortex-M23"/>
    </condition>
    <condition id="CM33">
      <description>Cortex-M33 based device</description>
      <require condition="ARM Compiler"/>
      <accept  Dcore="Cortex-M33"/>
    </condition>
    <condition id="CM3">
      <description>Cortex-M3 based device</description>
      <require condition="ARM Compiler"/>
      <accept  Dcore="Cortex-M3"/>
    </condition>
    <condition id="CM4">
      <description>Cortex-M4 based device</description>
      <require condition="ARM Compiler"/>
      <accept  Dcore="Cortex-M4"/>
    </condition>
    <condition id="CM7">
      <description>Cortex-M7 based device</description>
      <require condition="ARM Compiler"/>
      <accept  Dcore="Cortex-M7"/>
    </condition>
    <condition id="CMSIS Core with RTOS">
      <description>CMSIS Core with RTOS for Cortex-M processor</description>
      <accept  condition="Cortex-M"/>
    </condition>

    <condition id="shell">
      <description>rt-thread finsh</description>
     <!-- <require Cclass="RTOS" Cgroup="device drivers"/> -->
    </condition>
    <condition id="device">
      <description>rt-thread device</description>
    </condition>
  </conditions>
  
  <components>
    <bundle Cbundle="RT-Thread" Cclass="RTOS" Cversion="3.1.5">
    <description>rt-thread</description>
    <doc></doc>
    <component Cgroup="kernel" condition="CMSIS Core with RTOS">
        <description>rt-thread kernel files</description>
        <files>
            <file category="include" name="include/"/>
            <file category="header" name="bsp/_template/rtconfig.h" attr="config" version="3.1.5"/>
            <file category="source" name="bsp/_template/board.c" attr="config" version="3.1.5"/>

            <file category="source" name="src/clock.c"/>
            <file category="source" name="src/components.c"/>
            <file category="source" name="src/idle.c"/>
            <file category="source" name="src/ipc.c"/>
            <file category="source" name="src/irq.c"/>
            <file category="source" name="src/kservice.c"/>
            <file category="source" name="src/mem.c"/>
            <file category="source" name="src/mempool.c"/>
            <file category="source" name="src/object.c"/>
            <file category="source" name="src/scheduler.c"/>
            <file category="source" name="src/thread.c"/>
            <file category="source" name="src/timer.c"/>
            
            <file category="source" name="libcpu/arm/cortex-m0/cpuport.c" condition="CM0"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m0/context_rvds.S" condition="CM0"/>

            <file category="source" name="libcpu/arm/cortex-m23/cpuport.c" condition="CM23"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m23/context_rvds.S" condition="CM23"/>

            <file category="source" name="libcpu/arm/cortex-m33/cpuport.c" condition="CM33"/>
            <file category="source" name="libcpu/arm/cortex-m33/trustzone.c" condition="CM33"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m33/context_rvds.S" condition="CM33"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m33/syscall_rvds.S" condition="CM33"/>

            <file category="source" name="libcpu/arm/cortex-m3/cpuport.c" condition="CM3"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m3/context_rvds.S" condition="CM3"/>

            <file category="source" name="libcpu/arm/cortex-m4/cpuport.c" condition="CM4"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m4/context_rvds.S" condition="CM4"/>

            <file category="source" name="libcpu/arm/cortex-m7/cpuport.c" condition="CM7"/>
            <file category="source" name="libcpu/arm/cortex-m7/cpu_cache.c" condition="CM7"/>
            <file category="sourceAsm" name="libcpu/arm/cortex-m7/context_rvds.S" condition="CM7"/>	
        </files>
    </component>
    
    <component Cgroup="shell" condition="shell">
        <description>rt-thread finsh</description>
        <files>
            <file category="include" name="components/finsh/"/>
            <file category="header" name="components/finsh/finsh_config.h" attr="config" version="3.1.5"/>
            <file category="source" name="components/finsh/finsh_port.c" attr="config" version="3.1.5"/>
            
            <file category="source" name="components/finsh/shell.c"/>
            <file category="source" name="components/finsh/cmd.c"/>
            <file category="source" name="components/finsh/msh.c"/>           
        </files>
    </component>

    <component Cgroup="device" condition="device">
        <description>rt-thread device</description>
        <files>
            <file category="source" name="components/device/device.c"/>
        </files>
    </component>
    
    </bundle>
  </components>

  
  <examples>
    <!-- stm32f1 example -->
    <example name="rt-thread blinky-hal" doc="Abstract.txt" folder="bsp/stm32f103-blink">
      <description>rt-thread blink example [hal]</description>
      <board name="NUCLEO-F103RB" vendor="STMicroelectronics"/>
      <project>
        <environment name="uv" load="Project.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="rtthread" Cgroup="Kernel"/>
        <category>Getting Started</category>
        <category>rt-thread</category>
      </attributes>
    </example>

    <!-- stm32f1 example -->
    <example name="rt-thread blinky-ll" doc="Abstract.txt" folder="bsp/stm32f103-ll-blink">
      <description>rt-thread blink example [ll]</description>
      <board name="NUCLEO-F103RB" vendor="STMicroelectronics"/>
      <project>
        <environment name="uv" load="Project.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="rtthread" Cgroup="Kernel"/>
        <category>Getting Started</category>
        <category>rt-thread</category>
      </attributes>
    </example>

    <!-- stm32f1 example -->
    <example name="rt-thread msh-hal" doc="Abstract.txt" folder="bsp/stm32f103-msh">
      <description>rt-thread msh example [hal]</description>
      <board name="NUCLEO-F103RB" vendor="STMicroelectronics"/>
      <project>
        <environment name="uv" load="Project.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="rtthread" Cgroup="Kernel"/>
        <category>Getting Started</category>
        <category>rt-thread</category>
      </attributes>
    </example>

  </examples>
  
  <!-- optional taxonomy section for defining new component Class and Group names -->
  <!--
  <taxonomy>
  </taxonomy>
  -->
  
</package>
