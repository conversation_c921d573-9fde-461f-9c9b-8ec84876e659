<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_FGM23_DFP</name>
  <description>Silicon Labs FGM23 Flex Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>FGM23</keyword>
    <keyword>FGM23</keyword>
    <keyword>Flex Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="FGM23 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <book name="Documents/efr32xg23-rm.pdf"  title="FGM23 Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency&#xD;&#xA;- Up to 512 kB of flash and 64 kB of RAM&#xD;&#xA;- Energy-efficient radio core with low active and sleep currents&#xD;&#xA;- Integrated PA with up to 20 dBm (Sub-GHz) TX power&#xD;&#xA;- Robust peripheral set and up to 31 GPIO&#xD;&#xA;
      </description>

      <subFamily DsubFamily="FGM230S">
        <!-- *************************  Device 'FGM230SA27HGN'  ***************************** -->
        <device Dname="FGM230SA27HGN">
          <compile header="Device/SiliconLabs/FGM23/Include/em_device.h"  define="FGM230SA27HGN"/>
          <debug      svd="SVD/FGM23/FGM230SA27HGN.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x10000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'FGM230SB27HGN'  ***************************** -->
        <device Dname="FGM230SB27HGN">
          <compile header="Device/SiliconLabs/FGM23/Include/em_device.h"  define="FGM230SB27HGN"/>
          <debug      svd="SVD/FGM23/FGM230SB27HGN.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x10000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="FGM23">
      <description>Silicon Labs FGM23 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="FGM23*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="FGM23">
      <description>System Startup for Silicon Labs FGM23 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/FGM23/Include/"/>
        <file category="header"  name="Device/SiliconLabs/FGM23/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/FGM23/Source/system_fgm23.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
