<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>WIFI</name>
  <vendor>NXP</vendor>
  <description>Software Pack for wifi</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="3.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="2.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LWIP" vendor="NXP" version="3.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.edgefast_wifi_nxp.condition_id">
      <require condition="allOf.middleware.wifi, middleware.freertos-kernel.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi, middleware.freertos-kernel.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi"/>
      <require Cclass="RTOS" Cgroup="Core"/>
    </condition>
    <condition id="middleware.wifi.condition_id">
      <require condition="allOf.anyOf=allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console, middleware.wifi.template, middleware.wifi.wifidriver.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console, middleware.wifi.template, middleware.wifi.wifidriver.internal_condition">
      <require condition="anyOf.allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console.internal_condition"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_template"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifidriver"/>
    </condition>
    <condition id="anyOf.allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console.internal_condition">
      <accept condition="allOf.utility.debug_console, not=utility.debug_console_lite.internal_condition"/>
      <accept condition="allOf.utility.debug_console_lite, not=utility.debug_console.internal_condition"/>
    </condition>
    <condition id="allOf.utility.debug_console, not=utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require condition="not.utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="not.utility.debug_console_lite.internal_condition">
      <deny Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="allOf.utility.debug_console_lite, not=utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
      <require condition="not.utility.debug_console.internal_condition"/>
    </condition>
    <condition id="not.utility.debug_console.internal_condition">
      <deny Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="middleware.wifi.cli.condition_id">
      <require condition="allOf.middleware.wifi, middleware.wifi.wifidriver, utility.debug_console.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi, middleware.wifi.wifidriver, utility.debug_console.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifidriver"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="middleware.wifi.common_files.condition_id">
      <require condition="allOf.middleware.wifi.osa.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi.osa.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa"/>
    </condition>
    <condition id="middleware.wifi.fwdnld.condition_id">
      <require condition="allOf.middleware.wifi.template, middleware.wifi.mlan_sdio, middleware.wifi.common_files, middleware.wifi.fwdnld_intf_abs.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi.template, middleware.wifi.mlan_sdio, middleware.wifi.common_files, middleware.wifi.fwdnld_intf_abs.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_template"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="mlan_sdio"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="common_files"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="fwdnld_intf_abs"/>
    </condition>
    <condition id="middleware.wifi.mlan_sdio.condition_id">
      <require condition="allOf.middleware.wifi.template, middleware.sdmmc.sdio, middleware.sdmmc.host.usdhc, middleware.wifi.common_files, anyOf=allOf=middleware.sdmmc.host.usdhc.freertos, allOf=middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi.template, middleware.sdmmc.sdio, middleware.sdmmc.host.usdhc, middleware.wifi.common_files, anyOf=allOf=middleware.sdmmc.host.usdhc.freertos, allOf=middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_template"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdio"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="common_files"/>
      <require condition="anyOf.allOf=middleware.sdmmc.host.usdhc.freertos, allOf=middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=middleware.sdmmc.host.usdhc.freertos, allOf=middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <accept condition="allOf.middleware.sdmmc.host.usdhc.freertos.internal_condition"/>
      <accept condition="allOf.middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.host.usdhc.freertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_freertos"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_azurertos"/>
    </condition>
    <condition id="middleware.wifi.ncp_supp_wmcrypto.condition_id">
      <require condition="allOf.middleware.wifi, middleware.wifi.wifidriver.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi, middleware.wifi.wifidriver.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifidriver"/>
    </condition>
    <condition id="middleware.wifi.net.condition_id">
      <require condition="allOf.anyOf=middleware.wifi.net_free_rtos, middleware.wifi.net_thread.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=middleware.wifi.net_free_rtos, middleware.wifi.net_thread.internal_condition">
      <require condition="anyOf.middleware.wifi.net_free_rtos, middleware.wifi.net_thread.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.wifi.net_free_rtos, middleware.wifi.net_thread.internal_condition">
      <accept Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_net_free_rtos"/>
      <accept Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_net_thread"/>
    </condition>
    <condition id="middleware.wifi.net_free_rtos.condition_id">
      <require condition="allOf.component.osa_free_rtos, middleware.lwip, middleware.lwip.apps.lwiperf.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa_free_rtos, middleware.lwip, middleware.lwip.apps.lwiperf.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_free_rtos"/>
      <require Cclass="Network" Cgroup="lwIP TCPIP Networking Stack" Csub="lwip"/>
      <require Cclass="Network" Cgroup="lwIP TCPIP Application protocols" Csub="lwiperf"/>
    </condition>
    <condition id="middleware.wifi.net_thread.condition_id">
      <require condition="allOf.component.osa_thread.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa_thread.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_thread"/>
    </condition>
    <condition id="middleware.wifi.osa.condition_id">
      <require condition="allOf.anyOf=middleware.wifi.osa_free_rtos, middleware.wifi.osa_thread.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=middleware.wifi.osa_free_rtos, middleware.wifi.osa_thread.internal_condition">
      <require condition="anyOf.middleware.wifi.osa_free_rtos, middleware.wifi.osa_thread.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.wifi.osa_free_rtos, middleware.wifi.osa_thread.internal_condition">
      <accept Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa_free_rtos"/>
      <accept Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa_thread"/>
    </condition>
    <condition id="middleware.wifi.osa_free_rtos.condition_id">
      <require condition="allOf.component.osa_free_rtos.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa_free_rtos.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_free_rtos"/>
    </condition>
    <condition id="middleware.wifi.osa_thread.condition_id">
      <require condition="allOf.component.osa_thread.internal_condition"/>
    </condition>
    <condition id="middleware.wifi.sdio.condition_id">
      <require condition="allOf.middleware.wifi.mlan_sdio, middleware.wifi.wifidriver.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi.mlan_sdio, middleware.wifi.wifidriver.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="mlan_sdio"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifidriver"/>
    </condition>
    <condition id="middleware.wifi.template.condition_id">
      <require condition="anyOf.allOf=component.wifi_bt_module.tx_pwr_limits, driver.imu, allOf=component.wifi_bt_module.tx_pwr_limits, component.wifi_bt_module.config, middleware.wifi.wifi_bt_config.template, not=driver.imu.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.wifi_bt_module.tx_pwr_limits, driver.imu, allOf=component.wifi_bt_module.tx_pwr_limits, component.wifi_bt_module.config, middleware.wifi.wifi_bt_config.template, not=driver.imu.internal_condition">
      <accept condition="allOf.component.wifi_bt_module.tx_pwr_limits, driver.imu.internal_condition"/>
      <accept condition="allOf.component.wifi_bt_module.tx_pwr_limits, component.wifi_bt_module.config, middleware.wifi.wifi_bt_config.template, not=driver.imu.internal_condition"/>
    </condition>
    <condition id="allOf.component.wifi_bt_module.tx_pwr_limits, driver.imu.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="tx_pwr_limits"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="imu"/>
    </condition>
    <condition id="allOf.component.wifi_bt_module.tx_pwr_limits, component.wifi_bt_module.config, middleware.wifi.wifi_bt_config.template, not=driver.imu.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="tx_pwr_limits"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="wifi_bt_module_config"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_bt_config_template"/>
      <require condition="not.driver.imu.internal_condition"/>
    </condition>
    <condition id="not.driver.imu.internal_condition">
      <deny Cclass="Device" Cgroup="SDK Drivers" Csub="imu"/>
    </condition>
    <condition id="middleware.wifi.wifidriver.condition_id">
      <require condition="allOf.anyOf=allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console, middleware.wifi.template, middleware.wifi.osa, middleware.wifi.net, anyOf=allOf=middleware.wifi.sdio, middleware.wifi.fwdnld, middleware.wifi.fwdnld_intf_abs.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console, middleware.wifi.template, middleware.wifi.osa, middleware.wifi.net, anyOf=allOf=middleware.wifi.sdio, middleware.wifi.fwdnld, middleware.wifi.fwdnld_intf_abs.internal_condition">
      <require condition="anyOf.allOf=utility.debug_console, not=utility.debug_console_lite, allOf=utility.debug_console_lite, not=utility.debug_console.internal_condition"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_template"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_net"/>
      <require condition="anyOf.allOf=middleware.wifi.sdio, middleware.wifi.fwdnld, middleware.wifi.fwdnld_intf_abs.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=middleware.wifi.sdio, middleware.wifi.fwdnld, middleware.wifi.fwdnld_intf_abs.internal_condition">
      <accept condition="allOf.middleware.wifi.sdio, middleware.wifi.fwdnld, middleware.wifi.fwdnld_intf_abs.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi.sdio, middleware.wifi.fwdnld, middleware.wifi.fwdnld_intf_abs.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="sdio"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="fwdnld"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="fwdnld_intf_abs"/>
    </condition>
    <condition id="middleware.wifi.wls.condition_id">
      <require condition="allOf.middleware.wifi, middleware.wifi.wifidriver.internal_condition"/>
    </condition>
    <condition id="middleware.wifi.wmcrypto.condition_id">
      <require condition="allOf.middleware.wifi, middleware.wifi.wifidriver.internal_condition"/>
    </condition>
    <condition id="middleware.wifi.wmtime.condition_id">
      <require condition="allOf.middleware.wifi, middleware.wifi.wifidriver.internal_condition"/>
    </condition>
    <condition id="middleware.wifi.wps.condition_id">
      <require condition="allOf.middleware.wifi, middleware.wifi.wifidriver, middleware.wifi.wmtime, middleware.wifi.wmcrypto.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.wifi, middleware.wifi.wifidriver, middleware.wifi.wmtime, middleware.wifi.wmcrypto.internal_condition">
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifidriver"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wmtime"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wmcrypto"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Wireless" Cgroup="Edgefast Wi-Fi" Cversion="1.0.0" condition="middleware.edgefast_wifi_nxp.condition_id">
      <description>Edgefast Wi-Fi NXP is a blocking layer for Wi-Fi NXP</description>
      <files>
        <file category="sourceC" name="components/edgefast_wifi/source/wpl_nxp.c" projectpath="edgefast_wifi/source"/>
        <file category="header" name="components/edgefast_wifi/include/wpl.h" projectpath="edgefast_wifi/include"/>
        <file category="include" name="components/edgefast_wifi/include/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi" Cversion="1.3.46" condition="middleware.wifi.condition_id">
      <description>NXP Wi-Fi functionality enables customers to quickly develop applications of interest to add connectivity to different sensors and appliances</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/dhcpd/dhcp-bootp.h" projectpath="wifi/dhcpd"/>
        <file category="header" name="middleware/wifi_nxp/dhcpd/dhcp-priv.h" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dhcp-server-main.c" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dhcp-server.c" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dns-server.c" projectpath="wifi/dhcpd"/>
        <file category="header" name="middleware/wifi_nxp/dhcpd/dns.h" projectpath="wifi/dhcpd"/>
        <file category="header" name="middleware/wifi_nxp/incl/port/net/wm_net.h" projectpath="wifi/incl/port/net"/>
        <file category="header" name="middleware/wifi_nxp/incl/wmstats.h" projectpath="wifi/incl"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/net/net.c" projectpath="wifi/port/net"/>
        <file category="header" name="middleware/wifi_nxp/port/net/netif_decl.h" projectpath="wifi/port/net"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/net/wifi_netif.c" projectpath="wifi/port/net"/>
        <file category="sourceC" name="middleware/wifi_nxp/wlcmgr/wlan.c" projectpath="wifi/wlcmgr"/>
        <file category="sourceC" name="middleware/wifi_nxp/wlcmgr/wlan_txpwrlimit_cfg.c" projectpath="wifi/wlcmgr"/>
        <file category="doc" name="middleware/wifi_nxp/ChangeLogKSDK.txt" projectpath="wifi"/>
        <file category="doc" name="middleware/wifi_nxp/CMakeLists.txt" projectpath="wifi"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/net/"/>
        <file category="include" name="middleware/wifi_nxp/port/net/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/net/hooks/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="cli" Cversion="1.3.46" condition="middleware.wifi.cli.condition_id">
      <description>Middlware Wi-Fi CLI</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/cli/cli_mem.h" projectpath="wifi/cli"/>
        <file category="sourceC" name="middleware/wifi_nxp/cli/cli.c" projectpath="wifi/cli"/>
        <file category="sourceC" name="middleware/wifi_nxp/cli/cli_mem_simple.c" projectpath="wifi/cli"/>
        <file category="sourceC" name="middleware/wifi_nxp/cli/cli_utils.c" projectpath="wifi/cli"/>
        <file category="header" name="middleware/wifi_nxp/cli/wifi_shell.h" projectpath="wifi/cli"/>
        <file category="sourceC" name="middleware/wifi_nxp/cli/wifi_shell.c" projectpath="wifi/cli"/>
        <file category="header" name="middleware/wifi_nxp/incl/cli.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/cli_utils.h" projectpath="wifi/incl"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/osa_cli.c" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dhcp-server-cli.c" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/nw_utils/wifi_ping.c" projectpath="wifi/nw_utils"/>
        <file category="sourceC" name="middleware/wifi_nxp/nw_utils/iperf.c" projectpath="wifi/nw_utils"/>
        <file category="header" name="middleware/wifi_nxp/incl/iperf.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wifi_ping.h" projectpath="wifi/incl"/>
        <file category="sourceC" name="middleware/wifi_nxp/nw_utils/init_enet.c" projectpath="wifi/nw_utils"/>
        <file category="header" name="middleware/wifi_nxp/nw_utils/network_cfg.h" projectpath="wifi/nw_utils"/>
        <file category="header" name="middleware/wifi_nxp/nw_utils/telnet/telnet_server.h" projectpath="wifi/nw_utils/telnet"/>
        <file category="sourceC" name="middleware/wifi_nxp/nw_utils/telnet/telnet_server.c" projectpath="wifi/nw_utils/telnet"/>
        <file category="sourceC" name="middleware/wifi_nxp/wlcmgr/wlan_basic_cli.c" projectpath="wifi/wlcmgr"/>
        <file category="sourceC" name="middleware/wifi_nxp/wlcmgr/wlan_enhanced_tests.c" projectpath="wifi/wlcmgr"/>
        <file category="sourceC" name="middleware/wifi_nxp/wlcmgr/wlan_tests.c" projectpath="wifi/wlcmgr"/>
        <file category="sourceC" name="middleware/wifi_nxp/wlcmgr/wlan_test_mode_tests.c" projectpath="wifi/wlcmgr"/>
        <file category="header" name="middleware/wifi_nxp/incl/wlcmgr/wlan_tests.h" projectpath="wifi/incl/wlcmgr"/>
        <file category="include" name="middleware/wifi_nxp/cli/"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wlcmgr/"/>
        <file category="include" name="middleware/wifi_nxp/nw_utils/"/>
        <file category="include" name="middleware/wifi_nxp/nw_utils/telnet/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="common_files" Cversion="1.0.0" condition="middleware.wifi.common_files.condition_id">
      <description>NXP WLAN common files</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/wifi_config_default.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wifidriver/wifi-decl.h" projectpath="wifi/incl/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/incl/wifidriver/wifi.h" projectpath="wifi/incl/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/incl/wifidriver/wifi_events.h" projectpath="wifi/incl/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/incl/wifi_cal_data_ext.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wm_utils.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wmerrno.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wmlog.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wmtypes.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_decl.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_ioctl.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/type_decls.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/wlan_bt_fw.h" projectpath="wifi/wifi_bt_firmware"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/wifi_bt_firmware/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/incl/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="fwdnld" Cversion="1.0.0" condition="middleware.wifi.fwdnld.condition_id">
      <description>NXP WLAN f/w dnld driver</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/WIFI_IW416_BOARD_AW_AM457_CAL_DATA_EXT.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_main_defs.h" projectpath="wifi/wifidriver/incl"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/8801/sd8801_wlan.bin.inc" projectpath="wifi/wifi_bt_firmware/8801"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/8801/sd8801_wlan.h" projectpath="wifi/wifi_bt_firmware/8801"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/8801/8801_cpu1.c" projectpath="wifi/wifi_bt_firmware/8801"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/sduartIW416_wlan_bt.bin.inc" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/sdIW416_wlan.bin.inc" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/uartIW416_bt.bin.inc" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/sduartIW416_wlan_bt.h" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/sdIW416_wlan.h" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/uartIW416_bt.h" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/IW416_cpu12.c" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/IW416_cpu1.c" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/IW416_cpu2.c" projectpath="wifi/wifi_bt_firmware/IW416"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/8987/sduart8987_wlan_bt.bin.inc" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/8987/sd8987_wlan.bin.inc" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/8987/uart8987_bt.bin.inc" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/8987/sduart8987_wlan_bt.h" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/8987/sd8987_wlan.h" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/8987/uart8987_bt.h" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/8987/8987_cpu12.c" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/8987/8987_cpu1.c" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/8987/8987_cpu2.c" projectpath="wifi/wifi_bt_firmware/8987"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/sduart_nw61x.bin.se.inc" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/sd_nw61x.bin.se.inc" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="other" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/uart_nw61x.bin.se.inc" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/sduart_nw61x_se.h" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/sd_nw61x_se.h" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="header" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/uart_nw61x_se.h" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/nw61x_cpu12_se.c" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/nw61x_cpu1_se.c" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/nw61x_cpu2_se.c" projectpath="wifi/wifi_bt_firmware/nw61x"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/sdio.c" projectpath="wifi/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/sdio.h" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/firmware_dnld/firmware_dnld.c" projectpath="wifi/firmware_dnld"/>
        <file category="header" name="middleware/wifi_nxp/firmware_dnld/firmware_dnld.h" projectpath="wifi/firmware_dnld"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/wifi_bt_firmware/"/>
        <file category="include" name="middleware/wifi_nxp/wifi_bt_firmware/8801/"/>
        <file category="include" name="middleware/wifi_nxp/wifi_bt_firmware/IW416/"/>
        <file category="include" name="middleware/wifi_nxp/wifi_bt_firmware/8987/"/>
        <file category="include" name="middleware/wifi_nxp/wifi_bt_firmware/nw61x/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/incl/"/>
        <file category="include" name="middleware/wifi_nxp/firmware_dnld/"/>
        <file category="include" name="middleware/wifi_nxp/sdio_nxp_abs/"/>
        <file category="include" name="middleware/wifi_nxp/sdio_nxp_abs/incl/"/>
        <file category="include" name="middleware/wifi_nxp/fwdnld_intf_abs/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="fwdnld_intf_abs" Cversion="1.3.46">
      <description>NXP Wi-Fi Interface Abstraction Layer</description>
      <files>
        <file category="sourceC" name="middleware/wifi_nxp/fwdnld_intf_abs/fwdnld_intf_abs.c" projectpath="wifi/fwdnld_intf_abs"/>
        <file category="header" name="middleware/wifi_nxp/fwdnld_intf_abs/fwdnld_intf_abs.h" projectpath="wifi/fwdnld_intf_abs"/>
        <file category="include" name="middleware/wifi_nxp/fwdnld_intf_abs/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="mlan_sdio" Cversion="1.3.46" condition="middleware.wifi.mlan_sdio.condition_id">
      <description>NXP Wi-Fi SDIO driver</description>
      <files>
        <file category="sourceC" name="middleware/wifi_nxp/sdio_nxp_abs/mlan_sdio.c" projectpath="wifi/sdio_nxp_abs"/>
        <file category="sourceC" name="middleware/wifi_nxp/sdio_nxp_abs/fwdnld_sdio.c" projectpath="wifi/sdio_nxp_abs"/>
        <file category="header" name="middleware/wifi_nxp/sdio_nxp_abs/incl/fwdnld_sdio.h" projectpath="wifi/sdio_nxp_abs/incl"/>
        <file category="header" name="middleware/wifi_nxp/sdio_nxp_abs/incl/mlan_sdio_defs.h" projectpath="wifi/sdio_nxp_abs/incl"/>
        <file category="header" name="middleware/wifi_nxp/sdio_nxp_abs/incl/mlan_sdio.h" projectpath="wifi/sdio_nxp_abs/incl"/>
        <file category="header" name="middleware/wifi_nxp/sdio_nxp_abs/incl/mlan_sdio_api.h" projectpath="wifi/sdio_nxp_abs/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/wifi-sdio.h" projectpath="wifi/wifidriver"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/sdio_nxp_abs/incl/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="ncp_supp" Cversion="1.0.0" condition="middleware.wifi.ncp_supp_wmcrypto.condition_id">
      <description>Middlware Wi-Fi NCP_SUPP_WMCRYPTO</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/wmcrypto/wmcrypto.h" projectpath="wifi/incl/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wmcrypto_mem.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wmcrypto_mem.h" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_debug.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_entropy.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_entropy.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_mem.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_mem.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_net.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_net.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_helper_api.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_helper_api.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_utils.c" projectpath="wifi/wmcrypto"/>
        <file category="include" name="middleware/wifi_nxp/wmcrypto/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wmcrypto/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_net" Cversion="1.0.0" condition="middleware.wifi.net.condition_id">
      <description>NXP WLAN OSA</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/port/net/wm_net.h" projectpath="wifi/incl/port/net" path="middleware/wifi_nxp"/>
        <file category="include" name="middleware/wifi_nxp/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_net_free_rtos" Cversion="1.0.0" condition="middleware.wifi.net_free_rtos.condition_id">
      <description>NXP WLAN OSA</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/port/net/wm_net.h" projectpath="wifi/incl/port/net"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/net/net.c" projectpath="wifi/port/net"/>
        <file category="header" name="middleware/wifi_nxp/port/net/netif_decl.h" projectpath="wifi/port/net" path="middleware/wifi_nxp"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/net/wifi_netif.c" projectpath="wifi/port/net"/>
        <file category="header" name="middleware/wifi_nxp/incl/port/net/hooks/lwip_default_hooks.h" projectpath="wifi/incl/port/net/hooks"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/net/hooks/lwip_default_hooks.c" projectpath="wifi/port/net/hooks"/>
        <file category="header" name="middleware/wifi_nxp/dhcpd/dhcp-bootp.h" projectpath="wifi/dhcpd"/>
        <file category="header" name="middleware/wifi_nxp/dhcpd/dhcp-priv.h" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dhcp-server-main.c" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dhcp-server.c" projectpath="wifi/dhcpd"/>
        <file category="sourceC" name="middleware/wifi_nxp/dhcpd/dns-server.c" projectpath="wifi/dhcpd"/>
        <file category="header" name="middleware/wifi_nxp/dhcpd/dns.h" projectpath="wifi/dhcpd"/>
        <file category="include" name="middleware/wifi_nxp/"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/net/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/net/hooks/"/>
        <file category="include" name="middleware/wifi_nxp/dhcpd/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wlcmgr/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/incl/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_net_thread" Cversion="1.0.0" condition="middleware.wifi.net_thread.condition_id">
      <description>NXP WLAN OSA</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/port/net/wm_net.h" projectpath="wifi/incl/port/net"/>
        <file category="include" name="middleware/wifi_nxp/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/net/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa" Cversion="1.0.0" condition="middleware.wifi.osa.condition_id">
      <description>NXP WLAN OSA</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/port/osa/osa.h" projectpath="wifi/incl/port/osa"/>
        <file category="header" name="middleware/wifi_nxp/incl/port/osa/mem_pool.h" projectpath="wifi/incl/port/osa"/>
        <file category="header" name="middleware/wifi_nxp/incl/port/osa/mem_pool_config.h" projectpath="wifi/incl/port/osa"/>
        <file category="header" name="middleware/wifi_nxp/incl/port/osa/slist.h" projectpath="wifi/incl/port/osa"/>
        <file category="header" name="middleware/wifi_nxp/incl/port/osa/stack_simple.h" projectpath="wifi/incl/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/osa.c" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/mem_pool.c" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/mem_pool_config.c" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/slist.c" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/stack_simple.c" projectpath="wifi/port/osa"/>
        <file category="include" name="middleware/wifi_nxp/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa_free_rtos" Cversion="1.0.0" condition="middleware.wifi.osa_free_rtos.condition_id">
      <description>NXP WLAN OSA</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/port/osa/osa_freertos.h" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/osa_freertos.c" projectpath="wifi/port/osa"/>
        <file category="include" name="middleware/wifi_nxp/"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/port/osa/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="nxp_wifi_osa_thread" Cversion="1.0.0" condition="middleware.wifi.osa_thread.condition_id">
      <description>NXP WLAN OSA</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/port/osa/osa_threadx.h" projectpath="wifi/port/osa"/>
        <file category="sourceC" name="middleware/wifi_nxp/port/osa/osa_threadx.c" projectpath="wifi/port/osa"/>
        <file category="include" name="middleware/wifi_nxp/"/>
        <file category="include" name="middleware/wifi_nxp/incl/port/osa/"/>
        <file category="include" name="middleware/wifi_nxp/port/osa/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="sdio" Cversion="1.3.46" condition="middleware.wifi.sdio.condition_id">
      <description>NXP Wi-Fi SDIO driver</description>
      <files>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi-sdio.c" projectpath="wifi/wifidriver"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_template" Cversion="1.0.0" condition="middleware.wifi.template.condition_id">
      <description>Template configuration file to be edited by user.</description>
      <Pre_Include_Global_h>
#ifndef OSA_USED
#define OSA_USED 
#endif
#ifndef FSL_OSA_TASK_ENABLE
#define FSL_OSA_TASK_ENABLE 1
#endif
#ifndef LWIP_DNS
#define LWIP_DNS 1
#endif
#ifndef LWIP_NETIF_HOSTNAME
#define LWIP_NETIF_HOSTNAME 1
#endif
#ifndef LWIP_IGMP
#define LWIP_IGMP 1
#endif
#ifndef _XOPEN_SOURCE
#define _XOPEN_SOURCE 500
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" attr="config" name="components/wifi_bt_module/template/app_config.h" version="1.0.0" projectpath="wifi/template"/>
        <file category="header" attr="config" name="components/wifi_bt_module/template/wifi_config.h" version="1.0.0" projectpath="wifi/template"/>
        <file category="include" name="components/wifi_bt_module/template/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi_bt_config_template" Cversion="1.0.0">
      <description>Template configuration file to be edited by user.</description>
      <Pre_Include_Global_h>
#ifndef SDIO_ENABLED
#define SDIO_ENABLED 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" attr="config" name="components/wifi_bt_module/template/wifi_bt_config.h" version="1.0.0" projectpath="wifi/template"/>
        <file category="sourceC" attr="config" name="components/wifi_bt_module/template/wifi_bt_config.c" version="1.0.0" projectpath="wifi/template"/>
        <file category="include" name="components/wifi_bt_module/template/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifidriver" Cversion="1.3.46" condition="middleware.wifi.wifidriver.condition_id">
      <description>NXP Wi-Fi driver</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/dhcp-server.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wlcmgr/wlan.h" projectpath="wifi/incl/wlcmgr"/>
        <file category="header" name="middleware/wifi_nxp/incl/nxp_wifi.h" projectpath="wifi/incl"/>
        <file category="header" name="middleware/wifi_nxp/incl/wlcmgr/wlan_11d.h" projectpath="wifi/incl/wlcmgr"/>
        <file category="header" name="middleware/wifi_nxp/incl/wifidriver/wifi_nxp.h" projectpath="wifi/incl/wifidriver" path="middleware/wifi_nxp/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11ac.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11ax.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11h.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11n.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11n_aggr.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11n_rxreorder.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11v.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_action.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_11k.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_mbo.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_api.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_fw.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_ieee.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_init.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_join.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_main.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_meas.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_remap_mem_operations.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_uap.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_util.h" projectpath="wifi/wifidriver/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/incl/mlan_wmm.h" projectpath="wifi/wifidriver/incl"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11ac.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11ax.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11d.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11h.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11n.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11n_aggr.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11n_rxreorder.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11v.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_action.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_11k.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_mbo.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_api.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_cfp.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_cmdevt.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_glue.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_init.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_join.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_misc.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_scan.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_shim.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_sta_cmd.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_sta_cmdresp.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_sta_event.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_sta_ioctl.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_sta_rx.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_txrx.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_uap_cmdevent.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_uap_ioctl.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/mlan_wmm.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi-debug.c" projectpath="wifi/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/wifi-debug.h" projectpath="wifi/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/wifi-internal.h" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi-mem.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi-uap.c" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi.c" projectpath="wifi/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/wifi_common.h" projectpath="wifi/wifidriver"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi_pwrmgr.c" projectpath="wifi/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/incl/rtos_wpa_supp_if.h" projectpath="wifi/wifidriver/wpa_supp_if/incl"/>
        <file category="header" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/incl/wifi_nxp_internal.h" projectpath="wifi/wifidriver/wpa_supp_if/incl"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/wifi_nxp.c" projectpath="wifi/wifidriver/wpa_supp_if"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/rtos_wpa_supp_if.c" projectpath="wifi/wifidriver/wpa_supp_if"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/wifi_nxp_internal.c" projectpath="wifi/wifidriver/wpa_supp_if"/>
        <file category="sourceC" name="middleware/wifi_nxp/wifidriver/wifi-wps.c" projectpath="wifi/wifidriver"/>
        <file category="header" name="middleware/wifi_nxp/certs/ca-cert.h" projectpath="wifi/certs"/>
        <file category="header" name="middleware/wifi_nxp/certs/client-cert.h" projectpath="wifi/certs"/>
        <file category="header" name="middleware/wifi_nxp/certs/client-key.h" projectpath="wifi/certs"/>
        <file category="header" name="middleware/wifi_nxp/certs/server-cert.h" projectpath="wifi/certs"/>
        <file category="header" name="middleware/wifi_nxp/certs/server-key.h" projectpath="wifi/certs"/>
        <file category="header" name="middleware/wifi_nxp/certs/dh-param.h" projectpath="wifi/certs"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wlcmgr/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/incl/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/"/>
        <file category="include" name="middleware/wifi_nxp/wifidriver/wpa_supp_if/incl/"/>
        <file category="include" name="middleware/wifi_nxp/certs/"/>
        <file category="include" name="middleware/wifi_nxp/firmware_dnld/"/>
        <file category="include" name="middleware/wifi_nxp/sdio_nxp_abs/"/>
        <file category="include" name="middleware/wifi_nxp/sdio_nxp_abs/incl/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wls" Cversion="1.3.46" condition="middleware.wifi.wls.condition_id">
      <description>Middlware Wi-Fi Location Service</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/wls/range_kalman.h" projectpath="wifi/wls"/>
        <file category="sourceC" name="middleware/wifi_nxp/wls/range_kalman.c" projectpath="wifi/wls"/>
        <file category="sourceC" name="middleware/wifi_nxp/wls/wls_api.c" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_api.h" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_param_defines.h" projectpath="wifi/wls"/>
        <file category="sourceC" name="middleware/wifi_nxp/wls/wls_processing.c" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_processing.h" projectpath="wifi/wls"/>
        <file category="sourceC" name="middleware/wifi_nxp/wls/wls_QR_algorithm.c" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_QR_algorithm.h" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_radix4Fft.h" projectpath="wifi/wls"/>
        <file category="sourceC" name="middleware/wifi_nxp/wls/wls_radix4Fft.c" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_structure_defs.h" projectpath="wifi/wls"/>
        <file category="header" name="middleware/wifi_nxp/wls/wls_subspace_processing.h" projectpath="wifi/wls"/>
        <file category="sourceC" name="middleware/wifi_nxp/wls/wls_subspace_processing.c" projectpath="wifi/wls"/>
        <file category="include" name="middleware/wifi_nxp/wls/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wmcrypto" Cversion="1.0.0" condition="middleware.wifi.wmcrypto.condition_id">
      <description>Middlware Wi-Fi WMCRYPTO</description>
      <files>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wmcrypto.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/incl/wmcrypto/wmcrypto.h" projectpath="wifi/incl/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wmcrypto_mem.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wmcrypto_mem.h" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_debug.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_entropy.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_entropy.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_mem.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_mem.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_net.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_net.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_helper_api.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/wm_mbedtls_helper_api.h" projectpath="wifi/wmcrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/wm_utils.c" projectpath="wifi/wmcrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes_siv.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes-siv.c" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes-ctr.c" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes-omac1.c" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="sourceC" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes-wrap.c" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/aes_wrap.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/common.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/crypto.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/includes.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="header" name="middleware/wifi_nxp/wmcrypto/aescrypto/type_def.h" projectpath="wifi/wmcrypto/aescrypto"/>
        <file category="include" name="middleware/wifi_nxp/wmcrypto/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wmcrypto/"/>
        <file category="include" name="middleware/wifi_nxp/wmcrypto/aescrypto/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wmtime" Cversion="1.0.0" condition="middleware.wifi.wmtime.condition_id">
      <description>Middlware Wi-Fi WMTIME</description>
      <files>
        <file category="sourceC" name="middleware/wifi_nxp/wmtime/wmtime.c" projectpath="wifi/wmtime"/>
        <file category="header" name="middleware/wifi_nxp/incl/wmtime.h" projectpath="wifi/incl"/>
        <file category="include" name="middleware/wifi_nxp/wmtime/"/>
        <file category="include" name="middleware/wifi_nxp/incl/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wps" Cversion="1.0.0" condition="middleware.wifi.wps.condition_id">
      <description>Middlware Wi-Fi WPS</description>
      <files>
        <file category="header" name="middleware/wifi_nxp/incl/wps/wifi_nxp_wps.h" projectpath="wifi/incl/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_def.h" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_eapol.c" projectpath="wifi/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_eapol.h" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_l2.c" projectpath="wifi/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_l2.h" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_main.c" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_mem.c" projectpath="wifi/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_mem.h" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_msg.c" projectpath="wifi/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_msg.h" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_os.c" projectpath="wifi/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_os.h" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_start.c" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_state.c" projectpath="wifi/wps"/>
        <file category="sourceC" name="middleware/wifi_nxp/wps/wps_wlan.c" projectpath="wifi/wps"/>
        <file category="header" name="middleware/wifi_nxp/wps/wps_wlan.h" projectpath="wifi/wps"/>
        <file category="include" name="middleware/wifi_nxp/wps/"/>
        <file category="include" name="middleware/wifi_nxp/incl/wps/"/>
      </files>
    </component>
    <component Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="coex" Cversion="1.0.0">
      <description>Middlware Coex</description>
      <files>
        <file category="header" name="middleware/wireless/coex/boards/rw612/app_config.h" projectpath="coex/boards/rw612"/>
        <file category="sourceC" name="middleware/wireless/coex/boards/rw612/app_services_init.c" projectpath="coex/boards/rw612"/>
        <file category="sourceC" name="middleware/wireless/coex/boards/rw612/board.c" projectpath="coex/boards/rw612"/>
        <file category="header" name="middleware/wireless/coex/boards/rw612/board.h" projectpath="coex/boards/rw612"/>
        <file category="sourceC" name="middleware/wireless/coex/boards/rw612/clock_config.c" projectpath="coex/boards/rw612"/>
        <file category="header" name="middleware/wireless/coex/boards/rw612/clock_config.h" projectpath="coex/boards/rw612"/>
        <file category="doc" name="middleware/wireless/coex/boards/rw612/CMakeLists.txt" projectpath="coex/boards/rw612"/>
        <file category="header" name="middleware/wireless/coex/boards/rw612/FreeRTOSConfig.h" projectpath="coex/boards/rw612"/>
        <file category="sourceC" name="middleware/wireless/coex/boards/rw612/hardware_init.c" projectpath="coex/boards/rw612"/>
        <file category="sourceC" name="middleware/wireless/coex/boards/rw612/peripherals.c" projectpath="coex/boards/rw612"/>
        <file category="header" name="middleware/wireless/coex/boards/rw612/peripherals.h" projectpath="coex/boards/rw612"/>
        <file category="sourceC" name="middleware/wireless/coex/boards/rw612/pin_mux.c" projectpath="coex/boards/rw612"/>
        <file category="header" name="middleware/wireless/coex/boards/rw612/pin_mux.h" projectpath="coex/boards/rw612"/>
        <file category="doc" name="middleware/wireless/coex/boards/CMakeLists.txt" projectpath="coex/boards"/>
        <file category="other" name="middleware/wireless/coex/cmake/platform/rw612/config_frdmrw612.cmake" projectpath="coex/cmake/platform/rw612"/>
        <file category="other" name="middleware/wireless/coex/cmake/platform/rw612/config_rdrw612bga.cmake" projectpath="coex/cmake/platform/rw612"/>
        <file category="other" name="middleware/wireless/coex/cmake/platform/rw612/flags.cmake" projectpath="coex/cmake/platform/rw612"/>
        <file category="other" name="middleware/wireless/coex/cmake/platform/rw612/rw612.cmake" projectpath="coex/cmake/platform/rw612"/>
        <file category="other" name="middleware/wireless/coex/cmake/toolchain/arm-none-eabi.cmake" projectpath="coex/cmake/toolchain"/>
        <file category="other" name="middleware/wireless/coex/cmake/toolchain/armgcc_force_cpp.cmake" projectpath="coex/cmake/toolchain"/>
        <file category="other" name="middleware/wireless/coex/cmake/toolchain/armgcc.cmake" projectpath="coex/cmake/toolchain"/>
        <file category="other" name="middleware/wireless/coex/cmake/toolchain/mcux_config.cmake" projectpath="coex/cmake/toolchain"/>
        <file category="other" name="middleware/wireless/coex/cmake/toolchain/xcc.cmake" projectpath="coex/cmake/toolchain"/>
        <file category="other" name="middleware/wireless/coex/cmake/toolchain/xclang.cmake" projectpath="coex/cmake/toolchain"/>
        <file category="other" name="middleware/wireless/coex/cmake/options.cmake" projectpath="coex/cmake"/>
        <file category="other" name="middleware/wireless/coex/cmake/utils.cmake" projectpath="coex/cmake"/>
        <file category="doc" name="middleware/wireless/coex/examples/coex_app/CMakeLists.txt" projectpath="coex/examples/coex_app"/>
        <file category="sourceC" name="middleware/wireless/coex/examples/coex_app/main.c" projectpath="coex/examples/coex_app"/>
        <file category="doc" name="middleware/wireless/coex/examples/coex_app/readme.md" projectpath="coex/examples/coex_app"/>
        <file category="linkerScript" name="middleware/wireless/coex/examples/linker/RW61x_flash.ld" projectpath="coex/examples/linker"/>
        <file category="doc" name="middleware/wireless/coex/examples/CMakeLists.txt" projectpath="coex/examples"/>
        <file category="other" name="middleware/wireless/coex/script/build_rw612" projectpath="coex/script"/>
        <file category="other" name="middleware/wireless/coex/script/make-pretty" projectpath="coex/script"/>
        <file category="doc" name="middleware/wireless/coex/src/common/CMakeLists.txt" projectpath="coex/src/common"/>
        <file category="sourceC" name="middleware/wireless/coex/src/common/controller_coex_nxp.c" projectpath="coex/src/common"/>
        <file category="sourceC" name="middleware/wireless/coex/src/common/host_msd_fatfs.c" projectpath="coex/src/common"/>
        <file category="header" name="middleware/wireless/coex/src/common/host_msd_fatfs.h" projectpath="coex/src/common"/>
        <file category="header" name="middleware/wireless/coex/src/common/ringtone.h" projectpath="coex/src/common"/>
        <file category="header" name="middleware/wireless/coex/src/configs/ble/app_bluetooth_config.h" projectpath="coex/src/configs/ble"/>
        <file category="header" name="middleware/wireless/coex/src/configs/ble/edgefast_bluetooth_audio_config.h" projectpath="coex/src/configs/ble"/>
        <file category="header" name="middleware/wireless/coex/src/configs/ble/edgefast_bluetooth_config.h" projectpath="coex/src/configs/ble"/>
        <file category="header" name="middleware/wireless/coex/src/configs/ble/edgefast_bluetooth_debug_config.h" projectpath="coex/src/configs/ble"/>
        <file category="header" name="middleware/wireless/coex/src/configs/lwip/lwiphooks.h" projectpath="coex/src/configs/lwip"/>
        <file category="header" name="middleware/wireless/coex/src/configs/lwip/lwipopts.h" projectpath="coex/src/configs/lwip"/>
        <file category="header" name="middleware/wireless/coex/src/configs/lwip/lwippools.h" projectpath="coex/src/configs/lwip"/>
        <file category="header" name="middleware/wireless/coex/src/configs/mbedtls/mbedtls_config_client.h" projectpath="coex/src/configs/mbedtls"/>
        <file category="header" name="middleware/wireless/coex/src/configs/platform/ffconf.h" projectpath="coex/src/configs/platform"/>
        <file category="header" name="middleware/wireless/coex/src/configs/platform/usb_host_config.h" projectpath="coex/src/configs/platform"/>
        <file category="header" name="middleware/wireless/coex/src/configs/wifi/wifi_config.h" projectpath="coex/src/configs/wifi"/>
        <file category="doc" name="middleware/wireless/coex/src/configs/CMakeLists.txt" projectpath="coex/src/configs"/>
        <file category="sourceC" name="middleware/wireless/coex/src/edgefast/coex_shell.c" projectpath="coex/src/edgefast"/>
        <file category="header" name="middleware/wireless/coex/src/edgefast/coex_shell.h" projectpath="coex/src/edgefast"/>
        <file category="doc" name="middleware/wireless/coex/src/edgefast/CMakeLists.txt" projectpath="coex/src/edgefast"/>
        <file category="doc" name="middleware/wireless/coex/src/CMakeLists.txt" projectpath="coex/src"/>
        <file category="doc" name="middleware/wireless/coex/third_party/CMakeLists.txt" projectpath="coex/third_party"/>
        <file category="doc" name="middleware/wireless/coex/third_party/mcu-sdk/readme.md" projectpath="coex/third_party/mcu-sdk"/>
        <file category="other" name="middleware/wireless/coex/.gitmodules" projectpath="coex"/>
        <file category="other" name="middleware/wireless/coex/add_coex_v3.yml" projectpath="coex"/>
        <file category="utility" name="middleware/wireless/coex/build_coex.sh" projectpath="coex"/>
        <file category="doc" name="middleware/wireless/coex/CMakeLists.txt" projectpath="coex"/>
        <file category="other" name="middleware/wireless/coex/LICENSE" projectpath="coex"/>
        <file category="doc" name="middleware/wireless/coex/README.md" projectpath="coex"/>
        <file category="include" name="middleware/wireless/coex/"/>
        <file category="include" name="middleware/wireless/coex/boards/rw612/"/>
        <file category="include" name="middleware/wireless/coex/cmake/"/>
        <file category="include" name="middleware/wireless/coex/cmake/platform/rw612/"/>
        <file category="include" name="middleware/wireless/coex/cmake/toolchain/"/>
        <file category="include" name="middleware/wireless/coex/examples/"/>
        <file category="include" name="middleware/wireless/coex/examples/coex_app/"/>
        <file category="include" name="middleware/wireless/coex/examples/linker/"/>
        <file category="include" name="middleware/wireless/coex/script/"/>
        <file category="include" name="middleware/wireless/coex/src/"/>
        <file category="include" name="middleware/wireless/coex/src/common/"/>
        <file category="include" name="middleware/wireless/coex/src/configs/"/>
        <file category="include" name="middleware/wireless/coex/src/configs/ble/"/>
        <file category="include" name="middleware/wireless/coex/src/configs/lwip/"/>
        <file category="include" name="middleware/wireless/coex/src/configs/mbedtls/"/>
        <file category="include" name="middleware/wireless/coex/src/configs/platform/"/>
        <file category="include" name="middleware/wireless/coex/src/configs/wifi/"/>
        <file category="include" name="middleware/wireless/coex/src/edgefast/"/>
        <file category="include" name="middleware/wireless/coex/third_party/"/>
        <file category="include" name="middleware/wireless/coex/third_party/mcu-sdk/"/>
      </files>
    </component>
  </components>
</package>
