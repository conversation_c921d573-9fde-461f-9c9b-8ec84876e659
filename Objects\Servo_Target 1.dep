Dependencies for Project 'Servo', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (.\main.c)(0x688D4607)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/main.o -MMD)
I (main.h)(0x67F3BFBD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (json_parser.h)(0x68487D84)
I (motor_unified_config.h)(0x688BB252)
I (motor_config.h)(0x68445FF8)
I (motor_autocalibration.h)(0x684D3D40)
F (.\main.h)(0x67F3BFBD)()
F (.\UserFunction.c)(0x688D1C93)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/userfunction.o -MMD)
I (main.h)(0x67F3BFBD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (json_parser.h)(0x68487D84)
I (motor_unified_config.h)(0x688BB252)
F (.\UserFunction.h)(0x688D45F6)()
F (.\motor_autocalibration.c)(0x684E7708)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/motor_autocalibration.o -MMD)
I (motor_autocalibration.h)(0x684D3D40)
I (motor_unified_config.h)(0x688BB252)
I (UserFunction.h)(0x688D45F6)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
F (.\motor_unified_config.c)(0x684C1404)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/motor_unified_config.o -MMD)
I (motor_unified_config.h)(0x688BB252)
I (UserFunction.h)(0x688D45F6)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (main.h)(0x67F3BFBD)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
F (.\json_parser.c)(0x688D45EC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/json_parser.o -MMD)
I (json_parser.h)(0x68487D84)
I (main.h)(0x67F3BFBD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
I (motor_unified_config.h)(0x688BB252)
I (motor_config.h)(0x68445FF8)
I (motor_autocalibration.h)(0x684D3D40)
F (.\json_parser.h)(0x68487D84)()
F (.\motor_autocalibration.h)(0x684D3D40)()
F (.\motor_unified_config.h)(0x688BB252)()
F (.\IO_gpio.c)(0x67BC4375)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/io_gpio.o -MMD)
I (IO_gpio.h)(0x67BC4398)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
F (.\IO_gpio.h)(0x67BC4398)()
F (.\Rcc.c)(0x67E66ED4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/rcc.o -MMD)
I (main.h)(0x67F3BFBD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
F (.\Rcc.h)(0x66D98E0B)()
F (.\Timers.c)(0x67E687C1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/timers.o -MMD)
I (main.h)(0x67F3BFBD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
F (.\Timers.h)(0x67E66ED4)()
F (.\I2C.c)(0x6724BC14)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/i2c.o -MMD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (main.h)(0x67F3BFBD)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
F (.\I2C.h)(0x6724BC14)()
F (.\lcd.c)(0x6724CBE1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/lcd.o -MMD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
I (main.h)(0x67F3BFBD)
I (IO_gpio.h)(0x67BC4398)
I (Timers.h)(0x67E66ED4)
I (Rcc.h)(0x66D98E0B)
I (UserFunction.h)(0x688D45F6)
I (lcd.h)(0x6724C497)
I (I2C.h)(0x6724BC14)
F (.\lcd.h)(0x6724C497)()
F (RTE/Device/STM32F103ZE/RTE_Device.h)(0x6832D6C6)()
F (RTE/Device/STM32F103ZE/startup_config.h)(0x6832D77B)()
F (RTE/Device/STM32F103ZE/startup_stm32f10x_hd.s)(0x6832D6C3)(--target=arm-arm-none-eabi -mcpu=cortex-m3 -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"STM32F10X_HD SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o ./objects/startup_stm32f10x_hd.o)
F (RTE/Device/STM32F103ZE/system_stm32f10x.c)(0x6832D6C3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m3 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier

-I./RTE/Device/STM32F103ZE

-I./RTE/_Target_1

-IC:/Users/<USER>/AppData/Local/arm/packs/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Users/<USER>/AppData/Local/arm/packs/Keil/STM32F1xx_DFP/2.4.1/Device/Include

-D__UVISION_VERSION="542" -DSTM32F10X_HD -D_RTE_

-o ./objects/system_stm32f10x.o -MMD)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\stm32f10x.h)(0x6832D6C3)
I (RTE\_Target_1\RTE_Components.h)(0x688D42C3)
I (C:\Users\<USER>\AppData\Local\arm\packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm3.h)(0x664CAB78)
I (C:\Users\<USER>\AppData\Local\arm\packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include\system_stm32f10x.h)(0x6832D6C3)
