<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.7.26" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.26/schema/PACK.xsd">
  <vendor>YTMicro</vendor>
  <name>YTM32Z1MD0_DFP</name>
  <description>Device Family Pack for YTM32Z1MD0</description>
  <url>https://doc.ytmicro.com/keil/</url>

  <releases>
    <release version="1.0.0" date="2024-04-11"> 
      First release.
    </release>
  </releases>

  <keywords>
    <keyword>YTMicro</keyword>
    <keyword>YTM32Z1MD0</keyword>
    <keyword>Device Family Pack</keyword>
  </keywords>

  <devices>
    <family Dfamily="YTM32Zx" Dvendor="YTMicro:180">

      <!-- ******************************  Subfamily 'YTM32Z1M'  ****************************** -->
      <subFamily DsubFamily="YTM32Z1MD0">
        <processor Dcore="Cortex-M0+" DcoreVersion="r0p1" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="48000000" /> 
        <compile header="Device/Include/YTM32Z1MD0.h"/>
        <debug svd="SVD/YTM32Z1MD0.svd"/>
        <algorithm name="Flash/YTM32Z1MD0_Main.FLM" start="0x00000000" size="0x8000" default="1"/>
        <algorithm name="Flash/YTM32Z1MD0_Main.FLM" start="0x10000200" size="0x600" default="1"/>
        <memory name="IRAM1" access="rwx" start="0x20000000" size="0x2000" default="1"/>
        <memory name="IROM1" access="rx" start="0x00000000" size="0xC000" default="1"/>
        <memory name="IROM2" access="rx" start="0x10000200" size="0x600" default="1"/>
        <description>YTM32Z1MD0 48MHz MCU based on ARM Cortex-M0+ Core </description>

        <device Dname="YTM32Z1MD04">
          <description>QFN32 package. 48KB Flash, 1536B NVR, 8KB SRAM</description>
        </device>
      </subFamily>
    
    </family>
  </devices>

  <taxonomy>
    <description Cclass="Device" Cgroup="Startup">YTMicro SDK Startup</description>
    <description Cclass="Device" Cgroup="SDK Drivers">YTMicro SDK Peripheral Drivers</description>
  </taxonomy>

  <conditions>
    <condition id="YTM32Z1MD0">
      <description>YTMicro YTM32Z1MD0x device</description>
      <require Dvendor="YTMicro:180" Dname="YTM32Z1MD0*"/>
    </condition>

    <!-- Device + Compiler Conditions (GCC) -->
    <condition id="YTM32Z1M_GCC">
      <description>YTMicro device and GCC compiler</description>
      <require Tcompiler="GCC"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="YTM32Z1MD0">
      <description>Startup for MCU</description>
      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/startup.c" attr="config" version="1.0.0"/>
        <file category="source"  name="Device/Source/system_YTM32Z1MD0.c" attr="config" version="1.0.0"/>
        <file category="source"  condition="YTM32Z1M_GCC" name="Device/Source/YTM32Z1MD0_startup_gcc.S" attr="config" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
