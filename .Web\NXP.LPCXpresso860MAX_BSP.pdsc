<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso860MAX_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXpresso860MAX</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="16.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.13.1</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC865_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso860MAX">
      <description>LPCXpresso Development Board for the LPC86x family of MCUs</description>
      <image small="boards/lpcxpresso860max/lpcxpresso860max.png"/>
      <mountedDevice Dname="LPC865M201JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC864M201JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC864M201JHI33" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC864M201JHI48" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC865M201JHI33" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC865M201JHI48" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC864.internal_condition">
      <accept Dname="LPC864M201JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC864M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC864M201JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC865.internal_condition">
      <accept Dname="LPC865M201JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.LPC865.internal_condition">
      <accept Dname="LPC865M201JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.lpcxpresso860max.condition_id">
      <require condition="allOf.board=lpcxpresso860max, component.miniusart_adapter, device_id=LPC865, device.LPC865_startup, driver.clock, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.power_no_lib, driver.swm, utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpcxpresso860max, component.miniusart_adapter, device_id=LPC865, device.LPC865_startup, driver.clock, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.power_no_lib, driver.swm, utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.lpcxpresso860max.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require condition="device_id.LPC865.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="swm"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="board.lpcxpresso860max.internal_condition">
      <accept condition="device.LPC864.internal_condition"/>
      <accept condition="device.LPC865.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="acomp_basic" folder="boards/lpcxpresso860max/driver_examples/acomp/acomp_basic" doc="readme.md">
      <description>The ACOMP Basic Example shows the simplest way to use ACOMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_basic.uvprojx"/>
        <environment name="iar" load="iar/acomp_basic.ewp"/>
        <environment name="csolution" load="acomp_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acomp_interrupt" folder="boards/lpcxpresso860max/driver_examples/acomp/acomp_interrupt" doc="readme.md">
      <description>The ACOMP Interrupt Example shows how to use interrupt with ACOMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the ACOMP's negative channel...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/acomp_interrupt.ewp"/>
        <environment name="csolution" load="acomp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/lpcxpresso860max/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="boards/lpcxpresso860max/driver_examples/dma/channel_chain" doc="readme.md">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="iar" load="iar/dma_channel_chain.ewp"/>
        <environment name="csolution" load="dma_channel_chain.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="boards/lpcxpresso860max/driver_examples/dma/interleave_transfer" doc="readme.md">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_interleave_transfer.ewp"/>
        <environment name="csolution" load="dma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="boards/lpcxpresso860max/driver_examples/dma/linked_transfer" doc="readme.md">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_linked_transfer.ewp"/>
        <environment name="csolution" load="dma_linked_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_m2m_polling" folder="boards/lpcxpresso860max/driver_examples/dma/m2m_polling" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot polling transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_m2m_polling.uvprojx"/>
        <environment name="iar" load="iar/dma_m2m_polling.ewp"/>
        <environment name="csolution" load="dma_m2m_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/lpcxpresso860max/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="boards/lpcxpresso860max/driver_examples/dma/wrap_transfer" doc="readme.md">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes a wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_wrap_transfer.ewp"/>
        <environment name="csolution" load="dma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_combine_pwm" folder="boards/lpcxpresso860max/driver_examples/ftm/combine_pwm" doc="readme.md">
      <description>The FTM project is a demonstration program of generating a combined PWM signal by the SDK FTM driver. It sets up the FTMhardware block to output PWM signals on two TPM channels. The example also shows the...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_combine_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_combine_pwm.ewp"/>
        <environment name="csolution" load="ftm_combine_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_dual_edge_capture" folder="boards/lpcxpresso860max/driver_examples/ftm/dual_edge_capture" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a FTM channel-pair for dual-edge capture. Once the...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_dual_edge_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_dual_edge_capture.ewp"/>
        <environment name="csolution" load="ftm_dual_edge_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_input_capture" folder="boards/lpcxpresso860max/driver_examples/ftm/input_capture" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's input capture feature.The example sets up a FTM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_input_capture.ewp"/>
        <environment name="csolution" load="ftm_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_output_compare" folder="boards/lpcxpresso860max/driver_examples/ftm/output_compare" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's output compare feature.It sets up one FTM channel to toggle the output when a match occurs with the channel value. The usershould probe the FTM...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/ftm_output_compare.ewp"/>
        <environment name="csolution" load="ftm_output_compare.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pwm_twochannel" folder="boards/lpcxpresso860max/driver_examples/ftm/pwm_twochannel" doc="readme.md">
      <description>The FTM pwm two channel Example project is a demonstration program that uses the KSDK software to generate a square pulse PWM on 2 channel to control the LED brightness.- FTM generates a PWM with the increasing and...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/ftm_pwm_twochannel.ewp"/>
        <environment name="csolution" load="ftm_pwm_twochannel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_simple_pwm" folder="boards/lpcxpresso860max/driver_examples/ftm/simple_pwm" doc="readme.md">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver. It sets up the FTMhardware block to output a center-aligned PWM signal. The PWM dutycycle is periodically updated.On boards that have an LED...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_simple_pwm.ewp"/>
        <environment name="csolution" load="ftm_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="boards/lpcxpresso860max/driver_examples/ftm/timer" doc="readme.md">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" load="iar/ftm_timer.ewp"/>
        <environment name="csolution" load="ftm_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/lpcxpresso860max/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/lpcxpresso860max/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_master" folder="boards/lpcxpresso860max/component_examples/i3c_bus/master" doc="readme.md">
      <description>The i3c_bus_master example shows how to use i3c_bus component to create I3C bus and i3c master on bus.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_master.ewp"/>
        <environment name="csolution" load="i3c_bus_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_slave" folder="boards/lpcxpresso860max/component_examples/i3c_bus/slave" doc="readme.md">
      <description>The i3c_bus_slave example shows how to use i3c_bus component to work as i3c bus slave.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_slave.ewp"/>
        <environment name="csolution" load="i3c_bus_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_dma_b2b_transfer_master" folder="boards/lpcxpresso860max/driver_examples/i3c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i3c_dma_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using dma method. In this example, one i3c instance as master and another i3c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_dma_b2b_transfer_slave" folder="boards/lpcxpresso860max/driver_examples/i3c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_dma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a dma master. In this example, one i3c instance as slave and another i3c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_master" folder="boards/lpcxpresso860max/driver_examples/i3c/interrupt_b2b/master" doc="readme.md">
      <description>The i3c_interrupt_b2b_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the other board...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_slave" folder="boards/lpcxpresso860max/driver_examples/i3c/interrupt_b2b/slave" doc="readme.md">
      <description>The i3c_interrupt_b2b_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the other board...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master" folder="boards/lpcxpresso860max/driver_examples/i3c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso860max/driver_examples/i3c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_icm42688p" folder="boards/lpcxpresso860max/driver_examples/i3c/master_read_sensor_icm42688p" doc="readme.md">
      <description>The i3c_master_read_sensor_icm42688p example shows how to use i3c driver as master to communicate with sensor ICM-42688P.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_icm42688p.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_icm42688p.ewp"/>
        <environment name="csolution" load="i3c_master_read_sensor_icm42688p.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master" folder="boards/lpcxpresso860max/driver_examples/i3c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave" folder="boards/lpcxpresso860max/driver_examples/i3c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_basic" folder="boards/lpcxpresso860max/driver_examples/iap/iap_basic" doc="readme.md">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It reads part id, boot code version, unique id and reinvoke ISP. A message a printed on the UART terminal as various bascial iap operations are performed.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_basic.uvprojx"/>
        <environment name="iar" load="iar/iap_basic.ewp"/>
        <environment name="csolution" load="iap_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="boards/lpcxpresso860max/driver_examples/iap/iap_flash" doc="readme.md">
      <description>The IAP Flash project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="iar" load="iar/iap_flash.ewp"/>
        <environment name="csolution" load="iap_flash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/lpcxpresso860max/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_basic" folder="boards/lpcxpresso860max/driver_examples/adc/lpc_adc_basic" doc="readme.md">
      <description>The lpc_adc_basic example shows how to use LPC ADC driver in the simplest way.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_basic.ewp"/>
        <environment name="csolution" load="lpc_adc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_burst" folder="boards/lpcxpresso860max/driver_examples/adc/lpc_adc_burst" doc="readme.md">
      <description>The lpc_adc_burst example shows how to use LPC ADC driver with the burst mode.In this example, the internal temperature sensor is used to created the input analog signal.When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_burst.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_burst.ewp"/>
        <environment name="csolution" load="lpc_adc_burst.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_dma" folder="boards/lpcxpresso860max/driver_examples/adc/lpc_adc_dma" doc="readme.md">
      <description>The lpc_adc_dma example shows how to use LPC ADC driver with DMA.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_dma.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_dma.ewp"/>
        <environment name="csolution" load="lpc_adc_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_interrupt" folder="boards/lpcxpresso860max/driver_examples/adc/lpc_adc_interrupt" doc="readme.md">
      <description>The lpc_adc_interrupt example shows how to use interrupt with LPC ADC driver.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_interrupt.ewp"/>
        <environment name="csolution" load="lpc_adc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_bod" folder="boards/lpcxpresso860max/driver_examples/bod" doc="readme.md">
      <description>The bod example shows how to use LPC BOD(Brown-out detector) in the simplest way. To run this example, user should remove the jumper for the power source selector, and connect the adjustable input voltage to the...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_bod.uvprojx"/>
        <environment name="iar" load="iar/lpc_bod.ewp"/>
        <environment name="csolution" load="lpc_bod.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_dma_b2b_master" folder="boards/lpcxpresso860max/driver_examples/i2c/dma_b2b/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_dma_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_dma_b2b_master.ewp"/>
        <environment name="csolution" load="lpc_i2c_dma_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_dma_b2b_slave" folder="boards/lpcxpresso860max/driver_examples/i2c/dma_b2b/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_dma_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_dma_b2b_slave.ewp"/>
        <environment name="csolution" load="lpc_i2c_dma_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_master" folder="boards/lpcxpresso860max/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpc_i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso860max/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpc_i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_master" folder="boards/lpcxpresso860max/driver_examples/i2c/polling_b2b/master" doc="readme.md">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="lpc_i2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_slave" folder="boards/lpcxpresso860max/driver_examples/i2c/polling_b2b/slave" doc="readme.md">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="lpc_i2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/lpcxpresso860max/driver_examples/mrt" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/lpcxpresso860max/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="iar" load="iar/new_project.ewp"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/lpcxpresso860max/driver_examples/pint/pattern_match" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/lpcxpresso860max/driver_examples/pint/pin_interrupt" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_lpc" folder="boards/lpcxpresso860max/demo_apps/power_mode_switch_lpc" doc="readme.md">
      <description>The power_mode_switch application shows the usage of normal power mode control APIs for entering the three kinds oflow power mode: Sleep mode, Deep Sleep mode and Power Down mode, deep power down mode. When the...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_lpc.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch_lpc.ewp"/>
        <environment name="csolution" load="power_mode_switch_lpc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_master" folder="boards/lpcxpresso860max/driver_examples/spi/interrupt/master" doc="readme.md">
      <description>The spi_interrupt_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_slave" folder="boards/lpcxpresso860max/driver_examples/spi/interrupt/slave" doc="readme.md">
      <description>The spi_interrupt_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_master" folder="boards/lpcxpresso860max/driver_examples/spi/polling/master" doc="readme.md">
      <description>The spi_polling_transfer_master example shows how to use spi driver as master to do board to boardtransfer with polling:In this example, one spi instance as master and another spi instance on othere board as slave....See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_master.ewp"/>
        <environment name="csolution" load="spi_polling_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_slave" folder="boards/lpcxpresso860max/driver_examples/spi/polling/slave" doc="readme.md">
      <description>The spi_polling_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_slave.ewp"/>
        <environment name="csolution" load="spi_polling_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_dma_master" folder="boards/lpcxpresso860max/driver_examples/spi/transfer_dma/master" doc="readme.md">
      <description>The spi_dma_transfer_master example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_dma_master.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_dma_master.ewp"/>
        <environment name="csolution" load="spi_transfer_dma_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_dma_slave" folder="boards/lpcxpresso860max/driver_examples/spi/transfer_dma/slave" doc="readme.md">
      <description>The spi_dma_transfer_slave example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_dma_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_dma_slave.ewp"/>
        <environment name="csolution" load="spi_transfer_dma_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_master" folder="boards/lpcxpresso860max/driver_examples/spi/transfer_interrupt/master" doc="readme.md">
      <description>The spi_interrupt_transfer_master example shows how to use spi driver as master to do board to boardtransfer in interrupt way:In this example, one spi instance as master and another spi instance on othere board as...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_master.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_interrupt_master.ewp"/>
        <environment name="csolution" load="spi_transfer_interrupt_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_slave" folder="boards/lpcxpresso860max/driver_examples/spi/transfer_interrupt/slave" doc="readme.md">
      <description>The spi_interrupt_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_interrupt_slave.ewp"/>
        <environment name="csolution" load="spi_transfer_interrupt_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling_example" folder="boards/lpcxpresso860max/driver_examples/usart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC, the board will send back all characters that PCsend to the board. </description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling_example.uvprojx"/>
        <environment name="iar" load="iar/usart_polling_example.ewp"/>
        <environment name="csolution" load="usart_polling_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_terminal" folder="boards/lpcxpresso860max/driver_examples/usart/terminal" doc="readme.md">
      <description>This example demonstrate configuration and use of the USART module in interrupt-driven asynchronous mode on communication with a terminal emulator calling the USART transactional APIs. USART will echo back every...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_terminal.uvprojx"/>
        <environment name="iar" load="iar/usart_terminal.ewp"/>
        <environment name="csolution" load="usart_terminal.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_dma" folder="boards/lpcxpresso860max/driver_examples/usart/transfer_dma" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USART The example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_dma.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_dma.ewp"/>
        <environment name="csolution" load="usart_transfer_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_hardware_flow_control" folder="boards/lpcxpresso860max/driver_examples/usart/transfer_hardware_flow_control" doc="readme.md">
      <description>The usart_transfer_hardware_flow_control example project demonstrates the usage of the hardware flow control function. This example sends data to itself(loopback), and hardware flow control is enabled in the example....See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_hardware_flow_control.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_hardware_flow_control.ewp"/>
        <environment name="csolution" load="usart_transfer_hardware_flow_control.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_interrupt" folder="boards/lpcxpresso860max/driver_examples/usart/transfer_interrupt" doc="readme.md">
      <description>usart_transfer_interrupt</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_interrupt.ewp"/>
        <environment name="csolution" load="usart_transfer_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_rb_dma" folder="boards/lpcxpresso860max/driver_examples/usart/transfer_rb_dma" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a ring buffer to receive the data, and routine will send back every 8 characters. </description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_rb_dma.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_rb_dma.ewp"/>
        <environment name="csolution" load="usart_transfer_rb_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_ring_buffer" folder="boards/lpcxpresso860max/driver_examples/usart/transfer_ring_buffer" doc="readme.md">
      <description>The usart_interrupt_rb_transfer example shows how to use usart driver in interrupt way withRX ring buffer enabled.In this example, one uart instance connect to PC through, the board will send back all charactersthat...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_ring_buffer.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_ring_buffer.ewp"/>
        <environment name="csolution" load="usart_transfer_ring_buffer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_sync_mode" folder="boards/lpcxpresso860max/driver_examples/usart/transfer_sync_mode" doc="readme.md">
      <description>The usart_interrupt_sync_transfer example shows how to use usart API in synchronous mode:In this example, one usart instance will be selected as master ,and another as slave. The master will send data to slave in...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_sync_mode.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_sync_mode.ewp"/>
        <environment name="csolution" load="usart_transfer_sync_mode.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wkt_example" folder="boards/lpcxpresso860max/driver_examples/wkt" doc="readme.md">
      <description>The WKT project is a simple demonstration program of the SDK WKT driver. It sets up the WKT hardware block to trigger a periodic interrupt after loading a counter value and counting down to 0. When the WKT interrupt...See more details in readme document.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wkt_example.uvprojx"/>
        <environment name="iar" load="iar/wkt_example.ewp"/>
        <environment name="csolution" load="wkt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/lpcxpresso860max/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso860MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso860max" Cversion="1.0.0" condition="BOARD_Project_Template.lpcxpresso860max.condition_id">
      <description>Board_project_template lpcxpresso860max</description>
      <files>
        <file category="header" attr="config" name="boards/lpcxpresso860max/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso860max/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso860max/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso860max/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso860max/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso860max/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso860max/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso860max/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/lpcxpresso860max/project_template/"/>
      </files>
    </component>
  </components>
</package>
