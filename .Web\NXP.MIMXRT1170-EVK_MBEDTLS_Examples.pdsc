<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1170-EVK_MBEDTLS_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware mbedtls Examples Pack for MIMXRT1170-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1170-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MIMXRT1176_DFP" vendor="NXP" version="19.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="mbedtls_benchmark_cm4" folder="boards/evkmimxrt1170/mbedtls_examples/mbedtls_benchmark/cm4" doc="readme.md">
      <description>The mbedTLS Benchmark demo application performs cryptographic algorithm benchmarking and prints results to the terminal.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_benchmark_cm4.uvprojx"/>
        <environment name="csolution" load="mbedtls_benchmark_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mbedtls_selftest_cm4" folder="boards/evkmimxrt1170/mbedtls_examples/mbedtls_selftest/cm4" doc="readme.md">
      <description>The mbedTLS SelfTest program performs cryptographic algorithm testing and prints results to the terminal.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_selftest_cm4.uvprojx"/>
        <environment name="csolution" load="mbedtls_selftest_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mbedtls_benchmark_cm7" folder="boards/evkmimxrt1170/mbedtls_examples/mbedtls_benchmark/cm7" doc="readme.md">
      <description>The mbedTLS Benchmark demo application performs cryptographic algorithm benchmarking and prints results to the terminal.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_benchmark_cm7.uvprojx"/>
        <environment name="csolution" load="mbedtls_benchmark_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mbedtls_selftest_cm7" folder="boards/evkmimxrt1170/mbedtls_examples/mbedtls_selftest/cm7" doc="readme.md">
      <description>The mbedTLS SelfTest program performs cryptographic algorithm testing and prints results to the terminal.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_selftest_cm7.uvprojx"/>
        <environment name="csolution" load="mbedtls_selftest_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
