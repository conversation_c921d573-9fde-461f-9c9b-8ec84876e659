<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>MIMXRT595S_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MIMXRT595S</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version='18.0.0' date='2024-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version='17.0.0' date='2023-07-27'>NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version='16.1.0' date='2023-03-23'>NXP CMSIS Packs based on MCUXpresso SDK 2.13.1</release>
    <release version='16.0.0' date='2023-01-16'>NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version='15.1.0' date='2022-09-28'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.1</release>
    <release version='15.0.0' date='2022-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version='14.0.0' date='2022-01-05'>NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version='13.1.0' date='2021-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version='13.0.0' date='2021-02-23'>NXP CMSIS Packs based on MCUXpresso SDK 2.9.1</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='MIMXRT595S' Dvendor='NXP:11'>
      <debugconfig default='swd' clock='5000000' swj='true'/>
      <description>
        MIMXRT595S: i.MX® MIMXRT595S 200MHz, 5M SRAM Microcontrollers (MCUs) based on ARM® Cortex®-M33 Core
      </description>
      <device Dname='MIMXRT595S'>
        <processor Pname='cm33' Dcore='Cortex-M33' Dfpu='SP_FPU' Dmpu='MPU' Dtz='TZ' Ddsp='DSP' Dendian='Little-endian' Dclock='200000000'/>
        <environment Pname='cm33' name='iar'>
          <file category='linkerfile' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_ram.icf'/>
        </environment>
        <sequences>
          <sequence name='EnableDebugMailbox'>
            <block/>
            <control if='!(ReadAP(0x0) &amp; 0x40)'>
              <!-- Check AHB-AP CSW DbgStatus to decide if need enable DebugMailbox -->
              <block>
                __var status=0x55aa;
                Message(0, "RT5xx connect srcipt start");
                
                // Read APIDR
                __dp = 0;
                __ap = 2;
                status = ReadAP(0xFC);
                Message(0, "APIDR: 0x%08X", status);
                
                // Read DPIDR
                __dp = 0;
                __ap = 2;
                status = ReadDP(0x0);
                Message(0, "DPIDR: 0x%08X", status);
                
                // Active DebugMailbox
                __dp = 0;
                __ap = 2;
                WriteAP(0x0, 0x00000021);
                DAP_Delay(30000);
                ReadAP(0x0);
                
                // Enter Debug Session
                __dp = 0;
                __ap = 2;
                WriteAP(0x4, 0x00000007);
                DAP_Delay(30000);
                ReadAP(0x0);
                
                __dp = 0;
                __ap = 0;
                
                Message(0, "RT5xx connect srcipt end");
              </block>
            </control>
          </sequence>
          <sequence name='DebugPortStart' Pname='cm33'>
            <block>
              __var SW_DP_ABORT  = 0x0;
              __var DP_CTRL_STAT = 0x4;
              __var DP_SELECT    = 0x8;
              __var powered_down = 0;
              // Switch to DP Register Bank 0
              WriteDP(DP_SELECT, 0x00000000);
              
              // Read DP CTRL/STAT Register and check if CSYSPWRUPACK and CDBGPWRUPACK bits are set
              powered_down = ((ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000);
            </block>
            <control if='powered_down'>
              <block>
                // Request Debug/System Power-Up
                WriteDP(DP_CTRL_STAT, 0x50000000);
              </block>
              <!-- Wait for Power-Up Request to be acknowledged -->
              <control while='(ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000' timeout='1000000'/>
              <!-- SWD Specific Part of sequence -->
              <control if='(__protocol &amp; 0xFFFF) == 2'>
                <block>
                  // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                  WriteDP(DP_CTRL_STAT, 0x50000F00);
                  
                  // Clear WDATAERR, STICKYORUN, STICKYCMP, and STICKYERR bits of CTRL/STAT Register by write to ABORT register
                  WriteDP(SW_DP_ABORT, 0x0000001E);
                  
                  Sequence("EnableDebugMailbox");
                </block>
              </control>
            </control>
          </sequence>
          <sequence name='WaitForStopAfterReset' Pname='cm33'>
            <block>
              __var SCS_Addr    = 0xE000E000;
              __var DHCSR_Addr  = SCS_Addr + 0xDF0;
              __var DFSR_Addr  = SCS_Addr + 0xD30;
              DAP_Delay(100000);  // Give bootloader time to do what it needs to do
              Sequence("EnableDebugMailbox");
              Write32(DHCSR_Addr, 0xA05F0003);  // Halt the core in case it didn't stop at a breakpiont.
              // Clear watch point
              Write32(0xE0001020, 0x0);
              Write32(0xE0001028, 0x0);
            </block>
          </sequence>
          <sequence name='ResetHardware' Pname='cm33'>
            <block>
              __var nReset      = 0x80;
              __var canReadPins = 0;
              __var SCS_Addr    = 0xE000E000;
              __var DHCSR_Addr  = SCS_Addr + 0xDF0;
              
              // De-assert nRESET line
              canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
            </block>
            <!-- Keep reset active for 50 ms -->
            <control while='1' timeout='50000'/>
            <control if='canReadPins'>
              <!-- Assert nRESET line and wait max. 1s for recovery -->
              <control while='(DAP_SWJ_Pins(nReset, nReset, 0) &amp; nReset) == 0' timeout='1000000'/>
            </control>
            <control if='!canReadPins'>
              <block>
        // Assert nRESET line
        DAP_SWJ_Pins(nReset, nReset, 0);
      </block>
              <!-- Wait 100ms for recovery if nRESET not readable -->
              <control while='1' timeout='100000'/>
            </control>
            <control if='(__connection &amp; (1 &lt;&lt; 17)) == 0'>
              <block>Sequence("WaitForStopAfterReset");</block>
            </control>
          </sequence>
          <sequence name='TraceStart'>
            <control if='__traceout &amp; 0x1' info='SWO Trace output selected'>
              <block>// Sequence("EnableTraceClk");       // Enable and configure trace clock</block>
              <control if='SWO_Pin == 0' info='SWO via PIO2_24'>
                <block>Write32(0x40004160, 0x00000001); // Configure PIO2_24: FUNC - 1</block>
              </control>
              <control if='SWO_Pin == 1' info='SWO via PIO2_31'>
                <block>Write32(0x4000417C, 0x00000005); // Configure PIO2_31: FUNC - 5</block>
              </control>
            </control>
          </sequence>
          <sequence name='ResetSystem' Pname='cm33'>
            <block>
              __dp = 0;
              __ap = 0;
              // System Control Space (SCS) offset as defined in Armv6-M/Armv7-M.
              __var SCS_Addr   = 0xE000E000;
              __var AIRCR_Addr = SCS_Addr + 0xD0C;
              __var DHCSR_Addr = SCS_Addr + 0xDF0;
              __var DEMCR_Addr = SCS_Addr + 0xDFC;
              __var tmp;
              //Halt the core
              Write32(DHCSR_Addr, 0xA05F0003);
              //clear VECTOR CATCH and set TRCENA
              tmp = Read32(DEMCR_Addr);
              Write32(DEMCR_Addr, tmp | 0x1000000);
              // Set watch point at SYSTEM_STICK_CALIB access
              Write32(0xE0001020, 0x50002034);
              Write32(0xE0001028, 0x00000814);
              __errorcontrol = 1;
              // Execute SYSRESETREQ via AIRCR
              Write32(AIRCR_Addr, 0x05FA0004);
              Sequence("WaitForStopAfterReset");
              __errorcontrol = 0;
            </block>
          </sequence>
        </sequences>
        <debugvars configfile='devices/MIMXRT595S/arm/MIMXRT5xx.dbgconf'>
          // Debug Access Variables, can be modified by user via copies of DBGCONF files as created by uVision. Also see sub-family level.
          __var SWO_Pin               = 0;                    // Serial Wire Output pin: 0 = PIO2_24, 1 = PIO2_31
          __var Dbg_CR                = 0x00000000;           // DBG_CR
          __var BootTime              = 10000;                // 10 milliseconds
        </debugvars>
        <memory name='BootROM' start='0x03000000' size='0x020000' access='rx'/>
        <memory name='BootROM_alias' start='0x13000000' size='0x020000' access='rx' alias='BootROM'/>
        <memory name='SRAM' start='0x00280000' size='0x280000' access='rw' default='1'/>
        <memory name='SRAM_DSP' start='0x00080000' size='0x200000' access='rw' default='1'/>
        <memory name='SRAM_DSP_alias1' start='0x10080000' size='0x200000' access='rw' alias='SRAM_DSP'/>
        <memory name='SRAM_DSP_alias2' start='0x20080000' size='0x200000' access='rw' alias='SRAM_DSP'/>
        <memory name='SRAM_DSP_alias3' start='0x30080000' size='0x200000' access='rw' alias='SRAM_DSP'/>
        <memory name='SRAM_ROM' start='0x00000000' size='0x020000' access='rw' default='1'/>
        <memory name='SRAM_ROM_alias1' start='0x10000000' size='0x020000' access='rx' alias='SRAM_ROM'/>
        <memory name='SRAM_ROM_alias2' start='0x20000000' size='0x020000' access='rx' alias='SRAM_ROM'/>
        <memory name='SRAM_ROM_alias3' start='0x30000000' size='0x020000' access='rx' alias='SRAM_ROM'/>
        <memory name='SRAM_SHARED' start='0x00020000' size='0x060000' access='rw' default='1'/>
        <memory name='SRAM_SHARED_alias1' start='0x10020000' size='0x060000' access='rw' alias='SRAM_SHARED'/>
        <memory name='SRAM_SHARED_alias2' start='0x20020000' size='0x060000' access='rw' alias='SRAM_SHARED'/>
        <memory name='SRAM_SHARED_alias3' start='0x30020000' size='0x060000' access='rw' alias='SRAM_SHARED'/>
        <memory name='SRAM_alias1' start='0x10280000' size='0x280000' access='rw' alias='SRAM'/>
        <memory name='SRAM_alias2' start='0x20280000' size='0x280000' access='rw' alias='SRAM'/>
        <memory name='SRAM_alias3' start='0x30280000' size='0x280000' access='rw' alias='SRAM'/>
        <memory name='USB_RAM' start='0x40140000' size='0x4000' access='rw' default='1'/>
        <memory name='USB_RAM_alias' start='0x50140000' size='0x4000' access='rw' alias='USB_RAM'/>
        <algorithm name='devices/MIMXRT595S/arm/MIMXRT5XX_EVK_FLEXSPI.FLM' start='0x08000000' size='0x04000000' RAMstart='0x1001c000' RAMsize='0x00001000' default='1'/>
        <algorithm name='devices/MIMXRT595S/arm/MIMXRT5XX_EVK_FLEXSPI_S.FLM' start='0x18000000' size='0x04000000' RAMstart='0x1001c000' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/MIMXRT595S/MIMXRT595S_cm33.xml' Pname='cm33' __dp='0' __ap='0'/>
        <variant Dvariant='MIMXRT595SFFOC'>
          <compile Pname='cm33' header='devices/MIMXRT595S/fsl_device_registers.h' define='CPU_MIMXRT595SFFOC_cm33'/>
        </variant>
        <variant Dvariant='MIMXRT595SFAWC'>
          <compile Pname='cm33' header='devices/MIMXRT595S/fsl_device_registers.h' define='CPU_MIMXRT595SFAWC_cm33'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.MIMXRT595S.internal_condition'>
      <accept Dname='MIMXRT595SFAWC' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT595SFFOC' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.device=MIMXRT595S.internal_condition'>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.ak4497_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.audio_flexcomm_i2s_dma_adapter.condition_id'>
      <require condition='allOf.driver.flexcomm_i2s_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_i2s_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2s_dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='device.MIMXRT533S.internal_condition'>
      <accept Dname='MIMXRT533SFAWC' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT533SFFOC' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.MIMXRT555S.internal_condition'>
      <accept Dname='MIMXRT555SFAWC' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT555SFFOC' Dvendor='NXP:11'/>
    </condition>
    <condition id='component.button.condition_id'>
      <require condition='allOf.anyOf=allOf=anyOf=board=evkmimxrt595, driver.lpc_gpio, component.rt_gpio_adapter, component.timer_manager, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=anyOf=board=evkmimxrt595, driver.lpc_gpio, component.rt_gpio_adapter, component.timer_manager, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=anyOf=board=evkmimxrt595, driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=anyOf=board=evkmimxrt595, driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'>
      <accept condition='allOf.anyOf=board=evkmimxrt595, driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=board=evkmimxrt595, driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'>
      <require condition='anyOf.board=evkmimxrt595, driver.lpc_gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rt_gpio_adapter'/>
    </condition>
    <condition id='anyOf.board=evkmimxrt595, driver.lpc_gpio.internal_condition'>
      <accept condition='board.evkmimxrt595.internal_condition'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='board.evkmimxrt595.internal_condition'>
      <accept condition='device.MIMXRT533S.internal_condition'/>
      <accept condition='device.MIMXRT555S.internal_condition'/>
      <accept condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.codec_adapters.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.codec_i2c.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.cs42448_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.cs42888_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ctimer, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.ctimer_time_stamp_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.da7212_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.flash_adapter.condition_id'>
      <require condition='allOf.anyOf=allOf=board=evkmimxrt595, component.flexspi_nor_flash_adapter_rt595evk, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=board=evkmimxrt595, component.flexspi_nor_flash_adapter_rt595evk, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=board=evkmimxrt595, component.flexspi_nor_flash_adapter_rt595evk.internal_condition'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=board=evkmimxrt595, component.flexspi_nor_flash_adapter_rt595evk.internal_condition'>
      <accept condition='allOf.board=evkmimxrt595, component.flexspi_nor_flash_adapter_rt595evk.internal_condition'/>
    </condition>
    <condition id='allOf.board=evkmimxrt595, component.flexspi_nor_flash_adapter_rt595evk.internal_condition'>
      <require condition='board.evkmimxrt595.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='adapter_rt595evk'/>
    </condition>
    <condition id='component.flash_nand_flexspi.condition_id'>
      <require condition='allOf.driver.flexspi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexspi, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.flash_nor_flexspi.condition_id'>
      <require condition='allOf.driver.flexspi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.flexcomm_i2c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.flexcomm_i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.flexcomm_i2c, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.flexcomm_spi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm_spi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.flexcomm_spi, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.flexspi_nor_flash_adapter_rt595evk.condition_id'>
      <require condition='allOf.board=evkmimxrt595, driver.flexspi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.board=evkmimxrt595, driver.flexspi, device=MIMXRT595S.internal_condition'>
      <require condition='board.evkmimxrt595.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c, allOf=component.i3c_adapter, driver.i3c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c, allOf=component.i3c_adapter, driver.i3c, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c, allOf=component.i3c_adapter, driver.i3c.internal_condition'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c, allOf=component.i3c_adapter, driver.i3c.internal_condition'>
      <accept condition='allOf.component.flexcomm_i2c_adapter, driver.flexcomm_i2c.internal_condition'/>
      <accept condition='allOf.component.i3c_adapter, driver.i3c.internal_condition'/>
    </condition>
    <condition id='allOf.component.flexcomm_i2c_adapter, driver.flexcomm_i2c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_i2c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='allOf.component.i3c_adapter, driver.i3c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.i3c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.i3c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.i3c, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.i3c_bus.condition_id'>
      <require condition='allOf.component.lists, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.i3c_bus_adapter.condition_id'>
      <require condition='allOf.component.i3c_bus, driver.i3c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.i3c_bus, driver.i3c, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_bus'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.led.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, component.timer_manager, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, component.timer_manager, driver.common, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'>
      <accept condition='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_gpio_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.log.condition_id'>
      <require condition='allOf.driver.common, utility.str, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.str, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console_lite.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console_lite, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console_lite, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.log.backend.ringbuffer.condition_id'>
      <require condition='allOf.component.log, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.lpc_crc_adapter.condition_id'>
      <require condition='allOf.driver.lpc_crc, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_crc, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.lpc_gpio_adapter.condition_id'>
      <require condition='allOf.driver.inputmux, driver.lpc_gpio, driver.pint, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.inputmux, driver.lpc_gpio, driver.pint, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pint'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.lpcrtc.condition_id'>
      <require condition='allOf.driver.lpc_rtc, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_rtc, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_rtc'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.messaging.condition_id'>
      <require condition='allOf.component.lists, component.mem_manager, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, component.mem_manager, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.mflash_offchip.condition_id'>
      <require condition='allOf.anyOf=driver.cache_cache64, driver.flexspi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.cache_cache64, driver.flexspi, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.cache_cache64, driver.flexspi.internal_condition'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.cache_cache64, driver.flexspi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='cache_cache64'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
    </condition>
    <condition id='component.mflash_onchip_fmu.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.mpi_loader.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.mrt_adapter.condition_id'>
      <require condition='allOf.driver.mrt, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.mrt, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.nvm_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.ostimer_adapter.condition_id'>
      <require condition='allOf.driver.ostimer, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ostimer, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.ostimer_time_stamp_adapter.condition_id'>
      <require condition='allOf.driver.ostimer, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.pcm186x_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.pcm512x_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.power_manager_framework.condition_id'>
      <require condition='allOf.anyOf=driver.common, component.lists, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.common, component.lists, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.common.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.common.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
    </condition>
    <condition id='component.pwm_ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.trng_adapter, driver.trng, component.software_rng_adapter, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.trng_adapter, driver.trng, component.software_rng_adapter, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=component.trng_adapter, driver.trng, component.software_rng_adapter.internal_condition'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.trng_adapter, driver.trng, component.software_rng_adapter.internal_condition'>
      <accept condition='allOf.component.trng_adapter, driver.trng.internal_condition'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='allOf.component.trng_adapter, driver.trng.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='trng_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='trng'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.rt_gpio_adapter.condition_id'>
      <require condition='allOf.driver.lpc_gpio, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_gpio, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'>
      <accept condition='allOf.component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'/>
    </condition>
    <condition id='allOf.component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_spi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_usart, component.usart_adapter, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_usart, component.usart_adapter, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.flexcomm_usart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.flexcomm_usart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.sgtl_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.tfa9896_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.tfa9xxx_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, allOf=component.ostimer_adapter, driver.ostimer, component.lists, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, allOf=component.ostimer_adapter, driver.ostimer, component.lists, driver.common, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, allOf=component.ostimer_adapter, driver.ostimer.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, allOf=component.ostimer_adapter, driver.ostimer.internal_condition'>
      <accept condition='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'/>
      <accept condition='allOf.component.mrt_adapter, driver.mrt.internal_condition'/>
      <accept condition='allOf.component.ostimer_adapter, driver.ostimer.internal_condition'/>
    </condition>
    <condition id='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
    </condition>
    <condition id='allOf.component.mrt_adapter, driver.mrt.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt'/>
    </condition>
    <condition id='allOf.component.ostimer_adapter, driver.ostimer.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer'/>
    </condition>
    <condition id='component.trng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, driver.trng, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, driver.trng, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='trng'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.usart_adapter.condition_id'>
      <require condition='allOf.driver.flexcomm, driver.flexcomm_usart, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm, driver.flexcomm_usart, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.88W8987.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_aw_cm358_usd, component.wifi_bt_module.board_aw_cm358ma, component.wifi_bt_module.board_murata_1zm_m2, component.wifi_bt_module.board_murata_1zm_usd, component.wifi_bt_module.board_ubx_jody_w2_usd.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358ma'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w2_usd'/>
    </condition>
    <condition id='component.wifi_bt_module.IW416.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_aw_am457_usd, component.wifi_bt_module.board_aw_am457ma, component.wifi_bt_module.board_aw_am510_arduino, component.wifi_bt_module.board_aw_am510_usd, component.wifi_bt_module.board_aw_am510ma, component.wifi_bt_module.board_murata_1xk_m2, component.wifi_bt_module.board_murata_1xk_usd, component.wifi_bt_module.board_ubx_maya_w1_usd.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457ma'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_arduino'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510ma'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_maya_w1_usd'/>
    </condition>
    <condition id='component.wifi_bt_module.IW61X.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_murata_2dl_m2, component.wifi_bt_module.board_murata_2dl_usd, component.wifi_bt_module.board_murata_2el_m2, component.wifi_bt_module.board_murata_2el_usd, component.wifi_bt_module.board_murata_2ll_m2, component.wifi_bt_module.board_ubx_jody_w5_m2, component.wifi_bt_module.board_ubx_jody_w5_usd.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_usd'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ll_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_m2'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_usd'/>
    </condition>
    <condition id='component.wifi_bt_module.RW61X.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.board_frdm_rw61x, component.wifi_bt_module.board_rd_rw61x.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_frdm_rw61x'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rd_rw61x'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am457_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw416'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am457ma.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am510_arduino.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am510_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_am510ma.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_cm358_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_88w8987'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_cm358ma.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_nm191_usd.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_aw_nm191ma.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_frdm_rw61x.condition_id'>
      <require condition='allOf.component.wifi_bt_module.RW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.RW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rw61x'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_k32w061_transceiver.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1xk_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1xk_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1zm_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_1zm_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2dl_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw61x'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2dl_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2ds_m2.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2ds_usd.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2el_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2el_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_murata_2ll_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_rd_rw61x.condition_id'>
      <require condition='allOf.component.wifi_bt_module.RW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_jody_w2_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_jody_w5_m2.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_jody_w5_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW61X, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_lily_w1_usd.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.board_ubx_maya_w1_usd.condition_id'>
      <require condition='allOf.component.wifi_bt_module.IW416, component.wifi_bt_module.config, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.bt_only_fw.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.config.condition_id'>
      <require condition='allOf.anyOf=component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X.internal_condition'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.component.wifi_bt_module.88W8987, component.wifi_bt_module.IW416, component.wifi_bt_module.IW61X, component.wifi_bt_module.RW61X.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_88w8987'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw416'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw61x'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rw61x'/>
    </condition>
    <condition id='component.wifi_bt_module.tx_pwr_limits.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wifi_bt_module.wifi_bt_combo_fw.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wm8524_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wm8904_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wm8960_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='component.wm8962_adapter.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=MIMXRT595S.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.core_ids=cm33.condition_id'>
      <require condition='core_ids.cm33.internal_condition'/>
    </condition>
    <condition id='core_ids.cm33.internal_condition'>
      <accept Pname='cm33' Dname='*'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='device_id.MIMXRT595S.internal_condition'>
      <accept Dname='MIMXRT595SFAWC' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT595SFFOC' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MIMXRT595S.internal_condition'>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='device_ids.MIMXRT595S.internal_condition'>
      <accept Dname='MIMXRT595SFAWC' Dvendor='NXP:11'/>
      <accept Dname='MIMXRT595SFFOC' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='cores.cm33.internal_condition'/>
      <require condition='core_ids.cm33.internal_condition'/>
      <require condition='device_ids.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='cores.cm33.internal_condition'>
      <accept Dcore='Cortex-M33'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='cores.cm33.internal_condition'/>
      <require condition='core_ids.cm33.internal_condition'/>
      <require condition='device_ids.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='cores.cm33.internal_condition'/>
      <require condition='core_ids.cm33.internal_condition'/>
      <require condition='device_ids.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.component.usart_adapter, device_id=MIMXRT595S, device.startup, driver.cache_cache64, driver.clock, driver.common, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.lpc_iopctl, driver.power, driver.reset, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.usart_adapter, device_id=MIMXRT595S, device.startup, driver.cache_cache64, driver.clock, driver.common, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.lpc_iopctl, driver.power, driver.reset, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cache_cache64'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='iopctl'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='reset'/>
      <require condition='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <accept condition='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'/>
      <accept condition='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config'/>
    </condition>
    <condition id='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT595S_system'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, core_ids=cm33.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='core_ids.cm33.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, core_ids=cm33.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='core_ids.cm33.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk, core_ids=cm33.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='core_ids.cm33.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT595S_header'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.acmp.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ak4497.condition_id'>
      <require condition='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.cache_cache64.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.video-common, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ap1302.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-common, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-max9286.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-mt9m114.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov5640.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7670.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7725.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-sccb.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.flexcomm_i2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.flexcomm_i2c.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='driver.camera-receiver-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.casper.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_flexcomm_i2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.flexcomm_i2c_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.flexcomm_i2c_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_flexcomm_spi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.flexcomm_spi_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.flexcomm_spi_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi_dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_flexcomm_usart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, device.RTE, driver.flexcomm_usart_dma, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, device.RTE, driver.flexcomm_usart_dma, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_dma'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.codec.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm33.condition_id'>
      <require condition='cores.cm33.internal_condition'/>
    </condition>
    <condition id='driver.cs42448.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.cs42888.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dbi.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dbi_flexio_smartdma.condition_id'>
      <require condition='allOf.driver.dbi, driver.flexio_mculcd, driver.flexio_mculcd_smartdma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, driver.flexio_mculcd, driver.flexio_mculcd_smartdma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd_smartdma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dbi_lcdif.condition_id'>
      <require condition='allOf.driver.dbi, driver.lcdif, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, driver.lcdif, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lcdif'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dbi_lpc_spi_dma.condition_id'>
      <require condition='allOf.driver.dbi, driver.flexcomm_spi_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, driver.flexcomm_spi_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi_dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dbi.condition_id'>
      <require condition='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dsi-cmd.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-mipi-dsi-cmd'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-lcdif.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.lcdif, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.lcdif, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lcdif'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-ssd1963.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dialog7212.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-adv7535.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.video-i2c, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-co5300.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-mipi-dsi-cmd'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-hx8394.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-it6161.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-it6263.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-mipi-dsi-cmd.condition_id'>
      <require condition='allOf.anyOf=driver.mipi_dsi, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.mipi_dsi, driver.common, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.mipi_dsi.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.mipi_dsi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_dsi'/>
    </condition>
    <condition id='driver.display-rm67162.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-rm67191.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-rm68191.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-rm68200.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-rm692c9.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-rpi.condition_id'>
      <require condition='allOf.driver.display-common, driver.display-mipi-dsi-cmd, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.display-sn65dsi83.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dmic.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dmic_dma.condition_id'>
      <require condition='allOf.driver.dmic, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dmic, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dmic'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dmic_hwvad.condition_id'>
      <require condition='allOf.driver.dmic, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dmic, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dmic'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.dsp.condition_id'>
      <require condition='allOf.core=cm33, device_id=MIMXRT595S, driver.power.internal_condition'/>
    </condition>
    <condition id='allOf.core=cm33, device_id=MIMXRT595S, driver.power.internal_condition'>
      <require condition='core.cm33.internal_condition'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
    </condition>
    <condition id='core.cm33.internal_condition'>
      <accept Dcore='Cortex-M33'/>
    </condition>
    <condition id='driver.ektf2k.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2c.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.flexcomm, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2c_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_i2c, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_i2c, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2s.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2s_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_i2s, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_i2s, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2s'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_spi.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_spi_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_spi, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_spi, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_usart.condition_id'>
      <require condition='allOf.driver.flexcomm, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_usart_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_usart, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_usart, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2c_master.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexio, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2s.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio_mculcd.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio_mculcd_smartdma.condition_id'>
      <require condition='allOf.driver.flexio_mculcd, driver.smartdma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexio_mculcd, driver.smartdma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='smartdma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexspi.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.flexspi_dma.condition_id'>
      <require condition='allOf.driver.flexspi, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexspi, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.fmeas.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ft3267.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ft5406.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ft6x06.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device=MIMXRT595S.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.fxas21002cq.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.fxls8974cf.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.fxos8700cq.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.gt911.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.hashcrypt.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.htu21d.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.i2s_bridge.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.i3c.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.i3c_dma.condition_id'>
      <require condition='allOf.driver.i3c, driver.lpc_dma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.i3c, driver.lpc_dma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.iap3.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.icm42688p.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ili9341.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.inputmux.condition_id'>
      <require condition='allOf.driver.common, driver.inputmux_connections, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.inputmux_connections, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.inputmux_connections.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lcdif.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lpadc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lpc_crc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lpc_dma.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lpc_gpio.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lpc_iopctl.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lpc_rtc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.lsm6dso.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.max30101.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.memory.condition_id'>
      <require condition='allOf.core=cm33, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.core=cm33, device_id=MIMXRT595S.internal_condition'>
      <require condition='core.cm33.internal_condition'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mipi_dsi.condition_id'>
      <require condition='allOf.driver.common, driver.mipi_dsi_soc, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.mipi_dsi_soc, device_id=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_dsi'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mipi_dsi_smartdma.condition_id'>
      <require condition='allOf.driver.mipi_dsi, driver.smartdma, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.mipi_dsi, driver.smartdma, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_dsi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='smartdma'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mipi_dsi_soc.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mma8451q.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mma8652fc.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mrt.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.mu.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.nmh1000.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ostimer.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.otfad.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.p3t1755.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pca9420.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, driver.power, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_i2c, driver.common, driver.power, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.flexcomm_i2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pca9422.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, driver.power, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pcm186x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pcm512x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pf1550.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pf3000.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pf5020.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.pint.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.power.condition_id'>
      <require condition='allOf.core=cm33, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.powerquad.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.psp27801.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.puf.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.reset.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.sctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.sema42.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.sgtl5000.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.smartdma.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.smartdma_rt500.condition_id'>
      <require condition='allOf.anyOf=device_id=MIMXRT595S, driver.smartdma.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=device_id=MIMXRT595S, driver.smartdma.internal_condition'>
      <require condition='anyOf.device_id=MIMXRT595S.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='smartdma'/>
    </condition>
    <condition id='anyOf.device_id=MIMXRT595S.internal_condition'>
      <accept condition='device_id.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.ssd1963.condition_id'>
      <require condition='allOf.driver.dbi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.st7796s.condition_id'>
      <require condition='allOf.driver.dbi, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.tfa9896.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx_hal.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.tma525b.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.trng.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.tsl2561.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.usdhc.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.utick.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S, driver.power.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MIMXRT595S, driver.power.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MIMXRT595S.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
    </condition>
    <condition id='driver.video-common.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.video-i2c.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.wm8524.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.wm8904.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.wm8960.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.wm8962.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='driver.wwdt.condition_id'>
      <require condition='allOf.driver.common, device_id=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk, cores=cm33.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
      <require condition='cores.cm33.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_usart, driver.common, not=utility.debug_console, utility.str, component.usart_adapter, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_usart, driver.common, not=utility.debug_console, utility.str, component.usart_adapter, device=MIMXRT595S.internal_condition'>
      <require condition='anyOf.driver.flexcomm_usart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.debug_console.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='not.utility.debug_console.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=MIMXRT595S.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MIMXRT595S.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=MIMXRT595S.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter' Cversion='2.2.0' condition='component.ak4497_adapter.condition_id'>
      <description>Component ak4497 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.h' projectpath='codec/port/ak4497'/>
        <file category='sourceC' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.c' projectpath='codec/port/ak4497'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/ak4497/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='audio_flexcomm_i2s_dma_adapter' Cversion='1.0.0' condition='component.audio_flexcomm_i2s_dma_adapter.condition_id'>
      <description>Component flexcomm_i2s_dma_adapter</description>
      <files>
        <file category='header' name='components/audio/fsl_adapter_audio.h' projectpath='component/audio'/>
        <file category='sourceC' name='components/audio/fsl_adapter_flexcomm_i2s.c' projectpath='component/audio'/>
        <file category='include' name='components/audio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='button' Cversion='1.0.0' condition='component.button.condition_id'>
      <description>Component button</description>
      <files>
        <file category='header' name='components/button/fsl_component_button.h' projectpath='component/button'/>
        <file category='sourceC' name='components/button/fsl_component_button.c' projectpath='component/button'/>
        <file category='include' name='components/button/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters' Cversion='2.2.0' condition='component.codec_adapters.condition_id'>
      <description>Component codec adapters for multi codec</description>
      <RTE_Components_h>
#ifndef CODEC_MULTI_ADAPTERS
#define CODEC_MULTI_ADAPTERS 1
#endif
</RTE_Components_h>
      <files>
        <file category='sourceC' name='components/codec/port/fsl_codec_adapter.c' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c' Cversion='2.1.0' condition='component.codec_i2c.condition_id'>
      <description>Component codec_i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/i2c/fsl_codec_i2c.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/i2c/fsl_codec_i2c.c' projectpath='codec'/>
        <file category='include' name='components/codec/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter' Cversion='2.2.1' condition='component.cs42448_adapter.condition_id'>
      <description>Component cs42448 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.h' projectpath='codec/port/cs42448'/>
        <file category='sourceC' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.c' projectpath='codec/port/cs42448'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42448/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter' Cversion='2.2.1' condition='component.cs42888_adapter.condition_id'>
      <description>Component cs42888 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.h' projectpath='codec/port/cs42888'/>
        <file category='sourceC' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.c' projectpath='codec/port/cs42888'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42888/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter' Cversion='1.0.0' condition='component.ctimer_adapter.condition_id'>
      <description>Component ctimer_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_CTIMER
#define TIMER_PORT_TYPE_CTIMER 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ctimer.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_time_stamp_adapter' Cversion='1.0.0' condition='component.ctimer_time_stamp_adapter.condition_id'>
      <description>Component ctimer time stamp adapter</description>
      <files>
        <file category='header' name='components/time_stamp/fsl_adapter_time_stamp.h' projectpath='component/time_stamp'/>
        <file category='sourceC' name='components/time_stamp/fsl_adapter_ctimer_time_stamp.c' projectpath='component/time_stamp'/>
        <file category='include' name='components/time_stamp/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter' Cversion='2.2.0' condition='component.da7212_adapter.condition_id'>
      <description>Component da7212 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/da7212/fsl_codec_da7212_adapter.h' projectpath='codec/port/da7212'/>
        <file category='sourceC' name='components/codec/port/da7212/fsl_codec_da7212_adapter.c' projectpath='codec/port/da7212'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/da7212/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_adapter' Cversion='1.0.0' condition='component.flash_adapter.condition_id'>
      <description>Component flash_adapter</description>
      <files>
        <file category='doc' name='components/internal_flash/component.flash_adapter_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nand_flexspi' Cversion='1.0.0' condition='component.flash_nand_flexspi.condition_id'>
      <description>Component.flash_nand_flexspi</description>
      <files>
        <file category='header' name='components/flash/nand/fsl_nand_flash.h' projectpath='nand_flash/nand'/>
        <file category='sourceC' name='components/flash/nand/flexspi/fsl_flexspi_nand_flash.c' projectpath='nand_flash/nand/flexspi'/>
        <file category='header' name='components/flash/nand/flexspi/fsl_flexspi_nand_flash.h' projectpath='nand_flash/nand/flexspi'/>
        <file category='include' name='components/flash/nand/'/>
        <file category='include' name='components/flash/nand/flexspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nor_flexspi' Cversion='1.0.0' condition='component.flash_nor_flexspi.condition_id'>
      <description>Component flash_nor_flexspi</description>
      <files>
        <file category='header' name='components/flash/nor/fsl_nor_flash.h' projectpath='nor_flash/nor'/>
        <file category='sourceC' name='components/flash/nor/flexspi/fsl_flexspi_nor_flash.c' projectpath='nor_flash/nor/flexspi'/>
        <file category='header' name='components/flash/nor/flexspi/fsl_flexspi_nor_flash.h' projectpath='nor_flash/nor/flexspi'/>
        <file category='include' name='components/flash/nor/'/>
        <file category='include' name='components/flash/nor/flexspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_i2c_adapter' Cversion='1.0.0' condition='component.flexcomm_i2c_adapter.condition_id'>
      <description>Component flexcomm_i2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_flexcomm_i2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_spi_adapter' Cversion='1.0.0' condition='component.flexcomm_spi_adapter.condition_id'>
      <description>Component flexcomm_spi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_flexcomm_spi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adapter_rt595evk' Cversion='1.0.0' condition='component.flexspi_nor_flash_adapter_rt595evk.condition_id'>
      <description>Component flexspi_nor_flash_adapter_rt595evk</description>
      <files>
        <file category='sourceC' name='components/internal_flash/octal_flash/RT595/fsl_adapter_flexspi_nor_flash.c' projectpath='component/internal_flash/octal_flash/RT595'/>
        <file category='header' name='components/internal_flash/fsl_adapter_flash.h' projectpath='component/internal_flash'/>
        <file category='include' name='components/internal_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_adapter' Cversion='1.0.0' condition='component.i3c_adapter.condition_id'>
      <description>Component i3c_adapter</description>
      <RTE_Components_h>
        #ifndef SDK_I3C_BASED_COMPONENT_USED
        #define SDK_I3C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_i3c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_bus' Cversion='1.0.1' condition='component.i3c_bus.condition_id'>
      <description>Component i3c_bus</description>
      <files>
        <file category='header' name='components/i3c_bus/fsl_component_i3c.h' projectpath='component/i3c_bus'/>
        <file category='sourceC' name='components/i3c_bus/fsl_component_i3c.c' projectpath='component/i3c_bus'/>
        <file category='include' name='components/i3c_bus/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_bus_adapter' Cversion='1.1.0' condition='component.i3c_bus_adapter.condition_id'>
      <description>Component i3c_bus_adapter</description>
      <files>
        <file category='header' name='components/i3c_bus/fsl_component_i3c_adapter.h' projectpath='component/i3c_bus'/>
        <file category='sourceC' name='components/i3c_bus/fsl_component_i3c_adapter.c' projectpath='component/i3c_bus'/>
        <file category='include' name='components/i3c_bus/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='led' Cversion='1.0.0' condition='component.led.condition_id'>
      <description>Component led</description>
      <files>
        <file category='header' name='components/led/fsl_component_led.h' projectpath='component/led'/>
        <file category='sourceC' name='components/led/fsl_component_led.c' projectpath='component/led'/>
        <file category='include' name='components/led/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='log' Cversion='1.0.0' condition='component.log.condition_id'>
      <description>Component log</description>
      <files>
        <file category='header' name='components/log/fsl_component_log.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log.c' projectpath='component/log'/>
        <file category='header' name='components/log/fsl_component_log_config.h' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole' Cversion='1.0.0' condition='component.log.backend.debug_console.condition_id'>
      <description>Component log backend debug console</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole_lite' Cversion='1.0.0' condition='component.log.backend.debug_console_lite.condition_id'>
      <description>Component log backend debug console lite</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ringbuffer' Cversion='1.0.0' condition='component.log.backend.ringbuffer.condition_id'>
      <description>Component log backend ring buffer</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_ringbuffer.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_ringbuffer.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc_adapter' Cversion='1.0.0' condition='component.lpc_crc_adapter.condition_id'>
      <description>Component lpc_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_lpc_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_gpio_adapter' Cversion='1.0.1' condition='component.lpc_gpio_adapter.condition_id'>
      <description>Component lpc_gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_lpc_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpcrtc' Cversion='1.0.0' condition='component.lpcrtc.condition_id'>
      <description>Component lpcrtc</description>
      <RTE_Components_h>
        #ifndef RTC_LEGACY_FUNCTION_PROTOTYPE
        #define RTC_LEGACY_FUNCTION_PROTOTYPE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/rtc/fsl_adapter_rtc.h' projectpath='component/rtc'/>
        <file category='sourceC' name='components/rtc/fsl_adapter_lpcrtc.c' projectpath='component/rtc'/>
        <file category='include' name='components/rtc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='messaging' Cversion='1.0.0' condition='component.messaging.condition_id'>
      <description>Component messaging</description>
      <files>
        <file category='header' name='components/messaging/fsl_component_messaging.h' projectpath='component/messaging'/>
        <file category='sourceC' name='components/messaging/fsl_component_messaging.c' projectpath='component/messaging'/>
        <file category='include' name='components/messaging/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_offchip' Cversion='1.0.0' condition='component.mflash_offchip.condition_id'>
      <description>mflash offchip</description>
      <RTE_Components_h>
#ifndef MFLASH_FILE_BASEADDR
#define MFLASH_FILE_BASEADDR 7340032
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/flash/mflash/mflash_common.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mflash_file.c' projectpath='flash/mflash'/>
        <file category='header' name='components/flash/mflash/mflash_file.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mimxrt595/mflash_drv.c' projectpath='flash/mflash/mimxrt595'/>
        <file category='header' name='components/flash/mflash/mimxrt595/mflash_drv.h' projectpath='flash/mflash/mimxrt595'/>
        <file category='include' name='components/flash/mflash/'/>
        <file category='include' name='components/flash/mflash/mimxrt595/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip_fmu' Cversion='1.0.0' condition='component.mflash_onchip_fmu.condition_id'>
      <description>mflash onchip_fmu</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_fmu_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mpi_loader' Cversion='1.0.0' condition='component.mpi_loader.condition_id'>
      <description>Component mpi_loader</description>
      <files>
        <file category='header' name='components/mpi_loader/fsl_mpi_loader.h' projectpath='mpi_loader'/>
        <file category='sourceC' name='components/mpi_loader/fsl_mpi_loader.c' projectpath='mpi_loader'/>
        <file category='include' name='components/mpi_loader/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mrt_adapter' Cversion='1.0.0' condition='component.mrt_adapter.condition_id'>
      <description>Component mrt_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_MRT
#define TIMER_PORT_TYPE_MRT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_mrt.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='nvm_adapter' Cversion='1.0.0' condition='component.nvm_adapter.condition_id'>
      <description>Component nvm_adapter</description>
      <files>
        <file category='doc' name='components/nvm/component.nvm_adapter_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer_adapter' Cversion='1.0.0' condition='component.ostimer_adapter.condition_id'>
      <description>Component ostimer_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_OSTIMER
#define TIMER_PORT_TYPE_OSTIMER 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ostimer.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer_time_stamp_adapter' Cversion='1.0.0' condition='component.ostimer_time_stamp_adapter.condition_id'>
      <description>Component ostimer time stamp adapter</description>
      <files>
        <file category='header' name='components/time_stamp/fsl_adapter_time_stamp.h' projectpath='component/time_stamp'/>
        <file category='sourceC' name='components/time_stamp/fsl_adapter_ostimer_time_stamp.c' projectpath='component/time_stamp'/>
        <file category='include' name='components/time_stamp/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter' Cversion='2.0.0' condition='component.pcm186x_adapter.condition_id'>
      <description>Component pcm186x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.h' projectpath='codec/port/pcm186x'/>
        <file category='sourceC' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.c' projectpath='codec/port/pcm186x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm186x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter' Cversion='2.0.0' condition='component.pcm512x_adapter.condition_id'>
      <description>Component pcm512x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.h' projectpath='codec/port/pcm512x'/>
        <file category='sourceC' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.c' projectpath='codec/port/pcm512x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm512x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='power_manager_core' Cversion='2.0.0' condition='component.power_manager_framework.condition_id'>
      <description>Component power manager core level</description>
      <RTE_Components_h>
#ifndef GENERIC_LIST_LIGHT
#define GENERIC_LIST_LIGHT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/power_manager/core/fsl_pm_core.h' projectpath='component/power_manager'/>
        <file category='sourceC' name='components/power_manager/core/fsl_pm_core.c' projectpath='component/power_manager'/>
        <file category='header' name='components/power_manager/core/fsl_pm_config.h' projectpath='component/power_manager'/>
        <file category='sourceC' name='components/power_manager/devices/MIMXRT595S/fsl_pm_device.c' projectpath='component/devices/MIMXRT595S'/>
        <file category='header' name='components/power_manager/devices/MIMXRT595S/fsl_pm_device.h' projectpath='component/devices/MIMXRT595S'/>
        <file category='header' name='components/power_manager/devices/MIMXRT595S/fsl_pm_device_config.h' projectpath='component/devices/MIMXRT595S'/>
        <file category='include' name='components/power_manager/core/'/>
        <file category='include' name='components/power_manager/devices/MIMXRT595S/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm_ctimer_adapter' Cversion='1.0.0' condition='component.pwm_ctimer_adapter.condition_id'>
      <description>Component pwm_ctimer_adapter</description>
      <files>
        <file category='header' name='components/pwm/fsl_adapter_pwm.h' projectpath='component/pwm'/>
        <file category='sourceC' name='components/pwm/fsl_adapter_pwm_ctimer.c' projectpath='component/pwm'/>
        <file category='include' name='components/pwm/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rt_gpio_adapter' Cversion='1.0.1' condition='component.rt_gpio_adapter.condition_id'>
      <description>Component rt_gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_rt_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter' Cversion='2.2.0' condition='component.sgtl_adapter.condition_id'>
      <description>Component sgtl5000 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.h' projectpath='codec/port/sgtl5000'/>
        <file category='sourceC' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.c' projectpath='codec/port/sgtl5000'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/sgtl5000/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter' Cversion='2.2.0' condition='component.tfa9896_adapter.condition_id'>
      <description>Component tfa9896 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.h' projectpath='codec/port/tfa9896'/>
        <file category='sourceC' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.c' projectpath='codec/port/tfa9896'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9896/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter' Cversion='2.2.0' condition='component.tfa9xxx_adapter.condition_id'>
      <description>Component tfa9xxx adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.h' projectpath='codec/port/tfa9xxx'/>
        <file category='sourceC' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.c' projectpath='codec/port/tfa9xxx'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9xxx/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='trng_adapter' Cversion='1.0.0' condition='component.trng_adapter.condition_id'>
      <description>Component trng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_trng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter' Cversion='1.0.0' condition='component.usart_adapter.condition_id'>
      <description>Component usart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_usart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_88w8987' Cversion='1.0.0' condition='component.wifi_bt_module.88W8987.condition_id'>
      <description>88W8987 Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.88W8987_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw416' Cversion='1.0.0' condition='component.wifi_bt_module.IW416.condition_id'>
      <description>IW416 Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.IW416_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_iw61x' Cversion='1.0.0' condition='component.wifi_bt_module.IW61X.condition_id'>
      <description>IW61X Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.IW61X_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rw61x' Cversion='1.0.0' condition='component.wifi_bt_module.RW61X.condition_id'>
      <description>RW61X Wi-Fi</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.RW61X_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am457_usd.condition_id'>
      <description>IW416-AW-AM457-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM457_USD
        #define WIFI_IW416_BOARD_AW_AM457_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am457_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am457ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am457ma.condition_id'>
      <description>IW416-AW-AM457MA-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM457MA
        #define WIFI_IW416_BOARD_AW_AM457MA
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am457ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_arduino' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am510_arduino.condition_id'>
      <description>FRDM-IW416-AW-AM510 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM510_ARDUINO
        #define WIFI_IW416_BOARD_AW_AM510_ARDUINO
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am510_arduino_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am510_usd.condition_id'>
      <description>IW416-AW-AM510-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM510_USD
        #define WIFI_IW416_BOARD_AW_AM510_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am510_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_am510ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_am510ma.condition_id'>
      <description>IW416-AW-AM510-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_AW_AM510MA
        #define WIFI_IW416_BOARD_AW_AM510MA
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_am510ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_cm358_usd.condition_id'>
      <description>AW-CM358-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_AW_CM358_USD
        #define WIFI_88W8987_BOARD_AW_CM358_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_cm358_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_cm358ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_cm358ma.condition_id'>
      <description>AW-CM358MA-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_AW_CM358MA
        #define WIFI_88W8987_BOARD_AW_CM358MA
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_cm358ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_nm191_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_nm191_usd.condition_id'>
      <description>AW-NM191-USD Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_nm191_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_aw_nm191ma' Cversion='1.0.0' condition='component.wifi_bt_module.board_aw_nm191ma.condition_id'>
      <description>AW-NM191MA-M2 Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_aw_nm191ma_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_frdm_rw61x' Cversion='1.0.0' condition='component.wifi_bt_module.board_frdm_rw61x.condition_id'>
      <description>FRDM-RW61X Wi-Fi board</description>
      <RTE_Components_h>
#ifndef WIFI_BOARD_FRDM_RW61X
#define WIFI_BOARD_FRDM_RW61X 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_frdm_rw61x_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_k32w061_trnscver' Cversion='1.0.0' condition='component.wifi_bt_module.board_k32w061_transceiver.condition_id'>
      <description>IK32W061-TRANSCEIVER Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_k32w061_transceiver_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1xk_m2.condition_id'>
      <description>IW416-MURATA-1XK-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_MURATA_1XK_M2
        #define WIFI_IW416_BOARD_MURATA_1XK_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1xk_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1xk_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1xk_usd.condition_id'>
      <description>IW416-MURATA-1XK-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_MURATA_1XK_USD
        #define WIFI_IW416_BOARD_MURATA_1XK_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1xk_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1zm_m2.condition_id'>
      <description>MURATA-1ZM-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_MURATA_1ZM_M2
        #define WIFI_88W8987_BOARD_MURATA_1ZM_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1zm_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_1zm_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_1zm_usd.condition_id'>
      <description>MURATA-1ZM-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_MURATA_1ZM_USD
        #define WIFI_88W8987_BOARD_MURATA_1ZM_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_1zm_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2dl_m2.condition_id'>
      <description>IW611-MURATA-2DL-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW611_BOARD_MURATA_2DL_M2
        #define WIFI_IW611_BOARD_MURATA_2DL_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2dl_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2dl_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2dl_usd.condition_id'>
      <description>IW611-MURATA-2DL-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW611_BOARD_MURATA_2DL_USD
        #define WIFI_IW611_BOARD_MURATA_2DL_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2dl_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ds_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2ds_m2.condition_id'>
      <description>MURATA-2DS-M2 Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2ds_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ds_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2ds_usd.condition_id'>
      <description>MURATA-2DS-USD Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2ds_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2el_m2.condition_id'>
      <description>IW612-MURATA-2EL-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW612_BOARD_MURATA_2EL_M2
        #define WIFI_IW612_BOARD_MURATA_2EL_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2el_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2el_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2el_usd.condition_id'>
      <description>IW612-MURATA-2EL-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW612_BOARD_MURATA_2EL_USD
        #define WIFI_IW612_BOARD_MURATA_2EL_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2el_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_murata_2ll_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_murata_2ll_m2.condition_id'>
      <description>IW610-MURATA-2LL-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW610_BOARD_MURATA_2LL_M2
        #define WIFI_IW610_BOARD_MURATA_2LL_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_murata_2ll_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_rd_rw61x' Cversion='1.0.0' condition='component.wifi_bt_module.board_rd_rw61x.condition_id'>
      <description>RD-RW61X Wi-Fi board</description>
      <RTE_Components_h>
#ifndef WIFI_BOARD_RW610
#define WIFI_BOARD_RW610 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_rd_rw61x_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w2_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_jody_w2_usd.condition_id'>
      <description>UBX-JODY-W2-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_88W8987_BOARD_UBX_JODY_W2_USD
        #define WIFI_88W8987_BOARD_UBX_JODY_W2_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_jody_w2_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_m2' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_jody_w5_m2.condition_id'>
      <description>UBX-JODY-W5-M2 Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_AW611_BOARD_UBX_JODY_W5_M2
        #define WIFI_AW611_BOARD_UBX_JODY_W5_M2
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_jody_w5_m2_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_jody_w5_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_jody_w5_usd.condition_id'>
      <description>UBX-JODY-W5-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_AW611_BOARD_UBX_JODY_W5_USD
        #define WIFI_AW611_BOARD_UBX_JODY_W5_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_jody_w5_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_lily_w1_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_lily_w1_usd.condition_id'>
      <description>UBX-LILY-W1-USD Wi-Fi board</description>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_lily_w1_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_ubx_maya_w1_usd' Cversion='1.0.0' condition='component.wifi_bt_module.board_ubx_maya_w1_usd.condition_id'>
      <description>UBX-MAYA-W1-USD Wi-Fi board</description>
      <RTE_Components_h>
        #ifndef WIFI_IW416_BOARD_UBX_MAYA_W1_USD
        #define WIFI_IW416_BOARD_UBX_MAYA_W1_USD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.board_ubx_maya_w1_usd_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='bt_only_fw' Cversion='1.0.0' condition='component.wifi_bt_module.bt_only_fw.condition_id'>
      <description>BT only firmware</description>
      <RTE_Components_h>
        #ifndef CONFIG_BT_ONLY_DNLD
        #define CONFIG_BT_ONLY_DNLD
        #endif
        #ifndef CONFIG_BT_IND_DNLD
        #define CONFIG_BT_IND_DNLD
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.bt_only_fw_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_module_config' Cversion='1.0.0' condition='component.wifi_bt_module.config.condition_id'>
      <description>Wi-Fi and BT module configs</description>
      <files>
        <file category='header' name='components/wifi_bt_module/incl/wifi_bt_module_config.h' projectpath='component/wifi_bt_module/incl'/>
        <file category='include' name='components/wifi_bt_module/incl/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tx_pwr_limits' Cversion='1.0.0' condition='component.wifi_bt_module.tx_pwr_limits.condition_id'>
      <description>Wi-Fi module Tx power limits</description>
      <files>
        <file category='header' name='components/wifi_bt_module/AzureWave/tx_pwr_limits/wlan_txpwrlimit_cfg_WW.h' projectpath='component/wifi_bt_module/AzureWave/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/AzureWave/tx_pwr_limits/wlan_txpwrlimit_cfg_WW_rw610.h' projectpath='component/wifi_bt_module/AzureWave/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_1XK_WW.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_1ZM_WW.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_2EL_WW.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/Murata/tx_pwr_limits/wlan_txpwrlimit_cfg_murata_NH.h' projectpath='component/wifi_bt_module/Murata/tx_pwr_limits'/>
        <file category='header' name='components/wifi_bt_module/u-blox/tx_pwr_limits/wlan_txpwrlimit_cfg_jody_w5_WW.h' projectpath='component/wifi_bt_module/u-blox/tx_pwr_limits'/>
        <file category='include' name='components/wifi_bt_module/AzureWave/tx_pwr_limits/'/>
        <file category='include' name='components/wifi_bt_module/Murata/tx_pwr_limits/'/>
        <file category='include' name='components/wifi_bt_module/u-blox/tx_pwr_limits/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wifi_bt_combo_fw' Cversion='1.0.0' condition='component.wifi_bt_module.wifi_bt_combo_fw.condition_id'>
      <description>Wi-Fi/BT combo firmware</description>
      <RTE_Components_h>
        #ifndef CONFIG_WIFI_BT_CMOBO_DNLD
        #define CONFIG_WIFI_BT_CMOBO_DNLD
        #endif
        #ifndef CONFIG_WIFI_IND_DNLD
        #define CONFIG_WIFI_IND_DNLD 0
        #endif
        #ifndef CONFIG_WIFI_IND_RESET
        #define CONFIG_WIFI_IND_RESET 0
        #endif
      </RTE_Components_h>
      <files>
        <file category='doc' name='components/wifi_bt_module/component.wifi_bt_module.wifi_bt_combo_fw_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter' Cversion='2.2.0' condition='component.wm8524_adapter.condition_id'>
      <description>Component wm8524 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.h' projectpath='codec/port/wm8524'/>
        <file category='sourceC' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.c' projectpath='codec/port/wm8524'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8524/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter' Cversion='2.2.0' condition='component.wm8904_adapter.condition_id'>
      <description>Component wm8904 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.h' projectpath='codec/port/wm8904'/>
        <file category='sourceC' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.c' projectpath='codec/port/wm8904'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8904/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter' Cversion='2.2.0' condition='component.wm8960_adapter.condition_id'>
      <description>Component wm8960 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.h' projectpath='codec/port/wm8960'/>
        <file category='sourceC' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.c' projectpath='codec/port/wm8960'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8960/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter' Cversion='2.2.0' condition='component.wm8962_adapter.condition_id'>
      <description>Component wm8962 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.h' projectpath='codec/port/wm8962'/>
        <file category='sourceC' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.c' projectpath='codec/port/wm8962'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8962/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT595S_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device MIMXRT595S_cmsis</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/fsl_device_registers.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm33.condition_id' category='header' name='devices/MIMXRT595S/MIMXRT595S_cm33.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm33.condition_id' category='header' name='devices/MIMXRT595S/MIMXRT595S_cm33_COMMON.h' projectpath='device'/>
        <file condition='allOf.core_ids=cm33.condition_id' category='header' name='devices/MIMXRT595S/MIMXRT595S_cm33_features.h' projectpath='device'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_ADC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_AHB_SECURE_CTRL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_AXI_SWITCH_AMIB.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_AXI_SWITCH_ASIB.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CACHE64_CTRL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CACHE64_POLSEL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CASPER.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CLKCTL0.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CLKCTL1.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CMP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CRC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_CTIMER.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_DEBUGGER_MAILBOX.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_DMA.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_DMIC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_FLEXCOMM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_FLEXIO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_FLEXSPI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_FREQME.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_GPIO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_HASHCRYPT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_I2C.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_I2S.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_I3C.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_INPUTMUX.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_IOPCTL.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_LCDIF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_MIPI_DSI_HOST.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_MRT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_MU.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_OCOTP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_OSTIMER.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_OTFAD.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_PINT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_PMC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_POWERQUAD.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_PUF.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_ROMCP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_RSTCTL0.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_RSTCTL1.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_RTC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_SCT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_SEMA42.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_SPI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_SYSCTL0.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_SYSCTL1.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_TRNG.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_USART.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_USBHSD.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_USBHSDCD.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_USBHSH.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_USBPHY.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_USDHC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_UTICK.h' projectpath='device/periph'/>
        <file category='header' name='devices/MIMXRT595S/periph/PERI_WWDT.h' projectpath='device/periph'/>
        <file category='include' name='devices/MIMXRT595S/'/>
        <file category='include' name='devices/MIMXRT595S/periph/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='header' attr='config' name='devices/MIMXRT595S/template/RTE_Device.h' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MIMXRT595S/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='MIMXRT595S_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device MIMXRT595S_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/arm/MIMXRT595Sxxxx_cm33_ram.scf' version='1.0.0' projectpath='MIMXRT595S/arm'/>
        <file condition='allOf.toolchains=mdk, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/arm/MIMXRT595Sxxxx_cm33_flash.scf' version='1.0.0' projectpath='MIMXRT595S/arm'/>
        <file condition='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/gcc/MIMXRT595Sxxxx_cm33_ram.ld' version='1.0.0' projectpath='MIMXRT595S/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/gcc/MIMXRT595Sxxxx_cm33_flash.ld' version='1.0.0' projectpath='MIMXRT595S/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/gcc/MIMXRT595Sxxxx_cm33_ram_s.ld' version='1.0.0' projectpath='MIMXRT595S/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/gcc/MIMXRT595Sxxxx_cm33_flash_s.ld' version='1.0.0' projectpath='MIMXRT595S/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/gcc/MIMXRT595Sxxxx_cm33_ram_ns.ld' version='1.0.0' projectpath='MIMXRT595S/gcc'/>
        <file condition='allOf.toolchains=armgcc, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/gcc/MIMXRT595Sxxxx_cm33_flash_ns.ld' version='1.0.0' projectpath='MIMXRT595S/gcc'/>
        <file condition='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_ram.icf' version='1.0.0' projectpath='MIMXRT595S/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_flash.icf' version='1.0.0' projectpath='MIMXRT595S/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_ram_s.icf' version='1.0.0' projectpath='MIMXRT595S/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_flash_s.icf' version='1.0.0' projectpath='MIMXRT595S/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_ram_ns.icf' version='1.0.0' projectpath='MIMXRT595S/iar'/>
        <file condition='allOf.toolchains=iar, cores=cm33, core_ids=cm33, device_ids=MIMXRT595S.condition_id' category='linkerScript' attr='config' name='devices/MIMXRT595S/iar/MIMXRT595Sxxxx_cm33_flash_ns.icf' version='1.0.0' projectpath='MIMXRT595S/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='MIMXRT595S' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template MIMXRT595S</description>
      <files>
        <file category='header' attr='config' name='devices/MIMXRT595S/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT595S/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MIMXRT595S/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT595S/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MIMXRT595S/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT595S/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MIMXRT595S/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MIMXRT595S/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MIMXRT595S/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device MIMXRT595S_startup</description>
      <files>
        <file condition='allOf.toolchains=iar, core_ids=cm33.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT595S/iar/startup_MIMXRT595S_cm33.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc, core_ids=cm33.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT595S/gcc/startup_MIMXRT595S_cm33.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk, core_ids=cm33.condition_id' category='sourceAsm' attr='config' name='devices/MIMXRT595S/arm/startup_MIMXRT595S_cm33.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MIMXRT595S_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device MIMXRT595S_system</description>
      <files>
        <file condition='allOf.core_ids=cm33.condition_id' category='sourceC' name='devices/MIMXRT595S/system_MIMXRT595S_cm33.c' projectpath='device'/>
        <file condition='allOf.core_ids=cm33.condition_id' category='header' name='devices/MIMXRT595S/system_MIMXRT595S_cm33.h' projectpath='device'/>
        <file category='include' name='devices/MIMXRT595S/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='acmp' Cversion='2.3.0' condition='driver.acmp.condition_id'>
      <description>ACMP Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_acmp.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_acmp.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497' Cversion='2.1.2' condition='driver.ak4497.condition_id'>
      <description>Driver ak4497</description>
      <RTE_Components_h>
#ifndef CODEC_AK4497_ENABLE
#define CODEC_AK4497_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/ak4497/fsl_ak4497.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/ak4497/fsl_ak4497.c' projectpath='codec'/>
        <file category='include' name='components/codec/ak4497/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cache_cache64' Cversion='2.0.11' condition='driver.cache_cache64.condition_id'>
      <description>CACHE Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_cache.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_cache.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common' Cversion='1.0.0' condition='driver.camera-common.condition_id'>
      <description>Driver camera-common</description>
      <files>
        <file category='header' name='components/video/camera/fsl_camera.h' projectpath='video'/>
        <file category='include' name='components/video/camera/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ap1302' Cversion='1.0.1' condition='driver.camera-device-ap1302.condition_id'>
      <description>Driver camera-device-ap1302</description>
      <files>
        <file category='header' name='components/video/camera/device/ap1302/fsl_ap1302.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ap1302/fsl_ap1302.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ap1302/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common' Cversion='1.0.0' condition='driver.camera-device-common.condition_id'>
      <description>Driver camera-device-common</description>
      <files>
        <file category='header' name='components/video/camera/device/fsl_camera_device.h' projectpath='video'/>
        <file category='include' name='components/video/camera/device/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-max9286' Cversion='1.0.2' condition='driver.camera-device-max9286.condition_id'>
      <description>Driver camera-device-max9286</description>
      <files>
        <file category='header' name='components/video/camera/device/max9286/fsl_max9286.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/max9286/fsl_max9286.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/max9286/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-mt9m114' Cversion='1.0.2' condition='driver.camera-device-mt9m114.condition_id'>
      <description>Driver camera-device-mt9m114</description>
      <files>
        <file category='header' name='components/video/camera/device/mt9m114/fsl_mt9m114.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/mt9m114/fsl_mt9m114.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/mt9m114/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov5640' Cversion='1.0.1' condition='driver.camera-device-ov5640.condition_id'>
      <description>Driver camera-device-ov5640</description>
      <files>
        <file category='header' name='components/video/camera/device/ov5640/fsl_ov5640.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov5640/fsl_ov5640.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov5640/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7670' Cversion='1.0.2' condition='driver.camera-device-ov7670.condition_id'>
      <description>Driver camera-device-ov7670</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7670/fsl_ov7670.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7670/fsl_ov7670.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7670/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7725' Cversion='1.0.1' condition='driver.camera-device-ov7725.condition_id'>
      <description>Driver camera-device-ov7725</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7725/fsl_ov7725.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7725/fsl_ov7725.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7725/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb' Cversion='1.0.1' condition='driver.camera-device-sccb.condition_id'>
      <description>Driver camera-device-sccb</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/camera/device/sccb/fsl_sccb.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/sccb/fsl_sccb.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/sccb/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-common' Cversion='1.0.0' condition='driver.camera-receiver-common.condition_id'>
      <description>Driver camera-receiver-common</description>
      <files>
        <file category='header' name='components/video/camera/receiver/fsl_camera_receiver.h' projectpath='video'/>
        <file category='include' name='components/video/camera/receiver/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='casper' Cversion='2.2.4' condition='driver.casper.condition_id'>
      <description>CASPER Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_casper.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_casper.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.7.1' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='flexcomm_i2c_cmsis' Cversion='2.3.0' Capiversion='2.3.0' condition='driver.cmsis_flexcomm_i2c.condition_id'>
      <description>I2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/cmsis_drivers/fsl_i2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/cmsis_drivers/fsl_i2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='flexcomm_spi_cmsis' Cversion='2.5.0' Capiversion='2.2.0' condition='driver.cmsis_flexcomm_spi.condition_id'>
      <description>SPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/cmsis_drivers/fsl_spi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/cmsis_drivers/fsl_spi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='flexcomm_usart_cmsis' Cversion='2.4.0' Capiversion='2.3.0' condition='driver.cmsis_flexcomm_usart.condition_id'>
      <description>USART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/cmsis_drivers/fsl_usart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/cmsis_drivers/fsl_usart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec' Cversion='2.3.1' condition='driver.codec.condition_id'>
      <description>Driver codec</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/fsl_codec_common.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/fsl_codec_common.c' projectpath='codec'/>
        <file category='include' name='components/codec/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm33.condition_id' category='sourceC' name='devices/MIMXRT595S/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm33.condition_id' category='header' name='devices/MIMXRT595S/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448' Cversion='2.0.1' condition='driver.cs42448.condition_id'>
      <description>Driver cs42448</description>
      <RTE_Components_h>
#ifndef CODEC_CS42448_ENABLE
#define CODEC_CS42448_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42448/fsl_cs42448.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42448/fsl_cs42448.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42448/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888' Cversion='2.1.3' condition='driver.cs42888.condition_id'>
      <description>Driver cs42888</description>
      <RTE_Components_h>
#ifndef CODEC_CS42888_ENABLE
#define CODEC_CS42888_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42888/fsl_cs42888.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42888/fsl_cs42888.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42888/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer' Cversion='2.3.3' condition='driver.ctimer.condition_id'>
      <description>CTimer Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_ctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_ctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi' Cversion='1.0.0' condition='driver.dbi.condition_id'>
      <description>Driver dbi</description>
      <files>
        <file category='header' name='components/video/display/dbi/fsl_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/fsl_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi_flexio_smartdma' Cversion='1.0.1' condition='driver.dbi_flexio_smartdma.condition_id'>
      <description>Driver dbi_flexio_smartdma</description>
      <files>
        <file category='header' name='components/video/display/dbi/flexio/fsl_dbi_flexio_smartdma.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/flexio/fsl_dbi_flexio_smartdma.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/flexio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi_lcdif' Cversion='1.0.1' condition='driver.dbi_lcdif.condition_id'>
      <description>Driver dbi_lcdif</description>
      <files>
        <file category='header' name='components/video/display/dbi/lcdif/fsl_dbi_lcdif.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/lcdif/fsl_dbi_lcdif.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/lcdif/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi_lpc_spi_dma' Cversion='1.0.0' condition='driver.dbi_lpc_spi_dma.condition_id'>
      <description>Driver dbi_lpc_spi_dma</description>
      <files>
        <file category='header' name='components/video/display/dbi/lpc_spi/fsl_dbi_spi_dma.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/lpc_spi/fsl_dbi_spi_dma.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/lpc_spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common' Cversion='1.0.0' condition='driver.dc-fb-common.condition_id'>
      <description>Driver dc-fb-common</description>
      <files>
        <file category='header' name='components/video/display/dc/fsl_dc_fb.h' projectpath='video'/>
        <file category='include' name='components/video/display/dc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dbi' Cversion='1.0.0' condition='driver.dc-fb-dbi.condition_id'>
      <description>Driver dc-fb-dbi</description>
      <RTE_Components_h>
#ifndef MCUX_DBI_LEGACY
#define MCUX_DBI_LEGACY 0
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dsi-cmd' Cversion='1.1.1' condition='driver.dc-fb-dsi-cmd.condition_id'>
      <description>Driver dc-fb-dsi-cmd</description>
      <files>
        <file category='header' name='components/video/display/dc/dsi_cmd/fsl_dc_fb_dsi_cmd.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dsi_cmd/fsl_dc_fb_dsi_cmd.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dsi_cmd/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-lcdif' Cversion='1.0.1' condition='driver.dc-fb-lcdif.condition_id'>
      <description>Driver dc-fb-lcdif</description>
      <files>
        <file category='header' name='components/video/display/dc/lcdif/fsl_dc_fb_lcdif.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/lcdif/fsl_dc_fb_lcdif.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/lcdif/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-ssd1963' Cversion='1.0.2' condition='driver.dc-fb-ssd1963.condition_id'>
      <description>Driver dc-fb-ssd1963</description>
      <files>
        <file category='header' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dialog7212' Cversion='2.3.1' condition='driver.dialog7212.condition_id'>
      <description>Driver dialog7212</description>
      <RTE_Components_h>
#ifndef CODEC_DA7212_ENABLE
#define CODEC_DA7212_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/da7212/fsl_dialog7212.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/da7212/fsl_dialog7212.c' projectpath='codec'/>
        <file category='include' name='components/codec/da7212/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-adv7535' Cversion='1.0.1' condition='driver.display-adv7535.condition_id'>
      <description>Driver display-adv7535</description>
      <files>
        <file category='header' name='components/video/display/adv7535/fsl_adv7535.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/adv7535/fsl_adv7535.c' projectpath='video'/>
        <file category='include' name='components/video/display/adv7535/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-co5300' Cversion='1.0.0' condition='driver.display-co5300.condition_id'>
      <description>Driver display-co5300</description>
      <files>
        <file category='header' name='components/video/display/co5300/fsl_co5300.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/co5300/fsl_co5300.c' projectpath='video'/>
        <file category='include' name='components/video/display/co5300/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-common' Cversion='1.0.0' condition='driver.display-common.condition_id'>
      <description>Driver display-common</description>
      <files>
        <file category='header' name='components/video/display/fsl_display.h' projectpath='video'/>
        <file category='include' name='components/video/display/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-hx8394' Cversion='1.0.0' condition='driver.display-hx8394.condition_id'>
      <description>Driver display-hx8394</description>
      <files>
        <file category='header' name='components/video/display/hx8394/fsl_hx8394.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/hx8394/fsl_hx8394.c' projectpath='video'/>
        <file category='include' name='components/video/display/hx8394/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6161' Cversion='1.0.0' condition='driver.display-it6161.condition_id'>
      <description>Driver display-it6161</description>
      <files>
        <file category='header' name='components/video/display/it6161/fsl_it6161.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/fsl_it6161.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/hdmi_tx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/hdmi_tx.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/mipi_rx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/mipi_rx.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6161/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6263' Cversion='1.0.1' condition='driver.display-it6263.condition_id'>
      <description>Driver display-it6263</description>
      <files>
        <file category='header' name='components/video/display/it6263/fsl_it6263.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6263/fsl_it6263.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6263/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-mipi-dsi-cmd' Cversion='1.0.2' condition='driver.display-mipi-dsi-cmd.condition_id'>
      <description>Driver display-mipi-dsi-cmd</description>
      <files>
        <file category='header' name='components/video/display/mipi_dsi_cmd/fsl_mipi_dsi_cmd.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/mipi_dsi_cmd/fsl_mipi_dsi_cmd.c' projectpath='video'/>
        <file category='include' name='components/video/display/mipi_dsi_cmd/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm67162' Cversion='1.0.2' condition='driver.display-rm67162.condition_id'>
      <description>Driver display-rm67162</description>
      <files>
        <file category='header' name='components/video/display/rm67162/fsl_rm67162.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm67162/fsl_rm67162.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm67162/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm67191' Cversion='1.1.0' condition='driver.display-rm67191.condition_id'>
      <description>Driver display-rm67191</description>
      <files>
        <file category='header' name='components/video/display/rm67191/fsl_rm67191.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm67191/fsl_rm67191.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm67191/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm68191' Cversion='1.1.0' condition='driver.display-rm68191.condition_id'>
      <description>Driver display-rm68191</description>
      <files>
        <file category='header' name='components/video/display/rm68191/fsl_rm68191.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm68191/fsl_rm68191.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm68191/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm68200' Cversion='1.1.1' condition='driver.display-rm68200.condition_id'>
      <description>Driver display-rm68200</description>
      <files>
        <file category='header' name='components/video/display/rm68200/fsl_rm68200.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm68200/fsl_rm68200.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm68200/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rm692c9' Cversion='1.1.0' condition='driver.display-rm692c9.condition_id'>
      <description>Driver display-rm692c9</description>
      <files>
        <file category='header' name='components/video/display/rm692c9/fsl_rm692c9.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rm692c9/fsl_rm692c9.c' projectpath='video'/>
        <file category='include' name='components/video/display/rm692c9/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-rpi' Cversion='1.0.0' condition='driver.display-rpi.condition_id'>
      <description>Driver display-rpi</description>
      <files>
        <file category='header' name='components/video/display/rpi/fsl_rpi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/rpi/fsl_rpi.c' projectpath='video'/>
        <file category='include' name='components/video/display/rpi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-sn65dsi83' Cversion='1.0.0' condition='driver.display-sn65dsi83.condition_id'>
      <description>Driver display-sn65dsi83</description>
      <files>
        <file category='header' name='components/video/display/sn65dsi83/fsl_sn65dsi83.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/sn65dsi83/fsl_sn65dsi83.c' projectpath='video'/>
        <file category='include' name='components/video/display/sn65dsi83/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmic' Cversion='2.3.2' condition='driver.dmic.condition_id'>
      <description>DMIC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_dmic.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_dmic.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmic_dma' Cversion='2.4.0' condition='driver.dmic_dma.condition_id'>
      <description>DMIC DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_dmic_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_dmic_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmic_hwvad' Cversion='2.3.0' condition='driver.dmic_hwvad.condition_id'>
      <description>DMIC HWVAD Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_dmic.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_dmic.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dsp' Cversion='2.0.1' condition='driver.dsp.condition_id'>
      <description>dsp Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_dsp.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_dsp.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ektf2k' Cversion='1.0.0' condition='driver.ektf2k.condition_id'>
      <description>Driver ektf2k</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/ektf2k/fsl_ektf2k.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ektf2k/fsl_ektf2k.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ektf2k/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm' Cversion='2.0.2' condition='driver.flexcomm.condition_id'>
      <description>FLEXCOMM Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexcomm.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexcomm.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.3.3' condition='driver.flexcomm_i2c.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i2c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i2c.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_dma' Cversion='2.3.1' condition='driver.flexcomm_i2c_dma.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i2c_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i2c_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2s' Cversion='2.3.2' condition='driver.flexcomm_i2s.condition_id'>
      <description>i2s Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i2s.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i2s.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2s_dma' Cversion='2.3.3' condition='driver.flexcomm_i2s_dma.condition_id'>
      <description>i2s Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i2s_dma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i2s_dma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi' Cversion='2.3.2' condition='driver.flexcomm_spi.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_spi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_spi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi_dma' Cversion='2.2.1' condition='driver.flexcomm_spi_dma.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_spi_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_spi_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart' Cversion='2.8.5' condition='driver.flexcomm_usart.condition_id'>
      <description>usart Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_usart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_usart.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart_dma' Cversion='2.3.1' condition='driver.flexcomm_usart_dma.condition_id'>
      <description>usart Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_usart_dma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_usart_dma.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio' Cversion='2.3.0' condition='driver.flexio.condition_id'>
      <description>FLEXIO Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2c_master' Cversion='2.6.1' condition='driver.flexio_i2c_master.condition_id'>
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio_i2c_master.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio_i2c_master.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2s' Cversion='2.2.1' condition='driver.flexio_i2s.condition_id'>
      <description>FLEXIO I2S Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio_i2s.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio_i2s.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd' Cversion='2.3.0' condition='driver.flexio_mculcd.condition_id'>
      <description>FLEXIO MCULCD Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio_mculcd.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio_mculcd.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd_smartdma' Cversion='2.0.5' condition='driver.flexio_mculcd_smartdma.condition_id'>
      <description>FLEXIO MCULCD SMARTDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio_mculcd_smartdma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio_mculcd_smartdma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi' Cversion='2.4.2' condition='driver.flexio_spi.condition_id'>
      <description>FLEXIO SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio_spi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio_spi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart' Cversion='2.6.2' condition='driver.flexio_uart.condition_id'>
      <description>FLEXIO UART Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexio_uart.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexio_uart.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi' Cversion='2.7.0' condition='driver.flexspi.condition_id'>
      <description>FLEXSPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexspi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexspi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexspi_dma' Cversion='2.2.1' condition='driver.flexspi_dma.condition_id'>
      <description>FLEXSPI DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_flexspi_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_flexspi_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fmeas' Cversion='2.1.1' condition='driver.fmeas.condition_id'>
      <description>FMEAS Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_fmeas.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_fmeas.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft3267' Cversion='1.0.1' condition='driver.ft3267.condition_id'>
      <description>Touch panel controller FT3267 driver</description>
      <files>
        <file category='header' name='components/touch/ft3267/fsl_ft3267.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft3267/fsl_ft3267.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft3267/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406' Cversion='1.0.0' condition='driver.ft5406.condition_id'>
      <description>Driver ft5406</description>
      <files>
        <file category='header' name='components/touch/ft5406/fsl_ft5406.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406/fsl_ft5406.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft6x06' Cversion='1.0.0' condition='driver.ft6x06.condition_id'>
      <description>Driver ft6x06</description>
      <files>
        <file category='header' name='components/touch/ft6x06/fsl_ft6x06.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft6x06/fsl_ft6x06.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft6x06/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxas21002cq' Cversion='1.0.0' condition='driver.fxas21002cq.condition_id'>
      <description>Driver fxas21002cq</description>
      <files>
        <file category='header' name='components/sensor/fxas21002cq/fsl_fxas.h' projectpath='gyroscope'/>
        <file category='sourceC' name='components/sensor/fxas21002cq/fsl_fxas.c' projectpath='gyroscope'/>
        <file category='include' name='components/sensor/fxas21002cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxls8974cf' Cversion='1.0.0' condition='driver.fxls8974cf.condition_id'>
      <description>Driver fxls8974cf</description>
      <files>
        <file category='header' name='components/sensor/fxls8974cf/fsl_fxls.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxls8974cf/fsl_fxls.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxls8974cf/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxos8700cq' Cversion='1.0.0' condition='driver.fxos8700cq.condition_id'>
      <description>Driver fxos8700cq</description>
      <files>
        <file category='header' name='components/sensor/fxos8700cq/fsl_fxos.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxos8700cq/fsl_fxos.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxos8700cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gt911' Cversion='1.0.4' condition='driver.gt911.condition_id'>
      <description>Driver gt911</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/gt911/fsl_gt911.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/gt911/fsl_gt911.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/gt911/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='hashcrypt' Cversion='2.2.16' condition='driver.hashcrypt.condition_id'>
      <description>Hashcrypt Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_hashcrypt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_hashcrypt.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='htu21d' Cversion='1.0.0' condition='driver.htu21d.condition_id'>
      <description>Driver htu21d</description>
      <files>
        <file category='header' name='components/sensor/htu21d/fsl_htu21d.h' projectpath='htu'/>
        <file category='sourceC' name='components/sensor/htu21d/fsl_htu21d.c' projectpath='htu'/>
        <file category='include' name='components/sensor/htu21d/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2s_bridge' Cversion='2.0.0' condition='driver.i2s_bridge.condition_id'>
      <description>I2S Bridge Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i2s_bridge.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i2s_bridge.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c' Cversion='2.14.1' condition='driver.i3c.condition_id'>
      <description>I3C Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i3c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i3c.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_dma' Cversion='2.1.8' condition='driver.i3c_dma.condition_id'>
      <description>I3C DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_i3c_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_i3c_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iap3' Cversion='2.1.3' condition='driver.iap3.condition_id'>
      <description>IAP3 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_iap.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_iap.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='icm42688p' Cversion='1.0.0' condition='driver.icm42688p.condition_id'>
      <description>Driver icm42688p</description>
      <files>
        <file category='header' name='components/sensor/icm42688p/fsl_icm42688p.h' projectpath='icm42688p'/>
        <file category='sourceC' name='components/sensor/icm42688p/fsl_icm42688p.c' projectpath='icm42688p'/>
        <file category='include' name='components/sensor/icm42688p/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ili9341' Cversion='1.0.2' condition='driver.ili9341.condition_id'>
      <description>Driver ili9341</description>
      <files>
        <file category='header' name='components/display/ili9341/fsl_ili9341.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ili9341/fsl_ili9341.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ili9341/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux' Cversion='2.0.9' condition='driver.inputmux.condition_id'>
      <description>INPUTMUX Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_inputmux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_inputmux.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections' Cversion='2.0.0' condition='driver.inputmux_connections.condition_id'>
      <description>Inputmux_connections Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_inputmux_connections.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lcdif' Cversion='2.3.1' condition='driver.lcdif.condition_id'>
      <description>LCDIF Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_lcdif.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_lcdif.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpadc' Cversion='2.9.3' condition='driver.lpadc.condition_id'>
      <description>LPADC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_lpadc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_lpadc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc' Cversion='2.1.1' condition='driver.lpc_crc.condition_id'>
      <description>CRC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_crc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_crc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dma' Cversion='2.5.3' condition='driver.lpc_dma.condition_id'>
      <description>DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.1.7' condition='driver.lpc_gpio.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iopctl' Cversion='2.0.0' condition='driver.lpc_iopctl.condition_id'>
      <description>iopctl Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_iopctl.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_rtc' Cversion='2.2.0' condition='driver.lpc_rtc.condition_id'>
      <description>RTC Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_rtc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_rtc.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lsm6dso' Cversion='1.0.0' condition='driver.lsm6dso.condition_id'>
      <description>Driver lsm6dso</description>
      <files>
        <file category='header' name='components/sensor/lsm6dso/fsl_lsm.h' projectpath='tilt_pedo'/>
        <file category='sourceC' name='components/sensor/lsm6dso/fsl_lsm.c' projectpath='tilt_pedo'/>
        <file category='include' name='components/sensor/lsm6dso/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='max30101' Cversion='1.0.0' condition='driver.max30101.condition_id'>
      <description>Driver max30101</description>
      <files>
        <file category='header' name='components/sensor/max30101/fsl_max.h' projectpath='heartrate'/>
        <file category='sourceC' name='components/sensor/max30101/fsl_max.c' projectpath='heartrate'/>
        <file category='include' name='components/sensor/max30101/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='memory' Cversion='2.0.0' condition='driver.memory.condition_id'>
      <description>MEMORY Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_memory.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_dsi' Cversion='2.2.2' condition='driver.mipi_dsi.condition_id'>
      <description>MIPI DSI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_mipi_dsi.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_mipi_dsi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mipi_dsi_smartdma' Cversion='2.3.2' condition='driver.mipi_dsi_smartdma.condition_id'>
      <description>MIPI DSI Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_mipi_dsi_smartdma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_mipi_dsi_smartdma.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='soc_mipi_dsi' Cversion='2.0.0' condition='driver.mipi_dsi_soc.condition_id'>
      <description>SOC MIPI DSI Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_soc_mipi_dsi.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8451q' Cversion='1.0.0' condition='driver.mma8451q.condition_id'>
      <description>Driver mma8451q</description>
      <files>
        <file category='header' name='components/sensor/mma8451q/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8451q/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8451q/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8652fc' Cversion='1.0.0' condition='driver.mma8652fc.condition_id'>
      <description>Driver mma8652fc</description>
      <files>
        <file category='header' name='components/sensor/mma8652fc/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8652fc/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8652fc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mrt' Cversion='2.0.5' condition='driver.mrt.condition_id'>
      <description>MRT Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_mrt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_mrt.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mu' Cversion='2.2.0' condition='driver.mu.condition_id'>
      <description>MU Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_mu.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_mu.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nmh1000' Cversion='1.0.0' condition='driver.nmh1000.condition_id'>
      <description>Driver nmh1000</description>
      <files>
        <file category='header' name='components/sensor/nmh1000/fsl_nmh1000.h' projectpath='nmh1000'/>
        <file category='sourceC' name='components/sensor/nmh1000/fsl_nmh1000.c' projectpath='nmh1000'/>
        <file category='include' name='components/sensor/nmh1000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer' Cversion='2.2.4' condition='driver.ostimer.condition_id'>
      <description>OSTimer Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_ostimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_ostimer.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='otfad' Cversion='2.1.4' condition='driver.otfad.condition_id'>
      <description>OTFAD Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_otfad.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_otfad.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='p3t1755' Cversion='2.0.0' condition='driver.p3t1755.condition_id'>
      <description>Driver p3t1755</description>
      <files>
        <file category='header' name='components/sensor/p3t1755/fsl_p3t1755.h' projectpath='component/p3t1755'/>
        <file category='sourceC' name='components/sensor/p3t1755/fsl_p3t1755.c' projectpath='component/p3t1755'/>
        <file category='include' name='components/sensor/p3t1755/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pca9420' Cversion='1.0.0' condition='driver.pca9420.condition_id'>
      <description>Driver pca9420</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/pmic/pca9420/fsl_pca9420.c' projectpath='component/pmic/pca9420'/>
        <file category='header' name='components/pmic/pca9420/fsl_pca9420.h' projectpath='component/pmic/pca9420'/>
        <file category='include' name='components/pmic/pca9420/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pca9422' Cversion='1.0.0' condition='driver.pca9422.condition_id'>
      <description>Driver pca9422</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/pmic/pca9422/fsl_pca9422.c' projectpath='component/pmic/pca9422'/>
        <file category='header' name='components/pmic/pca9422/fsl_pca9422.h' projectpath='component/pmic/pca9422'/>
        <file category='include' name='components/pmic/pca9422/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x' Cversion='2.0.1' condition='driver.pcm186x.condition_id'>
      <description>Driver pcm186x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM186X_ENABLE
#define CODEC_PCM186X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm186x/fsl_pcm186x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm186x/fsl_pcm186x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm186x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x' Cversion='2.0.1' condition='driver.pcm512x.condition_id'>
      <description>Driver pcm512x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM512X_ENABLE
#define CODEC_PCM512X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm512x/fsl_pcm512x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm512x/fsl_pcm512x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm512x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf1550' Cversion='1.0.0' condition='driver.pf1550.condition_id'>
      <description>Driver pf1550</description>
      <files>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550.h' projectpath='component/pmic/pf1550'/>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550_charger.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550_charger.h' projectpath='component/pmic/pf1550'/>
        <file category='include' name='components/pmic/pf1550/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf3000' Cversion='1.0.0' condition='driver.pf3000.condition_id'>
      <description>Driver pf3000</description>
      <files>
        <file category='sourceC' name='components/pmic/pf3000/fsl_pf3000.c' projectpath='component/pmic/pf3000'/>
        <file category='header' name='components/pmic/pf3000/fsl_pf3000.h' projectpath='component/pmic/pf3000'/>
        <file category='include' name='components/pmic/pf3000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf5020' Cversion='2.0.0' condition='driver.pf5020.condition_id'>
      <description>Driver pf5020</description>
      <files>
        <file category='sourceC' name='components/pmic/pf5020/fsl_pf5020.c' projectpath='component/pmic/pf5020'/>
        <file category='header' name='components/pmic/pf5020/fsl_pf5020.h' projectpath='component/pmic/pf5020'/>
        <file category='include' name='components/pmic/pf5020/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pint' Cversion='2.2.0' condition='driver.pint.condition_id'>
      <description>PINT Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_pint.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_pint.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='power' Cversion='2.6.1' condition='driver.power.condition_id'>
      <description>Power driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_power.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_power.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='powerquad' Cversion='2.2.0' condition='driver.powerquad.condition_id'>
      <description>POWERQUAD Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_powerquad_data.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_powerquad_data.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_powerquad.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_powerquad_basic.c' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_powerquad_math.c' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_powerquad_matrix.c' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_powerquad_filter.c' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_powerquad_transform.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='psp27801' Cversion='1.0.0' condition='driver.psp27801.condition_id'>
      <description>Driver psp27801</description>
      <files>
        <file category='header' name='components/display/psp27801/fsl_psp27801.h' projectpath='oled'/>
        <file category='sourceC' name='components/display/psp27801/fsl_psp27801.c' projectpath='oled'/>
        <file category='include' name='components/display/psp27801/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='PUF' Cversion='2.2.0' condition='driver.puf.condition_id'>
      <description>PUF Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_puf.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_puf.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset' Cversion='2.1.0' condition='driver.reset.condition_id'>
      <description>Reset Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_reset.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_reset.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sctimer' Cversion='2.5.1' condition='driver.sctimer.condition_id'>
      <description>SCT Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_sctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_sctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sema42' Cversion='2.1.0' condition='driver.sema42.condition_id'>
      <description>SEMA42 Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_sema42.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_sema42.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl5000' Cversion='2.1.1' condition='driver.sgtl5000.condition_id'>
      <description>Driver sgtl5000</description>
      <RTE_Components_h>
#ifndef CODEC_SGTL5000_ENABLE
#define CODEC_SGTL5000_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/sgtl5000/fsl_sgtl5000.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/sgtl5000/fsl_sgtl5000.c' projectpath='codec'/>
        <file category='include' name='components/codec/sgtl5000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='smartdma' Cversion='2.13.0' condition='driver.smartdma.condition_id'>
      <description>SMARTDMA Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_smartdma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_smartdma.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_smartdma_prv.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='smartdma_rt500' Cversion='2.12.0' condition='driver.smartdma_rt500.condition_id'>
      <description>SMARTDMA Driver for RT500</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_smartdma_rt500.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_smartdma_rt500.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963' Cversion='1.2.0' condition='driver.ssd1963.condition_id'>
      <description>Driver ssd1963</description>
      <files>
        <file category='header' name='components/display/ssd1963/fsl_ssd1963.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ssd1963/fsl_ssd1963.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='st7796s' Cversion='1.0.0' condition='driver.st7796s.condition_id'>
      <description>Driver st7796s</description>
      <files>
        <file category='header' name='components/display/st7796s/fsl_st7796s.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/st7796s/fsl_st7796s.c' projectpath='lcdc'/>
        <file category='include' name='components/display/st7796s/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896' Cversion='6.0.2' condition='driver.tfa9896.condition_id'>
      <description>Driver tfa9896</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9896_ENABLE
#define CODEC_TFA9896_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896_buffer.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_hal_registers.c' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_tfa9896.c' projectpath='codec'/>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896.h' projectpath='codec'/>
        <file category='doc' name='components/codec/tfa9896/MIMXRT595595-EVK_TFA9896_SW.pdf' projectpath='codec'/>
        <file category='include' name='components/codec/tfa9896/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx' Cversion='2.1.0' condition='driver.tfa9xxx.condition_id'>
      <description>Driver tfa9xxx</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9XXX_ENABLE
#define CODEC_TFA9XXX_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9892N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N2.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx.c' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/fsl_tfa9xxx.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx_IMX.c' projectpath='codec/tfa9xxx'/>
        <file category='doc' name='components/codec/tfa9xxx/README.md' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/config.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dsp_fw.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_init.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa9xxx_parameters.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_container_crc32.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_haptic_fw_defs.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
        <file category='include' name='components/codec/tfa9xxx/vas_tfa_drv/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_hal' Cversion='2.1.0' condition='driver.tfa9xxx_hal.condition_id'>
      <description>Driver tfa9xxx_hal</description>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_device_hal.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/tfa_device_hal.c' projectpath='codec/tfa9xxx'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tma525b' Cversion='1.0.0' condition='driver.tma525b.condition_id'>
      <description>Driver tma525b</description>
      <files>
        <file category='header' name='components/touch/tma525b/fsl_tma525b.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/tma525b/fsl_tma525b.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/tma525b/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='trng' Cversion='2.0.18' condition='driver.trng.condition_id'>
      <description>TRNG Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_trng.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_trng.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tsl2561' Cversion='1.0.0' condition='driver.tsl2561.condition_id'>
      <description>Driver tsl2561</description>
      <files>
        <file category='header' name='components/sensor/tsl2561/fsl_tsl2561.h' projectpath='tsl'/>
        <file category='sourceC' name='components/sensor/tsl2561/fsl_tsl2561.c' projectpath='tsl'/>
        <file category='include' name='components/sensor/tsl2561/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sdhc' Cversion='2.8.5' condition='driver.usdhc.condition_id'>
      <description>USDHC Driver</description>
      <files>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_usdhc.c' projectpath='drivers'/>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_usdhc.h' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='utick' Cversion='2.0.5' condition='driver.utick.condition_id'>
      <description>UTICK Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_utick.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_utick.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-common' Cversion='1.1.0' condition='driver.video-common.condition_id'>
      <description>Driver video-common</description>
      <files>
        <file category='header' name='components/video/fsl_video_common.h' projectpath='video'/>
        <file category='sourceC' name='components/video/fsl_video_common.c' projectpath='video'/>
        <file category='include' name='components/video/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c' Cversion='1.0.1' condition='driver.video-i2c.condition_id'>
      <description>Driver video-i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/i2c/fsl_video_i2c.h' projectpath='video'/>
        <file category='sourceC' name='components/video/i2c/fsl_video_i2c.c' projectpath='video'/>
        <file category='include' name='components/video/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524' Cversion='2.1.1' condition='driver.wm8524.condition_id'>
      <description>Driver wm8524</description>
      <RTE_Components_h>
#ifndef CODEC_WM8524_ENABLE
#define CODEC_WM8524_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8524/fsl_wm8524.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8524/fsl_wm8524.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8524/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904' Cversion='2.5.1' condition='driver.wm8904.condition_id'>
      <description>Driver wm8904</description>
      <RTE_Components_h>
#ifndef CODEC_WM8904_ENABLE
#define CODEC_WM8904_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8904/fsl_wm8904.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8904/fsl_wm8904.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8904/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960' Cversion='2.2.4' condition='driver.wm8960.condition_id'>
      <description>Driver wm8960</description>
      <RTE_Components_h>
#ifndef CODEC_WM8960_ENABLE
#define CODEC_WM8960_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8960/fsl_wm8960.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8960/fsl_wm8960.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8960/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962' Cversion='2.2.0' condition='driver.wm8962.condition_id'>
      <description>Driver wm8962</description>
      <RTE_Components_h>
#ifndef CODEC_WM8962_ENABLE
#define CODEC_WM8962_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8962/fsl_wm8962.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8962/fsl_wm8962.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8962/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wwdt' Cversion='2.1.9' condition='driver.wwdt.condition_id'>
      <description>WWDT Driver</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/drivers/fsl_wwdt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MIMXRT595S/drivers/fsl_wwdt.c' projectpath='drivers'/>
        <file category='include' name='devices/MIMXRT595S/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MIMXRT595S/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MIMXRT595S/utilities/fsl_sbrk.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc, mdk, cores=cm33.condition_id' category='sourceAsm' name='devices/MIMXRT595S/utilities/fsl_memcpy.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MIMXRT595S/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MIMXRT595S/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MIMXRT595S/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MIMXRT595S/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/MIMXRT595S/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/MIMXRT595S/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/MIMXRT595S/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/MIMXRT595S/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/MIMXRT595S/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/MIMXRT595S/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MIMXRT595S/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/MIMXRT595S/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/MIMXRT595S/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/MIMXRT595S/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/MIMXRT595S/utilities/str/'/>
      </files>
    </component>
  </components>
</package>