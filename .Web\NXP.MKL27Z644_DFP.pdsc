<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MKL27Z644_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MKL27Z644</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Project Template">NXP MCUXpresso SDK RTE Device Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="CMSIS Driver">NXP MCUXpresso SDK Peripheral CMSIS Drivers</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="MKL27Z644" Dvendor="NXP:11">
      <description>Kinetis KL2x-48 MHz, USB Ultra-Low-Power Microcontrollers (MCUs) based on ARM Cortex-M0+ Core</description>
      <device Dname="MKL27Z32xxx4">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="48000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKL27Z32xxx4_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x8000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x1ffff800" size="0x2000" access="rw" default="1"/>
        <memory name="USB_RAM" start="0x400fe000" size="0x0200" access="rw" default="1"/>
        <algorithm name="arm/MK_P32_48MHZ_KL43.FLM" start="0x00000000" size="0x00008000" RAMstart="0x1ffff800" RAMsize="0x00000800" default="1"/>
        <debug svd="MKL27Z644.xml"/>
        <variant Dvariant="MKL27Z32VDA4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z32VDA4"/>
        </variant>
        <variant Dvariant="MKL27Z32VLH4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z32VLH4"/>
        </variant>
        <variant Dvariant="MKL27Z32VFM4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z32VFM4"/>
        </variant>
        <variant Dvariant="MKL27Z32VMP4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z32VMP4"/>
        </variant>
        <variant Dvariant="MKL27Z32VFT4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z32VFT4"/>
        </variant>
      </device>
      <device Dname="MKL27Z64xxx4">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="48000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKL27Z64xxx4_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x010000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x1ffff000" size="0x4000" access="rw" default="1"/>
        <memory name="USB_RAM" start="0x400fe000" size="0x0200" access="rw" default="1"/>
        <algorithm name="arm/MK_P64_48MHZ_KL43.FLM" start="0x00000000" size="0x00010000" RAMstart="0x1ffff000" RAMsize="0x00000800" default="1"/>
        <debug svd="MKL27Z644.xml"/>
        <variant Dvariant="MKL27Z64VDA4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z64VDA4"/>
        </variant>
        <variant Dvariant="MKL27Z64VFT4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z64VFT4"/>
        </variant>
        <variant Dvariant="MKL27Z64VMP4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z64VMP4"/>
        </variant>
        <variant Dvariant="MKL27Z64VFM4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z64VFM4"/>
        </variant>
        <variant Dvariant="MKL27Z64VLH4">
          <compile header="fsl_device_registers.h" define="CPU_MKL27Z64VLH4"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.MKL27Z644">
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VLH4" Dvendor="NXP:11"/>
    </condition>
    <condition id="component.serial_manager_AND_utility.debug_console">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
    </condition>
    <condition id="_component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite">
      <accept condition="component.serial_manager_AND_utility.debug_console"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="device.MKL27Z644_AND___component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite__AND_component.lpuart_adapter_AND_device.MKL27Z644_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.i2c_AND_driver.lpuart_AND_driver.port_AND_driver.smc">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKL27Z644_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require condition="_component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.dmamux">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.osa_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="osa"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.crc">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="crc"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.flash">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flash"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.gpio_AND_driver.port">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.i2c">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.log_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="log"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.log_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="log"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.lptmr">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.lpuart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.lists_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.pit">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="pit"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.tpm">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="tpm"/>
    </condition>
    <condition id="component.serial_manager_uart_OR_component.serial_manager_virtual">
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_virtual"/>
    </condition>
    <condition id="device.MKL27Z644_AND__component.serial_manager_uart_OR_component.serial_manager_virtual__AND_component.lists_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.serial_manager_uart_OR_component.serial_manager_virtual"/>
    </condition>
    <condition id="driver.lpuart_OR_driver.uart">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MKL27Z644_AND__driver.lpuart_OR_driver.uart__AND_component.lpuart_adapter_AND_component.serial_manager">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require condition="driver.lpuart_OR_driver.uart"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.serial_manager_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="tpm_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="pit_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter"/>
    </condition>
    <condition id="device.MKL27Z644_AND__component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter__AND_component.lists_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.uart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
    </condition>
    <condition id="device.MKL27Z644_AND_CMSIS_Include_core_cm0plus">
      <require condition="device.MKL27Z644"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="armclang_OR_iar">
      <accept Tcompiler="ARMCC" Toptions="AC6"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="device.MKL27Z644_AND__armclang_OR_iar__AND_device.MKL27Z644_system">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKL27Z644_system"/>
      <require condition="armclang_OR_iar"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="device.MKL27Z644_AND_device.MKL27Z644_CMSIS">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKL27Z644_header"/>
    </condition>
    <condition id="device.MKL27Z644_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL27Z644_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL27Z644_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.spi">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" Capiversion="2.2.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL27Z644_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.uart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL27Z644_AND_device.MKL27Z644_CMSIS_AND_driver.clock">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKL27Z644_header"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.common_AND_driver.dmamux">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.flexio">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.flexio_i2s">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.flexio_spi">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.flexio_uart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.i2c">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.lpuart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.spi">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL27Z644_AND_driver.dma_AND_driver.uart">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL27Z644_AND_utility.debug_console">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKL27Z644_AND_utility.debug_console_lite">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.lpuart_adapter_AND_driver.common">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKL27Z644_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="MKL27Z644" Cversion="1.0.0" condition="device.MKL27Z644_AND___component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite__AND_component.lpuart_adapter_AND_device.MKL27Z644_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.i2c_AND_driver.lpuart_AND_driver.port_AND_driver.smc" isDefaultVariant="1">
      <description>Devices_project_template MKL27Z644; {for-development:SDK-Manifest-ID: project_template.MKL27Z644.MKL27Z644}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.dma_AND_driver.dmamux">
      <description>Rte_device; {for-development:SDK-Manifest-ID: RTE_Device.MKL27Z644}</description>
      <files>
        <file category="header" attr="config" name="template/RTE_Device.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="button" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component button; {for-development:SDK-Manifest-ID: component.button.MKL27Z644}</description>
      <files>
        <file category="header" name="components/button/fsl_component_button.h"/>
        <file category="sourceC" name="components/button/fsl_component_button.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common_task" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.osa_AND_driver.common">
      <description>Component common_task; {for-development:SDK-Manifest-ID: component.common_task.MKL27Z644}</description>
      <files>
        <file category="header" name="components/common_task/fsl_component_common_task.h"/>
        <file category="sourceC" name="components/common_task/fsl_component_common_task.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.crc">
      <description>Component crc_adapter; {for-development:SDK-Manifest-ID: component.crc_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/crc/fsl_adapter_crc.h"/>
        <file category="sourceC" name="components/crc/fsl_adapter_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.flash">
      <description>Component flash_adapter; {for-development:SDK-Manifest-ID: component.flash_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/internal_flash/fsl_adapter_flash.h"/>
        <file category="sourceC" name="components/internal_flash/fsl_adapter_flash.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.gpio_AND_driver.port">
      <description>Component gpio_adapter; {for-development:SDK-Manifest-ID: component.gpio_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/gpio/fsl_adapter_gpio.h"/>
        <file category="sourceC" name="components/gpio/fsl_adapter_gpio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.i2c">
      <description>Component i2c_adapter; {for-development:SDK-Manifest-ID: component.i2c_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/i2c/fsl_adapter_i2c.h"/>
        <file category="sourceC" name="components/i2c/fsl_adapter_i2c.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="led" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component led; {for-development:SDK-Manifest-ID: component.led.MKL27Z644}</description>
      <files>
        <file category="header" name="components/led/fsl_component_led.h"/>
        <file category="sourceC" name="components/led/fsl_component_led.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common">
      <description>Component lists; {for-development:SDK-Manifest-ID: component.lists.MKL27Z644}</description>
      <files>
        <file category="header" name="components/lists/fsl_component_generic_list.h"/>
        <file category="sourceC" name="components/lists/fsl_component_generic_list.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="log" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_utility.debug_console">
      <description>Component log; {for-development:SDK-Manifest-ID: component.log.MKL27Z644}</description>
      <files>
        <file category="header" name="components/log/fsl_component_log.h"/>
        <file category="header" name="components/log/fsl_component_log_config.h"/>
        <file category="sourceC" name="components/log/fsl_component_log.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="debugconsole" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.log_AND_driver.common_AND_utility.debug_console">
      <description>Component log backend debug console; {for-development:SDK-Manifest-ID: component.log.backend.debugconsole.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="components/log/fsl_component_log_backend_debugconsole.c"/>
        <file category="header" name="components/log/fsl_component_log_backend_debugconsole.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ringbuffer" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.log_AND_driver.common">
      <description>Component log backend ring buffer; {for-development:SDK-Manifest-ID: component.log.backend.ringbuffer.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="components/log/fsl_component_log_backend_ringbuffer.c"/>
        <file category="header" name="components/log/fsl_component_log_backend_ringbuffer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.lptmr">
      <description>Component lptmr_adapter; {for-development:SDK-Manifest-ID: component.lptmr_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/timer/fsl_adapter_timer.h"/>
        <file category="sourceC" name="components/timer/fsl_adapter_lptmr.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.lpuart">
      <description>Component lpuart_adapter; {for-development:SDK-Manifest-ID: component.lpuart_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/uart/fsl_adapter_uart.h"/>
        <file category="sourceC" name="components/uart/fsl_adapter_lpuart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.lists_AND_driver.common">
      <description>Component mem_manager; {for-development:SDK-Manifest-ID: component.mem_manager.MKL27Z644}</description>
      <files>
        <file category="header" name="components/mem_manager/fsl_component_mem_manager.h"/>
        <file category="sourceC" name="components/mem_manager/fsl_component_mem_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager_light" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.lists_AND_driver.common">
      <description>Component mem_manager_light; {for-development:SDK-Manifest-ID: component.mem_manager_light.MKL27Z644}</description>
      <files>
        <file category="header" name="components/mem_manager/fsl_component_mem_manager.h"/>
        <file category="sourceC" name="components/mem_manager/fsl_component_mem_manager_light.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.lists_AND_driver.common">
      <description>Component osa; {for-development:SDK-Manifest-ID: component.osa.MKL27Z644}</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa_bm" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.lists_AND_driver.common">
      <description>Component osa_bm; {for-development:SDK-Manifest-ID: component.osa_bm.MKL27Z644}</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="panic" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_utility.debug_console">
      <description>Component panic; {for-development:SDK-Manifest-ID: component.panic.MKL27Z644}</description>
      <files>
        <file category="header" name="components/panic/fsl_component_panic.h"/>
        <file category="sourceC" name="components/panic/fsl_component_panic.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pit_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.pit">
      <description>Component pit_adapter; {for-development:SDK-Manifest-ID: component.pit_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/timer/fsl_adapter_timer.h"/>
        <file category="sourceC" name="components/timer/fsl_adapter_pit.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pwm_tpm_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.tpm">
      <description>Component pwm_tpm_adapter; {for-development:SDK-Manifest-ID: component.pwm_tpm_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/pwm/fsl_adapter_pwm.h"/>
        <file category="sourceC" name="components/pwm/fsl_adapter_pwm_tpm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager" Cversion="1.0.0" condition="device.MKL27Z644_AND__component.serial_manager_uart_OR_component.serial_manager_virtual__AND_component.lists_AND_driver.common">
      <description>Component serial_manager; {for-development:SDK-Manifest-ID: component.serial_manager.MKL27Z644}</description>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_manager.h"/>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_internal.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart" Cversion="1.0.0" condition="device.MKL27Z644_AND__driver.lpuart_OR_driver.uart__AND_component.lpuart_adapter_AND_component.serial_manager">
      <description>Component serial_manager_uart; {for-development:SDK-Manifest-ID: component.serial_manager_uart.MKL27Z644}</description>
      <RTE_Components_h>
#define SERIAL_PORT_TYPE_UART 1
</RTE_Components_h>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_uart.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_port_uart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_virtual" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.serial_manager_AND_driver.common">
      <description>Component serial_manager_virtual; {for-development:SDK-Manifest-ID: component.serial_manager_virtual.MKL27Z644}</description>
      <RTE_Components_h>
#define SERIAL_PORT_TYPE_VIRTUAL 1
#define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 
</RTE_Components_h>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_virtual.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_port_virtual.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_crc_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common">
      <description>Component software_crc_adapter; {for-development:SDK-Manifest-ID: component.software_crc_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/crc/fsl_adapter_crc.h"/>
        <file category="sourceC" name="components/crc/fsl_adapter_software_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_rng_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common">
      <description>Component software_rng_adapter; {for-development:SDK-Manifest-ID: component.software_rng_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/rng/fsl_adapter_rng.h"/>
        <file category="sourceC" name="components/rng/fsl_adapter_software_rng.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager" Cversion="1.0.0" condition="device.MKL27Z644_AND__component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter__AND_component.lists_AND_driver.common">
      <description>Component timer_manager; {for-development:SDK-Manifest-ID: component.timer_manager.MKL27Z644}</description>
      <files>
        <file category="header" name="components/timer_manager/fsl_component_timer_manager.h"/>
        <file category="sourceC" name="components/timer_manager/fsl_component_timer_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="tpm_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.tpm">
      <description>Component tpm_adapter; {for-development:SDK-Manifest-ID: component.tpm_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/timer/fsl_adapter_timer.h"/>
        <file category="sourceC" name="components/timer/fsl_adapter_tpm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.uart">
      <description>Component uart_adapter; {for-development:SDK-Manifest-ID: component.uart_adapter.MKL27Z644}</description>
      <files>
        <file category="header" name="components/uart/fsl_adapter_uart.h"/>
        <file category="sourceC" name="components/uart/fsl_adapter_uart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="MKL27Z644_header" Cversion="1.0.0" condition="device.MKL27Z644_AND_CMSIS_Include_core_cm0plus">
      <description>Device MKL27Z644_cmsis; {for-development:SDK-Manifest-ID: device.MKL27Z644_CMSIS.MKL27Z644}</description>
      <files>
        <file category="header" name="fsl_device_registers.h"/>
        <file category="header" name="MKL27Z644.h"/>
        <file category="header" name="MKL27Z644_features.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Csub="MKL27Z644_startup" Cversion="1.1.0" condition="device.MKL27Z644_AND__armclang_OR_iar__AND_device.MKL27Z644_system">
      <description>Device MKL27Z644_startup; {for-development:SDK-Manifest-ID: device.MKL27Z644_startup.MKL27Z644}</description>
      <files>
        <file condition="iar" category="sourceAsm" name="iar/startup_MKL27Z644.s"/>
        <file condition="mdk" category="sourceAsm" name="arm/startup_MKL27Z644.S"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL27Z32xxx4_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL27Z32xxx4_ram.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL27Z64xxx4_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL27Z64xxx4_ram.scf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL27Z32xxx4_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL27Z32xxx4_ram.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL27Z64xxx4_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL27Z64xxx4_ram.icf" version="1.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="MKL27Z644_system" Cversion="1.0.0" condition="device.MKL27Z644_AND_device.MKL27Z644_CMSIS">
      <description>Device MKL27Z644_system; {for-development:SDK-Manifest-ID: device.MKL27Z644_system.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="system_MKL27Z644.c"/>
        <file category="header" name="system_MKL27Z644.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="adc" Cversion="2.2.0" condition="device.MKL27Z644_AND_driver.common">
      <description>ADC16 Driver; {for-development:SDK-Manifest-ID: platform.drivers.adc16.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_adc16.c"/>
        <file category="header" name="drivers/fsl_adc16.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.3.1" condition="device.MKL27Z644_AND_driver.common">
      <description>Clock Driver; {for-development:SDK-Manifest-ID: platform.drivers.clock.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_clock.h"/>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cmp" Cversion="2.0.2" condition="device.MKL27Z644_AND_driver.common">
      <description>CMP Driver; {for-development:SDK-Manifest-ID: platform.drivers.cmp.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cmp.c"/>
        <file category="header" name="drivers/fsl_cmp.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="i2c_cmsis" Cversion="2.2.0" condition="device.MKL27Z644_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c">
      <description>I2C CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.i2c_cmsis.MKL27Z644}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_i2c_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_i2c_cmsis.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="lpuart_cmsis" Cversion="2.1.0" condition="device.MKL27Z644_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart">
      <description>LPUART CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpuart_cmsis.MKL27Z644}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_lpuart_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_lpuart_cmsis.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="spi_cmsis" Cversion="2.3.0" condition="device.MKL27Z644_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.spi">
      <description>SPI CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.spi_cmsis.MKL27Z644}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_spi_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_spi_cmsis.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="uart_cmsis" Cversion="2.1.0" condition="device.MKL27Z644_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.uart">
      <description>UART CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.uart_cmsis.MKL27Z644}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_uart_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_uart_cmsis.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.2.9" condition="device.MKL27Z644_AND_device.MKL27Z644_CMSIS_AND_driver.clock">
      <description>COMMON Driver; {for-development:SDK-Manifest-ID: platform.drivers.common.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_common.h"/>
        <file category="sourceC" name="drivers/fsl_common.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cop" Cversion="2.0.1" condition="device.MKL27Z644_AND_driver.common">
      <description>COP Driver; {for-development:SDK-Manifest-ID: platform.drivers.cop.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cop.c"/>
        <file category="header" name="drivers/fsl_cop.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc" Cversion="2.0.3" condition="device.MKL27Z644_AND_driver.common">
      <description>CRC Driver; {for-development:SDK-Manifest-ID: platform.drivers.crc.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_crc.c"/>
        <file category="header" name="drivers/fsl_crc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dma" Cversion="2.1.0" condition="device.MKL27Z644_AND_driver.common_AND_driver.dmamux">
      <description>DMA Driver; {for-development:SDK-Manifest-ID: platform.drivers.dma.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_dma.h"/>
        <file category="sourceC" name="drivers/fsl_dma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux" Cversion="2.0.5" condition="device.MKL27Z644_AND_driver.common">
      <description>DMAMUX Driver; {for-development:SDK-Manifest-ID: platform.drivers.dmamux.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_dmamux.h"/>
        <file category="sourceC" name="drivers/fsl_dmamux.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash" Cversion="3.0.0" condition="device.MKL27Z644_AND_driver.common">
      <description>Flash Driver; {for-development:SDK-Manifest-ID: platform.drivers.flash.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_ftfx_adapter.h"/>
        <file category="header" name="drivers/fsl_ftfx_utilities.h"/>
        <file category="header" name="drivers/fsl_ftfx_features.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_controller.c"/>
        <file category="header" name="drivers/fsl_ftfx_controller.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flash.c"/>
        <file category="header" name="drivers/fsl_ftfx_flash.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_cache.c"/>
        <file category="header" name="drivers/fsl_ftfx_cache.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flexnvm.c"/>
        <file category="header" name="drivers/fsl_ftfx_flexnvm.h"/>
        <file category="header" name="drivers/fsl_flash.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio" Cversion="2.0.4" condition="device.MKL27Z644_AND_driver.common">
      <description>FLEXIO Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio.c"/>
        <file category="header" name="drivers/fsl_flexio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2c_master" Cversion="2.4.0" condition="device.MKL27Z644_AND_driver.flexio">
      <description>FLEXIO I2C Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_i2c_master.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2c_master.c"/>
        <file category="header" name="drivers/fsl_flexio_i2c_master.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s" Cversion="2.2.0" condition="device.MKL27Z644_AND_driver.flexio">
      <description>FLEXIO I2S Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_i2s.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2s.c"/>
        <file category="header" name="drivers/fsl_flexio_i2s.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s_dma" Cversion="2.1.7" condition="device.MKL27Z644_AND_driver.dma_AND_driver.flexio_i2s">
      <description>FLEXIO I2S DMA Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_i2s_dma.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2s_dma.c"/>
        <file category="header" name="drivers/fsl_flexio_i2s_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi" Cversion="2.2.0" condition="device.MKL27Z644_AND_driver.flexio">
      <description>FLEXIO SPI Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_spi.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_spi.c"/>
        <file category="header" name="drivers/fsl_flexio_spi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi_dma" Cversion="2.2.0" condition="device.MKL27Z644_AND_driver.dma_AND_driver.flexio_spi">
      <description>FLEXIO SPI DMA Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_spi_dma.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_spi_dma.c"/>
        <file category="header" name="drivers/fsl_flexio_spi_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart" Cversion="2.3.0" condition="device.MKL27Z644_AND_driver.flexio">
      <description>FLEXIO UART Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_uart.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart.c"/>
        <file category="header" name="drivers/fsl_flexio_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart_dma" Cversion="2.3.0" condition="device.MKL27Z644_AND_driver.dma_AND_driver.flexio_uart">
      <description>FLEXIO UART DMA Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexio_uart_dma.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart_dma.c"/>
        <file category="header" name="drivers/fsl_flexio_uart_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.5.1" condition="device.MKL27Z644_AND_driver.common">
      <description>GPIO Driver; {for-development:SDK-Manifest-ID: platform.drivers.gpio.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_gpio.h"/>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.0.9" condition="device.MKL27Z644_AND_driver.common">
      <description>I2C Driver; {for-development:SDK-Manifest-ID: platform.drivers.i2c.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_i2c.h"/>
        <file category="sourceC" name="drivers/fsl_i2c.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_dma" Cversion="2.0.9" condition="device.MKL27Z644_AND_driver.dma_AND_driver.i2c">
      <description>I2C Driver; {for-development:SDK-Manifest-ID: platform.drivers.i2c_dma.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c_dma.c"/>
        <file category="header" name="drivers/fsl_i2c_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="llwu" Cversion="2.0.5" condition="device.MKL27Z644_AND_driver.common">
      <description>LLWU Driver; {for-development:SDK-Manifest-ID: platform.drivers.llwu.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_llwu.h"/>
        <file category="sourceC" name="drivers/fsl_llwu.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr" Cversion="2.1.1" condition="device.MKL27Z644_AND_driver.common">
      <description>LPTMR Driver; {for-development:SDK-Manifest-ID: platform.drivers.lptmr.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lptmr.c"/>
        <file category="header" name="drivers/fsl_lptmr.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart" Cversion="2.4.1" condition="device.MKL27Z644_AND_driver.common">
      <description>LPUART Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpuart.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_lpuart.h"/>
        <file category="sourceC" name="drivers/fsl_lpuart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_dma" Cversion="2.4.0" condition="device.MKL27Z644_AND_driver.dma_AND_driver.lpuart">
      <description>LPUART Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpuart_dma.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_lpuart_dma.h"/>
        <file category="sourceC" name="drivers/fsl_lpuart_dma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mma8451q" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common">
      <description>Driver mma8451q; {for-development:SDK-Manifest-ID: driver.mma8451q.MKL27Z644}</description>
      <files>
        <file category="header" name="components/mma8451q/fsl_mma.h"/>
        <file category="sourceC" name="components/mma8451q/fsl_mma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pit" Cversion="2.0.4" condition="device.MKL27Z644_AND_driver.common">
      <description>PIT Driver; {for-development:SDK-Manifest-ID: platform.drivers.pit.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pit.c"/>
        <file category="header" name="drivers/fsl_pit.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pmc" Cversion="2.0.3" condition="device.MKL27Z644_AND_driver.common">
      <description>PMC Driver; {for-development:SDK-Manifest-ID: platform.drivers.pmc.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_pmc.h"/>
        <file category="sourceC" name="drivers/fsl_pmc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="port" Cversion="2.1.1" condition="device.MKL27Z644_AND_driver.common">
      <description>PORT Driver; {for-development:SDK-Manifest-ID: platform.drivers.port.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_port.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rcm" Cversion="2.0.4" condition="device.MKL27Z644_AND_driver.common">
      <description>RCM Driver; {for-development:SDK-Manifest-ID: platform.drivers.rcm.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rcm.c"/>
        <file category="header" name="drivers/fsl_rcm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rtc" Cversion="2.2.1" condition="device.MKL27Z644_AND_driver.common">
      <description>RTC Driver; {for-development:SDK-Manifest-ID: platform.drivers.rtc.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rtc.c"/>
        <file category="header" name="drivers/fsl_rtc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sim" Cversion="2.1.2" condition="device.MKL27Z644_AND_driver.common">
      <description>SIM Driver; {for-development:SDK-Manifest-ID: platform.drivers.sim.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_sim.h"/>
        <file category="sourceC" name="drivers/fsl_sim.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="smc" Cversion="2.0.7" condition="device.MKL27Z644_AND_driver.common">
      <description>SMC Driver; {for-development:SDK-Manifest-ID: platform.drivers.smc.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_smc.h"/>
        <file category="sourceC" name="drivers/fsl_smc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi" Cversion="2.1.1" condition="device.MKL27Z644_AND_driver.common">
      <description>SPI Driver; {for-development:SDK-Manifest-ID: platform.drivers.spi.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_spi.h"/>
        <file category="sourceC" name="drivers/fsl_spi.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi_dma" Cversion="2.1.1" condition="device.MKL27Z644_AND_driver.dma_AND_driver.spi">
      <description>SPI Driver; {for-development:SDK-Manifest-ID: platform.drivers.spi_dma.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_spi_dma.h"/>
        <file category="sourceC" name="drivers/fsl_spi_dma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="tpm" Cversion="2.0.7" condition="device.MKL27Z644_AND_driver.common">
      <description>TPM Driver; {for-development:SDK-Manifest-ID: platform.drivers.tpm.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_tpm.c"/>
        <file category="header" name="drivers/fsl_tpm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart" Cversion="2.4.0" condition="device.MKL27Z644_AND_driver.common">
      <description>UART Driver; {for-development:SDK-Manifest-ID: platform.drivers.uart.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_uart.h"/>
        <file category="sourceC" name="drivers/fsl_uart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart_dma" Cversion="2.4.0" condition="device.MKL27Z644_AND_driver.dma_AND_driver.uart">
      <description>UART Driver; {for-development:SDK-Manifest-ID: platform.drivers.uart_dma.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_uart_dma.h"/>
        <file category="sourceC" name="drivers/fsl_uart_dma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="vref" Cversion="2.1.2" condition="device.MKL27Z644_AND_driver.common">
      <description>VREF Driver; {for-development:SDK-Manifest-ID: platform.drivers.vref.MKL27Z644}</description>
      <files>
        <file category="header" name="drivers/fsl_vref.h"/>
        <file category="sourceC" name="drivers/fsl_vref.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert" Cversion="1.0.0" condition="device.MKL27Z644_AND_utility.debug_console">
      <description>Utility assert; {for-development:SDK-Manifest-ID: platform.utilities.assert.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="utilities/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite" Cversion="1.0.0" condition="device.MKL27Z644_AND_utility.debug_console_lite">
      <description>Utility assert_lite; {for-development:SDK-Manifest-ID: platform.utilities.assert_lite.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="utilities/debug_console_lite/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.serial_manager_AND_driver.common">
      <description>Utility debug_console; {for-development:SDK-Manifest-ID: utility.debug_console.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="utilities/str/fsl_str.c"/>
        <file category="header" name="utilities/str/fsl_str.h"/>
        <file category="sourceC" name="utilities/debug_console/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console.h"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console_conf.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.lpuart_adapter_AND_driver.common">
      <description>Utility debug_console_lite; {for-development:SDK-Manifest-ID: utility.debug_console_lite.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="utilities/debug_console_lite/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console_lite/fsl_debug_console.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="notifier" Cversion="1.0.0" condition="device.MKL27Z644_AND_driver.common">
      <description>Utility notifier; {for-development:SDK-Manifest-ID: platform.utilities.notifier.MKL27Z644}</description>
      <files>
        <file category="sourceC" name="utilities/fsl_notifier.c"/>
        <file category="header" name="utilities/fsl_notifier.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="shell" Cversion="1.0.0" condition="device.MKL27Z644_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <description>Utility shell; {for-development:SDK-Manifest-ID: utility.shell.MKL27Z644}</description>
      <RTE_Components_h>
#define DEBUG_CONSOLE_RX_ENABLE 0
</RTE_Components_h>
      <files>
        <file category="sourceC" name="utilities/fsl_shell.c"/>
        <file category="header" name="utilities/fsl_shell.h"/>
      </files>
    </component>
  </components>
</package>
