<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso54018_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXPRESSO54018</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.4" date="2018-07-18">NXP CMSIS packs based on MCUXpresso SDK 2.4.2</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC54018_DFP" vendor="NXP" version="12.2.0"/>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso54018">
      <description>LPCXpresso Development Board for LPC540xx MCUs</description>
      <mountedDevice Dname="LPC54018" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC54018">
      <accept Dname="LPC54018" Dvariant="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018" Dvariant="LPC54018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JET180" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC54018_AND_component.serial_manager_AND_component.usart_adapter_AND_device.LPC54018_startup_AND_driver.clock_AND_driver.emc_AND_driver.flexcomm_AND_driver.flexcomm_usart_AND_driver.lpc_gpio_AND_driver.power_AND_utility.debug_console">
      <require condition="device.LPC54018"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexcomm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="emc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="LPC54018_startup"/>
    </condition>
  </conditions>
  <examples>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_spi_dma_b2b_transfer_master example shows how to use spi driver as master to do board to board transfer with DMA:In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_spi_dma_b2b_transfer_slave example shows how to use spi driver as master to do board to board transfer with DMA:In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_master" folder="cmsis_driver_examples/spi/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_spi_interrupt_b2b_transfer_master example shows how to use CMSIS spi driver as master to do board to board transfer with interrupt:In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_slave" folder="cmsis_driver_examples/spi/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_spi_interrupt_b2b_transfer_slave example shows how to use CMSIS spi driver as master to do board to board transfer with interrupt:In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_dma_transfer" folder="cmsis_driver_examples/usart/dma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_usart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_interrupt_transfer" folder="cmsis_driver_examples/usart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_usart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="driver_examples/ctimer/simple_match" doc="readme.txt">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ctimer_match_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="driver_examples/ctimer/simple_match_interrupt" doc="readme.txt">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset, so it would generate a square wave.With an interrupt callback the match value is changed frequently in such a way that the frequency of the output square wave is increased gradually.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="driver_examples/ctimer/simple_pwm" doc="readme.txt">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ctimer_pwm_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="driver_examples/ctimer/simple_pwm_interrupt" doc="readme.txt">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a PWM signal.With an interrupt callback the PWM duty cycle is changed frequently in such a way that the LED brightness can be varied.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="driver_examples/dma/channel_chain" doc="readme.txt">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_channel_chain.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="driver_examples/dma/interleave_transfer" doc="readme.txt">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA interleave feature.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_interleave_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="driver_examples/dma/linked_transfer" doc="readme.txt">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a linked trnasfer example.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_linked_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_m2m_polling" folder="driver_examples/dma/m2m_polling" doc="readme.txt">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot polling transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example for debugging and further development.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_m2m_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_m2m_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="driver_examples/dma/memory_to_memory" doc="readme.txt">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example fordebugging and further development.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="driver_examples/dma/wrap_transfer" doc="readme.txt">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes a wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA wrap feature.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_wrap_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_dma" folder="driver_examples/dmic/dmic_dma" doc="readme.txt">
      <description>This example shows how to use DMA to transfer data from DMIC to memory.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_dma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dmic_dma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_i2s_dma" folder="driver_examples/dmic/dmic_i2s_dma" doc="readme.txt">
      <description>Demonstrates the DMIC working with I2S. Audio is converted to samples in the DMIC module.Then, the data is placed memory buffer. Last, it is send data to the I2S buffer and send to the CODEC, then the audio data will be output to Lineout of CODEC.~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_i2s_dma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dmic_i2s_dma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="emc_sdram" folder="driver_examples/emc/sdram" doc="readme.txt">
      <description>The emc_sdram example shows how to access the SDRAM.In this example, user shall initialize the EMC (external memory controller), initialize theEMC dynamic memory controller before access the external SDRAM.If the sdram example fails, please make sure to check the following points:1. Please take refer to the board.readme to check the jumper settings on your board.2. Please take refer to the EMC chapter in API RM to do the delay calibration to found the best delay for your board, then update the delay to the EMC clock delay control registers in the system configure module registers.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/emc_sdram.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/emc_sdram.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_ptp1588_transfer" folder="driver_examples/enet/txrx_ptp1588_transfer" doc="readme.txt">
      <description>The enet_rxtx_ptp1588_transfer example shows the way to use ENET driver to   receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the ENET.2. How to get the time stamp of the PTP 1588 timer.3. How to use Get the ENET transmit and receive frame time stamp.The example transmits 20 number PTP event frame, shows the timestamp of the transmitted frame.The length, source MAC address and destination MAC address of the received frame will be print. The time stamp of the received timestamp will be print when the PTP message frame is received(the outside loopback cable can be used to see the right rx time-stamping log since we send the ptp message). </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_ptp1588_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/enet_txrx_ptp1588_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_rxinterrupt" folder="driver_examples/enet/txrx_rxinterrupt" doc="readme.txt">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET functional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame in interrupt irq handler and to transmit frame.The example transmits 20 number broadcast frame, print the number of recieved frames. To avoidthe receive number overflow, the transmit/receive loop with automatically break when 20 numberare received.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_rxinterrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/enet_txrx_rxinterrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_rxpoll" folder="driver_examples/enet/txrx_rxpoll" doc="readme.txt">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET functional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame in polling and to transmit frame.The example transmits 20 number broadcast frame, print the number and the mac address of the recieved frames.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_rxpoll.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/enet_txrx_rxpoll.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer_rxinterrupt" folder="driver_examples/enet/txrx_transfer_rxinterrupt" doc="readme.txt">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET transactional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame in interrupt irq handler and to transmit frame.The example transmits 20 number broadcast frame, print the number of recieved frames. To avoidthe receive number overflow, the transmit/receive loop with automatically break when 20 numberare received.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer_rxinterrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/enet_txrx_transfer_rxinterrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer_rxpoll" folder="driver_examples/enet/txrx_transfer_rxpoll" doc="readme.txt">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET transactional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame in polling and to transmit frame.The example transmits 20 number broadcast frame, print the number and the mac address of the recieved frames.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer_rxpoll.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/enet_txrx_transfer_rxpoll.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="fmeas" folder="driver_examples/fmeas" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Frequency Measure feature of SYSCON module on LPC devices.It shows how to measure a target frequency using a (faster) reference frequency. The example uses the internal main clock as the reference frequency to measure the frequencies of the RTC, watchdog oscillator, and internal RC oscillator.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmeas.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/fmeas.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gint" folder="driver_examples/gint" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Group GPIO input interrupt peripheral.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gint.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gint.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_qspi_xip" folder="demo_apps/hello_world_qspi_xip" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_qspi_xip.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world_qspi_xip.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="driver_examples/i2c/dma_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_master" folder="driver_examples/i2c/polling_b2b/master" doc="readme.txt">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_polling_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_slave" folder="driver_examples/i2c/polling_b2b/slave" doc="readme.txt">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_polling_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_record_playback" folder="driver_examples/i2s/dma_record_playback" doc="readme.txt">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with WM8904 codec.It needs to have 1-2 connected on JP3.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_record_playback.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2s_dma_record_playback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_transfer" folder="driver_examples/i2s/dma_transfer" doc="readme.txt">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with WM8904 codec.It needs to have 1-2 connected on JP3.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2s_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_record_playback" folder="driver_examples/i2s/interrupt_record_playback" doc="readme.txt">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with WM8904 codec.It needs to have 1-2 connected on JP3.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_record_playback.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2s_interrupt_record_playback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_transfer" folder="driver_examples/i2s/interrupt_transfer" doc="readme.txt">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with WM8904 codec.It needs to have 1-2 connected on JP3.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2s_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_basic" folder="driver_examples/iap/iap_basic" doc="readme.txt">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It reads part id, boot code version, unique id and reinvoke ISP. A message a printed on the UART terminal as various bascial iap operations are performed.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_basic.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/iap_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdc_cursor" folder="driver_examples/lcdc/lcdc_cursor" doc="readme.txt">
      <description>The lcdc_cursor example shows how to use LCD hardware cursor.In this example, a 32x32 cursor is shown. The cursor's position is changed atthe end of every frame.The background is:+------------------------------------------------++                                                ++                    Red                         ++                                                ++            +--------------------+              ++            +                    +              ++            +      Blank         +              ++            +                    +              ++            +--------------------+              ++                                                ++                                                ++                                                ++------------------------------------------------+</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdc_cursor.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lcdc_cursor.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdc_tft" folder="driver_examples/lcdc/lcdc_tft" doc="readme.txt">
      <description>The lcdc_tft example shows how to use LCD driver to drive TFT panel.In this example, the cursor palette is used. A rectangle is shown in the panel,its color and position are changed every frame.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdc_tft.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lcdc_tft.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_basic" folder="driver_examples/adc/lpc_adc_basic" doc="readme.txt">
      <description>The lpc_adc_basic example shows how to use LPC ADC driver in the simplest way.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software trigger API is called to start the conversion. Then it polls the conversion sequence A's flag till the conversion is completed. When the conversion is completed, just print the conversion result to terminal.Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above. (In this example, we use this way.)      2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored.  Program Flow1.This example demonstrates how to configure the A sequences with polling, assigning one channel with software  trigger, you can configure channel via "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.    3.After ADC channels are assigned to each of the sequences, the software trigger is chosen. Setting   SEQA_CTRL_START to '1' will trigger sequence A.  4.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, software trigger will start. 5.Read the corresponding DATAVALID field with polling to judge whether the conversion completes and the result is ready.  If the result is ready, the example will printf result information to terminal.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_basic.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_adc_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_burst" folder="driver_examples/adc/lpc_adc_burst" doc="readme.txt">
      <description>The lpc_adc_burst example shows how to use LPC ADC driver with the burst mode.In this example, the internal temperature sensor is used to created the input analog signal.When user type in any key from the keyboard, the burst mode is enabled. Then the conversion sequence A would be started automatically, till the burst would be disabled in conversion completed ISR. Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above.       2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored. (In this example, we use this way.)         3. What kinds of interrupt do ADC have?   There are four interrupts that can be generated by the ADC:     • Conversion-Complete or Sequence-Complete interrupts for sequences A and B     • Threshold-Compare Out-of-Range Interrupt     • Data Overrun Interrupt   Any of these interrupt requests may be individually enabled or disabled in the INTEN register.  Program Flow1.This example demonstrates how to configure the A sequences with burst mode, you can configure channel via   "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.  3.Enable the Conversion-Complete or Sequence-Complete interrupt for sequences A.  4.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, burst mode will start.    5.When the first conversion completes, the interrupt would be triggered. The ISR will stop the burst mode and print conversion result   to terminal.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_burst.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_adc_burst.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_dma" folder="driver_examples/adc/lpc_adc_dma" doc="readme.txt">
      <description>The lpc_adc_dma example shows how to use LPC ADC driver with DMA.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software trigger API is called to start the conversion. When the ADC conversion is completed, it would trigger the DMA to move the ADC conversion result from ADC conversion data register to user indicated memory. Then the main loop waits for the transfer to be done and print the result to terminal.Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above. (In this example, we use this way.)      2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored.          3. How to use DMA to work with ADC?   The sequence-A or sequence-B conversion/sequence-complete interrupts may also be   used to generate a DMA trigger. To trigger a DMA transfer, the same conditions must be   met as the conditions for generating an interrupt.   Remark: If the DMA is used, the ADC interrupt must be disabled in the NVIC.   Program Flow1.This example demonstrates how to configure the A sequences with interrupt, assigning one channel with software  trigger, you can configure channel via "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.  3.Configure the DMA and DMAMUX to work with ADC sequences.4.Enable the Conversion-Complete or Sequence-Complete DMA for sequences A.  5.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, software trigger will start.    6.When the conversion completes, the DMA would be requested.7.When the DMA transfer completes, DMA will trigger a interrupt. ISR would set the "bDmaTransferDone" to 'true'. Then main function will   print conversion result to terminal.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_dma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_adc_dma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_interrupt" folder="driver_examples/adc/lpc_adc_interrupt" doc="readme.txt">
      <description>The lpc_adc_interrupt example shows how to use interrupt with LPC ADC driver.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software trigger API is called to start the conversion. Then it polls the flag variable which would be asserted when the conversion completed ISR is executed. Then just print the conversion result to terminal.Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above. (In this example, we use this way.)      2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored.          3. What kinds of interrupt do ADC have?   There are four interrupts that can be generated by the ADC:     • Conversion-Complete or Sequence-Complete interrupts for sequences A and B     • Threshold-Compare Out-of-Range Interrupt     • Data Overrun Interrupt   Any of these interrupt requests may be individually enabled or disabled in the INTEN register.  Program Flow1.This example demonstrates how to configure the A sequences with interrupt, assigning one channel with software  trigger, you can configure channel via "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.  3.Enable the Conversion-Complete or Sequence-Complete interrupt for sequences A.  4.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, software trigger will start.    5.When the conversion completes, the interrupt would be triggered. The ISR will print conversion result to terminal.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_adc_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcan_interrupt_transfer" folder="driver_examples/mcan/interrupt_transfer" doc="readme.txt">
      <description>The mcan_interrupt example shows how to use MCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when users press space key in terminal. Endpoint B receives the message, printsthe message content to terminal and echoes back the message. Endpoint A will increase the receivedmessage and wait for the next transmission the users initiate.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcan_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mcan_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcan_loopback" folder="driver_examples/mcan/loopback" doc="readme.txt">
      <description>The mcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrate this example, only one board is needed. The example will config Tx Buffer to sendand Rx Fifo to receive. After that, the example will send a CAN Message throuth internal loopbackinterconnect and print out the Message payload to the terminal.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcan_loopback.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mcan_loopback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="driver_examples/mrt" doc="readme.txt">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mrt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="otp" folder="driver_examples/otp" doc="readme.txt">
      <description>The OTP example project is a demonstration program that uses the KSDK software to access OTP ROM API.It just prints the version of API at the moment.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/otp.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/otp.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="driver_examples/pint/pattern_match" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pint_pattern_match.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="driver_examples/pint/pin_interrupt" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pint_pin_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager_lpc_xip" folder="demo_apps/power_manager_lpc_xip" doc="readme.txt">
      <description>The power_manager_lpc application shows the usage of normal power mode control APIs for entering the three kinds of low power mode: Sleep mode, Deep Sleep mode and Sleep Power Down mode. When the application runs to each low power mode, the device would cut off the power for specific modules to save energy. The device can be also waken up by prepared wakeup source from external event.
Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - In order to meet typedef power consumption of DateSheet manual, Please configure MCU under the following conditions. 鈥?Configure all pins as GPIO with pull-up resistor disabled in the IOCON block. 鈥?Configure GPIO pins as outputs using the GPIO DIR register. 鈥?Write 1 to the GPIO CLR register to drive the outputs LOW. 鈥?All peripherals disabled.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager_lpc_xip.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/power_manager_lpc_xip.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rit_example" folder="driver_examples/rit" doc="readme.txt">
      <description>The RIT project is a simple demonstration program of the SDK RIT driver. It sets up the RIThardware block to trigger a periodic interrupt at 1 second period interval to toggel a specifiedLED on board.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rit_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rit_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rng_random" folder="driver_examples/rng/random" doc="readme.txt">
      <description>The RNG example project is a demonstration program that uses the KSDK software to generate random numbersand prints them to the terminal. 32 32-bit numbers are read and skipped per one 32-bit number read and used.This is to get a better entropy as suggested by user manual.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rng_random.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rng_random.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_audio_bm" folder="usb_examples/usb_rom_device_audio/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create and USB AUDIO class device. This device supports 2 channel audio in (MIC/LINE_IN) and 2 channel audio out (SPEAKERS/HEADPHONE).</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_audio_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rom_dev_audio_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_audio_dmic_bm" folder="usb_examples/usb_rom_device_audio_dmic/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create and USB AUDIO class device. This device supports 2 channels audio in.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_audio_dmic_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rom_dev_audio_dmic_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_cdc_bm" folder="usb_examples/usb_rom_device_cdc/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create a virtual comm port. When the USB on J3 is connected to a PC, the host would recognize the USB connection as a new serial port.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_cdc_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rom_dev_cdc_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_hid_generic_bm" folder="usb_examples/usb_rom_device_hid_generic/bm" doc="readme.txt">
      <description>The example shows how to us USBD ROM stack to creates a generic HID device. The example supports 1 byte report and loops back the data received in SET_REPORT message..</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_hid_generic_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rom_dev_hid_generic_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_msc_ram_bm" folder="usb_examples/usb_rom_device_msc_ram/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create a RAM disk. When the USB on J3 is connected to a PC, the host would recognize the USB connection as a mass storage device. Note that the storage happens on the RAM and is not a persistent storage..</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_msc_ram_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rom_dev_msc_ram_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_example" folder="driver_examples/rtc" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="driver_examples/sctimer/16bit_counter" doc="readme.txt">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_16bit_counter.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="driver_examples/sctimer/multi_state_pwm" doc="readme.txt">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that generate a PWM signal, it also has an event linked to an input signal to transition to State 1.State 1 has 4 events that generate 2 PWM signals, it also has an event linked to an input signal to transition to State 0.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_multi_state_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.txt">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="driver_examples/sctimer/simple_pwm" doc="readme.txt">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sha" folder="driver_examples/sha" doc="readme.txt">
      <description>The SHA Example project is a demonstration program that uses the KSDK software to generate SHA checksums.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sha.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sha.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="demo_apps/shell" doc="readme.txt">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/shell.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_transfer" folder="driver_examples/spi/dma_transfer" doc="readme.txt">
      <description>The spi_dma example shows how to use spi driver with DMA:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_dma_master" folder="driver_examples/spi/half_duplex_transfer/dma/master" doc="readme.txt">
      <description>The spi_half_duplex_dma_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer data in DMA way. </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_dma_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_half_duplex_dma_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_dma_slave" folder="driver_examples/spi/half_duplex_transfer/dma/slave" doc="readme.txt">
      <description>The spi_half_duplex_dma transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses dma mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_dma_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_half_duplex_dma_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_int_master" folder="driver_examples/spi/half_duplex_transfer/int/master" doc="readme.txt">
      <description>The spi_half_duplex_int_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer data in interrupt way. </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_int_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_half_duplex_int_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_int_slave" folder="driver_examples/spi/half_duplex_transfer/int/slave" doc="readme.txt">
      <description>The spi_half_duplex_int_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_int_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_half_duplex_int_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_polling_master" folder="driver_examples/spi/half_duplex_transfer/polling/master" doc="readme.txt">
      <description>The spi_half_duplex_polling_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the othere board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer data in polling way. </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_polling_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_half_duplex_polling_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_polling_slave" folder="driver_examples/spi/half_duplex_transfer/polling/slave" doc="readme.txt">
      <description>The spi_half_duplex_polling_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_polling_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_half_duplex_polling_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt" folder="driver_examples/spi/interrupt" doc="readme.txt">
      <description>The spi_interrupt example shows how to use spi functional API to do interrupt transfer:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="driver_examples/spi/interrupt_b2b/master" doc="readme.txt">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from slave,and checkif the data master received is correct. This example needs to work with spi_interrupt_b2b_slave example.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="driver_examples/spi/interrupt_b2b/slave" doc="readme.txt">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to master,and check if the data slave received is correct. This example needs to work with spi_interrupt_b2b_master example.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="driver_examples/spi/polling_b2b_transfer/master" doc="readme.txt">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="driver_examples/spi/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI slave.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spifi_dma_transfer" folder="driver_examples/spifi/dma_transfer" doc="readme.txt">
      <description>The SPIFI DMA Example project is a demonstration program that uses the KSDK software to program external serialflash using DMA and read through AHB bus.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spifi_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spifi_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spifi_polling_transfer" folder="driver_examples/spifi/polling_transfer" doc="readme.txt">
      <description>The SPIFI Polling Example project is a demonstration program that uses the KSDK software to program external serialflash using polling and read through AHB bus.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spifi_polling_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spifi_polling_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="touch_cursor" folder="demo_apps/touch_cursor" doc="readme.txt">
      <description>The touch_cursor demo shows how to use LCD hardware cursor and position it using touch panel.In this example, a 32x32 cursor is shown. The cursor's position is changed in reaction to detected touch.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/touch_cursor.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/touch_cursor.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_double_buffer_transfer" folder="driver_examples/usart/dma_double_buffer_transfer" doc="readme.txt">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USARTThe example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are cycled from one to the other.Things to note- The descriptors of the ping pong transfer need to be aligned to size 16- The inital transfer will perform the same job as first descriptor of ping pong, so the first linkeage is to go to g_pingpong_desc[1]- g_pingpong_desc[1] then chains the g_pingpong_desc[0] as the next descriptor- The properties are set up such that g_pingpong_desc[0] (and the initial configuration uses INTA to signal back to the callback)- g_pingpong_desc[1] uses INTB to signal to the callback- The scheduled callback uses this information to know which data was last writtenA note on PerformanceThe intent of this example is to illustrate how a double-buffer scheme can be implemented using the dma. The performance of this example will be limited to how quickly the echo printer can read-out the data from the ping pong buffer and display it. This means that the example will work well if characters are entered at a rate where the DMA callback to echo the string can keep up with the input stream. Connecting the USARTRX to a continuous fast speed will cause the DMA to fall behind. </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_double_buffer_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_dma_double_buffer_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_rb_transfer" folder="driver_examples/usart/dma_rb_transfer" doc="readme.txt">
      <description>The usart_dma ring buffer example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board will send back all characters that PC send to the board.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_dma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_transfer" folder="driver_examples/usart/dma_transfer" doc="readme.txt">
      <description>The usart_dma example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt" folder="driver_examples/usart/interrupt" doc="readme.txt">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_rb_transfer" folder="driver_examples/usart/interrupt_rb_transfer" doc="readme.txt">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_transfer" folder="driver_examples/usart/interrupt_transfer" doc="readme.txt">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling" folder="driver_examples/usart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_sync_transfer_master" folder="driver_examples/usart/sync_transfer/master" doc="readme.txt">
      <description>The usart synchronous transfer example shows how to use usart driver in synchronous way.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_sync_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_sync_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_sync_transfer_slave" folder="driver_examples/usart/sync_transfer/slave" doc="readme.txt">
      <description>The usart synchronous transfer example shows how to use usart driver in synchronous way.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_sync_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_sync_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_wakeup_xip" folder="demo_apps/utick_wakeup_xip" doc="readme.txt">
      <description>The purpose of this demo is to show wakeup from deep sleep mode using MicroTick timer.The demo sets the MicroTick Timer as a wake up source and puts the device in deep-sleep mode. The MicroTick timer wakes up the device. </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_wakeup_xip.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/utick_wakeup_xip.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_wakeup_xip_peripheral" folder="demo_apps/utick_wakeup_xip_peripheral" doc="readme.txt">
      <description>The purpose of this demo is to show wakeup from deep sleep mode using MicroTick timer.The demo sets the MicroTick Timer as a wake up source and puts the device in deep-sleep mode. The MicroTick timer wakes up the device. </description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_wakeup_xip_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/utick_wakeup_xip_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_qspi_xip" folder="driver_examples/wwdt" doc="readme.txt">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso54018" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_qspi_xip.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wwdt_qspi_xip.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso54018" Cversion="1.0.0" condition="device.LPC54018_AND_component.serial_manager_AND_component.usart_adapter_AND_device.LPC54018_startup_AND_driver.clock_AND_driver.emc_AND_driver.flexcomm_AND_driver.flexcomm_usart_AND_driver.lpc_gpio_AND_driver.power_AND_utility.debug_console">
      <description>Board_project_template lpcxpresso54018</description>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
