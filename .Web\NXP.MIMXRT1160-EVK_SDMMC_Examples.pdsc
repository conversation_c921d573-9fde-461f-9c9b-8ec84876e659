<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1160-EVK_SDMMC_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware sdmmc Examples Pack for MIMXRT1160-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1160-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MIMXRT1166_DFP" vendor="NXP" version="19.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="FATFS" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="sdcard_fatfs_cm4" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_fatfs/cm4" doc="readme.md">
      <description>The SDCARD FATFS project is a demonstration program that uses the SDK software. Tt mounts a file system based on a SD card then does "creat directory/read directory/create file/write file/read file"operation. The...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_freertos_cm4" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_fatfs_freertos/cm4" doc="readme.md">
      <description>The SDCARD FATFS FREERTOS project is a demonstration program that uses the SDK software. The purpose of this example is to show how to use SDCARD with fatfs and freertos.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_freertos_cm4" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_freertos/cm4" doc="readme.md">
      <description>The SDCARD FreeRTOS project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver with FreeRTOS in SDK...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_interrupt_cm4" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_interrupt/cm4" doc="readme.md">
      <description>The SDCARD Interrupt project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_interrupt_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_polling_cm4" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_polling/cm4" doc="readme.md">
      <description>The SDCARD Polling project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_polling_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_polling_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_cm7" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_fatfs/cm7" doc="readme.md">
      <description>The SDCARD FATFS project is a demonstration program that uses the SDK software. Tt mounts a file system based on a SD card then does "creat directory/read directory/create file/write file/read file"operation. The...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_cm7.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_freertos_cm7" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_fatfs_freertos/cm7" doc="readme.md">
      <description>The SDCARD FATFS FREERTOS project is a demonstration program that uses the SDK software. The purpose of this example is to show how to use SDCARD with fatfs and freertos.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_freertos_cm7" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_freertos/cm7" doc="readme.md">
      <description>The SDCARD FreeRTOS project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver with FreeRTOS in SDK...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="sdcard_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_interrupt_cm7" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_interrupt/cm7" doc="readme.md">
      <description>The SDCARD Interrupt project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="sdcard_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_polling_cm7" folder="boards/evkmimxrt1160/sdmmc_examples/sdcard_polling/cm7" doc="readme.md">
      <description>The SDCARD Polling project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_polling_cm7.uvprojx"/>
        <environment name="csolution" load="sdcard_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
