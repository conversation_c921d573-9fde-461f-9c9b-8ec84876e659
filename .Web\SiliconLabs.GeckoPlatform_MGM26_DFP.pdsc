<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_MGM26_DFP</name>
  <description>Silicon Labs MGM26 Mighty Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>MGM26</keyword>
    <keyword>MGM26</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="MGM26 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
- 32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in QFN packaging;- Integrated PA with up to 19.5 dBm transmit power;- Energy-efficient radio core with low active and sleep currents;- Secure Vault;- AI/ML Hardware Accelerator;- Up to 3200 kB of flash and 512 kB of RAM&#xD;&#xA;
      </description>

      <subFamily DsubFamily="MGM260P">
        <!-- *************************  Device 'MGM260PB22VNA'  ***************************** -->
        <device Dname="MGM260PB22VNA">
          <compile header="Device/SiliconLabs/MGM26/Include/em_device.h"  define="MGM260PB22VNA"/>
          <debug      svd="SVD/MGM26/MGM260PB22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM260PB32VNA'  ***************************** -->
        <device Dname="MGM260PB32VNA">
          <compile header="Device/SiliconLabs/MGM26/Include/em_device.h"  define="MGM260PB32VNA"/>
          <debug      svd="SVD/MGM26/MGM260PB32VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM260PB32VNN'  ***************************** -->
        <device Dname="MGM260PB32VNN">
          <compile header="Device/SiliconLabs/MGM26/Include/em_device.h"  define="MGM260PB32VNN"/>
          <debug      svd="SVD/MGM26/MGM260PB32VNN.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM260PD22VNA'  ***************************** -->
        <device Dname="MGM260PD22VNA">
          <compile header="Device/SiliconLabs/MGM26/Include/em_device.h"  define="MGM260PD22VNA"/>
          <debug      svd="SVD/MGM26/MGM260PD22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM260PD32VNA'  ***************************** -->
        <device Dname="MGM260PD32VNA">
          <compile header="Device/SiliconLabs/MGM26/Include/em_device.h"  define="MGM260PD32VNA"/>
          <debug      svd="SVD/MGM26/MGM260PD32VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM260PD32VNN'  ***************************** -->
        <device Dname="MGM260PD32VNN">
          <compile header="Device/SiliconLabs/MGM26/Include/em_device.h"  define="MGM260PD32VNN"/>
          <debug      svd="SVD/MGM26/MGM260PD32VNN.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="MGM26">
      <description>Silicon Labs MGM26 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="MGM26*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="MGM26">
      <description>System Startup for Silicon Labs MGM26 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/MGM26/Include/"/>
        <file category="header"  name="Device/SiliconLabs/MGM26/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/MGM26/Source/system_mgm26.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
