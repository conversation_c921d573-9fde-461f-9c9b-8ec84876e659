<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFM32LG_DFP</name>
  <description>Silicon Labs EFM32LG Leopard Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
    <release version="3.1.0" date="2020-12-11">
      New MCU's families efr32pg22, efr32fg23, efr32zg23 and efm32pg23.
    </release>
    <release version="3.0.0" date="2020-09-14">
      Addition of EFR32ZG13L, EFR32ZG13S and removal of EFR32BG14P device families.
    </release>
    <release version="2.7.3" date="2020-03-23">
      The PRORTC timer peripheral has been added to EFR32xG13 and EFR32xG14 device families.
    </release>
    <release version="2.7.0" date="2019-11-12">
      Changed pack names and version number sequence.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFM32LG</keyword>
    <keyword>EFM32</keyword>
    <keyword>Leopard Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFM32LG Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M3" DcoreVersion="r2p1" Dfpu="NO_FPU" Dmpu="MPU" Dtz="NO_TZ" Ddsp="NO_DSP" Dendian="Little-endian" Dclock="48000000"/>
      <book name="Documents/cortex_m3_dgug.pdf"      title="Cortex-M3 Generic User Guide"/>
      <book name="Documents/efm32-cortex-m3-rm.pdf"  title="EFM32 Cortex-M3 Reference Manual"/>
      <book name="Documents/EFM32LG-RM.pdf"  title="EFM32LG Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M3 processor running at up to 48 MHz&#xD;&#xA;- Up to 256 kB Flash and 32 kB RAM memory&#xD;&#xA;- Energy efficient and autonomous peripherals&#xD;&#xA;- Ultra low power Energy Modes with sub-uA operation&#xD;&#xA;- Fast wake-up time of only 2 us&#xD;&#xA;&#xD;&#xA;The EFM32LG microcontroller series revolutionizes the 8- to 32-bit market with a combination of unmatched performance and ultra low power consumption in both active- and sleep modes. EFM32LG devices consume as little as 211 uA/MHz in run mode.
      </description>

      <subFamily DsubFamily="EFM32LG230">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG230 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG230 Errata"/>
        <!-- *************************  Device 'EFM32LG230F128'  ***************************** -->
        <device Dname="EFM32LG230F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG230F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG230F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG230F256'  ***************************** -->
        <device Dname="EFM32LG230F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG230F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG230F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG230F64'  ***************************** -->
        <device Dname="EFM32LG230F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG230F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG230F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG232">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG232 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG232 Errata"/>
        <!-- *************************  Device 'EFM32LG232F128'  ***************************** -->
        <device Dname="EFM32LG232F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG232F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG232F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG232F256'  ***************************** -->
        <device Dname="EFM32LG232F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG232F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG232F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG232F64'  ***************************** -->
        <device Dname="EFM32LG232F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG232F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG232F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG280">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG280 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG280 Errata"/>
        <!-- *************************  Device 'EFM32LG280F128'  ***************************** -->
        <device Dname="EFM32LG280F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG280F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG280F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG280F256'  ***************************** -->
        <device Dname="EFM32LG280F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG280F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG280F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG280F64'  ***************************** -->
        <device Dname="EFM32LG280F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG280F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG280F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG290">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG290 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG290 Errata"/>
        <!-- *************************  Device 'EFM32LG290F128'  ***************************** -->
        <device Dname="EFM32LG290F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG290F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG290F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG290F256'  ***************************** -->
        <device Dname="EFM32LG290F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG290F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG290F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG290F64'  ***************************** -->
        <device Dname="EFM32LG290F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG290F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG290F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG295">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG295 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG295 Errata"/>
        <!-- *************************  Device 'EFM32LG295F128'  ***************************** -->
        <device Dname="EFM32LG295F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG295F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG295F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG295F256'  ***************************** -->
        <device Dname="EFM32LG295F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG295F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG295F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG295F64'  ***************************** -->
        <device Dname="EFM32LG295F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG295F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG295F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG330">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG330 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG330 Errata"/>
        <!-- *************************  Device 'EFM32LG330F128'  ***************************** -->
        <device Dname="EFM32LG330F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG330F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG330F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG330F256'  ***************************** -->
        <device Dname="EFM32LG330F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG330F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG330F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG330F64'  ***************************** -->
        <device Dname="EFM32LG330F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG330F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG330F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG332">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG332 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG332 Errata"/>
        <!-- *************************  Device 'EFM32LG332F128'  ***************************** -->
        <device Dname="EFM32LG332F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG332F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG332F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG332F256'  ***************************** -->
        <device Dname="EFM32LG332F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG332F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG332F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG332F64'  ***************************** -->
        <device Dname="EFM32LG332F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG332F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG332F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG360">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG360 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG360 Errata"/>
        <!-- *************************  Device 'EFM32LG360F128'  ***************************** -->
        <device Dname="EFM32LG360F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG360F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG360F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG360F256'  ***************************** -->
        <device Dname="EFM32LG360F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG360F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG360F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG360F64'  ***************************** -->
        <device Dname="EFM32LG360F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG360F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG360F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG380">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG380 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG380 Errata"/>
        <!-- *************************  Device 'EFM32LG380F128'  ***************************** -->
        <device Dname="EFM32LG380F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG380F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG380F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG380F256'  ***************************** -->
        <device Dname="EFM32LG380F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG380F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG380F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG380F64'  ***************************** -->
        <device Dname="EFM32LG380F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG380F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG380F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG390">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG390 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG390 Errata"/>
        <!-- *************************  Device 'EFM32LG390F128'  ***************************** -->
        <device Dname="EFM32LG390F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG390F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG390F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG390F256'  ***************************** -->
        <device Dname="EFM32LG390F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG390F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG390F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG390F64'  ***************************** -->
        <device Dname="EFM32LG390F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG390F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG390F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG395">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG395 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG395 Errata"/>
        <!-- *************************  Device 'EFM32LG395F128'  ***************************** -->
        <device Dname="EFM32LG395F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG395F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG395F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG395F256'  ***************************** -->
        <device Dname="EFM32LG395F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG395F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG395F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG395F64'  ***************************** -->
        <device Dname="EFM32LG395F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG395F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG395F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG840">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG840 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG840 Errata"/>
        <!-- *************************  Device 'EFM32LG840F128'  ***************************** -->
        <device Dname="EFM32LG840F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG840F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG840F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG840F256'  ***************************** -->
        <device Dname="EFM32LG840F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG840F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG840F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG840F64'  ***************************** -->
        <device Dname="EFM32LG840F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG840F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG840F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG842">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG842 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG842 Errata"/>
        <!-- *************************  Device 'EFM32LG842F128'  ***************************** -->
        <device Dname="EFM32LG842F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG842F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG842F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG842F256'  ***************************** -->
        <device Dname="EFM32LG842F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG842F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG842F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG842F64'  ***************************** -->
        <device Dname="EFM32LG842F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG842F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG842F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG880">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG880 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG880 Errata"/>
        <!-- *************************  Device 'EFM32LG880F128'  ***************************** -->
        <device Dname="EFM32LG880F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG880F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG880F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG880F256'  ***************************** -->
        <device Dname="EFM32LG880F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG880F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG880F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG880F64'  ***************************** -->
        <device Dname="EFM32LG880F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG880F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG880F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG890">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG890 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG890 Errata"/>
        <!-- *************************  Device 'EFM32LG890F128'  ***************************** -->
        <device Dname="EFM32LG890F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG890F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG890F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG890F256'  ***************************** -->
        <device Dname="EFM32LG890F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG890F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG890F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG890F64'  ***************************** -->
        <device Dname="EFM32LG890F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG890F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG890F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG895">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG895 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG895 Errata"/>
        <!-- *************************  Device 'EFM32LG895F128'  ***************************** -->
        <device Dname="EFM32LG895F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG895F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG895F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG895F256'  ***************************** -->
        <device Dname="EFM32LG895F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG895F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG895F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG895F64'  ***************************** -->
        <device Dname="EFM32LG895F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG895F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG895F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG900">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG900 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG900 Errata"/>
        <!-- *************************  Device 'EFM32LG900F256'  ***************************** -->
        <device Dname="EFM32LG900F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG900F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG900F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG940">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG940 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG940 Errata"/>
        <!-- *************************  Device 'EFM32LG940F128'  ***************************** -->
        <device Dname="EFM32LG940F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG940F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG940F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG940F256'  ***************************** -->
        <device Dname="EFM32LG940F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG940F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG940F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG940F64'  ***************************** -->
        <device Dname="EFM32LG940F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG940F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG940F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG942">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG942 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG942 Errata"/>
        <!-- *************************  Device 'EFM32LG942F128'  ***************************** -->
        <device Dname="EFM32LG942F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG942F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG942F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG942F256'  ***************************** -->
        <device Dname="EFM32LG942F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG942F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG942F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG942F64'  ***************************** -->
        <device Dname="EFM32LG942F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG942F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG942F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG980">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG980 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG980 Errata"/>
        <!-- *************************  Device 'EFM32LG980F128'  ***************************** -->
        <device Dname="EFM32LG980F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG980F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG980F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG980F256'  ***************************** -->
        <device Dname="EFM32LG980F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG980F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG980F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG980F64'  ***************************** -->
        <device Dname="EFM32LG980F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG980F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG980F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG990">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG990 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG990 Errata"/>
        <!-- *************************  Device 'EFM32LG990F128'  ***************************** -->
        <device Dname="EFM32LG990F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG990F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG990F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG990F256'  ***************************** -->
        <device Dname="EFM32LG990F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG990F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG990F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG990F64'  ***************************** -->
        <device Dname="EFM32LG990F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG990F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG990F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG995">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG995 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG995 Errata"/>
        <!-- *************************  Device 'EFM32LG995F128'  ***************************** -->
        <device Dname="EFM32LG995F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG995F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG995F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG995F256'  ***************************** -->
        <device Dname="EFM32LG995F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG995F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG995F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG995F64'  ***************************** -->
        <device Dname="EFM32LG995F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG995F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG995F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFM32LG">
      <description>Silicon Labs EFM32LG device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFM32LG*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="4.4.0" condition="EFM32LG">
      <description>System Startup for Silicon Labs EFM32LG device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFM32LG/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFM32LG/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFM32LG/Source/GCC/startup_efm32lg.S" condition="Compiler GCC"   attr="config" version="4.4.0"/>
        <file category="source"  name="Device/SiliconLabs/EFM32LG/Source/IAR/startup_efm32lg.s" condition="Compiler IAR"   attr="config" version="4.4.0"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFM32LG/Source/GCC/efm32lg.ld" condition="Compiler GCC" attr="config" version="4.4.0"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFM32LG/Source/system_efm32lg.c" attr="config" version="4.4.0"/>
      </files>
    </component>
  </components>
</package>
