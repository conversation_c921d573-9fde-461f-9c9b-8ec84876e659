<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MKV11Z7_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MKV11Z7</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Project Template">NXP MCUXpresso SDK RTE Device Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="CMSIS Driver">NXP MCUXpresso SDK Peripheral CMSIS Drivers</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="5.8.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="MKV11Z7" Dvendor="NXP:11">
      <description>Kinetis KV1x-75 MHz,  Entry-level 3ph FOC / Sensorless Motor Control MCUs based on ARM Cortex-M0+</description>
      <device Dname="MKV11Z128xxx7">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="75000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKV11Z128xxx7_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x020000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x1ffff000" size="0x4000" access="rw" default="1"/>
        <algorithm name="arm/MKV_P128_2KB_SEC.FLM" start="0x00000000" size="0x00020000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <algorithm name="arm/MKV1x_FAC.FLM" start="0xffff0000" size="0x00000024" RAMstart="0x20000000" RAMsize="0x00001000" default="0"/>
        <debug svd="MKV11Z7.xml"/>
        <variant Dvariant="MKV11Z128VFM7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z128VFM7"/>
        </variant>
        <variant Dvariant="MKV11Z128VLC7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z128VLC7"/>
        </variant>
        <variant Dvariant="MKV11Z128VLF7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z128VLF7"/>
        </variant>
        <variant Dvariant="MKV11Z128VLH7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z128VLH7"/>
        </variant>
      </device>
      <device Dname="MKV11Z64xxx7">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="75000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKV11Z64xxx7_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x010000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x1ffff000" size="0x4000" access="rw" default="1"/>
        <algorithm name="arm/MKV_P64_2KB_SEC.FLM" start="0x00000000" size="0x00010000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <algorithm name="arm/MKV1x_FAC.FLM" start="0xffff0000" size="0x00000024" RAMstart="0x20000000" RAMsize="0x00001000" default="0"/>
        <debug svd="MKV11Z7.xml"/>
        <variant Dvariant="MKV11Z64VFM7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z64VFM7"/>
        </variant>
        <variant Dvariant="MKV11Z64VLC7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z64VLC7"/>
        </variant>
        <variant Dvariant="MKV11Z64VLF7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z64VLF7"/>
        </variant>
        <variant Dvariant="MKV11Z64VLH7">
          <compile header="fsl_device_registers.h" define="CPU_MKV11Z64VLH7"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.MKV11Z7">
      <accept Dname="MKV11Z128xxx7" Dvariant="MKV11Z128VFM7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128VFM7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128xxx7" Dvariant="MKV11Z128VLC7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128VLC7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128xxx7" Dvariant="MKV11Z128VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128xxx7" Dvariant="MKV11Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64xxx7" Dvariant="MKV11Z64VFM7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64VFM7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64xxx7" Dvariant="MKV11Z64VLC7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64VLC7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64xxx7" Dvariant="MKV11Z64VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64xxx7" Dvariant="MKV11Z64VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKV11Z64VLH7" Dvendor="NXP:11"/>
    </condition>
    <condition id="component.serial_manager_AND_utility.assert_AND_utility.debug_console">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="utility.assert_lite_AND_utility.debug_console_lite">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="_component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite_">
      <accept condition="component.serial_manager_AND_utility.assert_AND_utility.debug_console"/>
      <accept condition="utility.assert_lite_AND_utility.debug_console_lite"/>
    </condition>
    <condition id="device.MKV11Z7_AND___component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite___AND_component.uart_adapter_AND_device.MKV11Z7_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.port_AND_driver.smc_AND_driver.uart">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require Cclass="Device" Cgroup="Startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require condition="_component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite_"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.dmamux_AND_driver.edma">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.osa_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="osa"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.crc">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="crc"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.dspi">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.flash">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flash"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.ftm">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ftm"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.gpio_AND_driver.port">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.log_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="log"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.log_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="log"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.lptmr">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.lists_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="component.serial_manager_spi_OR_component.serial_manager_uart_OR_component.serial_manager_virtual">
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_spi"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_virtual"/>
    </condition>
    <condition id="device.MKV11Z7_AND__component.serial_manager_spi_OR_component.serial_manager_uart_OR_component.serial_manager_virtual__AND_component.lists_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.serial_manager_spi_OR_component.serial_manager_uart_OR_component.serial_manager_virtual"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.dspi_adapter_AND_component.serial_manager_AND_driver.dspi">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi_adapter"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.serial_manager_AND_component.uart_adapter_AND_driver.uart">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.serial_manager_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="component.ftm_adapter_OR_component.lptmr_adapter">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="ftm_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter"/>
    </condition>
    <condition id="device.MKV11Z7_AND__component.ftm_adapter_OR_component.lptmr_adapter__AND_component.lists_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.ftm_adapter_OR_component.lptmr_adapter"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.uart">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
    </condition>
    <condition id="device.MKV11Z7_AND_CMSIS_Include_core_cm">
      <require condition="device.MKV11Z7"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="armclang_OR_iar">
      <accept Tcompiler="ARMCC"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="device.MKV11Z7_AND__armclang_OR_iar__AND_device.MKV11Z7_system">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKV11Z7_system"/>
      <require condition="armclang_OR_iar"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="device.MKV11Z7_AND_device.MKV11Z7_CMSIS">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKV11Z7_header"/>
    </condition>
    <condition id="device.MKV11Z7_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.dspi_edma">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" Csub="Custom" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKV11Z7_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c_AND_driver.i2c_edma">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Csub="Custom" Capiversion="2.4.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKV11Z7_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.uart_AND_driver.uart_edma">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Csub="Custom" Capiversion="2.4.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKV11Z7_AND_device.MKV11Z7_CMSIS_AND_driver.clock">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKV11Z7_header"/>
    </condition>
    <condition id="core_type.cm0p">
      <require Dcore="Cortex-M0+"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.dspi_AND_driver.edma">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.common_AND_driver.dmamux">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.edma_AND_driver.flexcan">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexcan"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.edma_AND_driver.i2c">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
    </condition>
    <condition id="device.MKV11Z7_AND_driver.edma_AND_driver.uart">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
    </condition>
    <condition id="device.MKV11Z7_AND_utility.debug_console">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKV11Z7_AND_utility.debug_console_lite">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.uart_adapter_AND_driver.common">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKV11Z7_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKV11Z7"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="MKV11Z7" Cversion="1.0.0" condition="device.MKV11Z7_AND___component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite___AND_component.uart_adapter_AND_device.MKV11Z7_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.port_AND_driver.smc_AND_driver.uart" isDefaultVariant="1">
      <description>Devices_project_template MKV11Z7; {for-development:SDK-Manifest-ID: project_template.MKV11Z7.MKV11Z7}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.dmamux_AND_driver.edma">
      <description>Rte_device; {for-development:SDK-Manifest-ID: RTE_Device.MKV11Z7}</description>
      <files>
        <file category="header" attr="config" name="template/RTE_Device.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="button" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component button; {for-development:SDK-Manifest-ID: component.button.MKV11Z7}</description>
      <files>
        <file category="header" name="components/button/fsl_component_button.h"/>
        <file category="sourceC" name="components/button/fsl_component_button.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common_task" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.osa_AND_driver.common">
      <description>Component common_task; {for-development:SDK-Manifest-ID: component.common_task.MKV11Z7}</description>
      <files>
        <file category="header" name="components/common_task/fsl_component_common_task.h"/>
        <file category="sourceC" name="components/common_task/fsl_component_common_task.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.crc">
      <description>Component crc_adapter; {for-development:SDK-Manifest-ID: component.crc_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/crc/fsl_adapter_crc.h"/>
        <file category="sourceC" name="components/crc/fsl_adapter_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dspi_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.dspi">
      <description>Component dspi_adapter; {for-development:SDK-Manifest-ID: component.dspi_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/spi/fsl_adapter_spi.h"/>
        <file category="sourceC" name="components/spi/fsl_adapter_dspi.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.flash">
      <description>Component flash_adapter; {for-development:SDK-Manifest-ID: component.flash_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/internal_flash/fsl_adapter_flash.h"/>
        <file category="sourceC" name="components/internal_flash/fsl_adapter_flash.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ftm_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.ftm">
      <description>Component ftm_adapter; {for-development:SDK-Manifest-ID: component.ftm_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/timer/fsl_adapter_timer.h"/>
        <file category="sourceC" name="components/timer/fsl_adapter_ftm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.gpio_AND_driver.port">
      <description>Component gpio_adapter; {for-development:SDK-Manifest-ID: component.gpio_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/gpio/fsl_adapter_gpio.h"/>
        <file category="sourceC" name="components/gpio/fsl_adapter_gpio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="led" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component led; {for-development:SDK-Manifest-ID: component.led.MKV11Z7}</description>
      <files>
        <file category="header" name="components/led/fsl_component_led.h"/>
        <file category="sourceC" name="components/led/fsl_component_led.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common">
      <description>Component lists; {for-development:SDK-Manifest-ID: component.lists.MKV11Z7}</description>
      <files>
        <file category="header" name="components/lists/fsl_component_generic_list.h"/>
        <file category="sourceC" name="components/lists/fsl_component_generic_list.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="log" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_utility.debug_console">
      <description>Component log; {for-development:SDK-Manifest-ID: component.log.MKV11Z7}</description>
      <files>
        <file category="header" name="components/log/fsl_component_log.h"/>
        <file category="header" name="components/log/fsl_component_log_config.h"/>
        <file category="sourceC" name="components/log/fsl_component_log.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="debugconsole" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.log_AND_driver.common_AND_utility.debug_console">
      <description>Component log backend debug console; {for-development:SDK-Manifest-ID: component.log.backend.debugconsole.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="components/log/fsl_component_log_backend_debugconsole.c"/>
        <file category="header" name="components/log/fsl_component_log_backend_debugconsole.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ringbuffer" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.log_AND_driver.common">
      <description>Component log backend ring buffer; {for-development:SDK-Manifest-ID: component.log.backend.ringbuffer.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="components/log/fsl_component_log_backend_ringbuffer.c"/>
        <file category="header" name="components/log/fsl_component_log_backend_ringbuffer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.lptmr">
      <description>Component lptmr_adapter; {for-development:SDK-Manifest-ID: component.lptmr_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/timer/fsl_adapter_timer.h"/>
        <file category="sourceC" name="components/timer/fsl_adapter_lptmr.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.lists_AND_driver.common">
      <description>Component mem_manager; {for-development:SDK-Manifest-ID: component.mem_manager.MKV11Z7}</description>
      <files>
        <file category="header" name="components/mem_manager/fsl_component_mem_manager.h"/>
        <file category="sourceC" name="components/mem_manager/fsl_component_mem_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager_light" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.lists_AND_driver.common">
      <description>Component mem_manager_light; {for-development:SDK-Manifest-ID: component.mem_manager_light.MKV11Z7}</description>
      <files>
        <file category="header" name="components/mem_manager/fsl_component_mem_manager.h"/>
        <file category="sourceC" name="components/mem_manager/fsl_component_mem_manager_light.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.lists_AND_driver.common">
      <description>Component osa; {for-development:SDK-Manifest-ID: component.osa.MKV11Z7}</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa_bm" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.lists_AND_driver.common">
      <description>Component osa_bm; {for-development:SDK-Manifest-ID: component.osa_bm.MKV11Z7}</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="panic" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_utility.debug_console">
      <description>Component panic; {for-development:SDK-Manifest-ID: component.panic.MKV11Z7}</description>
      <files>
        <file category="header" name="components/panic/fsl_component_panic.h"/>
        <file category="sourceC" name="components/panic/fsl_component_panic.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pwm_ftm_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.ftm">
      <description>Component pwm_ftm_adapter; {for-development:SDK-Manifest-ID: component.pwm_ftm_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/pwm/fsl_adapter_pwm.h"/>
        <file category="sourceC" name="components/pwm/fsl_adapter_pwm_ftm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager" Cversion="1.0.1" condition="device.MKV11Z7_AND__component.serial_manager_spi_OR_component.serial_manager_uart_OR_component.serial_manager_virtual__AND_component.lists_AND_driver.common">
      <description>Component serial_manager; {for-development:SDK-Manifest-ID: component.serial_manager.MKV11Z7}</description>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_manager.h"/>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_internal.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_spi" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.dspi_adapter_AND_component.serial_manager_AND_driver.dspi">
      <description>Component serial_manager_spi; {for-development:SDK-Manifest-ID: component.serial_manager_spi.MKV11Z7}</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SPI
#define SERIAL_PORT_TYPE_SPI 1
#endif
#ifndef SERIAL_PORT_TYPE_SPI_MASTER
#define SERIAL_PORT_TYPE_SPI_MASTER 1
#endif
#ifndef SERIAL_PORT_TYPE_SPI_SLAVE
#define SERIAL_PORT_TYPE_SPI_SLAVE 1
#endif
#ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
#define SERIAL_MANAGER_NON_BLOCKING_MODE 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_spi.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_port_spi.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.serial_manager_AND_component.uart_adapter_AND_driver.uart">
      <description>Component serial_manager_uart; {for-development:SDK-Manifest-ID: component.serial_manager_uart.MKV11Z7}</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_uart.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_port_uart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_virtual" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.serial_manager_AND_driver.common">
      <description>Component serial_manager_virtual; {for-development:SDK-Manifest-ID: component.serial_manager_virtual.MKV11Z7}</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_VIRTUAL
#define SERIAL_PORT_TYPE_VIRTUAL 1
#endif
#ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
#define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 
#endif
</RTE_Components_h>
      <files>
        <file category="header" name="components/serial_manager/fsl_component_serial_port_virtual.h"/>
        <file category="sourceC" name="components/serial_manager/fsl_component_serial_port_virtual.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_crc_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common">
      <description>Component software_crc_adapter; {for-development:SDK-Manifest-ID: component.software_crc_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/crc/fsl_adapter_crc.h"/>
        <file category="sourceC" name="components/crc/fsl_adapter_software_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_rng_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common">
      <description>Component software_rng_adapter; {for-development:SDK-Manifest-ID: component.software_rng_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/rng/fsl_adapter_rng.h"/>
        <file category="sourceC" name="components/rng/fsl_adapter_software_rng.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager" Cversion="1.0.0" condition="device.MKV11Z7_AND__component.ftm_adapter_OR_component.lptmr_adapter__AND_component.lists_AND_driver.common">
      <description>Component timer_manager; {for-development:SDK-Manifest-ID: component.timer_manager.MKV11Z7}</description>
      <files>
        <file category="header" name="components/timer_manager/fsl_component_timer_manager.h"/>
        <file category="sourceC" name="components/timer_manager/fsl_component_timer_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common_AND_driver.uart">
      <description>Component uart_adapter; {for-development:SDK-Manifest-ID: component.uart_adapter.MKV11Z7}</description>
      <files>
        <file category="header" name="components/uart/fsl_adapter_uart.h"/>
        <file category="sourceC" name="components/uart/fsl_adapter_uart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="MKV11Z7_header" Cversion="1.0.0" condition="device.MKV11Z7_AND_CMSIS_Include_core_cm">
      <description>Device MKV11Z7_cmsis; {for-development:SDK-Manifest-ID: device.MKV11Z7_CMSIS.MKV11Z7}</description>
      <files>
        <file category="header" name="fsl_device_registers.h"/>
        <file category="header" name="MKV11Z7.h"/>
        <file category="header" name="MKV11Z7_features.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Cversion="1.1.0" condition="device.MKV11Z7_AND__armclang_OR_iar__AND_device.MKV11Z7_system">
      <description>Device MKV11Z7_startup; {for-development:SDK-Manifest-ID: device.MKV11Z7_startup.MKV11Z7}</description>
      <files>
        <file condition="iar" category="sourceAsm" attr="config" name="iar/startup_MKV11Z7.s" version="1.1.0"/>
        <file condition="mdk" category="sourceAsm" attr="config" name="arm/startup_MKV11Z7.S" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKV11Z128xxx7_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKV11Z128xxx7_ram.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKV11Z64xxx7_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKV11Z64xxx7_ram.scf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKV11Z128xxx7_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKV11Z128xxx7_ram.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKV11Z64xxx7_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKV11Z64xxx7_ram.icf" version="1.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="MKV11Z7_system" Cversion="1.0.0" condition="device.MKV11Z7_AND_device.MKV11Z7_CMSIS">
      <description>Device MKV11Z7_system; {for-development:SDK-Manifest-ID: device.MKV11Z7_system.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="system_MKV11Z7.c"/>
        <file category="header" name="system_MKV11Z7.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="adc" Cversion="2.3.0" condition="device.MKV11Z7_AND_driver.common">
      <description>ADC16 Driver; {for-development:SDK-Manifest-ID: platform.drivers.adc16.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_adc16.c"/>
        <file category="header" name="drivers/fsl_adc16.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.4.1" condition="device.MKV11Z7_AND_driver.common">
      <description>Clock Driver; {for-development:SDK-Manifest-ID: platform.drivers.clock.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_clock.h"/>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cmp" Cversion="2.0.2" condition="device.MKV11Z7_AND_driver.common">
      <description>CMP Driver; {for-development:SDK-Manifest-ID: platform.drivers.cmp.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cmp.c"/>
        <file category="header" name="drivers/fsl_cmp.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="dspi_cmsis" Cversion="2.3.0" Capiversion="2.2.0" condition="device.MKV11Z7_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.dspi_edma">
      <description>DSPI CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.dspi_cmsis.MKV11Z7}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_dspi_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_dspi_cmsis.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="i2c_cmsis" Cversion="2.2.0" Capiversion="2.3.0" condition="device.MKV11Z7_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c_AND_driver.i2c_edma">
      <description>I2C CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.i2c_cmsis.MKV11Z7}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_i2c_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_i2c_cmsis.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="uart_cmsis" Cversion="2.1.0" Capiversion="2.3.0" condition="device.MKV11Z7_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.uart_AND_driver.uart_edma">
      <description>UART CMSIS Driver; {for-development:SDK-Manifest-ID: platform.drivers.uart_cmsis.MKV11Z7}</description>
      <files>
        <file category="header" name="cmsis_drivers/fsl_uart_cmsis.h"/>
        <file category="sourceC" name="cmsis_drivers/fsl_uart_cmsis.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.4.0" condition="device.MKV11Z7_AND_device.MKV11Z7_CMSIS_AND_driver.clock">
      <description>COMMON Driver; {for-development:SDK-Manifest-ID: platform.drivers.common.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_common.h"/>
        <file category="sourceC" name="drivers/fsl_common.c"/>
        <file condition="core_type.cm0p" category="sourceC" name="drivers/fsl_common_arm.c"/>
        <file condition="core_type.cm0p" category="header" name="drivers/fsl_common_arm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc" Cversion="2.0.3" condition="device.MKV11Z7_AND_driver.common">
      <description>CRC Driver; {for-development:SDK-Manifest-ID: platform.drivers.crc.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_crc.c"/>
        <file category="header" name="drivers/fsl_crc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dac" Cversion="2.0.2" condition="device.MKV11Z7_AND_driver.common">
      <description>DAC Driver; {for-development:SDK-Manifest-ID: platform.drivers.dac.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dac.c"/>
        <file category="header" name="drivers/fsl_dac.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux" Cversion="2.0.5" condition="device.MKV11Z7_AND_driver.common">
      <description>DMAMUX Driver; {for-development:SDK-Manifest-ID: platform.drivers.dmamux.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_dmamux.h"/>
        <file category="sourceC" name="drivers/fsl_dmamux.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dspi" Cversion="2.2.5" condition="device.MKV11Z7_AND_driver.common">
      <description>DSPI Driver; {for-development:SDK-Manifest-ID: platform.drivers.dspi.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_dspi.h"/>
        <file category="sourceC" name="drivers/fsl_dspi.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dspi_edma" Cversion="2.2.5" condition="device.MKV11Z7_AND_driver.dspi_AND_driver.edma">
      <description>DSPI Driver; {for-development:SDK-Manifest-ID: platform.drivers.dspi_edma.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_dspi_edma.h"/>
        <file category="sourceC" name="drivers/fsl_dspi_edma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="edma" Cversion="2.4.3" condition="device.MKV11Z7_AND_driver.common_AND_driver.dmamux">
      <description>EDMA Driver; {for-development:SDK-Manifest-ID: platform.drivers.edma.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_edma.h"/>
        <file category="sourceC" name="drivers/fsl_edma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ewm" Cversion="2.0.3" condition="device.MKV11Z7_AND_driver.common">
      <description>EWM Driver; {for-development:SDK-Manifest-ID: platform.drivers.ewm.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ewm.c"/>
        <file category="header" name="drivers/fsl_ewm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash" Cversion="3.0.0" condition="device.MKV11Z7_AND_driver.common">
      <description>Flash Driver; {for-development:SDK-Manifest-ID: platform.drivers.flash.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_ftfx_adapter.h"/>
        <file category="header" name="drivers/fsl_ftfx_utilities.h"/>
        <file category="header" name="drivers/fsl_ftfx_features.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_controller.c"/>
        <file category="header" name="drivers/fsl_ftfx_controller.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flash.c"/>
        <file category="header" name="drivers/fsl_ftfx_flash.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_cache.c"/>
        <file category="header" name="drivers/fsl_ftfx_cache.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flexnvm.c"/>
        <file category="header" name="drivers/fsl_ftfx_flexnvm.h"/>
        <file category="header" name="drivers/fsl_flash.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexcan" Cversion="2.9.2" condition="device.MKV11Z7_AND_driver.common">
      <description>FLEXCAN Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexcan.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_flexcan.h"/>
        <file category="sourceC" name="drivers/fsl_flexcan.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexcan_edma" Cversion="2.9.2" condition="device.MKV11Z7_AND_driver.edma_AND_driver.flexcan">
      <description>FLEXCAN Driver; {for-development:SDK-Manifest-ID: platform.drivers.flexcan_edma.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_flexcan_edma.h"/>
        <file category="sourceC" name="drivers/fsl_flexcan_edma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ftm" Cversion="2.6.0" condition="device.MKV11Z7_AND_driver.common">
      <description>FTM Driver; {for-development:SDK-Manifest-ID: platform.drivers.ftm.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ftm.c"/>
        <file category="header" name="drivers/fsl_ftm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="fxos8700cq" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common">
      <description>Driver fxos8700cq; {for-development:SDK-Manifest-ID: driver.fxos8700cq.MKV11Z7}</description>
      <files>
        <file category="header" name="components/fxos8700cq/fsl_fxos.h"/>
        <file category="sourceC" name="components/fxos8700cq/fsl_fxos.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.7.1" condition="device.MKV11Z7_AND_driver.common">
      <description>GPIO Driver; {for-development:SDK-Manifest-ID: platform.drivers.gpio.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_gpio.h"/>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.0.9" condition="device.MKV11Z7_AND_driver.common">
      <description>I2C Driver; {for-development:SDK-Manifest-ID: platform.drivers.i2c.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_i2c.h"/>
        <file category="sourceC" name="drivers/fsl_i2c.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_edma" Cversion="2.0.9" condition="device.MKV11Z7_AND_driver.edma_AND_driver.i2c">
      <description>I2C Driver; {for-development:SDK-Manifest-ID: platform.drivers.i2c_edma.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c_edma.c"/>
        <file category="header" name="drivers/fsl_i2c_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="llwu" Cversion="2.0.5" condition="device.MKV11Z7_AND_driver.common">
      <description>LLWU Driver; {for-development:SDK-Manifest-ID: platform.drivers.llwu.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_llwu.h"/>
        <file category="sourceC" name="drivers/fsl_llwu.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr" Cversion="2.1.1" condition="device.MKV11Z7_AND_driver.common">
      <description>LPTMR Driver; {for-development:SDK-Manifest-ID: platform.drivers.lptmr.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lptmr.c"/>
        <file category="header" name="drivers/fsl_lptmr.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mmdvsq" Cversion="2.0.3" condition="device.MKV11Z7_AND_driver.common">
      <description>MMDVSQ Driver; {for-development:SDK-Manifest-ID: platform.drivers.mmdvsq.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_mmdvsq.h"/>
        <file category="sourceC" name="drivers/fsl_mmdvsq.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pdb" Cversion="2.0.4" condition="device.MKV11Z7_AND_driver.common">
      <description>PDB Driver; {for-development:SDK-Manifest-ID: platform.drivers.pdb.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pdb.c"/>
        <file category="header" name="drivers/fsl_pdb.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pmc" Cversion="2.0.3" condition="device.MKV11Z7_AND_driver.common">
      <description>PMC Driver; {for-development:SDK-Manifest-ID: platform.drivers.pmc.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_pmc.h"/>
        <file category="sourceC" name="drivers/fsl_pmc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="port" Cversion="2.4.1" condition="device.MKV11Z7_AND_driver.common">
      <description>PORT Driver; {for-development:SDK-Manifest-ID: platform.drivers.port.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_port.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rcm" Cversion="2.0.4" condition="device.MKV11Z7_AND_driver.common">
      <description>RCM Driver; {for-development:SDK-Manifest-ID: platform.drivers.rcm.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rcm.c"/>
        <file category="header" name="drivers/fsl_rcm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sim" Cversion="2.1.3" condition="device.MKV11Z7_AND_driver.common">
      <description>SIM Driver; {for-development:SDK-Manifest-ID: platform.drivers.sim.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_sim.h"/>
        <file category="sourceC" name="drivers/fsl_sim.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="smc" Cversion="2.0.7" condition="device.MKV11Z7_AND_driver.common">
      <description>SMC Driver; {for-development:SDK-Manifest-ID: platform.drivers.smc.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_smc.h"/>
        <file category="sourceC" name="drivers/fsl_smc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart" Cversion="2.5.1" condition="device.MKV11Z7_AND_driver.common">
      <description>UART Driver; {for-development:SDK-Manifest-ID: platform.drivers.uart.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_uart.h"/>
        <file category="sourceC" name="drivers/fsl_uart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart_edma" Cversion="2.5.3" condition="device.MKV11Z7_AND_driver.edma_AND_driver.uart">
      <description>UART Driver; {for-development:SDK-Manifest-ID: platform.drivers.uart_edma.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_uart_edma.h"/>
        <file category="sourceC" name="drivers/fsl_uart_edma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wdog" Cversion="2.0.1" condition="device.MKV11Z7_AND_driver.common">
      <description>WDOG Driver; {for-development:SDK-Manifest-ID: platform.drivers.wdog.MKV11Z7}</description>
      <files>
        <file category="header" name="drivers/fsl_wdog.h"/>
        <file category="sourceC" name="drivers/fsl_wdog.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert" Cversion="1.0.0" condition="device.MKV11Z7_AND_utility.debug_console">
      <description>Utility assert; {for-development:SDK-Manifest-ID: platform.utilities.assert.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="utilities/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite" Cversion="1.0.0" condition="device.MKV11Z7_AND_utility.debug_console_lite">
      <description>Utility assert_lite; {for-development:SDK-Manifest-ID: platform.utilities.assert_lite.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="utilities/debug_console_lite/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console" Cversion="1.0.1" condition="device.MKV11Z7_AND_component.serial_manager_AND_driver.common">
      <description>Utility debug_console; {for-development:SDK-Manifest-ID: utility.debug_console.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="utilities/str/fsl_str.c"/>
        <file category="header" name="utilities/str/fsl_str.h"/>
        <file category="sourceC" name="utilities/debug_console/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console.h"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console_conf.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.uart_adapter_AND_driver.common">
      <description>Utility debug_console_lite; {for-development:SDK-Manifest-ID: utility.debug_console_lite.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="utilities/str/fsl_str.c"/>
        <file category="header" name="utilities/str/fsl_str.h"/>
        <file category="sourceC" name="utilities/debug_console_lite/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console_lite/fsl_debug_console.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="notifier" Cversion="1.0.0" condition="device.MKV11Z7_AND_driver.common">
      <description>Utility notifier; {for-development:SDK-Manifest-ID: platform.utilities.notifier.MKV11Z7}</description>
      <files>
        <file category="sourceC" name="utilities/fsl_notifier.c"/>
        <file category="header" name="utilities/fsl_notifier.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="shell" Cversion="1.0.0" condition="device.MKV11Z7_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <description>Utility shell; {for-development:SDK-Manifest-ID: utility.shell.MKV11Z7}</description>
      <RTE_Components_h>
#ifndef DEBUG_CONSOLE_RX_ENABLE
#define DEBUG_CONSOLE_RX_ENABLE 0
#endif
</RTE_Components_h>
      <files>
        <file category="sourceC" name="utilities/fsl_shell.c"/>
        <file category="header" name="utilities/fsl_shell.h"/>
      </files>
    </component>
  </components>
</package>
