<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>RD-RW612-BGA_MBEDTLS_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware mbedtls Examples Pack for RD-RW612-BGA</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="RD-RW612-BGA_BSP" vendor="NXP" version="19.0.0"/>
      <package name="RW612_DFP" vendor="NXP" version="19.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="mbedtls_benchmark" folder="boards/rdrw612bga/mbedtls_examples/mbedtls_benchmark" doc="readme.md">
      <description>The mbdedTLS Benchmark demo application performs cryptographic algorithm benchmarking and prints results to the terminal.</description>
      <board name="RD-RW612-BGA" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_benchmark.uvprojx"/>
        <environment name="csolution" load="mbedtls_benchmark.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mbedtls_selftest" folder="boards/rdrw612bga/mbedtls_examples/mbedtls_selftest" doc="readme.md">
      <description>The mbdedTLS SelfTest program performs cryptographic algorithm testing and prints results to the terminal.</description>
      <board name="RD-RW612-BGA" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_selftest.uvprojx"/>
        <environment name="csolution" load="mbedtls_selftest.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mbedtls_selftest_thread" folder="boards/rdrw612bga/mbedtls_examples/mbedtls_selftest_thread" doc="readme.md">
      <description>The mbedTLS SelfTest Thread program contains two threads that performs cryptographic algorithm testing and prints results to the terminal in parallel.</description>
      <board name="RD-RW612-BGA" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_selftest_thread.uvprojx"/>
        <environment name="csolution" load="mbedtls_selftest_thread.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
