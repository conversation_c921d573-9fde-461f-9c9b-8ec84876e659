<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-RW612_FMSTR_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware fmstr Examples Pack for FRDM-RW612</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="FRDM-RW612_BSP" vendor="NXP" version="19.0.0"/>
      <package name="FREEMASTER" vendor="NXP" version="2.0.0"/>
      <package name="RW612_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="fmstr_example_uart" folder="boards/frdmrw612/freemaster_examples/fmstr_uart" doc="readme.txt">
      <description>FreeMASTER example using Serial-UART communication with the target microcontroller. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow by...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_uart.uvprojx"/>
        <environment name="csolution" load="fmstr_example_uart.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
