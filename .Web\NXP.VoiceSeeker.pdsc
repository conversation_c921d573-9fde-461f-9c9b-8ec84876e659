<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>VoiceSeeker</name>
  <vendor>NXP</vendor>
  <description>Software Pack for voice_seeker</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="device.RW610.internal_condition">
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.RW612.internal_condition">
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.voice_seeker.cm33_nodsp.condition_id">
      <require condition="allOf.board=rdrw612bga, middleware.voice_seeker.rdsp_utilities_public.cm33_noDSP.internal_condition"/>
    </condition>
    <condition id="allOf.board=rdrw612bga, middleware.voice_seeker.rdsp_utilities_public.cm33_noDSP.internal_condition">
      <require condition="board.rdrw612bga.internal_condition"/>
      <require Cclass="Voice" Cgroup="Voice Control AFE" Csub="rdsp_utilities_public" Cvariant="cm33_noDSP"/>
    </condition>
    <condition id="board.rdrw612bga.internal_condition">
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
    </condition>
    <condition id="allOf.toolchains=armgcc.condition_id">
      <require condition="toolchains.armgcc.internal_condition"/>
    </condition>
    <condition id="toolchains.armgcc.internal_condition">
      <accept Tcompiler="GCC"/>
    </condition>
    <condition id="device.MIMXRT1041.internal_condition">
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1042.internal_condition">
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1051.internal_condition">
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1052.internal_condition">
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1061.internal_condition">
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1062.internal_condition">
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1064.internal_condition">
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1165.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1166.internal_condition">
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1171.internal_condition">
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1172.internal_condition">
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1173.internal_condition">
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1175.internal_condition">
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1176.internal_condition">
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.voice_seeker.cm7.condition_id">
      <require condition="allOf.board=evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, middleware.voice_seeker.rdsp_utilities_public.cm7.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170, middleware.voice_seeker.rdsp_utilities_public.cm7.internal_condition">
      <require condition="board.evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
      <require Cclass="Voice" Cgroup="Voice Control AFE" Csub="rdsp_utilities_public" Cvariant="cm7"/>
    </condition>
    <condition id="board.evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <accept condition="device.MIMXRT1041.internal_condition"/>
      <accept condition="device.MIMXRT1042.internal_condition"/>
      <accept condition="device.MIMXRT1051.internal_condition"/>
      <accept condition="device.MIMXRT1052.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1064.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
    </condition>
    <condition id="middleware.voice_seeker.rdsp_utilities_public.cm33_noDSP.condition_id">
      <require condition="allOf.core=cm33, board=rdrw612bga.internal_condition"/>
    </condition>
    <condition id="allOf.core=cm33, board=rdrw612bga.internal_condition">
      <require condition="core.cm33.internal_condition"/>
      <require condition="board.rdrw612bga.internal_condition"/>
    </condition>
    <condition id="core.cm33.internal_condition">
      <accept Dcore="Cortex-M33"/>
    </condition>
    <condition id="middleware.voice_seeker.rdsp_utilities_public.cm7.condition_id">
      <require condition="allOf.core=cm7f, board=evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="allOf.core=cm7f, board=evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition">
      <require condition="core.cm7f.internal_condition"/>
      <require condition="board.evkmimxrt1040, evkbimxrt1050, evkmimxrt1060, evkmimxrt1064, evkbmimxrt1060, evkcmimxrt1060, evkmimxrt1160, evkmimxrt1170, evkbmimxrt1170.internal_condition"/>
    </condition>
    <condition id="core.cm7f.internal_condition">
      <accept Dcore="Cortex-M7"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Voice" Cgroup="Voice Control AFE" Csub="cm33_noDSP" Cversion="0.6.9" condition="middleware.voice_seeker.cm33_nodsp.condition_id">
      <description>Voice seeker library for Cortex M33 without DSP</description>
      <files>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/include/RdspCycleCounter.h" projectpath="voice_seeker/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/include/RdspStatusCodes.h" projectpath="voice_seeker/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/include/libVoiceSeekerLight.h" projectpath="voice_seeker/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="library" name="middleware/voice_seeker/ARM_CortexM33_noDSP/lib/libvoiceseeker_no_aec.a" projectpath="voice_seeker/lib"/>
        <file category="doc" name="middleware/voice_seeker/ARM_CortexM33_noDSP/LICENSE.txt" projectpath="voice_seeker"/>
        <file category="doc" name="middleware/voice_seeker/ARM_CortexM33_noDSP/SCR-ARM_CortexM33_noDSP-v0.6.9.txt" projectpath="voice_seeker"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="include" name="middleware/voice_seeker/ARM_CortexM33_noDSP/include/"/>
      </files>
    </component>
    <component Cclass="Voice" Cgroup="Voice Control AFE" Csub="cm7" Cversion="0.6.9" condition="middleware.voice_seeker.cm7.condition_id">
      <description>Voice seeker library for Cortex M7</description>
      <files>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/include/RdspCycleCounter.h" projectpath="voice_seeker/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/include/RdspStatusCodes.h" projectpath="voice_seeker/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/include/libVoiceSeekerLight.h" projectpath="voice_seeker/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="library" name="middleware/voice_seeker/ARM_CortexM7/lib/libvoiceseeker_no_aec.a" projectpath="voice_seeker/lib"/>
        <file category="doc" name="middleware/voice_seeker/ARM_CortexM7/LICENSE.txt" projectpath="voice_seeker"/>
        <file category="doc" name="middleware/voice_seeker/ARM_CortexM7/SCR-ARM_CortexM7-v0.6.9.txt" projectpath="voice_seeker"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="include" name="middleware/voice_seeker/ARM_CortexM7/include/"/>
      </files>
    </component>
    <component Cclass="Voice" Cgroup="Voice Control AFE" Csub="rdsp_utilities_public" Cvariant="cm33_noDSP" Cversion="0.6.9" condition="middleware.voice_seeker.rdsp_utilities_public.cm33_noDSP.condition_id">
      <description>Voice seeker memory utilities for Cortex M33 without DSP</description>
      <files>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include/RdspDeviceConfig.h" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include/RdspPlatforms.h" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include/RdspTypes.h" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public/RdspMemoryUtilsPublic.h" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public/RdspStackCheck.h" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public/memcheck.h" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="sourceC" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public/RdspMemoryUtilsPublic.c" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="sourceC" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public/memcheck.c" projectpath="voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="include" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/include/"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="include" name="middleware/voice_seeker/ARM_CortexM33_noDSP/rdsp_utilities_public/rdsp_memory_utils_public/"/>
      </files>
    </component>
    <component Cclass="Voice" Cgroup="Voice Control AFE" Csub="rdsp_utilities_public" Cvariant="cm7" Cversion="0.6.9" condition="middleware.voice_seeker.rdsp_utilities_public.cm7.condition_id">
      <description>Voice seeker memory utilities for Cortex M7</description>
      <files>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/include/RdspDeviceConfig.h" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/include/RdspPlatforms.h" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/include/RdspTypes.h" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/include"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public/RdspMemoryUtilsPublic.h" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public/RdspStackCheck.h" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="header" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public/memcheck.h" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="sourceC" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public/RdspMemoryUtilsPublic.c" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="sourceC" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public/memcheck.c" projectpath="voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="include" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/include/"/>
        <file condition="allOf.toolchains=armgcc.condition_id" category="include" name="middleware/voice_seeker/ARM_CortexM7/rdsp_utilities_public/rdsp_memory_utils_public/"/>
      </files>
    </component>
  </components>
</package>
