<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1040-EVK_AZURE_RTOS_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware azure_rtos Examples Pack for MIMXRT1040-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1040-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="AZURE_RTOS" vendor="NXP" version="2.0.0"/>
      <package name="MIMXRT1042_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="azure_iot_embedded_sdk" folder="boards/evkmimxrt1040/azure_rtos_examples/azure_iot_embedded_sdk" doc="readme.md">
      <description>A simple mqtt example with azure-sdk-for-c.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/azure_iot_embedded_sdk.uvprojx"/>
        <environment name="csolution" load="azure_iot_embedded_sdk.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="azure_iot_embedded_sdk_adu" folder="boards/evkmimxrt1040/azure_rtos_examples/azure_iot_embedded_sdk_adu" doc="readme.md">
      <description>A example that connects to azure iot hub with plug and play, and support ADU feature.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/azure_iot_embedded_sdk_adu.uvprojx"/>
        <environment name="csolution" load="azure_iot_embedded_sdk_adu.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="azure_iot_embedded_sdk_pnp" folder="boards/evkmimxrt1040/azure_rtos_examples/azure_iot_embedded_sdk_pnp" doc="readme.md">
      <description>A example that connects to azure iot hub with plug and play.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/azure_iot_embedded_sdk_pnp.uvprojx"/>
        <environment name="csolution" load="azure_iot_embedded_sdk_pnp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="azure_iot_mqtt" folder="boards/evkmimxrt1040/azure_rtos_examples/azure_iot_mqtt" doc="readme.md">
      <description>A simple mqtt example with azure IoT.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/azure_iot_mqtt.uvprojx"/>
        <environment name="csolution" load="azure_iot_mqtt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="combine_usbx_netxduo_filex" folder="boards/evkmimxrt1040/azure_rtos_examples/combine_usbx_netxduo_filex" doc="readme.md">
      <description>The combine_usbx_netxduo_filex example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/combine_usbx_netxduo_filex.uvprojx"/>
        <environment name="csolution" load="combine_usbx_netxduo_filex.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="filex_levelx_mflash" folder="boards/evkmimxrt1040/azure_rtos_examples/filex_levelx_mflash" doc="readme.md">
      <description>The filex_levelx_mflash example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/filex_levelx_mflash.uvprojx"/>
        <environment name="csolution" load="filex_levelx_mflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="filex_ram_disk" folder="boards/evkmimxrt1040/azure_rtos_examples/filex_ram_disk" doc="readme.md">
      <description>The filex_ram_disk example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/filex_ram_disk.uvprojx"/>
        <environment name="csolution" load="filex_ram_disk.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="filex_sdcard" folder="boards/evkmimxrt1040/azure_rtos_examples/filex_sdcard" doc="readme.md">
      <description>The filex_sdcard example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/filex_sdcard.uvprojx"/>
        <environment name="csolution" load="filex_sdcard.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="guix_washing_machine" folder="boards/evkmimxrt1040/azure_rtos_examples/guix_washing_machine" doc="readme.md">
      <description>The guix_washing_machine example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/guix_washing_machine.uvprojx"/>
        <environment name="csolution" load="guix_washing_machine.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_example" folder="boards/evkmimxrt1040/azure_rtos_examples/i2c_example" doc="readme.md">
      <description>The example shows I2C communication between two I2C ports.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_example.uvprojx"/>
        <environment name="csolution" load="i2c_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="netx_duo_iperf" folder="boards/evkmimxrt1040/azure_rtos_examples/netx_duo_iperf" doc="readme.md">
      <description>The netx_duo_iperf example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/netx_duo_iperf.uvprojx"/>
        <environment name="csolution" load="netx_duo_iperf.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="netx_duo_ping" folder="boards/evkmimxrt1040/azure_rtos_examples/netx_duo_ping" doc="readme.md">
      <description>The netx_duo_ping example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/netx_duo_ping.uvprojx"/>
        <environment name="csolution" load="netx_duo_ping.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pnp_temperature_controller" folder="boards/evkmimxrt1040/azure_rtos_examples/pnp_temperature_controller" doc="readme.md">
      <description>A example that implements the temperature controller with the azure Plug and Play.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pnp_temperature_controller.uvprojx"/>
        <environment name="csolution" load="pnp_temperature_controller.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_b2b_example_master" folder="boards/evkmimxrt1040/azure_rtos_examples/spi_b2b_example/master" doc="readme.md">
      <description>The example shows SPI communication between two SPI ports.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_b2b_example_master.uvprojx"/>
        <environment name="csolution" load="spi_b2b_example_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_b2b_example_slave" folder="boards/evkmimxrt1040/azure_rtos_examples/spi_b2b_example/slave" doc="readme.md">
      <description>The example shows SPI communication between two SPI ports.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_b2b_example_slave.uvprojx"/>
        <environment name="csolution" load="spi_b2b_example_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="threadx_demo" folder="boards/evkmimxrt1040/azure_rtos_examples/threadx_demo" doc="readme.md">
      <description>The ThreadX example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/threadx_demo.uvprojx"/>
        <environment name="csolution" load="threadx_demo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_example" folder="boards/evkmimxrt1040/azure_rtos_examples/uart_example" doc="readme.md">
      <description>The example shows how to use the uart driver in Azure RTOS.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_example.uvprojx"/>
        <environment name="csolution" load="uart_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_audio_microphone" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_audio_microphone" doc="readme.md">
      <description>The usbx_device_audio_microphone example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_audio_microphone.uvprojx"/>
        <environment name="csolution" load="usbx_device_audio_microphone.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_audio_speaker" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_audio_speaker" doc="readme.md">
      <description>The usbx_device_audio_speaker example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_audio_speaker.uvprojx"/>
        <environment name="csolution" load="usbx_device_audio_speaker.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_composite_cdc_acm_cdc_acm" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_composite_cdc_acm_cdc_acm" doc="readme.md">
      <description>The usbx_device_composite_cdc_acm_cdc_acm example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_composite_cdc_acm_cdc_acm.uvprojx"/>
        <environment name="csolution" load="usbx_device_composite_cdc_acm_cdc_acm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_hid_generic" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_hid_generic" doc="readme.md">
      <description>The usbx_device_hid_generic example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_hid_generic.uvprojx"/>
        <environment name="csolution" load="usbx_device_hid_generic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_hid_keyboard" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_hid_keyboard" doc="readme.md">
      <description>The usbx_device_hid_keyboard example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_hid_keyboard.uvprojx"/>
        <environment name="csolution" load="usbx_device_hid_keyboard.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_hid_mouse" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_hid_mouse" doc="readme.md">
      <description>The usbx_device_hid_mouse example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_hid_mouse.uvprojx"/>
        <environment name="csolution" load="usbx_device_hid_mouse.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_device_mass_storage" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_device_mass_storage" doc="readme.md">
      <description>The usbx_device_mass_storage example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_device_mass_storage.uvprojx"/>
        <environment name="csolution" load="usbx_device_mass_storage.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_host_hid_keyboard" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_host_hid_keyboard" doc="readme.md">
      <description>The usbx_host_hid_keyboard example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_host_hid_keyboard.uvprojx"/>
        <environment name="csolution" load="usbx_host_hid_keyboard.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_host_hid_mouse" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_host_hid_mouse" doc="readme.md">
      <description>The usbx_host_hid_mouse example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_host_hid_mouse.uvprojx"/>
        <environment name="csolution" load="usbx_host_hid_mouse.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usbx_host_mass_storage" folder="boards/evkmimxrt1040/azure_rtos_examples/usbx_host_mass_storage" doc="readme.md">
      <description>The usbx_host_mass_storage example.</description>
      <board name="MIMXRT1040-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usbx_host_mass_storage.uvprojx"/>
        <environment name="csolution" load="usbx_host_mass_storage.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
