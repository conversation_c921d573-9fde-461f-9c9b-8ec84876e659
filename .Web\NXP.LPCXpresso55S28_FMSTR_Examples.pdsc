<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso55S28_FMSTR_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware fmstr Examples Pack for LPCXpresso55S28</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPCXpresso55S28_BSP" vendor="NXP" version="19.0.0"/>
      <package name="LPC55S28_DFP" vendor="NXP" version="19.0.0"/>
      <package name="FREEMASTER" vendor="NXP" version="2.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="fmstr_example_any" folder="boards/lpcxpresso55s28/freemaster_examples/fmstr_any" doc="readme.txt">
      <description>FreeMASTER example fully configured by MCUXpresso ConfigTools. Serial communication is used by default, but it can be changed easily to CAN or other in the MCUXpresso Peripheral Tool. Also FreeMASTER driver features...See more details in readme document.</description>
      <board name="LPCXpresso55S28" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_any.uvprojx"/>
        <environment name="csolution" load="fmstr_example_any.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_pdbdm" folder="boards/lpcxpresso55s28/freemaster_examples/fmstr_pdbdm" doc="readme.txt">
      <description>FreeMASTER example using a special packet-driven protocol on top of JTAG or BDM direct memory access. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the...See more details in readme document.</description>
      <board name="LPCXpresso55S28" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_pdbdm.uvprojx"/>
        <environment name="csolution" load="fmstr_example_pdbdm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_rtt" folder="boards/lpcxpresso55s28/freemaster_examples/fmstr_rtt" doc="readme.txt">
      <description>FreeMASTER example using Segger RTT communication over J-Link interface. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow by modifying...See more details in readme document.</description>
      <board name="LPCXpresso55S28" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_rtt.uvprojx"/>
        <environment name="csolution" load="fmstr_example_rtt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_uart" folder="boards/lpcxpresso55s28/freemaster_examples/fmstr_uart" doc="readme.txt">
      <description>FreeMASTER example using Serial-UART communication with the target microcontroller. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow by...See more details in readme document.</description>
      <board name="LPCXpresso55S28" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_uart.uvprojx"/>
        <environment name="csolution" load="fmstr_example_uart.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmstr_example_usb_cdc" folder="boards/lpcxpresso55s28/freemaster_examples/fmstr_usb_cdc" doc="readme.txt">
      <description>FreeMASTER example using virtual serial communication at USB port and CDC VCOM class. This example application demonstrates use of FreeMASTER tool to visualize internal variables and to control the application flow...See more details in readme document.</description>
      <board name="LPCXpresso55S28" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmstr_example_usb_cdc.uvprojx"/>
        <environment name="csolution" load="fmstr_example_usb_cdc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
