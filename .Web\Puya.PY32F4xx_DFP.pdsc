<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.7.27"
  xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.27/schema/PACK.xsd">
  <vendor>Puya</vendor>
  <url>https://www.puyasemi.com/uploadfiles/</url>
  <name>PY32F4xx_DFP</name>
  <description>Puya PY32F4 Series Device Support</description>
  <releases>
    <release version="1.0.1" date="2024-08-19">
        Add PY32F410 series.
    </release>
    <release version="1.0.0" date="2023-08-07">
        First Release version of PY32F4 Device Family Pack.
    </release>
  </releases>

  <keywords>
    <keyword>Puya</keyword>
    <keyword>Device Support</keyword>
    <keyword>PY32F4</keyword>
    <keyword>PY32F4xx</keyword>
  </keywords>
  <devices>
    <family Dfamily="PY32F4 Series" Dvendor="Puya:176">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian" />
      <description>
PUYA' PY32F4 series of mainstream MCUs covers the needs of a large variety of applications in the industrial, medical and consumer markets. High performance with first-class peripherals and low-power, low-voltage operation is paired with a high level of integration at accessible prices with a simple architecture and easy-to-use tools.
Typical applications include motor drives and application control, medical and handheld equipment, industrial applications, PLCs, inverters, printers, and scanners, alarm systems, video intercom, HVAC and home audio equipment.

    - 5 V-tolerant I/Os
    - Timer with quadrature (incremental) encoder input
    - 96-bit unique ID
      </description>

      <!-- ************************  Subfamily 'PY32F403'  **************************** -->
      <subFamily DsubFamily="PY32F403">
        <processor Dclock="144000000" />
        <debug svd="CMSIS/SVD/PY32F403xx.svd" />

        <memory name="OPT" access="r" start="0x1FFF5000" size="0x00000010" />
        <algorithm name="CMSIS/Flash/PY32F403xx_OPT.FLM" start="0x1FFF5000" size="0x00000010" default="0"/>

        <debugvars configfile="CMSIS/Debug/PY32F403xx.dbgconf">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR: DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <sequences>
          <sequence name="DebugCoreStart">
            <block>
              // Replication of Standard Functionality
              Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
            </block>

            <block>
              // Device Specific Debug Setup
              Write32(0xE0042004, DbgMCU_CR);
            </block>
          </sequence>
        </sequences>

        <!-- *************************  Device 'PY32F403xB'  ***************************** -->
        <device Dname="PY32F403xB">
          <compile header="Drivers/CMSIS/Device/PUYA/PY32F4xx/Include/py32f4xx.h" />
          <memory name="IROM1" access="rx" start="0x08000000" size="0x00020000" startup="1" default="1" />
          <memory name="IRAM1" access="rw" start="0x20000000" size="0x00010000" init="0" default="1" />
          <algorithm name="CMSIS/Flash/PY32F403xx_128.FLM" start="0x08000000" size="0x00020000" default="1" />
        </device>

        <!-- *************************  Device 'PY32F403xC'  ***************************** -->
        <device Dname="PY32F403xC">
          <compile header="Drivers/CMSIS/Device/PUYA/PY32F4xx/Include/py32f4xx.h" />
          <memory name="IROM1" access="rx" start="0x08000000" size="0x00040000" startup="1" default="1" />
          <memory name="IRAM1" access="rw" start="0x20000000" size="0x00010000" init="0" default="1" />
          <algorithm name="CMSIS/Flash/PY32F403xx_256.FLM" start="0x08000000" size="0x00040000" default="1" />
        </device>

        <!-- *************************  Device 'PY32F403xD'  ***************************** -->
        <device Dname="PY32F403xD">
          <compile header="Drivers/CMSIS/Device/PUYA/PY32F4xx/Include/py32f4xx.h" />
          <memory name="IROM1" access="rx" start="0x08000000" size="0x00060000" startup="1" default="1" />
          <memory name="IRAM1" access="rw" start="0x20000000" size="0x00010000" init="0" default="1" />
          <algorithm name="CMSIS/Flash/PY32F403xx_384.FLM" start="0x08000000" size="0x00060000" default="1" />
        </device>

      </subFamily>

            <!-- ************************  Subfamily 'PY32F410'  **************************** -->
      <subFamily DsubFamily="PY32F410">
        <processor Dclock="128000000" />
        <debug svd="CMSIS/SVD/PY32F410xx.svd" />

        <memory name="OPT" access="r" start="0x1FFF1100" size="0x00000020" />
        <memory name="OTP" access="r" start="0x1FFF1080" size="0x00000080" />
        <algorithm name="CMSIS/Flash/PY32F410xx_OPT.FLM" start="0x1FFF1100" size="0x00000020" default="0"/>
        <algorithm name="CMSIS/Flash/PY32F410xx_OTP.FLM" start="0x1FFF1080" size="0x00000080" default="0"/>

        <debugvars configfile="CMSIS/Debug/PY32F410xx.dbgconf">
          __var DbgMCU_CR1      = 0x00000307;   // DBGMCU_CR1: DBG_WWDG_STOP, DBG_IWDG_STOP, DBG_SLEEP, DBG_STOP, DBG_STANDBY
          __var DbgMCU_CR2      = 0x00000000;
        </debugvars>

        <sequences>
          <sequence name="DebugCoreStart">
            <block>
              // Replication of Standard Functionality
              Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR
            </block>
            <block>
              // Device Specific Debug Setup
              Write32(0xE0042004, DbgMCU_CR1);
              Write32(0xE0042008, DbgMCU_CR2);
            </block>
          </sequence>
        </sequences>

        <!-- *************************  Device 'PY32F410xB'  ***************************** -->
        <device Dname="PY32F410xB">
          <compile header="Drivers/CMSIS/Device/PUYA/PY32F4xx/Include/py32f4xx.h" />
          <memory name="IROM1" access="rx" start="0x08000000" size="0x00020000" startup="1" default="1" />
          <memory name="IRAM1" access="rw" start="0x20000000" size="0x00004000" init="0" default="1" />
          <algorithm name="CMSIS/Flash/PY32F410xx_128.FLM" start="0x08000000" size="0x00020000" default="1" />
        </device>

      </subFamily>
    </family>
  </devices>

  <conditions>
    <condition id="PY32F4">
      <description>Puya PY32F4 Series devices</description>
      <require Dvendor="Puya:176" Dname="PY32F4*" />
    </condition>

    <condition id="PY32F403xB">
      <description>Puya PY32F403xB Series devices</description>
      <require Dvendor="Puya:176" Dname="PY32F403?B*" />
    </condition>

    <condition id="PY32F403xC">
      <description>Puya PY32F403xC Series devices</description>
      <require Dvendor="Puya:176" Dname="PY32F403?C*" />
    </condition>

    <condition id="PY32F403xD">
      <description>Puya PY32F403xD Series devices</description>
      <require Dvendor="Puya:176" Dname="PY32F403?D*" />
    </condition>

    <condition id="PY32F403">
      <description>Puya PY32F403 Series devices</description>
      <accept condition="PY32F403xB" />
      <accept condition="PY32F403xC" />
      <accept condition="PY32F403xD" />
    </condition>

    <condition id="PY32F410xB">
      <description>Puya PY32F410xB Series devices</description>
      <require Dvendor="Puya:176" Dname="PY32F410?B*" />
    </condition>

    <condition id="PY32F4_ARMCC">
      <description>filter for PY32F4 devices and the ARM compiler</description>
      <require condition="PY32F4" />
      <require Tcompiler="ARMCC" />
    </condition>

    <condition id="PY32F4 CMSIS">
      <description>Puya PY32F4 Device and CMSIS-CORE</description>
      <require condition="PY32F4" />
      <require Cclass="CMSIS" Cgroup="CORE" />
    </condition>

  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.6" condition="PY32F4 CMSIS">
      <description>System Startup for Puya PY32F4 Series</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
            #define RTE_DEVICE_STARTUP_PY32F4XX    /* Device Startup for PY32F4 */
      </RTE_Components_h>
      <files>
        <!--  include folder -->
        <file category="include" name="Drivers/CMSIS/Device/PUYA/PY32F4xx/Include/"/>
        <!-- common device header file -->
        <file category="header" name="Drivers/CMSIS/Device/PUYA/PY32F4xx/Include/py32f4xx.h"/>


        <file category="source" condition="PY32F4_ARMCC" name="Drivers/CMSIS/Device/PUYA/PY32F4xx/Source/Templates/arm/startup_py32f4xx.s" attr="config" version="0.0.6"/>

        <file category="source" name="Drivers/CMSIS/Device/PUYA/PY32F4xx/Source/Templates/system_py32f4xx.c" attr="config" version="0.0.6"/>
      </files>
    </component>
  </components>
</package>