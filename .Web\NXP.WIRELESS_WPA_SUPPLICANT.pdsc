<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>WIRELESS_WPA_SUPPLICANT</name>
  <vendor>NXP</vendor>
  <description>Software Pack for wireless.wpa_supplicant</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="3.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="2.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LWIP" vendor="NXP" version="3.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.wireless.wpa_supplicant.rtos.condition_id">
      <require condition="allOf.middleware.freertos-kernel, utility.debug_console, component.osa_free_rtos, middleware.wifi, middleware.lwip, middleware.mbedtls.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.freertos-kernel, utility.debug_console, component.osa_free_rtos, middleware.wifi, middleware.lwip, middleware.mbedtls.internal_condition">
      <require Cclass="RTOS" Cgroup="Core"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_free_rtos"/>
      <require Cclass="Wireless" Cgroup="NXP Wi-Fi" Csub="wifi"/>
      <require Cclass="Network" Cgroup="lwIP TCPIP Networking Stack" Csub="lwip"/>
      <require Cclass="Security" Cgroup="mbedTLS library" Csub="mbedtls"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Wireless" Cgroup="wpa_supplicant" Csub="rtos" Cversion="2.11.0" condition="middleware.wireless.wpa_supplicant.rtos.condition_id">
      <description>Wpa supplicant rtos</description>
      <Pre_Include_Global_h>
#ifndef USE_RTOS
#define USE_RTOS 1
#endif
#ifndef SDK_OS_FREE_RTOS
#define SDK_OS_FREE_RTOS 
#endif
#ifndef FSL_OSA_TASK_ENABLE
#define FSL_OSA_TASK_ENABLE 1
#endif
#ifndef PRINTF_ADVANCED_ENABLE
#define PRINTF_ADVANCED_ENABLE 1
#endif
#ifndef MBEDTLS_CONFIG_FILE
#define MBEDTLS_CONFIG_FILE "wpa_supp_mbedtls_config.h"
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/port/mbedtls/wpa_supp_mbedtls_config.h" projectpath="wpa_supplicant-rtos/port/mbedtls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/port/mbedtls/wpa_supp_els_pkc_mbedtls_config.h" projectpath="wpa_supplicant-rtos/port/mbedtls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/port/mbedtls/wpa_supp_ele_s400_mbedtls_config.h" projectpath="wpa_supplicant-rtos/port/mbedtls"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/wpa_common.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/ieee802_11_common.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/hw_features_common.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/wpa_ctrl.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/cli.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/driver_common.c" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/drivers.c" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/l2_packet/l2_packet.h" projectpath="wpa_supplicant-rtos/src/l2_packet"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/l2_packet/l2_packet_freertos.c" projectpath="wpa_supplicant-rtos/src/l2_packet"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/driver_freertos.c" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/base64.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/common.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/wpabuf.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/bitfield.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/os_freertos.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/crc32.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/ip_addr.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/block_alloc.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/dl_list.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/ap.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/autoscan.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/bgscan.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/bss.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/bssid_ignore.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/config.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/config_ssid.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/ctrl_iface.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/dpp_supplicant.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/driver_i.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/gas_query.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/hs20_supplicant.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/ibss_rsn.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/interworking.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/mesh.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/mesh_mpm.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/mesh_rsn.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/notify.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/offchannel.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/p2p_supplicant.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/scan.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/sme.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wifi_display.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wmm_ac.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wnm_sta.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wpas_glue.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wpas_kay.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wpa_supplicant_i.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wps_supplicant.h" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/binder/binder.h" projectpath="wpa_supplicant-rtos/wpa_supplicant/binder" path="middleware/wireless/wpa_supplicant-rtos"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/dbus/dbus_common.h" projectpath="wpa_supplicant-rtos/wpa_supplicant/dbus" path="middleware/wireless/wpa_supplicant-rtos"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/dbus/dbus_new.h" projectpath="wpa_supplicant-rtos/wpa_supplicant/dbus" path="middleware/wireless/wpa_supplicant-rtos"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/supp_main.h" projectpath="wpa_supplicant-rtos/freertos/src"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/supp_api.h" projectpath="wpa_supplicant-rtos/freertos/src"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/wpa_cli.h" projectpath="wpa_supplicant-rtos/freertos/src"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/wpa_cli.c" projectpath="wpa_supplicant-rtos/freertos/src"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/config.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/notify.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/bss.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/eap_register.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/op_classes.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/rrm.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wmm_ac.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/config_none.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/sme.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wpa_supplicant.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/events.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/bssid_ignore.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wpas_glue.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/scan.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/robust_av.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/ctrl_iface.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/ctrl_iface_udp.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/mbo.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wnm_sta.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="other" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/README" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/wpa_debug.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/supp_main.c" projectpath="wpa_supplicant-rtos/freertos/src"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/supp_api.c" projectpath="wpa_supplicant-rtos/freertos/src"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/ap.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_config.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_drv_ops.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_list.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_mlme.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/authsrv.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/beacon.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/bss_load.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/dfs.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/drv_callbacks.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/eap_user_db.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/hostapd.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/hw_features.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11_auth.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11_he.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11_ht.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11_shared.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11_vht.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_1x.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/neighbor_db.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/p2p_hostapd.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/pmksa_cache_auth.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/preauth_auth.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/rrm.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/sta_info.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/tkip_countermeasures.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/utils.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wmm.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ctrl_iface_ap.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/mbo_ap.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_ie.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_ft.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_glue.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_common.c" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_identity.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_methods.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_auth/eapol_auth_sm.c" projectpath="wpa_supplicant-rtos/src/eapol_auth"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-eax.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-encblock.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-ctr.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-cbc.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-siv.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-unwrap.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-wrap.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/des-internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/dh_groups.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/rc4.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/md4-internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/md5.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/md5-internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1-internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/fips_prf_internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha256-internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha256.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/tls_none.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/ms_funcs.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1-tlsprf.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1-prf.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha256-prf.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1-pbkdf2.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-omac1.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-internal-enc.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-internal-dec.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes-internal.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/wpa.c" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/preauth.c" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/pmksa_cache.c" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/wpa_ie.c" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/crypto_mbedtls2.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/tls_mbedtls2.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha256-kdf.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha384-kdf.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha512-kdf.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/sae.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dragonfly.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/p2p_supplicant.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/p2p_supplicant_sd.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_utils.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_parse.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_build.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_go_neg.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_sd.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_pd.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_invitation.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_dev_disc.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_group.c" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/gas.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/gas_query.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/offchannel.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/wps_supplicant.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/uuid.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_wsc.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_wsc_common.c" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wps_hostapd.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_common.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_attr_parse.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_attr_build.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_attr_process.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_dev_attr.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_enrollee.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_registrar.c" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_supp/eapol_supp_sm.c" projectpath="wpa_supplicant-rtos/src/eapol_supp"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_methods.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_tls.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_tls_common.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_peap.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_peap_common.c" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_ttls.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_mschapv2.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/chap.c" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/mschapv2.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_fast.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_fast_pac.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_gtc.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_fast_common.c" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/hostapd/hlr_auc_gw.c" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/hostapd/ctrl_iface.c" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/hostapd/eap_register.c" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/hostapd/config_file.h" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/hostapd/ctrl_iface.h" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/hostapd/eap_register.h" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="other" name="middleware/wireless/wpa_supplicant-rtos/hostapd/README" projectpath="wpa_supplicant-rtos/hostapd"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/wpa_ft.c" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wnm_ap.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/eth_p_oui.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_wsc.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_tls.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius_server.c" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius_das.c" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius_client.c" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius.c" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_sim_db.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_sim_common.c" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_ttls.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_tls_common.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_peap.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_mschapv2.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_md5.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_gtc.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_fast.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_sim.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_server_aka.c" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/acs.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/acs.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/eloop_freertos.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/milenage.c" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_sim.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_aka.c" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/dpp_supplicant.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/interworking.c" projectpath="wpa_supplicant-rtos/wpa_supplicant"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_auth.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_backup.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_crypto.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_pkex.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_reconfig.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_tcp.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/common/gas_server.c" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/utils/json.c" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/tls/asn1.c" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/dpp_hostapd.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/gas_query_ap.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="sourceC" name="middleware/wireless/wpa_supplicant-rtos/src/ap/gas_serv.c" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/accounting.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/airtime_policy.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_config.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_drv_ops.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_list.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ap_mlme.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/authsrv.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/beacon.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/bss_load.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ctrl_iface_ap.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/dfs.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/dhcp_snoop.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/dpp_hostapd.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/eth_p_oui.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/fils_hlp.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/gas_query_ap.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/gas_serv.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/hostapd.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/hs20.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/hw_features.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11_auth.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_11.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ieee802_1x.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/mbo_ap.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/ndisc_snoop.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/neighbor_db.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/p2p_hostapd.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/pmksa_cache_auth.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/preauth_auth.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/rrm.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/sta_info.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/taxonomy.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/tkip_countermeasures.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/vlan.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/vlan_init.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/vlan_util.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wmm.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wnm_ap.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_glue.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_ie.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_i.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wpa_auth_kay.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/wps_hostapd.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/ap/x_snoop.h" projectpath="wpa_supplicant-rtos/src/ap"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/brcm_vendor.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/cli.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/ctrl_iface_common.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/defs.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/dhcp.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/dpp_i.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/dragonfly.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/eapol_common.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/gas.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/gas_server.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/hw_features_common.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/ieee802_11_common.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/ieee802_11_defs.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/ieee802_1x_defs.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/linux_bridge.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/linux_vlan.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/ocv.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/privsep_commands.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/ptksa_cache.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/qca-vendor-attr.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/qca-vendor.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/sae.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/tnc.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/version.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/wpa_common.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/wpa_ctrl.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/common/wpa_helpers.h" projectpath="wpa_supplicant-rtos/src/common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/driver.h" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/driver_hostap.h" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/driver_freertos.h" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/rfkill.h" projectpath="wpa_supplicant-rtos/src/drivers"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_auth/eapol_auth_sm.h" projectpath="wpa_supplicant-rtos/src/eapol_auth"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_auth/eapol_auth_sm_i.h" projectpath="wpa_supplicant-rtos/src/eapol_auth"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_config.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_fast_pac.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_i.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_methods.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_proxy.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_teap_pac.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/eap_tls_common.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/ikev2.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/mschapv2.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/tncc.h" projectpath="wpa_supplicant-rtos/src/eap_peer"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_ctrl_aux.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_ctrl_defs.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_ctrl_iface.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_defs.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_group.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_iface.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_internal.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/fst/fst_session.h" projectpath="wpa_supplicant-rtos/src/fst"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/pae/ieee802_1x_cp.h" projectpath="wpa_supplicant-rtos/src/pae"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/pae/ieee802_1x_kay.h" projectpath="wpa_supplicant-rtos/src/pae"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/pae/ieee802_1x_kay_i.h" projectpath="wpa_supplicant-rtos/src/pae"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/pae/ieee802_1x_key.h" projectpath="wpa_supplicant-rtos/src/pae"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/pae/ieee802_1x_secy_ops.h" projectpath="wpa_supplicant-rtos/src/pae"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/pmksa_cache.h" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/preauth.h" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/wpa.h" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/wpa_ie.h" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/wpa_i.h" projectpath="wpa_supplicant-rtos/src/rsn_supp"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/base64.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/bitfield.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/block_alloc.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/browser.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/build_config.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/common.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/config.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/const_time.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/crc32.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/edit.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/eloop.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/ext_password.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/ext_password_i.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/http-utils.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/includes.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/ip_addr.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/json.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/module_tests.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/os.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/pcsc_funcs.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/platform.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/state_machine.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/trace.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/uuid.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/wpabuf.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/wpa_debug.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/utils/xml-utils.h" projectpath="wpa_supplicant-rtos/src/utils"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes_siv.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/aes_wrap.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/crypto.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/des_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/dh_group5.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/dh_groups.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/md5.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/md5_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/milenage.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/ms_funcs.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/random.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha1_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha256.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha256_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha384.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha384_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha512.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/sha512_i.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/tls.h" projectpath="wpa_supplicant-rtos/src/crypto"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/chap.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_defs.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_eke_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_fast_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_gpsk_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_ikev2_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_pax_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_peap_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_psk_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_pwd_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_sake_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_sim_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_teap_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_tlv_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_ttls.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/eap_wsc_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/ikev2_common.h" projectpath="wpa_supplicant-rtos/src/eap_common"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_supp/eapol_supp_sm.h" projectpath="wpa_supplicant-rtos/src/eapol_supp"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_i.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_methods.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_sim_db.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/eap_tls_common.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/ikev2.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/tncs.h" projectpath="wpa_supplicant-rtos/src/eap_server"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p.h" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/p2p_i.h" projectpath="wpa_supplicant-rtos/src/p2p"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius_client.h" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius_das.h" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius.h" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/radius/radius_server.h" projectpath="wpa_supplicant-rtos/src/radius"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/asn1.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/bignum.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/pkcs1.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/pkcs5.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/pkcs8.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/rsa.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_client.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_client_i.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_common.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_cred.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_record.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_server.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/tlsv1_server_i.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/tls/x509v3.h" projectpath="wpa_supplicant-rtos/src/tls"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/http_client.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/http.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/httpread.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/http_server.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/upnp_xml.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_attr_parse.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_defs.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_dev_attr.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_er.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_i.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_upnp.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="header" name="middleware/wireless/wpa_supplicant-rtos/src/wps/wps_upnp_i.h" projectpath="wpa_supplicant-rtos/src/wps"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/utils/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/common/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/crypto/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/ap/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/drivers/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/eap_common/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/eap_peer/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/eap_server/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_auth/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/eapol_supp/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/fst/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/l2_packet/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/p2p/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/pae/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/radius/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/rsn_supp/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/tls/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/src/wps/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/port/mbedtls/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/hostapd/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/wpa_supplicant/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/freertos/"/>
        <file category="include" name="middleware/wireless/wpa_supplicant-rtos/freertos/src/"/>
      </files>
    </component>
  </components>
</package>
