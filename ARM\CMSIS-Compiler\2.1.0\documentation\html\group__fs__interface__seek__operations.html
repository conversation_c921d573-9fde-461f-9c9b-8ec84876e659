<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: Seek Operations</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('group__fs__interface__seek__operations.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Seek Operations<div class="ingroups"><a class="el" href="group__fs__interface__api.html">File Interface</a> &raquo; <a class="el" href="group__fs__interface__definitions.html">Definitions</a></div></div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gaa7bff078b3ae17a1df038264fed6ccc2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__seek__operations.html#gaa7bff078b3ae17a1df038264fed6ccc2">RT_SEEK_SET</a>&#160;&#160;&#160;0</td></tr>
<tr class="memdesc:gaa7bff078b3ae17a1df038264fed6ccc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">File Seek Operation definitions.  <br /></td></tr>
<tr class="separator:gaa7bff078b3ae17a1df038264fed6ccc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67541e46c175dd98383193ddc05d2979"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__seek__operations.html#ga67541e46c175dd98383193ddc05d2979">RT_SEEK_CUR</a>&#160;&#160;&#160;1</td></tr>
<tr class="memdesc:ga67541e46c175dd98383193ddc05d2979"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set position offset from the current location.  <br /></td></tr>
<tr class="separator:ga67541e46c175dd98383193ddc05d2979"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga207a21487f526c0b6ada9904eb788a97"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__seek__operations.html#ga207a21487f526c0b6ada9904eb788a97">RT_SEEK_END</a>&#160;&#160;&#160;2</td></tr>
<tr class="memdesc:ga207a21487f526c0b6ada9904eb788a97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set position offset from the end of the file.  <br /></td></tr>
<tr class="separator:ga207a21487f526c0b6ada9904eb788a97"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gaa7bff078b3ae17a1df038264fed6ccc2" name="gaa7bff078b3ae17a1df038264fed6ccc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa7bff078b3ae17a1df038264fed6ccc2">&#9670;&#160;</a></span>RT_SEEK_SET</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_SEEK_SET&#160;&#160;&#160;0</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File Seek Operation definitions. </p>
<p>Set position offset from the start of the file </p>

</div>
</div>
<a id="ga67541e46c175dd98383193ddc05d2979" name="ga67541e46c175dd98383193ddc05d2979"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga67541e46c175dd98383193ddc05d2979">&#9670;&#160;</a></span>RT_SEEK_CUR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_SEEK_CUR&#160;&#160;&#160;1</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set position offset from the current location. </p>

</div>
</div>
<a id="ga207a21487f526c0b6ada9904eb788a97" name="ga207a21487f526c0b6ada9904eb788a97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga207a21487f526c0b6ada9904eb788a97">&#9670;&#160;</a></span>RT_SEEK_END</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_SEEK_END&#160;&#160;&#160;2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set position offset from the end of the file. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
