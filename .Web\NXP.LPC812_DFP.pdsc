<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPC812_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for LPC812</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-06-21">NXP CMSIS packs based on MCUXpresso SDK 2.4.2. Fixed missing flash algorithms for LPC8xx CMSIS packs.</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="LPC812" Dvendor="NXP:11">
      <description>Low-Cost Microcontrollers (MCUs) based on Arm Cortex-M0+ Core</description>
      <device Dname="LPC812">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="30000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/LPC812_flash.icf"/>
        </environment>
        <memory name="IAP_SRAM" start="0x10000fe0" size="0x20" access="rw" default="1"/>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x4000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x10000000" size="0x0fe0" access="rw" default="1"/>
        <algorithm name="arm/LPC8xx_16.FLM" start="0x00000000" size="0x00004000" RAMstart="0x10000000" RAMsize="0x00000fe0" default="1"/>
        <debug svd="LPC812.xml"/>
        <variant Dvariant="LPC812M101JDH20">
          <compile header="fsl_device_registers.h" define="CPU_LPC812M101JDH20"/>
        </variant>
        <variant Dvariant="LPC812M101JDH16">
          <compile header="fsl_device_registers.h" define="CPU_LPC812M101JDH16"/>
        </variant>
        <variant Dvariant="LPC812M101JD20">
          <compile header="fsl_device_registers.h" define="CPU_LPC812M101JD20"/>
        </variant>
        <variant Dvariant="LPC812M101JTB16">
          <compile header="fsl_device_registers.h" define="CPU_LPC812M101JTB16"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.LPC812">
      <accept Dname="LPC812" Dvariant="LPC812M101JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC812" Dvariant="LPC812M101JDH16" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JDH16" Dvendor="NXP:11"/>
      <accept Dname="LPC812" Dvariant="LPC812M101JD20" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JD20" Dvendor="NXP:11"/>
      <accept Dname="LPC812" Dvariant="LPC812M101JTB16" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JTB16" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC812_AND_component.miniusart_adapter_AND_device.LPC812_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_lite_AND_driver.lpc_miniusart_AND_driver.power_no_lib_AND_driver.swm_AND_utility.debug_console_lite">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="Startup" Csub="LPC812_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="swm"/>
    </condition>
    <condition id="device.LPC812_AND_component.osa_AND_driver.common">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="osa"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common_AND_driver.lpc_crc">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_crc"/>
    </condition>
    <condition id="device.LPC812_AND_component.lists_AND_driver.common">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common_AND_driver.lpc_miniusart">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common_AND_driver.mrt">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mrt"/>
    </condition>
    <condition id="device.LPC812_AND_component.lists_AND_component.mrt_adapter_AND_driver.common">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mrt_adapter"/>
    </condition>
    <condition id="device.LPC812_AND_CMSIS_Include_core_cm0plus">
      <require condition="device.LPC812"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="armclang_OR_iar">
      <accept Tcompiler="ARMCC" Toptions="AC6"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="device.LPC812_AND__armclang_OR_iar__AND_device.LPC812_system">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="LPC812_system"/>
      <require condition="armclang_OR_iar"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="device.LPC812_AND_device.LPC812_CMSIS">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="LPC812_header"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common_AND_driver.power_no_lib">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
    </condition>
    <condition id="device.LPC812_AND_device.LPC812_CMSIS_AND_driver.clock_AND_driver.reset">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="LPC812_header"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common_AND_driver.swm_connections">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="swm_connections"/>
    </condition>
    <condition id="device.LPC812_AND_driver.common_AND_driver.syscon_connections">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="syscon_connections"/>
    </condition>
    <condition id="device.LPC812_AND_component.miniusart_adapter_AND_utility.debug_console_lite">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="device.LPC812_AND_component.miniusart_adapter_AND_driver.common">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="LPC812" Cversion="1.0.0" condition="device.LPC812_AND_component.miniusart_adapter_AND_device.LPC812_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_lite_AND_driver.lpc_miniusart_AND_driver.power_no_lib_AND_driver.swm_AND_utility.debug_console_lite" isDefaultVariant="1">
      <description>Devices_project_template LPC812; {for-development:SDK-Manifest-ID: project_template.LPC812.LPC812}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common_task" Cversion="1.0.0" condition="device.LPC812_AND_component.osa_AND_driver.common">
      <description>Component common_task; {for-development:SDK-Manifest-ID: component.common_task.LPC812}</description>
      <files>
        <file category="header" name="components/common_task/fsl_component_common_task.h"/>
        <file category="sourceC" name="components/common_task/fsl_component_common_task.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.LPC812_AND_driver.common">
      <description>Component lists; {for-development:SDK-Manifest-ID: component.lists.LPC812}</description>
      <files>
        <file category="header" name="components/lists/fsl_component_generic_list.h"/>
        <file category="sourceC" name="components/lists/fsl_component_generic_list.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_crc_adapter" Cversion="1.0.0" condition="device.LPC812_AND_driver.common_AND_driver.lpc_crc">
      <description>Component lpc_crc_adapter; {for-development:SDK-Manifest-ID: component.lpc_crc_adapter.LPC812}</description>
      <files>
        <file category="header" name="components/crc/fsl_adapter_crc.h"/>
        <file category="sourceC" name="components/crc/fsl_adapter_lpc_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager" Cversion="1.0.0" condition="device.LPC812_AND_component.lists_AND_driver.common">
      <description>Component mem_manager; {for-development:SDK-Manifest-ID: component.mem_manager.LPC812}</description>
      <files>
        <file category="header" name="components/mem_manager/fsl_component_mem_manager.h"/>
        <file category="sourceC" name="components/mem_manager/fsl_component_mem_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager_light" Cversion="1.0.0" condition="device.LPC812_AND_component.lists_AND_driver.common">
      <description>Component mem_manager_light; {for-development:SDK-Manifest-ID: component.mem_manager_light.LPC812}</description>
      <files>
        <file category="header" name="components/mem_manager/fsl_component_mem_manager.h"/>
        <file category="sourceC" name="components/mem_manager/fsl_component_mem_manager_light.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter" Cversion="1.0.0" condition="device.LPC812_AND_driver.common_AND_driver.lpc_miniusart">
      <description>Component miniusart_adapter; {for-development:SDK-Manifest-ID: component.miniusart_adapter.LPC812}</description>
      <files>
        <file category="header" name="components/uart/fsl_adapter_uart.h"/>
        <file category="sourceC" name="components/uart/fsl_adapter_miniusart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mrt_adapter" Cversion="1.0.0" condition="device.LPC812_AND_driver.common_AND_driver.mrt">
      <description>Component mrt_adapter; {for-development:SDK-Manifest-ID: component.mrt_adapter.LPC812}</description>
      <files>
        <file category="header" name="components/timer/fsl_adapter_timer.h"/>
        <file category="sourceC" name="components/timer/fsl_adapter_mrt.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa" Cversion="1.0.0" condition="device.LPC812_AND_component.lists_AND_driver.common">
      <description>Component osa; {for-development:SDK-Manifest-ID: component.osa.LPC812}</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa_bm" Cversion="1.0.0" condition="device.LPC812_AND_component.lists_AND_driver.common">
      <description>Component osa_bm; {for-development:SDK-Manifest-ID: component.osa_bm.LPC812}</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_crc_adapter" Cversion="1.0.0" condition="device.LPC812_AND_driver.common">
      <description>Component software_crc_adapter; {for-development:SDK-Manifest-ID: component.software_crc_adapter.LPC812}</description>
      <files>
        <file category="header" name="components/crc/fsl_adapter_crc.h"/>
        <file category="sourceC" name="components/crc/fsl_adapter_software_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_rng_adapter" Cversion="1.0.0" condition="device.LPC812_AND_driver.common">
      <description>Component software_rng_adapter; {for-development:SDK-Manifest-ID: component.software_rng_adapter.LPC812}</description>
      <files>
        <file category="header" name="components/rng/fsl_adapter_rng.h"/>
        <file category="sourceC" name="components/rng/fsl_adapter_software_rng.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager" Cversion="1.0.0" condition="device.LPC812_AND_component.lists_AND_component.mrt_adapter_AND_driver.common">
      <description>Component timer_manager; {for-development:SDK-Manifest-ID: component.timer_manager.LPC812}</description>
      <files>
        <file category="header" name="components/timer_manager/fsl_component_timer_manager.h"/>
        <file category="sourceC" name="components/timer_manager/fsl_component_timer_manager.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="LPC812_header" Cversion="1.0.0" condition="device.LPC812_AND_CMSIS_Include_core_cm0plus">
      <description>Device LPC812_cmsis; {for-development:SDK-Manifest-ID: device.LPC812_CMSIS.LPC812}</description>
      <files>
        <file category="header" name="fsl_device_registers.h"/>
        <file category="header" name="LPC812.h"/>
        <file category="header" name="LPC812_features.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Csub="LPC812_startup" Cversion="1.1.0" condition="device.LPC812_AND__armclang_OR_iar__AND_device.LPC812_system">
      <description>Device LPC812_startup; {for-development:SDK-Manifest-ID: device.LPC812_startup.LPC812}</description>
      <files>
        <file condition="iar" category="sourceAsm" name="iar/startup_LPC812.s"/>
        <file condition="mdk" category="sourceAsm" name="arm/startup_LPC812.S"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/LPC812_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/LPC812_ram.scf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/LPC812_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/LPC812_ram.icf" version="1.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="LPC812_system" Cversion="1.0.0" condition="device.LPC812_AND_device.LPC812_CMSIS">
      <description>Device LPC812_system; {for-development:SDK-Manifest-ID: device.LPC812_system.LPC812}</description>
      <files>
        <file category="sourceC" name="system_LPC812.c"/>
        <file category="header" name="system_LPC812.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.1.0" condition="device.LPC812_AND_driver.common_AND_driver.power_no_lib">
      <description>Clock Driver; {for-development:SDK-Manifest-ID: platform.drivers.clock.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_clock.h"/>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.2.9" condition="device.LPC812_AND_device.LPC812_CMSIS_AND_driver.clock_AND_driver.reset">
      <description>COMMON Driver; {for-development:SDK-Manifest-ID: platform.drivers.common.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_common.h"/>
        <file category="sourceC" name="drivers/fsl_common.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="iap" Cversion="2.0.4" condition="device.LPC812_AND_driver.common">
      <description>IAP Driver; {for-development:SDK-Manifest-ID: platform.drivers.iap.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_iap.h"/>
        <file category="sourceC" name="drivers/fsl_iap.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_acomp" Cversion="2.1.0" condition="device.LPC812_AND_driver.common">
      <description>LPC_ACOMP Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_acomp.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_acomp.h"/>
        <file category="sourceC" name="drivers/fsl_acomp.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_crc" Cversion="2.1.1" condition="device.LPC812_AND_driver.common">
      <description>CRC Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_crc.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_crc.h"/>
        <file category="sourceC" name="drivers/fsl_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.1.7" condition="device.LPC812_AND_driver.common">
      <description>GPIO Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_gpio.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_gpio.h"/>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.1.0" condition="device.LPC812_AND_driver.common">
      <description>I2C Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_i2c.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_i2c.h"/>
        <file category="sourceC" name="drivers/fsl_i2c.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="iocon" Cversion="2.0.0" condition="device.LPC812_AND_driver.common">
      <description>IOCON Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_iocon_lite.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_iocon.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi" Cversion="2.0.4" condition="device.LPC812_AND_driver.common">
      <description>SPI Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_minispi.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_spi.h"/>
        <file category="sourceC" name="drivers/fsl_spi.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="usart" Cversion="2.3.0" condition="device.LPC812_AND_driver.common">
      <description>USART Driver; {for-development:SDK-Manifest-ID: platform.drivers.lpc_miniusart.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_usart.h"/>
        <file category="sourceC" name="drivers/fsl_usart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mrt" Cversion="2.0.3" condition="device.LPC812_AND_driver.common">
      <description>MRT Driver; {for-development:SDK-Manifest-ID: platform.drivers.mrt.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_mrt.h"/>
        <file category="sourceC" name="drivers/fsl_mrt.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pint" Cversion="2.1.8" condition="device.LPC812_AND_driver.common">
      <description>PINT Driver; {for-development:SDK-Manifest-ID: platform.drivers.pint.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_pint.h"/>
        <file category="sourceC" name="drivers/fsl_pint.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="power" Cversion="2.0.0" condition="device.LPC812_AND_driver.common">
      <description>Power Driver; {for-development:SDK-Manifest-ID: platform.drivers.power_no_lib.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_power.h"/>
        <file category="sourceC" name="drivers/fsl_power.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="reset" Cversion="2.0.1" condition="device.LPC812_AND_driver.common">
      <description>Reset Driver; {for-development:SDK-Manifest-ID: platform.drivers.reset.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_reset.h"/>
        <file category="sourceC" name="drivers/fsl_reset.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sctimer" Cversion="2.3.0" condition="device.LPC812_AND_driver.common">
      <description>SCT Driver; {for-development:SDK-Manifest-ID: platform.drivers.sctimer.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_sctimer.h"/>
        <file category="sourceC" name="drivers/fsl_sctimer.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="swm" Cversion="2.0.2" condition="device.LPC812_AND_driver.common_AND_driver.swm_connections">
      <description>SWM Driver; {for-development:SDK-Manifest-ID: platform.drivers.swm.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_swm.h"/>
        <file category="sourceC" name="drivers/fsl_swm.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="swm_connections" Cversion="2.0.1" condition="device.LPC812_AND_driver.common">
      <description>Swm_connections Driver; {for-development:SDK-Manifest-ID: platform.drivers.swm_connections.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_swm_connections.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="syscon" Cversion="2.0.1" condition="device.LPC812_AND_driver.common_AND_driver.syscon_connections">
      <description>SYSCON Driver; {for-development:SDK-Manifest-ID: platform.drivers.syscon.LPC812}</description>
      <files>
        <file category="sourceC" name="drivers/fsl_syscon.c"/>
        <file category="header" name="drivers/fsl_syscon.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="syscon_connections" Cversion="2.0.1" condition="device.LPC812_AND_driver.common">
      <description>Syscon_connections Driver; {for-development:SDK-Manifest-ID: platform.drivers.syscon_connections.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_syscon_connections.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wkt" Cversion="2.0.2" condition="device.LPC812_AND_driver.common">
      <description>WKT Driver; {for-development:SDK-Manifest-ID: platform.drivers.wkt.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_wkt.h"/>
        <file category="sourceC" name="drivers/fsl_wkt.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wwdt" Cversion="2.1.9" condition="device.LPC812_AND_driver.common">
      <description>WWDT Driver; {for-development:SDK-Manifest-ID: platform.drivers.wwdt.LPC812}</description>
      <files>
        <file category="header" name="drivers/fsl_wwdt.h"/>
        <file category="sourceC" name="drivers/fsl_wwdt.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite" Cversion="1.0.0" condition="device.LPC812_AND_component.miniusart_adapter_AND_utility.debug_console_lite">
      <description>Utility assert_lite; {for-development:SDK-Manifest-ID: platform.utilities.assert_lite.LPC812}</description>
      <files>
        <file category="sourceC" name="utilities/debug_console_lite/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite" Cversion="1.0.0" condition="device.LPC812_AND_component.miniusart_adapter_AND_driver.common">
      <description>Utility debug_console_lite; {for-development:SDK-Manifest-ID: utility.debug_console_lite.LPC812}</description>
      <files>
        <file category="sourceC" name="utilities/debug_console_lite/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console_lite/fsl_debug_console.h"/>
      </files>
    </component>
  </components>
</package>
