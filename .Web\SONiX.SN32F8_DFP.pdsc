<?xml version="1.0" encoding="utf-8"?>

<package schemaVersion="1.3" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SONiX</vendor>
  <url>https://liveupdate.sonix.com.tw/sonix/develop_tool/MCU/DFP/</url>
  <name>SN32F8_DFP</name>
  <description>SONiX SN32F8 Series Device Support and Examples</description>

  <releases>
	<release version="1.0.1" date="2022-08-30">
      Change liveupdate serve link to https://liveupdate.sonix.com.tw/sonix/develop_tool/MCU/DFP/
    </release>
    <release version="1.0.0" date="2021-11-17">
      First Release version of SN32F8 Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>SONiX</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package SONiX</keyword>
    <keyword>SN32F8</keyword>
    <keyword>SN32</keyword>
  </keywords>

  <devices>
    <!-- generated, do not modify this section! -->

    <family Dfamily="SN32F8 Series" Dvendor="SONiX:110">
      <processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
      <book    name="Documents/dui0497a_cortex_m0_r0p0_generic_ug.pdf" title="Cortex-M0 Generic User Guide"/>
        <description>
SN32F8 series 32-bit micro-controller is a new series of extremely Low Power Consumption and High Performance MCU powered by ARM Cortex M0 processor with Flash ROM architecture.
        </description>

      <!-- ************************  Subfamily 'SN32F800'  **************************** -->
      <subFamily DsubFamily="SN32F800">
      	<processor Dclock="60000000"/>
        <compile header="Device/Include/SN32F800.h"  define="SN32F800"/>
        <debug      svd="SVD/SN32F800.svd"/>
        <memory     id="IROM1"                      start="0x00000000"	size="0x8000"	startup="1"	default="1"/>
        <memory     id="IRAM1"                      start="0x20000000"  size="0x2000"	init   ="0"	default="1"/>
        <algorithm  name="Flash/SN32F800_32.FLM"    start="0x00000000"  size="0x8000"	RAMstart="0x20000000"	RAMsize="0x2000"	default="1"/>
        <feature type="VCC"           n="1.80"    m="5.50"/>
        <feature type="Timer"         n="4"       m="16"/>   
        <feature type="I2C"           n="2"/>
        <feature type="Other"         n="1"                           name="Boot Loader"/>
        <feature type="WDT"           n="1"/>
        <feature type="RTC"           n="1"/>
        <feature type="ExtInt"        n="4"/>
        <feature type="Other"         n="2"                           name="CMP"/>
        <feature type="Other"         n="1"                           name="CRC"/>
        
      <!-- *************************  Device 'SN32F805J'  ***************************** -->
      <device Dname="SN32F805J">
        <feature type="IOs"           n="30"/>
        <feature type="ADC"           n="12"       m="12"/>
        <feature type="PWM"           n="16"/>
        <feature type="UART"          n="2"/>
        <feature type="SPI"           n="1"/>
        <feature type="Other"         n="4"                           name="DAC"/>
        <feature type="QFN"           n="32"/>
      </device>
      
      <!-- *************************  Device 'SN32F805F'  ***************************** -->
      <device Dname="SN32F805F">
        <feature type="IOs"           n="30"/>
        <feature type="ADC"           n="12"       m="12"/>
        <feature type="PWM"           n="16"/>
        <feature type="UART"          n="2"/>
        <feature type="SPI"           n="1"/>
        <feature type="Other"         n="4"                           name="DAC"/>
        <feature type="QFP"           n="32"/>
      </device>
     </subFamily>
    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARM">
      <require Tcompiler="ARMCC"/>
    </condition>
    
    <!-- Device Conditions -->
    <condition id="SN32F800">
      <description>SONiX SN32F800 Series devices</description>
      <accept Dvendor="SONiX:110" Dname="SN32F80**"/>
    </condition>
    
    <!-- Device + CMSIS Conditions -->
    <condition id="SN32F800 CMSIS Device">
      <description>SONiX SN32F800 series Devices and CMSIS-Core</description>
      <require condition="SN32F800"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>


  <boards>
    <board vendor="SONiX" name="SN32F800 Starter Kit Rev1_0" revision="v1.0" salesContact="<EMAIL>" orderForm="http://www.sonix.com.tw/article-en-1019-4193#">
      <description>SONiX SN32F800 Starter Kit</description>
      <book category="schematic" name="Documents/SN32F800_STARTKIT_V1.0.pdf" title="Schematics"/>
      <mountedDevice    deviceIndex="0" Dvendor="SONiX:110" Dname="SN32F80*"/>
      <compatibleDevice deviceIndex="0" Dvendor="SONiX:110" Dfamily="SN32F800"/>
      <feature type="PWR"       n="5"              name="USB Powered"/>
      <feature type="Button"    n="3"              name="Push-buttons: User and Reset"/>
      <feature type="USB"       n="1"              name="USB device with USB Standard-B Connector"/>
      <feature type="RS232"     n="1"              name="DB9 Female Connector"/>
      <feature type="DIO"       n="41"             name="Digital IOs (shared with Analog IOs)"/>
      <feature type="LED"       n="2"              name="LEDs: One user, one PWR LED"/>
      <feature type="ConnOther" n="1"              name="JTAG Interface"/>
    </board>
  </boards>

  <examples>
    <example name="CMSIS-RTOS Blinky" doc="Abstract.txt" folder="Boards/SONiX/SN32F800 Starter Kit Rev1_0/RTX_Blinky">
      <description>CMSIS-RTOS based Blinky example</description>
      <board name="SN32F800 Starter Kit Rev1_0" vendor="SONiX" Dvendor="SONiX:110"/>
      <project>
        <environment name="uv" load="Blinky.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="CMSIS" Cgroup="CORE"/>
        <component Cclass="Device" Cgroup="Startup"/>
        <component Cclass="CMSIS" Cgroup="RTOS"/>
        <category>Getting Started</category>
        <category>CMSIS-RTX</category>
      </attributes>
    </example>
  </examples>

  <components>  
    <!-- Startup SN32F800 -->  
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.2" condition="SN32F800 CMSIS Device">
      <description>System Startup for SONiX SN32F800 Series</description>     
      <files>
        <file category="include"	name="Device/Include/"/>
        <file category="sourceAsm"	name="Device/Source/ARM/startup_SN32F800.s"	attr="config"	version="1.0.0"	condition="Compiler ARM"/>
        <file category="sourceC"	name="Device/Source/system_SN32F800.c"		attr="config"   version="1.0.2"/>   
      </files>
    </component>
  </components>
</package>
