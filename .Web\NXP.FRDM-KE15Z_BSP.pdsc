<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-KE15Z_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-KE15Z</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKE15Z7_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-KE15Z">
      <description>Freedom Development Board for Kinetis KE14 and KE15 MCUs</description>
      <image small="boards/frdmke15z/frdmke15z.png"/>
      <book category="overview" name="https://www.nxp.com/pip/FRDM-KE15Z" title="Freedom Development Board for Kinetis KE14 and KE15 MCUs" public="true"/>
      <mountedDevice Dname="MKE15Z256VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z128VLH7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z128VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z256VLH7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z256VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE15Z128VLH7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE15Z128VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE15Z256VLH7" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKE14Z7.internal_condition">
      <accept Dname="MKE14Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z128VLL7" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MKE15Z7.internal_condition">
      <accept Dname="MKE15Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z128VLL7" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MKE15Z256xxx7.internal_condition">
      <accept Dname="MKE15Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z256VLL7" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmke15z.condition_id">
      <require condition="allOf.board=frdmke15z, component.lpuart_adapter, device_id=MKE15Z256xxx7, device.MKE15Z7_startup, driver.common, driver.gpio, driver.lpi2c, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmke15z, component.lpuart_adapter, device_id=MKE15Z256xxx7, device.MKE15Z7_startup, driver.common, driver.gpio, driver.lpi2c, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.frdmke15z.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="device_id.MKE15Z256xxx7.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmke15z.internal_condition">
      <accept condition="device.MKE14Z7.internal_condition"/>
      <accept condition="device.MKE15Z7.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_interrupt" folder="boards/frdmke15z/driver_examples/acmp/interrupt" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/acmp_interrupt.ewp"/>
        <environment name="csolution" load="acmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling" folder="boards/frdmke15z/driver_examples/acmp/polling" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling.uvprojx"/>
        <environment name="iar" load="iar/acmp_polling.ewp"/>
        <environment name="csolution" load="acmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_round_robin" folder="boards/frdmke15z/driver_examples/acmp/round_robin" doc="readme.md">
      <description>The ACMP Round-Robin project is a simple demonstration program that uses the SDK software. Usermust set the round-robin mode trigger in specific board properly according to the board resourcebefore running the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_round_robin.uvprojx"/>
        <environment name="iar" load="iar/acmp_round_robin.ewp"/>
        <environment name="csolution" load="acmp_round_robin.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_continuous_edma" folder="boards/frdmke15z/driver_examples/adc12/continuous_edma" doc="readme.md">
      <description>The ADC12 continuous EDMA demo application demonstrates the usage of the ADC and EDMA peripheral while in a continuous mode. TheADC12 is first set to continuous mode. In continuous convert configuration, only the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_continuous_edma.uvprojx"/>
        <environment name="iar" load="iar/adc12_continuous_edma.ewp"/>
        <environment name="csolution" load="adc12_continuous_edma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_interrupt" folder="boards/frdmke15z/driver_examples/adc12/interrupt" doc="readme.md">
      <description>The adc12_interrupt example shows how to use interrupt with ADC12 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12's sample input.When...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc12_interrupt.ewp"/>
        <environment name="csolution" load="adc12_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_low_power" folder="boards/frdmke15z/demo_apps/adc12_low_power" doc="readme.md">
      <description>The ADC Low Power Demo project is a demonstration program that uses the KSDK software. Themicrocontroller is set to a very low power stop (VLPS) mode, and every 500 ms an interrupt wakes upthe ADC module and takes...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_low_power.uvprojx"/>
        <environment name="iar" load="iar/adc12_low_power.ewp"/>
        <environment name="csolution" load="adc12_low_power.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_low_power_peripheral" folder="boards/frdmke15z/demo_apps/adc12_low_power_peripheral" doc="readme.md">
      <description>The ADC Low Power Demo project is a demonstration program that uses the KSDK software. Themicrocontroller is set to a very low power stop (VLPS) mode, and every 500 ms an interrupt wakes upthe ADC module and takes...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_low_power_peripheral.uvprojx"/>
        <environment name="iar" load="iar/adc12_low_power_peripheral.ewp"/>
        <environment name="csolution" load="adc12_low_power_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_lpit_trgmux" folder="boards/frdmke15z/driver_examples/adc12/lpit_trgmux" doc="readme.md">
      <description>The adc12_lpit_trgmux example shows how to use the LPIT and TRGMUX to generate a ADC trigger.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_lpit_trgmux.uvprojx"/>
        <environment name="iar" load="iar/adc12_lpit_trgmux.ewp"/>
        <environment name="csolution" load="adc12_lpit_trgmux.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_polling" folder="boards/frdmke15z/driver_examples/adc12/polling" doc="readme.md">
      <description>The adc12_polling example shows the simplest way to use ADC12 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12's sample input.When running...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_polling.uvprojx"/>
        <environment name="iar" load="iar/adc12_polling.ewp"/>
        <environment name="csolution" load="adc12_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral" folder="boards/frdmke15z/demo_apps/bubble_peripheral" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis....See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral.ewp"/>
        <environment name="csolution" load="bubble_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master" folder="boards/frdmke15z/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave" folder="boards/frdmke15z/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master" folder="boards/frdmke15z/cmsis_driver_examples/lpi2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave" folder="boards/frdmke15z/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master" folder="boards/frdmke15z/cmsis_driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave" folder="boards/frdmke15z/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master" folder="boards/frdmke15z/cmsis_driver_examples/lpspi/int_b2b_transfer/master" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave" folder="boards/frdmke15z/cmsis_driver_examples/lpspi/int_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="boards/frdmke15z/cmsis_driver_examples/lpuart/edma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="boards/frdmke15z/cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/frdmke15z/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ecompass_peripheral" folder="boards/frdmke15z/demo_apps/ecompass_peripheral" doc="readme.md">
      <description>The E-Compass demo application demonstrates the use of the FXOS8700 sensor. The tilt-compensated algorithm calculatesall three angles (pitch, roll, and yaw or compass heading). You can open the mex file with...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecompass_peripheral.uvprojx"/>
        <environment name="iar" load="iar/ecompass_peripheral.ewp"/>
        <environment name="csolution" load="ecompass_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_channel_link" folder="boards/frdmke15z/driver_examples/edma/channel_link" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_channel_link.uvprojx"/>
        <environment name="iar" load="iar/edma_channel_link.ewp"/>
        <environment name="csolution" load="edma_channel_link.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_interleave_transfer" folder="boards/frdmke15z/driver_examples/edma/interleave_transfer" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma_interleave_transfer.ewp"/>
        <environment name="csolution" load="edma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="boards/frdmke15z/driver_examples/edma/memory_to_memory" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/edma_memory_to_memory.ewp"/>
        <environment name="csolution" load="edma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_ping_pong_transfer" folder="boards/frdmke15z/driver_examples/edma/ping_pong_transfer" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_ping_pong_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma_ping_pong_transfer.ewp"/>
        <environment name="csolution" load="edma_ping_pong_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="boards/frdmke15z/driver_examples/edma/scatter_gather" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" load="iar/edma_scatter_gather.ewp"/>
        <environment name="csolution" load="edma_scatter_gather.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_wrap_transfer" folder="boards/frdmke15z/driver_examples/edma/wrap_transfer" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma_wrap_transfer.ewp"/>
        <environment name="csolution" load="edma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm" folder="boards/frdmke15z/driver_examples/ewm" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm.uvprojx"/>
        <environment name="iar" load="iar/ewm.ewp"/>
        <environment name="csolution" load="ewm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer" folder="boards/frdmke15z/driver_examples/flexio/i2c/interrupt_lpi2c_transfer" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer" folder="boards/frdmke15z/driver_examples/flexio/i2c/read_accel_value_transfer" doc="readme.md">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_read_accel_value_transfer.ewp"/>
        <environment name="csolution" load="flexio_i2c_read_accel_value_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="boards/frdmke15z/driver_examples/flexio/pwm" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm.ewp"/>
        <environment name="csolution" load="flexio_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master" folder="boards/frdmke15z/driver_examples/flexio/spi/edma_lpspi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave" folder="boards/frdmke15z/driver_examples/flexio/spi/edma_lpspi_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master" folder="boards/frdmke15z/driver_examples/flexio/spi/int_lpspi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave" folder="boards/frdmke15z/driver_examples/flexio/spi/int_lpspi_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer" folder="boards/frdmke15z/driver_examples/flexio/uart/edma_transfer" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="boards/frdmke15z/driver_examples/flexio/uart/int_rb_transfer" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="boards/frdmke15z/driver_examples/flexio/uart/interrupt_transfer" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="boards/frdmke15z/driver_examples/flexio/uart/polling_transfer" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_dflash" folder="boards/frdmke15z/driver_examples/flash/flexnvm_dflash" doc="readme.md">
      <description>The flexnvm_dflash example shows how to use flash driver to operate data flash:</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_dflash.uvprojx"/>
        <environment name="iar" load="iar/flexnvm_dflash.ewp"/>
        <environment name="csolution" load="flexnvm_dflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_eeprom" folder="boards/frdmke15z/driver_examples/flash/flexnvm_eeprom" doc="readme.md">
      <description>The flexnvm_eeprom example shows how to use flash driver to operate eeprom:</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_eeprom.uvprojx"/>
        <environment name="iar" load="iar/flexnvm_eeprom.ewp"/>
        <environment name="csolution" load="flexnvm_eeprom.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_flexram" folder="boards/frdmke15z/driver_examples/flash/flexnvm_flexram" doc="readme.md">
      <description>The flexnvm_flexram example shows how to use flash driver to operate eeprom:</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_flexram.uvprojx"/>
        <environment name="iar" load="iar/flexnvm_flexram.ewp"/>
        <environment name="csolution" load="flexnvm_flexram.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_combine_pwm" folder="boards/frdmke15z/driver_examples/ftm/combine_pwm" doc="readme.md">
      <description>The FTM project is a demonstration program of generating a combined PWM signal by the SDK FTM driver. It sets up the FTMhardware block to output PWM signals on two TPM channels. The example also shows the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_combine_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_combine_pwm.ewp"/>
        <environment name="csolution" load="ftm_combine_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_dual_edge_capture" folder="boards/frdmke15z/driver_examples/ftm/dual_edge_capture" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a FTM channel-pair for dual-edge capture. Once the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_dual_edge_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_dual_edge_capture.ewp"/>
        <environment name="csolution" load="ftm_dual_edge_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_input_capture" folder="boards/frdmke15z/driver_examples/ftm/input_capture" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's input capture feature.The example sets up a FTM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_input_capture.ewp"/>
        <environment name="csolution" load="ftm_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_output_compare" folder="boards/frdmke15z/driver_examples/ftm/output_compare" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's output compare feature.It sets up one FTM channel to toggle the output when a match occurs with the channel value. The usershould probe the FTM...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/ftm_output_compare.ewp"/>
        <environment name="csolution" load="ftm_output_compare.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pdb_adc12" folder="boards/frdmke15z/demo_apps/ftm_pdb_adc12" doc="readme.md">
      <description>This application demonstrates how to use the trigger signal generated by FTM to trigger the ADCconversion through PDB module.For instance, the FTM0 is configured to work in PWM mode. And its channel 0 monitors the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pdb_adc12.uvprojx"/>
        <environment name="iar" load="iar/ftm_pdb_adc12.ewp"/>
        <environment name="csolution" load="ftm_pdb_adc12.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pwm_twochannel" folder="boards/frdmke15z/driver_examples/ftm/pwm_twochannel" doc="readme.md">
      <description>The FTM pwm two channel Example project is a demonstration program that uses the KSDK software to generate a square pulse PWM on 2 channel to control the LED brightness.- FTM generates a PWM with the increasing and...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/ftm_pwm_twochannel.ewp"/>
        <environment name="csolution" load="ftm_pwm_twochannel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_simple_pwm" folder="boards/frdmke15z/driver_examples/ftm/simple_pwm" doc="readme.md">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver. It sets up the FTMhardware block to output a center-aligned PWM signal. The PWM dutycycle is periodically updated.On boards that have an LED...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_simple_pwm.ewp"/>
        <environment name="csolution" load="ftm_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="boards/frdmke15z/driver_examples/ftm/timer" doc="readme.md">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" load="iar/ftm_timer.ewp"/>
        <environment name="csolution" load="ftm_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/frdmke15z/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/frdmke15z/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmke15z/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/frdmke15z/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master" folder="boards/frdmke15z/driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave" folder="boards/frdmke15z/driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt" folder="boards/frdmke15z/driver_examples/lpi2c/interrupt" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master" folder="boards/frdmke15z/driver_examples/lpi2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave" folder="boards/frdmke15z/driver_examples/lpi2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master" folder="boards/frdmke15z/driver_examples/lpi2c/polling_b2b/master" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave" folder="boards/frdmke15z/driver_examples/lpi2c/polling_b2b/slave" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_read_accel_value_transfer" folder="boards/frdmke15z/driver_examples/lpi2c/read_accel_value_transfer" doc="readme.md">
      <description>The lpi2c_read_accel_value example shows how to use LPI2C driver to communicate with an lpi2c device: 1. How to use the lpi2c driver to read a lpi2c device who_am_I register. 2. How to use the lpi2c driver to...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_read_accel_value_transfer.ewp"/>
        <environment name="csolution" load="lpi2c_read_accel_value_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_vlps" folder="boards/frdmke15z/demo_apps/lpi2c_vlps" doc="readme.md">
      <description>This example describes how to use kinetis SDK drivers to implement LPI2C transmit and receive in the VLPS (very low power STOP mode) with async DMA.The LPI2C module is designed to have ability to work under low power...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_vlps.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_vlps.ewp"/>
        <environment name="csolution" load="lpi2c_vlps.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_vlps_peripheral" folder="boards/frdmke15z/demo_apps/lpi2c_vlps_peripheral" doc="readme.md">
      <description>This example describes how to use kinetis SDK drivers to implement LPI2C transmit and receive in the VLPS (very low power STOP mode) with async DMA.The LPI2C module is designed to have ability to work under low power...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_vlps_peripheral.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_vlps_peripheral.ewp"/>
        <environment name="csolution" load="lpi2c_vlps_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_chained_channel" folder="boards/frdmke15z/driver_examples/lpit/chained_channel" doc="readme.md">
      <description>The LPIT chained channel project is a simple example of the SDK LPIT driver. It sets up the LPIT hardware block to trigger a periodic interrupt after every 1 second in the channel No.0, the channel No.1 chained with...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_chained_channel.uvprojx"/>
        <environment name="iar" load="iar/lpit_chained_channel.ewp"/>
        <environment name="csolution" load="lpit_chained_channel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_pwm" folder="boards/frdmke15z/driver_examples/lpit/lpit_pwm" doc="readme.md">
      <description>This example show how to use SDK drivers to implement the PWM feature by LPIT IP module in dualperiodcounter mode.You can set up PWM singal frequency and duty in this example.Connect PWM singal output pin to oscilloscope, you will see PWM wave.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_pwm.uvprojx"/>
        <environment name="iar" load="iar/lpit_pwm.ewp"/>
        <environment name="csolution" load="lpit_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_single_channel" folder="boards/frdmke15z/driver_examples/lpit/single_channel" doc="readme.md">
      <description>The LPIT single channel project is a simple example of the SDK LPIT driver. It sets up the LPIThardware block to trigger a periodic interrupt after every 1 second. When the LPIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_single_channel.uvprojx"/>
        <environment name="iar" load="iar/lpit_single_channel.ewp"/>
        <environment name="csolution" load="lpit_single_channel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_3wire_edma_transfer_master" folder="boards/frdmke15z/driver_examples/lpspi/3wire_transfer/edma_transfer/master" doc="readme.md">
      <description>The lpspi_3wire_edma_transfer example shows how to use LPSPI's 3-wire mode in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_3wire_edma_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_3wire_edma_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_3wire_edma_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_3wire_edma_transfer_slave" folder="boards/frdmke15z/driver_examples/lpspi/3wire_transfer/edma_transfer/slave" doc="readme.md">
      <description>The lpspi_3wire_edma_transfer example shows how to use LPSPI's 3-wire mode in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_3wire_edma_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_3wire_edma_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_3wire_edma_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_3wire_int_transfer_master" folder="boards/frdmke15z/driver_examples/lpspi/3wire_transfer/int_transfer/master" doc="readme.md">
      <description>The lpspi_3wire_int_transfer example shows how to use LPSPI's 3-wire mode in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_3wire_int_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_3wire_int_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_3wire_int_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_3wire_int_transfer_slave" folder="boards/frdmke15z/driver_examples/lpspi/3wire_transfer/int_transfer/slave" doc="readme.md">
      <description>The lpspi_3wire_int_transfer example shows how to use LPSPI's 3-wire mode in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_3wire_int_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_3wire_int_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_3wire_int_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_3wire_polling_transfer_master" folder="boards/frdmke15z/driver_examples/lpspi/3wire_transfer/polling_transfer/master" doc="readme.md">
      <description>The lpspi_3wire_polling_transfer example shows how to use LPSPI's 3-wire mode in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_3wire_polling_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_3wire_polling_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_3wire_polling_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_3wire_polling_transfer_slave" folder="boards/frdmke15z/driver_examples/lpspi/3wire_transfer/polling_transfer/slave" doc="readme.md">
      <description>The lpspi_3wire_polling_transfer example shows how to use LPSPI's 3-wire mode in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_3wire_polling_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_3wire_polling_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_3wire_polling_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master" folder="boards/frdmke15z/driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave" folder="boards/frdmke15z/driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt" folder="boards/frdmke15z/driver_examples/lpspi/interrupt" doc="readme.md">
      <description>The lpspi_functional_interrupt example shows how to use LPSPI driver in interrupt way:In this example , one lpspi instance used as LPSPI master and another lpspi instance used as LPSPI slave .1. LPSPI master...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt.ewp"/>
        <environment name="csolution" load="lpspi_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master" folder="boards/frdmke15z/driver_examples/lpspi/interrupt_b2b/master" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave" folder="boards/frdmke15z/driver_examples/lpspi/interrupt_b2b/slave" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master" folder="boards/frdmke15z/driver_examples/lpspi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave" folder="boards/frdmke15z/driver_examples/lpspi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master" folder="boards/frdmke15z/driver_examples/lpspi/polling_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave" folder="boards/frdmke15z/driver_examples/lpspi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="boards/frdmke15z/driver_examples/lptmr" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
        <environment name="csolution" load="lptmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_interrupt_transfer" folder="boards/frdmke15z/driver_examples/lpuart/9bit_interrupt_transfer" doc="readme.md">
      <description>The lpuart_9bit_interrupt_transfer example shows how to use lpuart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_rb_transfer" folder="boards/frdmke15z/driver_examples/lpuart/edma_rb_transfer" doc="readme.md">
      <description>The lpuart_edma Ring Buffer Example project is to demonstrate usage of the KSDK lpuart driver.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_edma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="boards/frdmke15z/driver_examples/lpuart/edma_transfer" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/frdmke15z/driver_examples/lpuart/interrupt" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/frdmke15z/driver_examples/lpuart/interrupt_rb_transfer" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/frdmke15z/driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits" folder="boards/frdmke15z/driver_examples/lpuart/interrupt_transfer_seven_bits" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/frdmke15z/driver_examples/lpuart/polling" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits" folder="boards/frdmke15z/driver_examples/lpuart/polling_seven_bits" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mmdvsq" folder="boards/frdmke15z/driver_examples/mmdvsq" doc="readme.md">
      <description>The MMDVSQ Example project is a demonstration program that uses the KSDK software to Calculation square root and QuotientMMDVSQ Peripheral Driver ExampleStart MMDVSQ ExampleCalculation square root, please enter...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mmdvsq.uvprojx"/>
        <environment name="iar" load="iar/mmdvsq.ewp"/>
        <environment name="csolution" load="mmdvsq.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/frdmke15z/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="iar" load="iar/new_project.ewp"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_adc12_trigger" folder="boards/frdmke15z/driver_examples/pdb/adc12_trigger" doc="readme.md">
      <description>The pdb_adc12_trigger example shows how to use the PDB to generate a ADC trigger.Based on the basic counter, to use the ADC trigger, just to enable the ADC trigger's "milestone" and set the user-defined value for...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_adc12_trigger.uvprojx"/>
        <environment name="iar" load="iar/pdb_adc12_trigger.ewp"/>
        <environment name="csolution" load="pdb_adc12_trigger.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_delay_interrupt" folder="boards/frdmke15z/driver_examples/pdb/delay_interrupt" doc="readme.md">
      <description>The pdb_delay_interrupt example show how to use the PDB as a general programmable interrupt timer.The PDB is triggered by software, and other external triggers are generated from PDB in this project,so that user can...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_delay_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pdb_delay_interrupt.ewp"/>
        <environment name="csolution" load="pdb_delay_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="boards/frdmke15z/driver_examples/flash/pflash" doc="readme.md">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" load="iar/pflash.ewp"/>
        <environment name="csolution" load="pflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="boards/frdmke15z/demo_apps/power_manager" doc="readme.md">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" load="iar/power_manager.ewp"/>
        <environment name="csolution" load="power_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="boards/frdmke15z/demo_apps/power_mode_switch" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="csolution" load="power_mode_switch.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwt_example" folder="boards/frdmke15z/driver_examples/pwt" doc="readme.md">
      <description>The PWT project is a simple demonstration program of the SDK PWT driver. It sets up the PWThardware block to edge detection, capture control part and detects measurement trigger edges andcontrols when and which pulse...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwt_example.uvprojx"/>
        <environment name="iar" load="iar/pwt_example.ewp"/>
        <environment name="csolution" load="pwt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="boards/frdmke15z/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" load="iar/rtc.ewp"/>
        <environment name="csolution" load="rtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="boards/frdmke15z/demo_apps/rtc_func" doc="readme.md">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar + Get the current date time with Year, Month,...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="csolution" load="rtc_func.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func_peripheral" folder="boards/frdmke15z/demo_apps/rtc_func_peripheral" doc="readme.md">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar + Get the current date time with Year, Month,...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func_peripheral.uvprojx"/>
        <environment name="iar" load="iar/rtc_func_peripheral.ewp"/>
        <environment name="csolution" load="rtc_func_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="scg" folder="boards/frdmke15z/driver_examples/scg" doc="readme.md">
      <description>The SCG example shows how to use SCG driver: 1. How to setup the SCG clock source. 2. How to use SCG clock while power mode switch. 3. How to use SCG APIs to get clock frequency.This example prints the clock...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/scg.uvprojx"/>
        <environment name="iar" load="iar/scg.ewp"/>
        <environment name="csolution" load="scg.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/frdmke15z/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="trgmux_clone_pwm" folder="boards/frdmke15z/driver_examples/trgmux/clone_pwm" doc="readme.md">
      <description>The TRGMUX project is a simple demonstration program of the SDK TRGMUX driver. It generatesa 10KHz PWM by LPIT0 CH0, and clone by TRGMUX to output this PWM to eight TRGMUX_OUTx pins.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/trgmux_clone_pwm.uvprojx"/>
        <environment name="iar" load="iar/trgmux_clone_pwm.ewp"/>
        <environment name="csolution" load="trgmux_clone_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v5_mutual_cap" folder="boards/frdmke15z/driver_examples/tsi_v5/mutual_cap" doc="readme.md">
      <description>The tsi_v5_mutual_cap example shows how to use TSI_V5 driver in mutual-cap mode:In this example , we make use of the available electrodes on board to show driver usage.1. Firstly, we get the non-touch calibration...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v5_mutual_cap.uvprojx"/>
        <environment name="iar" load="iar/tsi_v5_mutual_cap.ewp"/>
        <environment name="csolution" load="tsi_v5_mutual_cap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v5_mutualmode" folder="boards/frdmke15z/demo_apps/tsi_v5/tsi_v5_mutualmode" doc="readme.md">
      <description>The tsi_v5_mutualmode demo shows how to use TSI_V5 driver in mutual-cap mode:In this example , available electrodes on FRDM-TOUCH board are used to show how to realize touch key.1. Firstly, get the non-touch...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v5_mutualmode.uvprojx"/>
        <environment name="csolution" load="tsi_v5_mutualmode.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v5_self_cap" folder="boards/frdmke15z/driver_examples/tsi_v5/self_cap" doc="readme.md">
      <description>The tsi_v5_self_cap example shows how to use TSI_V5 driver in self-cap mode:In this example , we make use of the available electrodes on board to show driver usage.1. Firstly, we get the non-touch calibration results...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v5_self_cap.uvprojx"/>
        <environment name="iar" load="iar/tsi_v5_self_cap.ewp"/>
        <environment name="csolution" load="tsi_v5_self_cap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v5_selfmode" folder="boards/frdmke15z/demo_apps/tsi_v5/tsi_v5_selfmode" doc="readme.md">
      <description>The tsi_v5_selfmode demo shows how to use TSI_V5 driver in self-cap mode:In this example , available electrodes on board is used to show how to realize touch key.1. Firstly, get the non-touch calibration results as...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v5_selfmode.uvprojx"/>
        <environment name="csolution" load="tsi_v5_selfmode.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog32" folder="boards/frdmke15z/driver_examples/wdog32" doc="readme.md">
      <description>The WDOG32 Example project is to demonstrate usage of the KSDK wdog32 driver.In this example, fast testing is first implemented to test the wdog32.After this, refreshing the watchdog in None-window mode and window...See more details in readme document.</description>
      <board name="FRDM-KE15Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog32.uvprojx"/>
        <environment name="iar" load="iar/wdog32.ewp"/>
        <environment name="csolution" load="wdog32.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmke15z" Cversion="1.0.0" condition="BOARD_Project_Template.frdmke15z.condition_id">
      <description>Board_project_template frdmke15z</description>
      <files>
        <file category="header" attr="config" name="boards/frdmke15z/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmke15z/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmke15z/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmke15z/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmke15z/project_template/"/>
      </files>
    </component>
  </components>
</package>
