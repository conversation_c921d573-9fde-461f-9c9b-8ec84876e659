<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.7.26" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.26/schema/PACK.xsd">
  <vendor>YTMicro</vendor>
  <name>YTM32B1HA0_DFP</name>
  <description>Device Family Pack for YTM32B1HA0</description>
  <url>https://doc.ytmicro.com/keil/</url>

  <releases>
    <release version="1.3.1" date="2024-07-05">
      Updated head and svd file.
    </release>
    <release version="1.2.0" date="2024-03-12">
      Updated head and svd file.
    </release>
    <release version="1.0.1" date="2023-09-21">
      Updated devices.
    </release>
    <release version="1.0.0" date="2023-09-01">
      Initial release.
    </release>
  </releases>

  <keywords>
    <keyword>YTMicro</keyword>
    <keyword>YTM32B1HA0</keyword>
    <keyword>Device Family Pack</keyword>
  </keywords>

  <devices>
    <family Dfamily="YTM32Bx" Dvendor="YTMicro:180">

      <!-- ******************************  Subfamily 'YTM32B1HA0'  ****************************** -->
      <subFamily DsubFamily="YTM32B1HA0">
        <processor Dcore="Cortex-M7" DcoreVersion="r0p4" Dfpu="SP_FPU" Dmpu="MPU" Dtz="NO_TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="200000000" /> 
        <compile header="Device/Include/YTM32B1HA0.h"/>
        <debug svd="SVD/YTM32B1HA0.svd"/>
        <algorithm name="Flash/YTM32B1HA0_Main.FLM" start="0x02000000" size="0x00200000" default="1"/>
        <algorithm name="Flash/YTM32B1HA0_Dflash.FLM" start="0x06000000" size="0x00040000" default="0"/>
        <memory name="ROM" access="rx" start="0x01000000" size="0x10000" default="1"/>
        <memory name="ITCM" access="rwx" start="0x00000000" size="0x8000" default="1"/>
        <memory name="DTCM" access="rwx" start="0x20000000" size="0x20000" default="1"/>
        <memory name="OCRAM1" access="rwx" start="0x20020000" size="0x20000" default="1"/>
        <memory name="OCRAM2" access="rwx" start="0x20040000" size="0x20000" default="1"/>
        <description>YTM32B1HA0 200MHz MCU based on ARM Cortex-M7 Core </description>

        <device Dname="YTM32B1HA01">
          <description>Includes LQFP176/144/100 packages. 2MB PFlash, 256KB DFlash, 32KB ITCM, 128KB DTCM, 256KB OCRAM</description>
        </device>
      </subFamily>
    
    </family>
  </devices>

  <taxonomy>
    <description Cclass="Device" Cgroup="Startup">YTMicro SDK Startup</description>
    <description Cclass="Device" Cgroup="SDK Drivers">YTMicro SDK Peripheral Drivers</description>
  </taxonomy>

  <conditions>
    <condition id="YTM32B1HA0">
      <description>YTMicro YTM32B1HA0x device</description>
      <require Dvendor="YTMicro:180" Dname="YTM32B1HA0*"/>
    </condition>

    <!-- Device + Compiler Conditions (GCC) -->
    <condition id="YTM32B1H_GCC">
      <description>YTMicro device and GCC compiler</description>
      <require Tcompiler="GCC"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="YTM32B1HA0">
      <description>Startup for MCU</description>
      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/startup.c" attr="config" version="1.0.1"/>
        <file category="source"  name="Device/Source/system_YTM32B1HA0.c" attr="config" version="1.0.1"/>
        <file category="source"  condition="YTM32B1H_GCC" name="Device/Source/YTM32B1HA0_startup_gcc.S" attr="config" version="1.0.1"/>
      </files>
    </component>
  </components>
</package>
