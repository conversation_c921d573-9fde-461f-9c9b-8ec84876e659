<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>USB_PD</name>
  <vendor>NXP</vendor>
  <description>Software Pack for usb_pd</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="3.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="2.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.usb_pd.condition_id">
      <require condition="allOf.middleware.usb_pd.phy.ptn5110.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb_pd.phy.ptn5110.internal_condition">
      <require Cclass="USB" Cgroup="USB PD PHY" Csub="ptn5110"/>
    </condition>
    <condition id="middleware.usb_pd.altmode.condition_id">
      <require condition="allOf.middleware.usb_pd.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb_pd.internal_condition">
      <require Cclass="USB" Cgroup="USB Type-C PD Stack" Csub="usb_pd"/>
    </condition>
    <condition id="middleware.usb_pd.common_header.condition_id">
      <require condition="allOf.component.osa.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
    </condition>
    <condition id="middleware.usb_pd.phy.ptn5110.condition_id">
      <require condition="allOf.component.osa, middleware.usb_pd.config_header, middleware.usb_pd.common_header, anyOf=allOf=component.rgpio_adapter, component.lpi2c_adapter, allOf=component.gpio_adapter, component.lpi2c_adapter, allOf=component.lpc_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.igpio_adapter, component.lpi2c_adapter, allOf=component.rt_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.gpio_adapter, component.i2c_adapter.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa, middleware.usb_pd.config_header, middleware.usb_pd.common_header, anyOf=allOf=component.rgpio_adapter, component.lpi2c_adapter, allOf=component.gpio_adapter, component.lpi2c_adapter, allOf=component.lpc_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.igpio_adapter, component.lpi2c_adapter, allOf=component.rt_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.gpio_adapter, component.i2c_adapter.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa"/>
      <require Cclass="USB" Cgroup="USB Type-C PD Stack" Csub="config_header"/>
      <require Cclass="USB" Cgroup="USB PD Common Header" Csub="common_header"/>
      <require condition="anyOf.allOf=component.rgpio_adapter, component.lpi2c_adapter, allOf=component.gpio_adapter, component.lpi2c_adapter, allOf=component.lpc_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.igpio_adapter, component.lpi2c_adapter, allOf=component.rt_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.gpio_adapter, component.i2c_adapter.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.rgpio_adapter, component.lpi2c_adapter, allOf=component.gpio_adapter, component.lpi2c_adapter, allOf=component.lpc_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.igpio_adapter, component.lpi2c_adapter, allOf=component.rt_gpio_adapter, component.flexcomm_i2c_adapter, allOf=component.gpio_adapter, component.i2c_adapter.internal_condition">
      <accept condition="allOf.component.rgpio_adapter, component.lpi2c_adapter.internal_condition"/>
      <accept condition="allOf.component.gpio_adapter, component.lpi2c_adapter.internal_condition"/>
      <accept condition="allOf.component.lpc_gpio_adapter, component.flexcomm_i2c_adapter.internal_condition"/>
      <accept condition="allOf.component.igpio_adapter, component.lpi2c_adapter.internal_condition"/>
      <accept condition="allOf.component.rt_gpio_adapter, component.flexcomm_i2c_adapter.internal_condition"/>
      <accept condition="allOf.component.gpio_adapter, component.i2c_adapter.internal_condition"/>
    </condition>
    <condition id="allOf.component.rgpio_adapter, component.lpi2c_adapter.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rgpio_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpi2c_adapter"/>
    </condition>
    <condition id="allOf.component.gpio_adapter, component.lpi2c_adapter.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpi2c_adapter"/>
    </condition>
    <condition id="allOf.component.lpc_gpio_adapter, component.flexcomm_i2c_adapter.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_gpio_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexcomm_i2c_adapter"/>
    </condition>
    <condition id="allOf.component.igpio_adapter, component.lpi2c_adapter.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="igpio_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpi2c_adapter"/>
    </condition>
    <condition id="allOf.component.rt_gpio_adapter, component.flexcomm_i2c_adapter.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rt_gpio_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexcomm_i2c_adapter"/>
    </condition>
    <condition id="allOf.component.gpio_adapter, component.i2c_adapter.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_adapter"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="USB" Cgroup="USB Type-C PD Stack" Csub="usb_pd" Cversion="2.10.0" condition="middleware.usb_pd.condition_id">
      <description>Middleware usb_pd</description>
      <files>
        <file category="header" name="middleware/usb/pd/usb_pd.h" projectpath="usb/pd"/>
        <file category="sourceC" name="middleware/usb/pd/usb_pd_connect.c" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_interface.h" projectpath="usb/pd"/>
        <file category="sourceC" name="middleware/usb/pd/usb_pd_interface.c" projectpath="usb/pd"/>
        <file category="sourceC" name="middleware/usb/pd/usb_pd_msg.c" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_phy.h" projectpath="usb/pd"/>
        <file category="sourceC" name="middleware/usb/pd/usb_pd_policy.c" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_spec.h" projectpath="usb/pd"/>
        <file category="sourceC" name="middleware/usb/pd/usb_pd_timer.c" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_timer.h" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_auto_policy.h" projectpath="usb/pd"/>
        <file category="include" name="middleware/usb/pd/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Type-C PD Stack" Csub="altmode" Cversion="2.10.0" condition="middleware.usb_pd.altmode.condition_id">
      <description>Middleware usb_pd altmode</description>
      <files>
        <file category="sourceC" name="middleware/usb/pd/alt_mode/usb_pd_alt_mode.c" projectpath="usb/pd/altmode"/>
        <file category="header" name="middleware/usb/pd/alt_mode/usb_pd_alt_mode.h" projectpath="usb/pd/altmode"/>
        <file category="sourceC" name="middleware/usb/pd/alt_mode/usb_pd_alt_mode_dp.c" projectpath="usb/pd/altmode"/>
        <file category="header" name="middleware/usb/pd/alt_mode/usb_pd_alt_mode_dp.h" projectpath="usb/pd/altmode"/>
        <file category="include" name="middleware/usb/pd/alt_mode/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB PD Common Header" Csub="common_header" Cversion="2.10.0" condition="middleware.usb_pd.common_header.condition_id">
      <description>Middleware usb pd common_header</description>
      <files>
        <file category="header" name="middleware/usb/include/usb.h" projectpath="usb/include"/>
        <file category="header" name="middleware/usb/include/usb_misc.h" projectpath="usb/include"/>
        <file category="header" name="middleware/usb/include/usb_spec.h" projectpath="usb/include"/>
        <file category="include" name="middleware/usb/include/"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Type-C PD Stack" Csub="compliance_test_report" Cversion="2.10.0">
      <description>Middleware usb_pd compliance_test_report</description>
      <files>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/consumer_provider/USB Compliance Report.html" projectpath="."/>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/drp/USB Compliance Report.html" projectpath="."/>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/drp_try_snk/USB Compliance Report.html" projectpath="."/>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/drp_try_src/USB Compliance Report.html" projectpath="."/>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/provider_consumer/USB Compliance Report.html" projectpath="."/>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/displayport_dock/USB Compliance Report.html" projectpath="."/>
        <file category="doc" name="middleware/usb/pd/compliance_test_report/ellisys/displayport_host/USB Compliance Report.html" projectpath="."/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB Type-C PD Stack" Csub="config_header" Cversion="2.10.0">
      <description>USB Type-C PD Stack</description>
      <files>
        <file category="header" attr="config" name="middleware/usb/output/npw/pd_config/usb_pd_config.h" version="2.10.0" projectpath="source/generated"/>
      </files>
    </component>
    <component Cclass="USB" Cgroup="USB PD PHY" Csub="ptn5110" Cversion="2.10.0" condition="middleware.usb_pd.phy.ptn5110.condition_id">
      <description>Middleware usb_pd phy ptn5110</description>
      <files>
        <file category="header" name="middleware/usb/pd/ptn5110/usb_pd_ptn5110.h" projectpath="usb/ptn5110"/>
        <file category="sourceC" name="middleware/usb/pd/ptn5110/usb_pd_ptn5110_connect.c" projectpath="usb/ptn5110"/>
        <file category="sourceC" name="middleware/usb/pd/ptn5110/usb_pd_ptn5110_hal.c" projectpath="usb/ptn5110"/>
        <file category="sourceC" name="middleware/usb/pd/ptn5110/usb_pd_ptn5110_interface.c" projectpath="usb/ptn5110"/>
        <file category="sourceC" name="middleware/usb/pd/ptn5110/usb_pd_ptn5110_msg.c" projectpath="usb/ptn5110"/>
        <file category="header" name="middleware/usb/pd/ptn5110/usb_pd_ptn5110_register.h" projectpath="usb/ptn5110"/>
        <file category="header" name="middleware/usb/pd/usb_pd.h" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_interface.h" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_phy.h" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_spec.h" projectpath="usb/pd"/>
        <file category="sourceC" name="middleware/usb/pd/usb_pd_timer.c" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/usb_pd_timer.h" projectpath="usb/pd"/>
        <file category="header" name="middleware/usb/pd/phy_interface/usb_pd_i2c.h" projectpath="usb/phy_interface"/>
        <file category="sourceC" name="middleware/usb/pd/phy_interface/usb_pd_i2c.c" projectpath="usb/phy_interface"/>
        <file category="include" name="middleware/usb/pd/ptn5110/"/>
        <file category="include" name="middleware/usb/pd/"/>
        <file category="include" name="middleware/usb/pd/phy_interface/"/>
      </files>
    </component>
  </components>
</package>
