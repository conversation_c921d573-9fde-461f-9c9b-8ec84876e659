<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MCX-N5XX-EVK_MBEDTLS_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware mbedtls Examples Pack for MCX-N5XX-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCX-N5XX-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MCXN547_DFP" vendor="NXP" version="19.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="mbedtls_benchmark" folder="boards/mcxn5xxevk/mbedtls_examples/mbedtls_benchmark/cm33_core0" doc="readme.md">
      <description>The mbdedTLS Benchmark demo application performs cryptographic algorithm benchmarking and prints results to the terminal.</description>
      <board name="MCX-N5XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_benchmark.uvprojx"/>
        <environment name="csolution" load="mbedtls_benchmark.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mbedtls_selftest" folder="boards/mcxn5xxevk/mbedtls_examples/mbedtls_selftest/cm33_core0" doc="readme.md">
      <description>The mbdedTLS SelfTest program performs cryptographic algorithm testing and prints results to the terminal.</description>
      <board name="MCX-N5XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mbedtls_selftest.uvprojx"/>
        <environment name="csolution" load="mbedtls_selftest.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
