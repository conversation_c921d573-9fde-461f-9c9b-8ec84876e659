<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EFM32LG_DFP</name>
  <description>Silicon Labs EFM32LG Leopard Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EFM32LG_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EFM32LG_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFM32LG</keyword>
    <keyword>EFM32</keyword>
    <keyword>Leopard Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFM32LG Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M3" DcoreVersion="r2p1" Dfpu="0" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="48000000"/>
      <book name="Documents/cortex_m3_dgug.pdf"      title="Cortex-M3 Generic User Guide"/>
      <book name="Documents/efm32-cortex-m3-rm.pdf"  title="EFM32 Cortex-M3 Reference Manual"/>
      <book name="Documents/EFM32LG-RM.pdf"  title="EFM32LG Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M3 processor running at up to 48 MHz&#xD;&#xA;- Up to 256 kB Flash and 32 kB RAM memory&#xD;&#xA;- Energy efficient and autonomous peripherals&#xD;&#xA;- Ultra low power Energy Modes with sub-uA operation&#xD;&#xA;- Fast wake-up time of only 2 us&#xD;&#xA;&#xD;&#xA;The EFM32LG microcontroller series revolutionizes the 8- to 32-bit market with a combination of unmatched performance and ultra low power consumption in both active- and sleep modes. EFM32LG devices consume as little as 211 uA/MHz in run mode.
      </description>

      <subFamily DsubFamily="EFM32LG230">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG230 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG230 Errata"/>
        <!-- *************************  Device 'EFM32LG230F128'  ***************************** -->
        <device Dname="EFM32LG230F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG230F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG230F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG230F256'  ***************************** -->
        <device Dname="EFM32LG230F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG230F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG230F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG230F64'  ***************************** -->
        <device Dname="EFM32LG230F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG230F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG230F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG232">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG232 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG232 Errata"/>
        <!-- *************************  Device 'EFM32LG232F128'  ***************************** -->
        <device Dname="EFM32LG232F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG232F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG232F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG232F256'  ***************************** -->
        <device Dname="EFM32LG232F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG232F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG232F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG232F64'  ***************************** -->
        <device Dname="EFM32LG232F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG232F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG232F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG280">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG280 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG280 Errata"/>
        <!-- *************************  Device 'EFM32LG280F128'  ***************************** -->
        <device Dname="EFM32LG280F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG280F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG280F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG280F256'  ***************************** -->
        <device Dname="EFM32LG280F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG280F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG280F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG280F64'  ***************************** -->
        <device Dname="EFM32LG280F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG280F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG280F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG290">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG290 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG290 Errata"/>
        <!-- *************************  Device 'EFM32LG290F128'  ***************************** -->
        <device Dname="EFM32LG290F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG290F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG290F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG290F256'  ***************************** -->
        <device Dname="EFM32LG290F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG290F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG290F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG290F64'  ***************************** -->
        <device Dname="EFM32LG290F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG290F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG290F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG295">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG295 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG295 Errata"/>
        <!-- *************************  Device 'EFM32LG295F128'  ***************************** -->
        <device Dname="EFM32LG295F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG295F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG295F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG295F256'  ***************************** -->
        <device Dname="EFM32LG295F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG295F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG295F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG295F64'  ***************************** -->
        <device Dname="EFM32LG295F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG295F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG295F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG330">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG330 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG330 Errata"/>
        <!-- *************************  Device 'EFM32LG330F128'  ***************************** -->
        <device Dname="EFM32LG330F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG330F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG330F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG330F256'  ***************************** -->
        <device Dname="EFM32LG330F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG330F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG330F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG330F64'  ***************************** -->
        <device Dname="EFM32LG330F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG330F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG330F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG332">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG332 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG332 Errata"/>
        <!-- *************************  Device 'EFM32LG332F128'  ***************************** -->
        <device Dname="EFM32LG332F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG332F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG332F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG332F256'  ***************************** -->
        <device Dname="EFM32LG332F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG332F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG332F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG332F64'  ***************************** -->
        <device Dname="EFM32LG332F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG332F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG332F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG360">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG360 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG360 Errata"/>
        <!-- *************************  Device 'EFM32LG360F128'  ***************************** -->
        <device Dname="EFM32LG360F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG360F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG360F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG360F256'  ***************************** -->
        <device Dname="EFM32LG360F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG360F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG360F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG360F64'  ***************************** -->
        <device Dname="EFM32LG360F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG360F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG360F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG380">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG380 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG380 Errata"/>
        <!-- *************************  Device 'EFM32LG380F128'  ***************************** -->
        <device Dname="EFM32LG380F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG380F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG380F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG380F256'  ***************************** -->
        <device Dname="EFM32LG380F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG380F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG380F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG380F64'  ***************************** -->
        <device Dname="EFM32LG380F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG380F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG380F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG390">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG390 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG390 Errata"/>
        <!-- *************************  Device 'EFM32LG390F128'  ***************************** -->
        <device Dname="EFM32LG390F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG390F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG390F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG390F256'  ***************************** -->
        <device Dname="EFM32LG390F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG390F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG390F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG390F64'  ***************************** -->
        <device Dname="EFM32LG390F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG390F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG390F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG395">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG395 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG395 Errata"/>
        <!-- *************************  Device 'EFM32LG395F128'  ***************************** -->
        <device Dname="EFM32LG395F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG395F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG395F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG395F256'  ***************************** -->
        <device Dname="EFM32LG395F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG395F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG395F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG395F64'  ***************************** -->
        <device Dname="EFM32LG395F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG395F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG395F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG840">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG840 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG840 Errata"/>
        <!-- *************************  Device 'EFM32LG840F128'  ***************************** -->
        <device Dname="EFM32LG840F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG840F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG840F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG840F256'  ***************************** -->
        <device Dname="EFM32LG840F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG840F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG840F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG840F64'  ***************************** -->
        <device Dname="EFM32LG840F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG840F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG840F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG842">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG842 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG842 Errata"/>
        <!-- *************************  Device 'EFM32LG842F128'  ***************************** -->
        <device Dname="EFM32LG842F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG842F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG842F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG842F256'  ***************************** -->
        <device Dname="EFM32LG842F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG842F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG842F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG842F64'  ***************************** -->
        <device Dname="EFM32LG842F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG842F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG842F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG880">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG880 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG880 Errata"/>
        <!-- *************************  Device 'EFM32LG880F128'  ***************************** -->
        <device Dname="EFM32LG880F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG880F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG880F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG880F256'  ***************************** -->
        <device Dname="EFM32LG880F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG880F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG880F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG880F64'  ***************************** -->
        <device Dname="EFM32LG880F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG880F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG880F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG890">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG890 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG890 Errata"/>
        <!-- *************************  Device 'EFM32LG890F128'  ***************************** -->
        <device Dname="EFM32LG890F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG890F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG890F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG890F256'  ***************************** -->
        <device Dname="EFM32LG890F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG890F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG890F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG890F64'  ***************************** -->
        <device Dname="EFM32LG890F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG890F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG890F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG895">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG895 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG895 Errata"/>
        <!-- *************************  Device 'EFM32LG895F128'  ***************************** -->
        <device Dname="EFM32LG895F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG895F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG895F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG895F256'  ***************************** -->
        <device Dname="EFM32LG895F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG895F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG895F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG895F64'  ***************************** -->
        <device Dname="EFM32LG895F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG895F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG895F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG900">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG900 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG900 Errata"/>
        <!-- *************************  Device 'EFM32LG900F256'  ***************************** -->
        <device Dname="EFM32LG900F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG900F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG900F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG940">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG940 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG940 Errata"/>
        <!-- *************************  Device 'EFM32LG940F128'  ***************************** -->
        <device Dname="EFM32LG940F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG940F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG940F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG940F256'  ***************************** -->
        <device Dname="EFM32LG940F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG940F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG940F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG940F64'  ***************************** -->
        <device Dname="EFM32LG940F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG940F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG940F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG942">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG942 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG942 Errata"/>
        <!-- *************************  Device 'EFM32LG942F128'  ***************************** -->
        <device Dname="EFM32LG942F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG942F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG942F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG942F256'  ***************************** -->
        <device Dname="EFM32LG942F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG942F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG942F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG942F64'  ***************************** -->
        <device Dname="EFM32LG942F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG942F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG942F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG980">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG980 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG980 Errata"/>
        <!-- *************************  Device 'EFM32LG980F128'  ***************************** -->
        <device Dname="EFM32LG980F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG980F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG980F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG980F256'  ***************************** -->
        <device Dname="EFM32LG980F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG980F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG980F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG980F64'  ***************************** -->
        <device Dname="EFM32LG980F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG980F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG980F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG990">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG990 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG990 Errata"/>
        <!-- *************************  Device 'EFM32LG990F128'  ***************************** -->
        <device Dname="EFM32LG990F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG990F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG990F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG990F256'  ***************************** -->
        <device Dname="EFM32LG990F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG990F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG990F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG990F64'  ***************************** -->
        <device Dname="EFM32LG990F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG990F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG990F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32LG995">
        <book         name="Documents/efm32lg-datasheet.pdf"      title="EFM32LG995 Data Sheet"/>
        <book         name="Documents/efm32lg-errata.pdf"         title="EFM32LG995 Errata"/>
        <!-- *************************  Device 'EFM32LG995F128'  ***************************** -->
        <device Dname="EFM32LG995F128">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG995F128"/>
          <debug      svd="SVD/EFM32LG/EFM32LG995F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG995F256'  ***************************** -->
        <device Dname="EFM32LG995F256">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG995F256"/>
          <debug      svd="SVD/EFM32LG/EFM32LG995F256.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32LG995F64'  ***************************** -->
        <device Dname="EFM32LG995F64">
          <compile header="Device/SiliconLabs/EFM32LG/Include/em_device.h"  define="EFM32LG995F64"/>
          <debug      svd="SVD/EFM32LG/EFM32LG995F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32LG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFM32LG">
      <description>Silicon Labs EFM32LG device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFM32LG*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EFM32LG">
      <description>System Startup for Silicon Labs EFM32LG device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFM32LG/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFM32LG/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFM32LG/Source/ARM/startup_efm32lg.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/EFM32LG/Source/GCC/startup_efm32lg.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFM32LG/Source/GCC/efm32lg.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFM32LG/Source/system_efm32lg.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
