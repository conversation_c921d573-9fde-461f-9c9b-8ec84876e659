<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXC444_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-MCXC444</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCXC444_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-MCXC444">
      <description>FRDM-MCXC444</description>
      <image small="boards/frdmmcxc444/frdmmcxc444.png"/>
      <mountedDevice Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC143VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC143VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC144VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC144VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC243VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC244VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC244VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC443VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC443VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC444VMP" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MCXC143.internal_condition">
      <accept Dname="MCXC143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC144.internal_condition">
      <accept Dname="MCXC144VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC243.internal_condition">
      <accept Dname="MCXC243VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC244.internal_condition">
      <accept Dname="MCXC244VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFT" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC443.internal_condition">
      <accept Dname="MCXC443VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VMP" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXC444.internal_condition">
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MCXC444.internal_condition">
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmmcxc444.condition_id">
      <require condition="allOf.board=frdmmcxc444, component.lpuart_adapter, device_id=MCXC444, device.MCXC444_startup, driver.common, driver.gpio, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmmcxc444, component.lpuart_adapter, device_id=MCXC444, device.MCXC444_startup, driver.common, driver.gpio, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.frdmmcxc444.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="device_id.MCXC444.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmmcxc444.internal_condition">
      <accept condition="device.MCXC143.internal_condition"/>
      <accept condition="device.MCXC144.internal_condition"/>
      <accept condition="device.MCXC243.internal_condition"/>
      <accept condition="device.MCXC244.internal_condition"/>
      <accept condition="device.MCXC443.internal_condition"/>
      <accept condition="device.MCXC444.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc16_continuous_dma" folder="boards/frdmmcxc444/driver_examples/adc16/continuous_dma" doc="readme.md">
      <description>The ADC16 continuous DMA demo application demonstrates the usage of the ADC and DMA peripheral while in a continuous mode. TheADC16 is first set to continuous mode. In continuous convert configuration, only the...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_continuous_dma.uvprojx"/>
        <environment name="iar" load="iar/adc16_continuous_dma.ewp"/>
        <environment name="csolution" load="adc16_continuous_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_interrupt" folder="boards/frdmmcxc444/driver_examples/adc16/interrupt" doc="readme.md">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc16_interrupt.ewp"/>
        <environment name="csolution" load="adc16_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power" folder="boards/frdmmcxc444/demo_apps/adc16_low_power" doc="readme.md">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power.ewp"/>
        <environment name="csolution" load="adc16_low_power.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_async_dma" folder="boards/frdmmcxc444/demo_apps/adc16_low_power_async_dma" doc="readme.md">
      <description>The ADC Low Power Async DMA demo application demonstrates the usage of the ADC and DMA peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 100 ms, low power...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_async_dma.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power_async_dma.ewp"/>
        <environment name="csolution" load="adc16_low_power_async_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="boards/frdmmcxc444/driver_examples/adc16/polling" doc="readme.md">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" load="iar/adc16_polling.ewp"/>
        <environment name="csolution" load="adc16_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble" folder="boards/frdmmcxc444/demo_apps/bubble" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble.uvprojx"/>
        <environment name="iar" load="iar/bubble.ewp"/>
        <environment name="csolution" load="bubble.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="boards/frdmmcxc444/driver_examples/cmp/interrupt" doc="readme.md">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input....See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
        <environment name="csolution" load="cmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="boards/frdmmcxc444/driver_examples/cmp/polling" doc="readme.md">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
        <environment name="csolution" load="cmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="boards/frdmmcxc444/cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="boards/frdmmcxc444/cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/frdmmcxc444/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/frdmmcxc444/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_dma_transfer" folder="boards/frdmmcxc444/cmsis_driver_examples/lpuart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="boards/frdmmcxc444/cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="boards/frdmmcxc444/cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="boards/frdmmcxc444/cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_master" folder="boards/frdmmcxc444/cmsis_driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use spi CMSIS driver in interrupt way:In this example , we need two boards, one board used as spi master and another board used as spi slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_slave" folder="boards/frdmmcxc444/cmsis_driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_dma_transfer" folder="boards/frdmmcxc444/cmsis_driver_examples/uart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_uart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="boards/frdmmcxc444/cmsis_driver_examples/uart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cop" folder="boards/frdmmcxc444/driver_examples/cop" doc="readme.md">
      <description>The COP Example project is to demonstrate usage of the KSDK cop driver.In this example, after 10 times of refreshing, a timeout reset is generated.Please notice that because COP control registers are write-once only,...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cop.uvprojx"/>
        <environment name="iar" load="iar/cop.ewp"/>
        <environment name="csolution" load="cop.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_adc" folder="boards/frdmmcxc444/demo_apps/dac_adc" doc="readme.md">
      <description>The DAC / ADC demo application demonstrates the use of the DAC and ADC peripherals. This application demonstrates how toconfigure the DAC and set the output on the DAC. This demo also demonstrates how to configure...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_adc.uvprojx"/>
        <environment name="iar" load="iar/dac_adc.ewp"/>
        <environment name="csolution" load="dac_adc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_basic" folder="boards/frdmmcxc444/driver_examples/dac/basic" doc="readme.md">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's buffer feature is not enabled, the first item of the buffer is used as the DAC output data register.The converter...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_basic.uvprojx"/>
        <environment name="iar" load="iar/dac_basic.ewp"/>
        <environment name="csolution" load="dac_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_buffer_interrupt" folder="boards/frdmmcxc444/driver_examples/dac/buffer_interrupt" doc="readme.md">
      <description>The dac_buffer_interrupt example shows how to use DAC buffer with interrupts.When the DAC's buffer feature is enabled, user can benefit from the automation of updating DAC output by hardware/software trigger. As we...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_buffer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dac_buffer_interrupt.ewp"/>
        <environment name="csolution" load="dac_buffer_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_continuous_pit_dma" folder="boards/frdmmcxc444/driver_examples/dac/continuous_pit_dma" doc="readme.md">
      <description>The demo shows how to use the PIT to generate a DAC trigger and use the DMA to transfer data into DAC buffer.In this example, DAC is first set to normal buffer mode. PIT is as DAC hardware trigger source and DMA...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_continuous_pit_dma.uvprojx"/>
        <environment name="iar" load="iar/dac_continuous_pit_dma.ewp"/>
        <environment name="csolution" load="dac_continuous_pit_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/frdmmcxc444/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_i2c_transfer" folder="boards/frdmmcxc444/driver_examples/flexio/i2c/interrupt_i2c_transfer" doc="readme.md">
      <description>The flexio_i2c_interrupt example shows how to use flexio i2c master driver in interrupt way:In this example, a flexio simulated i2c master connect to an I2C slave.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_i2c_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_i2c_transfer.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_i2c_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="boards/frdmmcxc444/driver_examples/flexio/pwm" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm.ewp"/>
        <environment name="csolution" load="flexio_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_dma_spi_transfer_master" folder="boards/frdmmcxc444/driver_examples/flexio/spi/dma_spi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_dma_spi_slave example shows how to use flexio spi master driver in dma way:In this example, a flexio simulated master connect to a spi slave .</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_dma_spi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_dma_spi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_dma_spi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_dma_spi_transfer_slave" folder="boards/frdmmcxc444/driver_examples/flexio/spi/dma_spi_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_dma_spi_master example shows how to use flexio spi slave driver in dma way:In this example, a flexio simulated slave connect to a spi master.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_dma_spi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_dma_spi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_dma_spi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_spi_transfer_master" folder="boards/frdmmcxc444/driver_examples/flexio/spi/int_spi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_interrupt_spi_slave example shows how to use flexio spi master driver in interrupt way:In this example, a flexio simulated master connect to a spi slave .</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_spi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_spi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_int_spi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_spi_transfer_slave" folder="boards/frdmmcxc444/driver_examples/flexio/spi/int_spi_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_spi_master example shows how to use flexio spi slave driver in interrupt way:In this example, a flexio simulated slave connect to a spi master.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_spi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_spi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_int_spi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_dma_transfer" folder="boards/frdmmcxc444/driver_examples/flexio/uart/dma_transfer" doc="readme.md">
      <description>The flexio_uart_dma example shows how to use flexio uart driver in dma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board....See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_dma_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="boards/frdmmcxc444/driver_examples/flexio/uart/int_rb_transfer" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="boards/frdmmcxc444/driver_examples/flexio/uart/interrupt_transfer" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="boards/frdmmcxc444/driver_examples/flexio/uart/polling_transfer" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/frdmmcxc444/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/frdmmcxc444/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmmcxc444/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="boards/frdmmcxc444/driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="boards/frdmmcxc444/driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt" folder="boards/frdmmcxc444/driver_examples/i2c/interrupt" doc="readme.md">
      <description>The i2c_functional_interrupt example shows how to use I2C functional driver to build a interrupt based application:In this example , one i2c instance used as I2C master and another i2c instance used as I2C slave .1....See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt.ewp"/>
        <environment name="csolution" load="i2c_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/frdmmcxc444/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/frdmmcxc444/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="boards/frdmmcxc444/driver_examples/i2c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="boards/frdmmcxc444/driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/frdmmcxc444/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="boards/frdmmcxc444/driver_examples/lptmr" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
        <environment name="csolution" load="lptmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_dma_rb_transfer" folder="boards/frdmmcxc444/driver_examples/lpuart/dma_rb_transfer" doc="readme.md">
      <description>The lpuart_dma ring buffer example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_dma_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_dma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_dma_transfer" folder="boards/frdmmcxc444/driver_examples/lpuart/dma_transfer" doc="readme.md">
      <description>The lpuart_dma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_dma_transfer.ewp"/>
        <environment name="csolution" load="lpuart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/frdmmcxc444/driver_examples/lpuart/interrupt" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/frdmmcxc444/driver_examples/lpuart/interrupt_rb_transfer" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/frdmmcxc444/driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/frdmmcxc444/driver_examples/lpuart/polling" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcglite" folder="boards/frdmmcxc444/driver_examples/mcglite" doc="readme.md">
      <description>The MCG_Lite example shows how to use MCG_Lite driver: 1. How to use the mode functions for MCG_Lite mode switch. 2. How to use the frequency functions to get current MCG_Lite frequency. 3. Work flow Reset mode --&gt;...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcglite.uvprojx"/>
        <environment name="iar" load="iar/mcglite.ewp"/>
        <environment name="csolution" load="mcglite.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="boards/frdmmcxc444/driver_examples/flash/pflash" doc="readme.md">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" load="iar/pflash.ewp"/>
        <environment name="csolution" load="pflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="boards/frdmmcxc444/driver_examples/pit" doc="readme.md">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" load="iar/pit.ewp"/>
        <environment name="csolution" load="pit.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="boards/frdmmcxc444/demo_apps/power_manager" doc="readme.md">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" load="iar/power_manager.ewp"/>
        <environment name="csolution" load="power_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="boards/frdmmcxc444/demo_apps/power_mode_switch" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="csolution" load="power_mode_switch.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="boards/frdmmcxc444/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" load="iar/rtc.ewp"/>
        <environment name="csolution" load="rtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="boards/frdmmcxc444/demo_apps/rtc_func" doc="readme.md">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar + Get the current date time with Year, Month,...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="csolution" load="rtc_func.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/frdmmcxc444/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="slcd" folder="boards/frdmmcxc444/driver_examples/slcd" doc="readme.md">
      <description>The SLCD example shows how to use SLCD driver.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/slcd.uvprojx"/>
        <environment name="csolution" load="slcd.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_master" folder="boards/frdmmcxc444/driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_board2board_master example shows how to use spi driver as master to do board to board transfer with DMA:In this example, one spi instance as master and another spi instance on othereboard as slave. Master...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_slave" folder="boards/frdmmcxc444/driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_board2board_slave example shows how to use spi driver as slave to do board to board transfer with DMA:In this example, one spi instance as slave and another spi instance on other board as master. Master...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt" folder="boards/frdmmcxc444/driver_examples/spi/interrupt" doc="readme.md">
      <description>The spi_interrupt example shows how to use spi functional API to do interrupt transfer:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt.ewp"/>
        <environment name="csolution" load="spi_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/frdmmcxc444/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/frdmmcxc444/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/frdmmcxc444/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/frdmmcxc444/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/frdmmcxc444/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/frdmmcxc444/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_input_capture" folder="boards/frdmmcxc444/driver_examples/tpm/input_capture" doc="readme.md">
      <description>The TPM project is a demonstration program of the SDK TPM driver's input capture feature.The example sets up a TPM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/tpm_input_capture.ewp"/>
        <environment name="csolution" load="tpm_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_output_compare" folder="boards/frdmmcxc444/driver_examples/tpm/output_compare" doc="readme.md">
      <description>The TPM project is a demonstration program of the SDK TPM driver's output compare feature.It sets up one TPM channel to toggle the output when a match occurs with the channel value. The usershould probe the TPM...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/tpm_output_compare.ewp"/>
        <environment name="csolution" load="tpm_output_compare.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_pwm_twochannel" folder="boards/frdmmcxc444/driver_examples/tpm/pwm_twochannel" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The PWM dutycycle on both channelsis manually updated. On boards...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/tpm_pwm_twochannel.ewp"/>
        <environment name="csolution" load="tpm_pwm_twochannel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_simple_pwm" folder="boards/frdmmcxc444/driver_examples/tpm/simple_pwm" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output a center-aligned PWM signal. The PWM dutycycle is manually updated.On boards that have an LED...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/tpm_simple_pwm.ewp"/>
        <environment name="csolution" load="tpm_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer" folder="boards/frdmmcxc444/driver_examples/tpm/timer" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer.uvprojx"/>
        <environment name="iar" load="iar/tpm_timer.ewp"/>
        <environment name="csolution" load="tpm_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_dma_rb_transfer" folder="boards/frdmmcxc444/driver_examples/uart/dma_rb_transfer" doc="readme.md">
      <description>The uart_dma example shows how to use uart driver with DMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_dma_rb_transfer.ewp"/>
        <environment name="csolution" load="uart_dma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_dma_transfer" folder="boards/frdmmcxc444/driver_examples/uart/dma_transfer" doc="readme.md">
      <description>The uart_dma example shows how to use uart driver with DMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_dma_transfer.ewp"/>
        <environment name="csolution" load="uart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="boards/frdmmcxc444/driver_examples/uart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt.ewp"/>
        <environment name="csolution" load="uart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="boards/frdmmcxc444/driver_examples/uart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="uart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="boards/frdmmcxc444/driver_examples/uart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="boards/frdmmcxc444/driver_examples/uart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="FRDM-MCXC444" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" load="iar/uart_polling.ewp"/>
        <environment name="csolution" load="uart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmmcxc444" Cversion="1.0.0" condition="BOARD_Project_Template.frdmmcxc444.condition_id">
      <description>Board_project_template frdmmcxc444</description>
      <files>
        <file category="header" attr="config" name="boards/frdmmcxc444/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc444/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxc444/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc444/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxc444/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc444/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxc444/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc444/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmmcxc444/project_template/"/>
      </files>
    </component>
  </components>
</package>
