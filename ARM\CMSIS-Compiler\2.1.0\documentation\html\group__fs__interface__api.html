<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: File Interface</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('group__fs__interface__api.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Content</a> &#124;
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">File Interface</div></div>
</div><!--header-->
<div class="contents">

<p>An API that enables integration of standard C library with an arbitrary file system.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="groups" name="groups"></a>
Content</h2></td></tr>
<tr class="memitem:group__fs__interface__definitions"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__definitions.html">Definitions</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:structrt__fs__time__t"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#structrt__fs__time__t">rt_fs_time_t</a></td></tr>
<tr class="memdesc:structrt__fs__time__t"><td class="mdescLeft">&#160;</td><td class="mdescRight">Time and Date Information.  <a href="group__fs__interface__api.html#structrt__fs__time__t">More...</a><br /></td></tr>
<tr class="separator:structrt__fs__time__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:structrt__fs__stat__t"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#structrt__fs__stat__t">rt_fs_stat_t</a></td></tr>
<tr class="memdesc:structrt__fs__stat__t"><td class="mdescLeft">&#160;</td><td class="mdescRight">File Status Information.  <a href="group__fs__interface__api.html#structrt__fs__stat__t">More...</a><br /></td></tr>
<tr class="separator:structrt__fs__stat__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga09c427a919e3c7c1bfb405e192c0849e"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#ga09c427a919e3c7c1bfb405e192c0849e">rt_fs_open</a> (const char *path, int32_t mode)</td></tr>
<tr class="memdesc:ga09c427a919e3c7c1bfb405e192c0849e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Open a file.  <br /></td></tr>
<tr class="separator:ga09c427a919e3c7c1bfb405e192c0849e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace1b759b5a1f2d5154c2c23126cd842b"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#gace1b759b5a1f2d5154c2c23126cd842b">rt_fs_close</a> (int32_t fd)</td></tr>
<tr class="memdesc:gace1b759b5a1f2d5154c2c23126cd842b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Close a file.  <br /></td></tr>
<tr class="separator:gace1b759b5a1f2d5154c2c23126cd842b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3472b0d88210dea8cd54b152e59f272b"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#ga3472b0d88210dea8cd54b152e59f272b">rt_fs_write</a> (int32_t fd, const void *buf, uint32_t cnt)</td></tr>
<tr class="memdesc:ga3472b0d88210dea8cd54b152e59f272b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Write to a file.  <br /></td></tr>
<tr class="separator:ga3472b0d88210dea8cd54b152e59f272b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf78d25103be5a1e6d0f21db8e096e5b1"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#gaf78d25103be5a1e6d0f21db8e096e5b1">rt_fs_read</a> (int32_t fd, void *buf, uint32_t cnt)</td></tr>
<tr class="memdesc:gaf78d25103be5a1e6d0f21db8e096e5b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read from a file.  <br /></td></tr>
<tr class="separator:gaf78d25103be5a1e6d0f21db8e096e5b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga62500e40f183a0d94eec65b6d305ffb9"><td class="memItemLeft" align="right" valign="top">int64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#ga62500e40f183a0d94eec65b6d305ffb9">rt_fs_seek</a> (int32_t fd, int64_t offset, int32_t whence)</td></tr>
<tr class="memdesc:ga62500e40f183a0d94eec65b6d305ffb9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Move the file position pointer.  <br /></td></tr>
<tr class="separator:ga62500e40f183a0d94eec65b6d305ffb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac0ee645fb88ca96df5dff0487fa43d66"><td class="memItemLeft" align="right" valign="top">int64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#gac0ee645fb88ca96df5dff0487fa43d66">rt_fs_size</a> (int32_t fd)</td></tr>
<tr class="memdesc:gac0ee645fb88ca96df5dff0487fa43d66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get file size.  <br /></td></tr>
<tr class="separator:gac0ee645fb88ca96df5dff0487fa43d66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9c5f8a04fa3f74e46555c93773843153"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#ga9c5f8a04fa3f74e46555c93773843153">rt_fs_stat</a> (int32_t fd, <a class="el" href="group__fs__interface__api.html#structrt__fs__stat__t">rt_fs_stat_t</a> *stat)</td></tr>
<tr class="memdesc:ga9c5f8a04fa3f74e46555c93773843153"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get file status information.  <br /></td></tr>
<tr class="separator:ga9c5f8a04fa3f74e46555c93773843153"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04336f96924935da0c98cdb8a51c484f"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#ga04336f96924935da0c98cdb8a51c484f">rt_fs_remove</a> (const char *path)</td></tr>
<tr class="memdesc:ga04336f96924935da0c98cdb8a51c484f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove a file or directory.  <br /></td></tr>
<tr class="separator:ga04336f96924935da0c98cdb8a51c484f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab58c54e3d90cc303e0120e3d3e9d2d0"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__api.html#gaab58c54e3d90cc303e0120e3d3e9d2d0">rt_fs_rename</a> (const char *oldpath, const char *newpath)</td></tr>
<tr class="memdesc:gaab58c54e3d90cc303e0120e3d3e9d2d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rename or move a file or directory.  <br /></td></tr>
<tr class="separator:gaab58c54e3d90cc303e0120e3d3e9d2d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>An API that enables integration of standard C library with an arbitrary file system. </p>
<hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="structrt__fs__time__t" id="structrt__fs__time__t"></a>
<h2 class="memtitle"><span class="permalink"><a href="#structrt__fs__time__t">&#9670;&#160;</a></span>rt_fs_time_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct rt_fs_time_t</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Time and Date Information. </p>
<p>This struct contains time and date information, including seconds, minutes, hours, day of the month, month of the year and year. Values outside of the specified range are treated as invalid. Reserved bits should be ignored. </p>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a id="a57ca98d8f6d4baf0fe41c583c7dcb0d5" name="a57ca98d8f6d4baf0fe41c583c7dcb0d5"></a>uint16_t</td>
<td class="fieldname">
year</td>
<td class="fielddoc">
Year. </td></tr>
<tr><td class="fieldtype">
<a id="ac994d1b867aab54062cdd1ee94510030" name="ac994d1b867aab54062cdd1ee94510030"></a>uint8_t</td>
<td class="fieldname">
mon</td>
<td class="fielddoc">
Month of year [0, 11]. </td></tr>
<tr><td class="fieldtype">
<a id="a72369a1087b2aeffe374bb054cb97c12" name="a72369a1087b2aeffe374bb054cb97c12"></a>uint8_t</td>
<td class="fieldname">
day</td>
<td class="fielddoc">
Day of month [1, 31]. </td></tr>
<tr><td class="fieldtype">
<a id="ae5af4ff48939d13d480f87e56a9385d6" name="ae5af4ff48939d13d480f87e56a9385d6"></a>uint8_t</td>
<td class="fieldname">
hour</td>
<td class="fielddoc">
Hours [0, 23]. </td></tr>
<tr><td class="fieldtype">
<a id="ac9b481208b43f7c37ed25e446bdec692" name="ac9b481208b43f7c37ed25e446bdec692"></a>uint8_t</td>
<td class="fieldname">
min</td>
<td class="fielddoc">
Minutes [0, 59]. </td></tr>
<tr><td class="fieldtype">
<a id="ad1696900026b287a87c563b733a21bc3" name="ad1696900026b287a87c563b733a21bc3"></a>uint8_t</td>
<td class="fieldname">
sec</td>
<td class="fielddoc">
Seconds [0, 60]. </td></tr>
<tr><td class="fieldtype">
<a id="a12144a8b7d28e438d97bd2606d534990" name="a12144a8b7d28e438d97bd2606d534990"></a>uint8_t</td>
<td class="fieldname">
rsvd</td>
<td class="fielddoc">
Reserved. </td></tr>
</table>

</div>
</div>
<a name="structrt__fs__stat__t" id="structrt__fs__stat__t"></a>
<h2 class="memtitle"><span class="permalink"><a href="#structrt__fs__stat__t">&#9670;&#160;</a></span>rt_fs_stat_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct rt_fs_stat_t</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>File Status Information. </p>
<p>This struct contains various attributes and metadata about a file or directory in a filesystem, including its attribute bitmap, access time, modification time, change time and filesystem specific block size and block count allocated to store the file. </p>
</div><table class="fieldtable">
<tr><th colspan="3">Data Fields</th></tr>
<tr><td class="fieldtype">
<a id="aa44be3989b143c3e08f816d223fa9292" name="aa44be3989b143c3e08f816d223fa9292"></a>uint32_t</td>
<td class="fieldname">
attr</td>
<td class="fielddoc">
File attribute bitmap. </td></tr>
<tr><td class="fieldtype">
<a id="a5efa606db28aed72c035733b047934c4" name="a5efa606db28aed72c035733b047934c4"></a><a class="el" href="group__fs__interface__api.html#structrt__fs__time__t">rt_fs_time_t</a></td>
<td class="fieldname">
access</td>
<td class="fielddoc">
Last file data access timestamp. </td></tr>
<tr><td class="fieldtype">
<a id="af8a9b0b0b34601ec9f8fa1a00b2b2982" name="af8a9b0b0b34601ec9f8fa1a00b2b2982"></a><a class="el" href="group__fs__interface__api.html#structrt__fs__time__t">rt_fs_time_t</a></td>
<td class="fieldname">
modify</td>
<td class="fielddoc">
Last file data modification timestamp. </td></tr>
<tr><td class="fieldtype">
<a id="ab032f8794e275c62e4912ed0ed21045a" name="ab032f8794e275c62e4912ed0ed21045a"></a><a class="el" href="group__fs__interface__api.html#structrt__fs__time__t">rt_fs_time_t</a></td>
<td class="fieldname">
change</td>
<td class="fielddoc">
Last file object change timestamp. </td></tr>
<tr><td class="fieldtype">
<a id="acca8be9b7b13270be578e8de1c5e252d" name="acca8be9b7b13270be578e8de1c5e252d"></a>uint32_t</td>
<td class="fieldname">
blksize</td>
<td class="fielddoc">
The size of filesystem block. </td></tr>
<tr><td class="fieldtype">
<a id="ac89bef465997728a29ce17f5ddf56aa6" name="ac89bef465997728a29ce17f5ddf56aa6"></a>uint32_t</td>
<td class="fieldname">
blkcount</td>
<td class="fielddoc">
Number of allocated filesystem blocks. </td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ga09c427a919e3c7c1bfb405e192c0849e" name="ga09c427a919e3c7c1bfb405e192c0849e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga09c427a919e3c7c1bfb405e192c0849e">&#9670;&#160;</a></span>rt_fs_open()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_open </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>path</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Open a file. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">path</td><td>string specifying the pathname of the file to be opened </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>integer bitmap specifying the file open mode </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a non-negative integer representing the file descriptor on success, or error code on failure</dd></dl>
<p>This function opens a file specified by the pathname.</p>
<p>The mode parameter is a bitmap that specifies the file open mode. The following bits are exclusive:</p><ul>
<li>FS_OPEN_RDONLY: open file for reading only</li>
<li>FS_OPEN_WRONLY: open file for writing only</li>
<li>FS_OPEN_RDWR: open file for reading and writing</li>
</ul>
<p>In addition, any combination of the values below can be set:</p><ul>
<li>FS_OPEN_APPEND: if set, the file offset is set to the end of file prior to each write</li>
<li>FS_OPEN_CREATE: if set, the file is created if it does not exist</li>
<li>FS_OPEN_TRUNCATE: if set, the size of an existing file opened for writing is truncated to zero</li>
</ul>
<p>The file position offset shall be set to the beginning of the file unless append mode is specified. </p>

</div>
</div>
<a id="gace1b759b5a1f2d5154c2c23126cd842b" name="gace1b759b5a1f2d5154c2c23126cd842b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gace1b759b5a1f2d5154c2c23126cd842b">&#9670;&#160;</a></span>rt_fs_close()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_close </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>fd</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Close a file. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fd</td><td>file descriptor of an opened file </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>zero if the file was successfully closed, or error code on failure</dd></dl>
<p>This function closes the file associated with the file descriptor fd. </p>

</div>
</div>
<a id="ga3472b0d88210dea8cd54b152e59f272b" name="ga3472b0d88210dea8cd54b152e59f272b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga3472b0d88210dea8cd54b152e59f272b">&#9670;&#160;</a></span>rt_fs_write()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_write </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>fd</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cnt</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Write to a file. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fd</td><td>file descriptor of an opened file </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">buf</td><td>pointer to the buffer containing data to write </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cnt</td><td>number of bytes to write </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of bytes actually written, or error code on failure</dd></dl>
<p>This function shall write cnt bytes from the buffer pointed to by buf to the file associated with the open file descriptor, fd. </p>

</div>
</div>
<a id="gaf78d25103be5a1e6d0f21db8e096e5b1" name="gaf78d25103be5a1e6d0f21db8e096e5b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf78d25103be5a1e6d0f21db8e096e5b1">&#9670;&#160;</a></span>rt_fs_read()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_read </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>fd</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cnt</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Read from a file. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fd</td><td>file descriptor of an opened file </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">buf</td><td>pointer to the buffer to store read data </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">cnt</td><td>number of bytes to read </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>number of bytes actually read, 0 at the EOF, or error code on failure</dd></dl>
<p>This function reads cnt bytes from the file associated with the file descriptor fd, into the buffer pointed to by buf. The actual number of bytes read can be less than cnt. </p>

</div>
</div>
<a id="ga62500e40f183a0d94eec65b6d305ffb9" name="ga62500e40f183a0d94eec65b6d305ffb9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga62500e40f183a0d94eec65b6d305ffb9">&#9670;&#160;</a></span>rt_fs_seek()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t rt_fs_seek </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>fd</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int64_t&#160;</td>
          <td class="paramname"><em>offset</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>whence</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Move the file position pointer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fd</td><td>file descriptor of an opened file </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">offset</td><td>the number of bytes to move </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">whence</td><td>file position location (RT_SEEK_SET, RT_SEEK_CUR, RT_SEEK_END) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>current file position from the beginning of the file, or error code on failure</dd></dl>
<p>This functions moves the file position pointer as specified with parameters offset and whence. Parameter whence can have the following possible values:</p><ul>
<li>RT_SEEK_SET: set the file position pointer to offset bytes from the start of the file</li>
<li>RT_SEEK_CUR: set the file position pointer to offset bytes from the current location</li>
<li>RT_SEEK_END: set the file position pointer to offset bytes from the end of the file </li>
</ul>

</div>
</div>
<a id="gac0ee645fb88ca96df5dff0487fa43d66" name="gac0ee645fb88ca96df5dff0487fa43d66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac0ee645fb88ca96df5dff0487fa43d66">&#9670;&#160;</a></span>rt_fs_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int64_t rt_fs_size </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>fd</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get file size. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fd</td><td>file descriptor of an opened file </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>file size in bytes, or error code on failure</dd></dl>
<p>This function retrieves the size of an opened file. </p>

</div>
</div>
<a id="ga9c5f8a04fa3f74e46555c93773843153" name="ga9c5f8a04fa3f74e46555c93773843153"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9c5f8a04fa3f74e46555c93773843153">&#9670;&#160;</a></span>rt_fs_stat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_stat </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>fd</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__fs__interface__api.html#structrt__fs__stat__t">rt_fs_stat_t</a> *&#160;</td>
          <td class="paramname"><em>stat</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get file status information. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">fd</td><td>file descriptor of an opened file </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">stat</td><td>pointer to a <a class="el" href="group__fs__interface__api.html#structrt__fs__stat__t" title="File Status Information.">rt_fs_stat_t</a> object to store the status information in </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>zero if the file status was successfully retrieved, or error code on failure</dd></dl>
<p>This function retrieves status information about the file associated with the given file descriptor. The status information is stored in the <a class="el" href="group__fs__interface__api.html#structrt__fs__stat__t" title="File Status Information.">rt_fs_stat_t</a> struct pointed to by stat. The function shall return with error if fd is not a valid file descriptor or if parameter stat is NULL. </p>

</div>
</div>
<a id="ga04336f96924935da0c98cdb8a51c484f" name="ga04336f96924935da0c98cdb8a51c484f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga04336f96924935da0c98cdb8a51c484f">&#9670;&#160;</a></span>rt_fs_remove()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_remove </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>path</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Remove a file or directory. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">path</td><td>string specifying the pathname </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>zero if the file or directory was successfully removed, or error code on failure</dd></dl>
<p>If removing a directory, the directory must be empty. </p>

</div>
</div>
<a id="gaab58c54e3d90cc303e0120e3d3e9d2d0" name="gaab58c54e3d90cc303e0120e3d3e9d2d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaab58c54e3d90cc303e0120e3d3e9d2d0">&#9670;&#160;</a></span>rt_fs_rename()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t rt_fs_rename </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>oldpath</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>newpath</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rename or move a file or directory. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">oldpath</td><td>string specifying file or directory to be renamed </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">newpath</td><td>string specifying the new pathname </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>zero if the file or directory was successfully renamed or moved, or error code on failure</dd></dl>
<p>This function changes the name of a file or directory.</p>
<p>If the destination exists, it must match the source in type. If the destination is a directory, the directory must be empty. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
