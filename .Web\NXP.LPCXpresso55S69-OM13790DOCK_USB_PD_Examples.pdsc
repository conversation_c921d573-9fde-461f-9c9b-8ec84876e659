<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso55S69-OM13790DOCK_USB_PD_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware usb_pd Examples Pack for LPCXpresso55S69-OM13790DOCK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC55S69_DFP" vendor="NXP" version="19.0.0"/>
      <package name="USB_PD" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso55S69-OM13790DOCK">
      <description>LPCXpresso Development Board for LPC55S6x family of MCUs with Sensor Toolbox Development Boards for a 9-Axis Solution using FXAS21002C and FXOS8700CQ</description>
      <image small="boards/lpcxpresso55s69_om13790dock/lpcxpresso55s69_om13790dock.png"/>
      <book category="overview" name="https://www.nxp.com/pip/OM13790" title="LPCXpresso Development Board for LPC55S6x family of MCUs with Sensor Toolbox Development Boards for a 9-Axis Solution using FXAS21002C and FXOS8700CQ" public="true"/>
      <mountedDevice Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.LPC55S69.internal_condition">
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S66.internal_condition">
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S69.internal_condition">
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.lpcxpresso55s69_om13790dock.condition_id">
      <require condition="allOf.component.serial_manager, component.serial_manager_uart, component.usart_adapter, device_id=LPC55S69, device.LPC55S69_startup, driver.clock, driver.common, driver.flexcomm, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, driver.power, kit=lpcxpresso55s69_om13790dock, utility.debug_console.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, component.serial_manager_uart, component.usart_adapter, device_id=LPC55S69, device.LPC55S69_startup, driver.clock, driver.common, driver.flexcomm, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, driver.power, kit=lpcxpresso55s69_om13790dock, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require condition="device_id.LPC55S69.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexcomm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require condition="kit.lpcxpresso55s69_om13790dock.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="kit.lpcxpresso55s69_om13790dock.internal_condition">
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="usb_pd_alt_mode_dp_dock_bm" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_alt_mode_dp_dock/bm/cm33_core0" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the shield dock board (om13790dock) to implement the DisplayPort alternate mode.It recognize attached video adapters...See more details in readme document.</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_alt_mode_dp_dock_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_alt_mode_dp_dock_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_alt_mode_dp_dock_freertos" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_alt_mode_dp_dock/freertos/cm33_core0" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the shield dock board (om13790dock) to implement the DisplayPort alternate mode.It recognize attached video adapters...See more details in readme document.</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_alt_mode_dp_dock_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_alt_mode_dp_dock_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_bm" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd/bm/cm33_core0" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the board keys and debug console to test the PD functions. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_charger_battery_bm" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_charger_battery/bm/cm33_core0" doc="readme.txt">
      <description>the application simulate battery product , it prints the battery percent continually. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_charger_battery_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_charger_battery_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_charger_battery_freertos" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_charger_battery/freertos/cm33_core0" doc="readme.txt">
      <description>The application simulate battery product, it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_charger_battery_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_charger_battery_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_freertos" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd/freertos/cm33_core0" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the board keys and debug console to test the PD functions. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_sink_battery_bm" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_sink_battery/bm/cm33_core0" doc="readme.txt">
      <description>The application simulate battery product , it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_sink_battery_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_sink_battery_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_sink_battery_freertos" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_sink_battery/freertos/cm33_core0" doc="readme.txt">
      <description>The application simulate battery product, it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_sink_battery_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_sink_battery_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_source_charger_bm" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_source_charger/bm/cm33_core0" doc="readme.txt">
      <description>The application simulate charger product. The demo only works as source and is external powered</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_source_charger_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_source_charger_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_source_charger_freertos" folder="boards/lpcxpresso55s69_om13790dock/usb_examples/usb_pd_source_charger/freertos/cm33_core0" doc="readme.txt">
      <description>The application simulate charger product. The demo only works as source and is external powered</description>
      <board name="LPCXpresso55S69-OM13790DOCK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_source_charger_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_source_charger_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="USB" Cgroup="USB PD Project Template" Csub="lpcxpresso55s69_om13790dock" Cvariant="lpcxpresso55s69_om13790dock" Cversion="1.0.0" condition="BOARD_Project_Template.lpcxpresso55s69_om13790dock.condition_id">
      <description>BOARD_Project_Template lpcxpresso55s69_om13790dock</description>
      <files>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/board.c" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/board.h" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/clock_config.c" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/clock_config.h" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/pin_mux.c" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/pin_mux.h" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/peripherals.c" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s69_om13790dock/project_template/peripherals.h" version="1.0.0" projectpath="lpcxpresso55s69_om13790dock/project_template"/>
        <file category="include" name="boards/lpcxpresso55s69_om13790dock/project_template/"/>
      </files>
    </component>
  </components>
</package>
