<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MK28F15_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MK28F15</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Project Template">NXP MCUXpresso SDK RTE Device Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="CMSIS Driver">NXP MCUXpresso SDK Peripheral CMSIS Drivers</description>
  </taxonomy>
  <devices>
    <family Dfamily="MK28F15" Dvendor="NXP:11">
      <description>The Kinetis K28F is a high performance 150 MHz ARM® Cortex®-M4 with FPU Microcontroller (MCU) which integrates 2 MB of embedded Flash, 1 MB of SRAM, dual.USB controllers (High-Speed and Full-Speed), an SDRAM controller, a QuadSPI interface and Power Management Unit with Core Voltage Bypass.</description>
      <device Dname="MK28FN2M0xxx15">
        <processor Dcore="Cortex-M4" Dfpu="SP_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="*********"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MK28FN2M0xxx15_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x200000" access="rx" default="1" startup="1"/>
        <memory name="SRAM_LOWER" start="0x1ffc0000" size="0x040000" access="rw" default="1"/>
        <memory name="SRAM_UPPER" start="0x20000000" size="0x040000" access="rw" default="1"/>
        <algorithm name="arm/MK_P2M0.FLM" start="0x00000000" size="0x00200000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <debug svd="MK28F15.xml"/>
        <variant Dvariant="MK28FN2M0VMI15">
          <compile header="fsl_device_registers.h" define="CPU_MK28FN2M0VMI15"/>
          <environment name="uv">
            <CMisc>--C99</CMisc>
            <LMisc>--scatter "./RTE/Device/MK28FN2M0VMI15/MK28FN2M0xxx15_flash.scf"</LMisc>
          </environment>
        </variant>
        <variant Dvariant="MK28FN2M0CAU15R">
          <compile header="fsl_device_registers.h" define="CPU_MK28FN2M0CAU15R"/>
          <environment name="uv">
            <CMisc>--C99</CMisc>
            <LMisc>--scatter "./RTE/Device/MK28FN2M0CAU15R/MK28FN2M0xxx15_flash.scf"</LMisc>
          </environment>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="cond_MCUXpressoConfigTools">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="device.MK28F15_AND_component.lpuart_adapter_AND_component.serial_manager_uart_AND_device.MK28F15_startup_AND_driver.adc16_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.i2c_AND_driver.lpuart_AND_driver.port_AND_driver.rtc_AND_driver.smc_AND_utility.debug_console">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MK28F15_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rtc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="adc"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.dmamux_AND_driver.edma">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.common">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.common_AND_driver.lpuart">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MK28F15_AND_component.lists_AND_driver.common">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.lpuart">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MK28F15_AND_CMSIS_Include_core_cm4">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.sdramc">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdramc"/>
    </condition>
    <condition id="device.MK28F15_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.dspi_edma">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" Capiversion="2.2.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MK28F15_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c_edma">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MK28F15_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart_edma">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MK28F15_AND_device.MK28F15_CMSIS_AND_driver.clock">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MK28F15_startup"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.dbi_AND_driver.flexio_mculcd">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dbi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_mculcd"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.dspi_AND_driver.edma">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.common_AND_driver.dmamux">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.flexio">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.flexio_camera">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_camera"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.flexio_i2s">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.flexio_mculcd">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_mculcd"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.flexio_spi">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.flexio_uart">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.i2c">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.lpuart">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.qspi">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="qspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.edma_AND_driver.sai">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sai"/>
    </condition>
    <condition id="device.MK28F15_AND_driver.dbi">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dbi"/>
    </condition>
    <condition id="device.MK28F15_AND_utility.debug_console">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MK28F15_AND_component.serial_manager_AND_driver.common">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MK28F15_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <accept Dname="MK28FN2M0???15*" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="MK28F15" Cversion="1.0.0" condition="device.MK28F15_AND_component.lpuart_adapter_AND_component.serial_manager_uart_AND_device.MK28F15_startup_AND_driver.adc16_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.i2c_AND_driver.lpuart_AND_driver.port_AND_driver.rtc_AND_driver.smc_AND_utility.debug_console" isDefaultVariant="1">
      <description/>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c"/>
        <file category="header" attr="config" name="project_template/board.h"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c"/>
        <file category="header" attr="config" name="project_template/clock_config.h"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c"/>
        <file category="header" attr="config" name="project_template/peripherals.h"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c"/>
        <file category="header" attr="config" name="project_template/pin_mux.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device" Cversion="1.0.0" condition="device.MK28F15_AND_driver.dmamux_AND_driver.edma">
      <description/>
      <files>
        <file category="header" attr="config" name="template/RTE_Device.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.MK28F15_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/lists/generic_list.c"/>
        <file category="header" name="components/lists/generic_list.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter" Cversion="1.0.0" condition="device.MK28F15_AND_driver.common_AND_driver.lpuart">
      <description/>
      <files>
        <file category="sourceC" name="components/uart/lpuart_adapter.c"/>
        <file category="header" name="components/uart/uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager" Cversion="1.0.0" condition="device.MK28F15_AND_component.lists_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_manager.c"/>
        <file category="header" name="components/serial_manager/serial_manager.h"/>
        <file category="header" name="components/serial_manager/serial_port_internal.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart" Cversion="1.0.0" condition="device.MK28F15_AND_driver.lpuart">
      <description/>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_port_uart.c"/>
        <file category="header" name="components/serial_manager/serial_port_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Csub="MK28F15_startup" Cversion="1.0.0" condition="device.MK28F15_AND_CMSIS_Include_core_cm4">
      <description/>
      <files>
        <file condition="mdk" category="sourceAsm" attr="config" name="arm/startup_MK28F15.s"/>
        <file condition="iar" category="sourceAsm" attr="config" name="iar/startup_MK28F15.s"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MK28FN2M0xxx15_flash.scf"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MK28FN2M0xxx15_ram.scf"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MK28FN2M0xxx15_flash.icf"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MK28FN2M0xxx15_ram.icf"/>
        <file category="header" attr="config" name="MK28F15.h"/>
        <file category="header" attr="config" name="MK28F15_features.h"/>
        <file category="header" attr="config" name="fsl_device_registers.h"/>
        <file category="sourceC" attr="config" name="system_MK28F15.c"/>
        <file category="header" attr="config" name="system_MK28F15.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="IS42SM16800H" Cversion="1.0.0" condition="device.MK28F15_AND_driver.sdramc">
      <description/>
      <files>
        <file category="sourceC" name="components/IS42SM16800H/fsl_sdram.c"/>
        <file category="header" name="components/IS42SM16800H/fsl_sdram.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="adc" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>ADC16 Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_adc16.c"/>
        <file category="header" name="drivers/fsl_adc16.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cache" Cversion="2.0.1" condition="device.MK28F15_AND_driver.common">
      <description>CACHE Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cache.c"/>
        <file category="header" name="drivers/fsl_cache.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.1.0" condition="device.MK28F15_AND_driver.common">
      <description>Clock Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
        <file category="header" name="drivers/fsl_clock.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cmp" Cversion="2.0.1" condition="device.MK28F15_AND_driver.common">
      <description>CMP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cmp.c"/>
        <file category="header" name="drivers/fsl_cmp.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="dspi_cmsis" Cversion="2.0.0" condition="device.MK28F15_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.dspi_edma">
      <description>DSPI CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_dspi_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_dspi_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="i2c_cmsis_edma" Cversion="2.0.2" condition="device.MK28F15_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c_edma">
      <description>I2C CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_i2c_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_i2c_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="lpuart_cmsis_edma" Cversion="2.0.1" condition="device.MK28F15_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart_edma">
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_lpuart_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_lpuart_cmsis.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cmt" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>CMT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cmt.c"/>
        <file category="header" name="drivers/fsl_cmt.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.1.0" condition="device.MK28F15_AND_device.MK28F15_CMSIS_AND_driver.clock">
      <description>COMMON Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_common.c"/>
        <file category="header" name="drivers/fsl_common.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc" Cversion="2.0.1" condition="device.MK28F15_AND_driver.common">
      <description>CRC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_crc.c"/>
        <file category="header" name="drivers/fsl_crc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dac" Cversion="2.0.1" condition="device.MK28F15_AND_driver.common">
      <description>DAC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dac.c"/>
        <file category="header" name="drivers/fsl_dac.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dbi" Cversion="1.0.0" condition="device.MK28F15_AND_driver.common">
      <description/>
      <files>
        <file category="header" name="components/video/display/dbi/fsl_dbi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dbi_flexio" Cversion="1.0.0" condition="device.MK28F15_AND_driver.dbi_AND_driver.flexio_mculcd">
      <description/>
      <files>
        <file category="sourceC" name="components/video/display/dbi/flexio/fsl_dbi_flexio.c"/>
        <file category="header" name="components/video/display/dbi/flexio/fsl_dbi_flexio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>DMAMUX Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dmamux.c"/>
        <file category="header" name="drivers/fsl_dmamux.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dspi" Cversion="2.2.1" condition="device.MK28F15_AND_driver.common">
      <description>DSPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dspi.c"/>
        <file category="header" name="drivers/fsl_dspi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dspi_edma" Cversion="2.2.1" condition="device.MK28F15_AND_driver.dspi_AND_driver.edma">
      <description>DSPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dspi_edma.c"/>
        <file category="header" name="drivers/fsl_dspi_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="edma" Cversion="2.1.8" condition="device.MK28F15_AND_driver.common_AND_driver.dmamux">
      <description>EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_edma.c"/>
        <file category="header" name="drivers/fsl_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ewm" Cversion="2.0.1" condition="device.MK28F15_AND_driver.common">
      <description>EWM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ewm.c"/>
        <file category="header" name="drivers/fsl_ewm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash" Cversion="3.0.0" condition="device.MK28F15_AND_driver.common">
      <description>Flash Driver</description>
      <files>
        <file category="header" name="drivers/fsl_flash.h"/>
        <file category="header" name="drivers/fsl_ftfx_adapter.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_cache.c"/>
        <file category="header" name="drivers/fsl_ftfx_cache.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_controller.c"/>
        <file category="header" name="drivers/fsl_ftfx_controller.h"/>
        <file category="header" name="drivers/fsl_ftfx_features.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flash.c"/>
        <file category="header" name="drivers/fsl_ftfx_flash.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flexnvm.c"/>
        <file category="header" name="drivers/fsl_ftfx_flexnvm.h"/>
        <file category="header" name="drivers/fsl_ftfx_utilities.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexbus" Cversion="2.1.0" condition="device.MK28F15_AND_driver.common">
      <description>FLEXBUS Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexbus.c"/>
        <file category="header" name="drivers/fsl_flexbus.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>FLEXIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio.c"/>
        <file category="header" name="drivers/fsl_flexio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_camera" Cversion="2.1.2" condition="device.MK28F15_AND_driver.flexio">
      <description>FLEXIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_camera.c"/>
        <file category="header" name="drivers/fsl_flexio_camera.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_camera_edma" Cversion="2.1.2" condition="device.MK28F15_AND_driver.edma_AND_driver.flexio_camera">
      <description>FLEXIO CAMERA EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_camera_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_camera_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2c_master" Cversion="2.1.8" condition="device.MK28F15_AND_driver.flexio">
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2c_master.c"/>
        <file category="header" name="drivers/fsl_flexio_i2c_master.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s" Cversion="2.1.5" condition="device.MK28F15_AND_driver.flexio">
      <description>FLEXIO I2S Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2s.c"/>
        <file category="header" name="drivers/fsl_flexio_i2s.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s_edma" Cversion="2.1.5" condition="device.MK28F15_AND_driver.edma_AND_driver.flexio_i2s">
      <description>FLEXIO I2S EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2s_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_i2s_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_mculcd" Cversion="2.0.2" condition="device.MK28F15_AND_driver.flexio">
      <description>FLEXIO MCULCD Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_mculcd.c"/>
        <file category="header" name="drivers/fsl_flexio_mculcd.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_mculcd_edma" Cversion="2.0.2" condition="device.MK28F15_AND_driver.edma_AND_driver.flexio_mculcd">
      <description>FLEXIO MCULCD EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_mculcd_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_mculcd_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi" Cversion="2.1.3" condition="device.MK28F15_AND_driver.flexio">
      <description>FLEXIO SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_spi.c"/>
        <file category="header" name="drivers/fsl_flexio_spi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi_edma" Cversion="2.1.3" condition="device.MK28F15_AND_driver.edma_AND_driver.flexio_spi">
      <description>FLEXIO SPI EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_spi_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_spi_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart" Cversion="2.1.5" condition="device.MK28F15_AND_driver.flexio">
      <description>FLEXIO UART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart.c"/>
        <file category="header" name="drivers/fsl_flexio_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart_edma" Cversion="2.1.5" condition="device.MK28F15_AND_driver.edma_AND_driver.flexio_uart">
      <description>FLEXIO UART EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_uart_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ft5406" Cversion="1.0.0" condition="device.MK28F15_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/ft5406/fsl_ft5406.c"/>
        <file category="header" name="components/ft5406/fsl_ft5406.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ftm" Cversion="2.1.1" condition="device.MK28F15_AND_driver.common">
      <description>FTM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ftm.c"/>
        <file category="header" name="drivers/fsl_ftm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="fxos8700cq" Cversion="1.0.0" condition="device.MK28F15_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/fxos8700cq/fsl_fxos.c"/>
        <file category="header" name="components/fxos8700cq/fsl_fxos.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.3.2" condition="device.MK28F15_AND_driver.common">
      <description>GPIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
        <file category="header" name="drivers/fsl_gpio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.0.7" condition="device.MK28F15_AND_driver.common">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c.c"/>
        <file category="header" name="drivers/fsl_i2c.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_edma" Cversion="2.0.7" condition="device.MK28F15_AND_driver.edma_AND_driver.i2c">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c_edma.c"/>
        <file category="header" name="drivers/fsl_i2c_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="llwu" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>LLWU Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_llwu.c"/>
        <file category="header" name="drivers/fsl_llwu.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lmem" Cversion="2.1.0" condition="device.MK28F15_AND_driver.common">
      <description>LMEM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lmem_cache.c"/>
        <file category="header" name="drivers/fsl_lmem_cache.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>LPTMR Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lptmr.c"/>
        <file category="header" name="drivers/fsl_lptmr.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart" Cversion="2.2.7" condition="device.MK28F15_AND_driver.common">
      <description>LPUART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpuart.c"/>
        <file category="header" name="drivers/fsl_lpuart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_edma" Cversion="2.2.7" condition="device.MK28F15_AND_driver.edma_AND_driver.lpuart">
      <description>LPUART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpuart_edma.c"/>
        <file category="header" name="drivers/fsl_lpuart_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pdb" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>PDB Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pdb.c"/>
        <file category="header" name="drivers/fsl_pdb.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pit" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>PIT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pit.c"/>
        <file category="header" name="drivers/fsl_pit.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pmc" Cversion="2.0.1" condition="device.MK28F15_AND_driver.common">
      <description>PMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pmc.c"/>
        <file category="header" name="drivers/fsl_pmc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="port" Cversion="2.1.0" condition="device.MK28F15_AND_driver.common">
      <description>PORT Driver</description>
      <files>
        <file category="header" name="drivers/fsl_port.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="qspi" Cversion="2.2.0" condition="device.MK28F15_AND_driver.common">
      <description>QSPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_qspi.c"/>
        <file category="header" name="drivers/fsl_qspi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="qspi_edma" Cversion="2.0.2" condition="device.MK28F15_AND_driver.edma_AND_driver.qspi">
      <description>QSPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_qspi_edma.c"/>
        <file category="header" name="drivers/fsl_qspi_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rcm" Cversion="2.0.2" condition="device.MK28F15_AND_driver.common">
      <description>RCM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rcm.c"/>
        <file category="header" name="drivers/fsl_rcm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rtc" Cversion="2.2.0" condition="device.MK28F15_AND_driver.common">
      <description>RTC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rtc.c"/>
        <file category="header" name="drivers/fsl_rtc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sai" Cversion="2.2.1" condition="device.MK28F15_AND_driver.common">
      <description>SAI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sai.c"/>
        <file category="header" name="drivers/fsl_sai.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sai_edma" Cversion="2.2.0" condition="device.MK28F15_AND_driver.edma_AND_driver.sai">
      <description>SAI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sai_edma.c"/>
        <file category="header" name="drivers/fsl_sai_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sdhc" Cversion="2.1.9" condition="device.MK28F15_AND_driver.common">
      <description>SDHC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sdhc.c"/>
        <file category="header" name="drivers/fsl_sdhc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sdramc" Cversion="2.1.0" condition="device.MK28F15_AND_driver.common">
      <description>SDRAMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sdramc.c"/>
        <file category="header" name="drivers/fsl_sdramc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sim" Cversion="2.1.0" condition="device.MK28F15_AND_driver.common">
      <description>SIM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sim.c"/>
        <file category="header" name="drivers/fsl_sim.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="smc" Cversion="2.0.5" condition="device.MK28F15_AND_driver.common">
      <description>SMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_smc.c"/>
        <file category="header" name="drivers/fsl_smc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ssd1963" Cversion="1.1.0" condition="device.MK28F15_AND_driver.dbi">
      <description/>
      <files>
        <file category="sourceC" name="components/ssd1963/fsl_ssd1963.c"/>
        <file category="header" name="components/ssd1963/fsl_ssd1963.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sysmpu" Cversion="2.2.1" condition="device.MK28F15_AND_driver.common">
      <description>SYSMPU Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sysmpu.c"/>
        <file category="header" name="drivers/fsl_sysmpu.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="tpm" Cversion="2.0.4" condition="device.MK28F15_AND_driver.common">
      <description>TPM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_tpm.c"/>
        <file category="header" name="drivers/fsl_tpm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="trng" Cversion="2.0.4" condition="device.MK28F15_AND_driver.common">
      <description>TRNG Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_trng.c"/>
        <file category="header" name="drivers/fsl_trng.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="vref" Cversion="2.1.1" condition="device.MK28F15_AND_driver.common">
      <description>VREF Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_vref.c"/>
        <file category="header" name="drivers/fsl_vref.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wdog" Cversion="2.0.0" condition="device.MK28F15_AND_driver.common">
      <description>WDOG Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_wdog.c"/>
        <file category="header" name="drivers/fsl_wdog.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert" Cversion="1.0.0" condition="device.MK28F15_AND_utility.debug_console">
      <description/>
      <files>
        <file category="sourceC" name="utilities/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console" Cversion="1.0.0" condition="device.MK28F15_AND_component.serial_manager_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="utilities/debug_console/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console.h"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console_conf.h"/>
        <file category="sourceC" name="utilities/str/fsl_str.c"/>
        <file category="header" name="utilities/str/fsl_str.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="notifier" Cversion="1.0.0" condition="device.MK28F15_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="utilities/fsl_notifier.c"/>
        <file category="header" name="utilities/fsl_notifier.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="shell" Cversion="1.0.0" condition="device.MK28F15_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <description/>
      <files>
        <file category="sourceC" name="utilities/fsl_shell.c"/>
        <file category="header" name="utilities/fsl_shell.h"/>
      </files>
    </component>
  </components>
</package>
