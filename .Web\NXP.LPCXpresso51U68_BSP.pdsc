<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso51U68_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXpresso51U68</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.1" date="2018-07-26">A problem with missing debug config options, which caused invalid ROM table error when using MDK IDE CMSIS-DAP debugging tool was fixed (KEX-4724).</release>
    <release version="10.0.0" date="2018-05-09">First version of the LPCXpresso51U68 board support pack</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC51U68_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso51U68">
      <description>LPCXpresso Development Board for LPC51U68</description>
      <image small="boards/lpcxpresso51u68/lpcxpresso51u68.png"/>
      <book category="overview" name="https://www.nxp.com/pip/OM40005" title="LPCXpresso Development Board for LPC51U68" public="true"/>
      <mountedDevice Dname="LPC51U68JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC51U68JBD48" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.LPC51U68.internal_condition">
      <accept Dname="LPC51U68JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC51U68JBD64" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC51U68.internal_condition">
      <accept Dname="LPC51U68JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC51U68JBD64" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.lpcxpresso51u68.condition_id">
      <require condition="allOf.component.usart_adapter, device_id=LPC51U68, device.LPC51U68_startup, driver.clock, driver.common, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, driver.power, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite, board=lpcxpresso51u68.internal_condition"/>
    </condition>
    <condition id="allOf.component.usart_adapter, device_id=LPC51U68, device.LPC51U68_startup, driver.clock, driver.common, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, driver.power, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite, board=lpcxpresso51u68.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require condition="device_id.LPC51U68.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
      <require condition="board.lpcxpresso51u68.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="board.lpcxpresso51u68.internal_condition">
      <accept condition="device.LPC51U68.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="boards/lpcxpresso51u68/cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="boards/lpcxpresso51u68/cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/lpcxpresso51u68/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/lpcxpresso51u68/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="boards/lpcxpresso51u68/cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="boards/lpcxpresso51u68/cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_master" folder="boards/lpcxpresso51u68/cmsis_driver_examples/spi/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_slave" folder="boards/lpcxpresso51u68/cmsis_driver_examples/spi/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_dma_transfer" folder="boards/lpcxpresso51u68/cmsis_driver_examples/usart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_interrupt_transfer" folder="boards/lpcxpresso51u68/cmsis_driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/lpcxpresso51u68/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/lpcxpresso51u68/driver_examples/ctimer/simple_match" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/lpcxpresso51u68/driver_examples/ctimer/simple_match_interrupt" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/lpcxpresso51u68/driver_examples/ctimer/simple_pwm" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/lpcxpresso51u68/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="boards/lpcxpresso51u68/driver_examples/dma/channel_chain" doc="readme.md">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="iar" load="iar/dma_channel_chain.ewp"/>
        <environment name="csolution" load="dma_channel_chain.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_hardware_trigger" folder="boards/lpcxpresso51u68/driver_examples/dma/hardware_trigger" doc="readme.md">
      <description>The DMA hardware trigger example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers by hardware trigger.The...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_hardware_trigger.uvprojx"/>
        <environment name="iar" load="iar/dma_hardware_trigger.ewp"/>
        <environment name="csolution" load="dma_hardware_trigger.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="boards/lpcxpresso51u68/driver_examples/dma/interleave_transfer" doc="readme.md">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_interleave_transfer.ewp"/>
        <environment name="csolution" load="dma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="boards/lpcxpresso51u68/driver_examples/dma/linked_transfer" doc="readme.md">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_linked_transfer.ewp"/>
        <environment name="csolution" load="dma_linked_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_m2m_polling" folder="boards/lpcxpresso51u68/driver_examples/dma/m2m_polling" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot polling transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_m2m_polling.uvprojx"/>
        <environment name="iar" load="iar/dma_m2m_polling.ewp"/>
        <environment name="csolution" load="dma_m2m_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/lpcxpresso51u68/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="boards/lpcxpresso51u68/driver_examples/dma/wrap_transfer" doc="readme.md">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes a wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_wrap_transfer.ewp"/>
        <environment name="csolution" load="dma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flashiap" folder="boards/lpcxpresso51u68/driver_examples/flashiap" doc="readme.md">
      <description>The FLASIAP project is a simple demonstration program of the SDK FLASIAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flashiap.uvprojx"/>
        <environment name="iar" load="iar/flashiap.ewp"/>
        <environment name="csolution" load="flashiap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmeas" folder="boards/lpcxpresso51u68/driver_examples/fmeas" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Frequency Measure feature of SYSCON module on LPC devices.It shows how to measure a target frequency using a (faster) reference frequency. The example uses the...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmeas.uvprojx"/>
        <environment name="iar" load="iar/fmeas.ewp"/>
        <environment name="csolution" load="fmeas.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gint" folder="boards/lpcxpresso51u68/driver_examples/gint" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Group GPIO input interrupt peripheral.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gint.uvprojx"/>
        <environment name="iar" load="iar/gint.ewp"/>
        <environment name="csolution" load="gint.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/lpcxpresso51u68/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/lpcxpresso51u68/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="boards/lpcxpresso51u68/driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="boards/lpcxpresso51u68/driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/lpcxpresso51u68/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso51u68/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_master" folder="boards/lpcxpresso51u68/driver_examples/i2c/polling_b2b/master" doc="readme.md">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_slave" folder="boards/lpcxpresso51u68/driver_examples/i2c/polling_b2b/slave" doc="readme.md">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_record_playback" folder="boards/lpcxpresso51u68/driver_examples/i2s/dma_record_playback" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_record_playback.uvprojx"/>
        <environment name="csolution" load="i2s_dma_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_transfer" folder="boards/lpcxpresso51u68/driver_examples/i2s/dma_transfer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_transfer.uvprojx"/>
        <environment name="csolution" load="i2s_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_record_playback" folder="boards/lpcxpresso51u68/driver_examples/i2s/interrupt_record_playback" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_record_playback.uvprojx"/>
        <environment name="csolution" load="i2s_interrupt_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_transfer" folder="boards/lpcxpresso51u68/driver_examples/i2s/interrupt_transfer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="i2s_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="boards/lpcxpresso51u68/driver_examples/iap/iap_flash" doc="readme.md">
      <description>The IAP Flash project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="iar" load="iar/iap_flash.ewp"/>
        <environment name="csolution" load="iap_flash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_basic" folder="boards/lpcxpresso51u68/driver_examples/adc/lpc_adc_basic" doc="readme.md">
      <description>The lpc_adc_basic example shows how to use LPC ADC driver in the simplest way.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_basic.ewp"/>
        <environment name="csolution" load="lpc_adc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_burst" folder="boards/lpcxpresso51u68/driver_examples/adc/lpc_adc_burst" doc="readme.md">
      <description>The lpc_adc_burst example shows how to use LPC ADC driver with the burst mode.In this example, the internal temperature sensor is used to created the input analog signal.When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_burst.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_burst.ewp"/>
        <environment name="csolution" load="lpc_adc_burst.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_dma" folder="boards/lpcxpresso51u68/driver_examples/adc/lpc_adc_dma" doc="readme.md">
      <description>The lpc_adc_dma example shows how to use LPC ADC driver with DMA.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_dma.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_dma.ewp"/>
        <environment name="csolution" load="lpc_adc_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_interrupt" folder="boards/lpcxpresso51u68/driver_examples/adc/lpc_adc_interrupt" doc="readme.md">
      <description>The lpc_adc_interrupt example shows how to use interrupt with LPC ADC driver.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_interrupt.ewp"/>
        <environment name="csolution" load="lpc_adc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_bod" folder="boards/lpcxpresso51u68/driver_examples/bod" doc="readme.md">
      <description>The bod example shows how to use LPC BOD(Brown-out detector) in the simplest way. To run this example, user should remove the jumper for the power source selector, and connect the adjustable input voltage to the...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_bod.uvprojx"/>
        <environment name="iar" load="iar/lpc_bod.ewp"/>
        <environment name="csolution" load="lpc_bod.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/lpcxpresso51u68/driver_examples/mrt" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/lpcxpresso51u68/driver_examples/pint/pattern_match" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/lpcxpresso51u68/driver_examples/pint/pin_interrupt" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager_lpc" folder="boards/lpcxpresso51u68/demo_apps/power_manager_lpc" doc="readme.md">
      <description>The power_manager_lpc application shows the usage of normal power mode control APIs for entering the three kinds of low power mode: Sleep mode, Deep Sleep mode and Sleep Power Down mode. When the application runs to...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager_lpc.uvprojx"/>
        <environment name="iar" load="iar/power_manager_lpc.ewp"/>
        <environment name="csolution" load="power_manager_lpc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_example" folder="boards/lpcxpresso51u68/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_example.uvprojx"/>
        <environment name="iar" load="iar/rtc_example.ewp"/>
        <environment name="csolution" load="rtc_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="boards/lpcxpresso51u68/driver_examples/sctimer/16bit_counter" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" load="iar/sctimer_16bit_counter.ewp"/>
        <environment name="csolution" load="sctimer_16bit_counter.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="boards/lpcxpresso51u68/driver_examples/sctimer/multi_state_pwm" doc="readme.md">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_multi_state_pwm.ewp"/>
        <environment name="csolution" load="sctimer_multi_state_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="boards/lpcxpresso51u68/driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.md">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
        <environment name="csolution" load="sctimer_pwm_with_dutycyle_change.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="boards/lpcxpresso51u68/driver_examples/sctimer/simple_pwm" doc="readme.md">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_simple_pwm.ewp"/>
        <environment name="csolution" load="sctimer_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/lpcxpresso51u68/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_transfer" folder="boards/lpcxpresso51u68/driver_examples/spi/dma_transfer" doc="readme.md">
      <description>The spi_dma example shows how to use spi driver with DMA:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_transfer.ewp"/>
        <environment name="csolution" load="spi_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_int_master" folder="boards/lpcxpresso51u68/driver_examples/spi/half_duplex_transfer/int/master" doc="readme.md">
      <description>The spi_half_duplex_int_transfer_master example shows how to use driver API to transfer in half-duplex way. In this example, one spi instance as master and another spi instance on the other board as slave. Master...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_int_master.uvprojx"/>
        <environment name="iar" load="iar/spi_half_duplex_int_master.ewp"/>
        <environment name="csolution" load="spi_half_duplex_int_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_int_slave" folder="boards/lpcxpresso51u68/driver_examples/spi/half_duplex_transfer/int/slave" doc="readme.md">
      <description>The spi_half_duplex_int_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_int_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_half_duplex_int_slave.ewp"/>
        <environment name="csolution" load="spi_half_duplex_int_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_polling_master" folder="boards/lpcxpresso51u68/driver_examples/spi/half_duplex_transfer/polling/master" doc="readme.md">
      <description>The spi_half_duplex_polling_transfer_master example shows how to use driver API to transfer in half-duplex way. In this example, one spi instance as master and another spi instance on the othere board as slave....See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_polling_master.uvprojx"/>
        <environment name="iar" load="iar/spi_half_duplex_polling_master.ewp"/>
        <environment name="csolution" load="spi_half_duplex_polling_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_half_duplex_polling_slave" folder="boards/lpcxpresso51u68/driver_examples/spi/half_duplex_transfer/polling/slave" doc="readme.md">
      <description>The spi_half_duplex_polling_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_half_duplex_polling_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_half_duplex_polling_slave.ewp"/>
        <environment name="csolution" load="spi_half_duplex_polling_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt" folder="boards/lpcxpresso51u68/driver_examples/spi/interrupt" doc="readme.md">
      <description>The spi_interrupt example shows how to use spi functional API to do interrupt transfer:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt.ewp"/>
        <environment name="csolution" load="spi_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/lpcxpresso51u68/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/lpcxpresso51u68/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/lpcxpresso51u68/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso51u68/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/lpcxpresso51u68/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/lpcxpresso51u68/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_9bit_interrupt_transfer" folder="boards/lpcxpresso51u68/driver_examples/usart/9bit_interrupt_transfer" doc="readme.md">
      <description>The usart_9bit_interrupt_transfer example shows how to use usart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it is...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_9bit_interrupt_transfer.ewp"/>
        <environment name="csolution" load="usart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_double_buffer_transfer" folder="boards/lpcxpresso51u68/driver_examples/usart/dma_double_buffer_transfer" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USARTThe example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_double_buffer_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_dma_double_buffer_transfer.ewp"/>
        <environment name="csolution" load="usart_dma_double_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_rb_transfer" folder="boards/lpcxpresso51u68/driver_examples/usart/dma_rb_transfer" doc="readme.md">
      <description>The usart_dma ring buffer example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board will send back all characters that PC send to the board.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_rb_transfer.uvprojx"/>
        <environment name="csolution" load="usart_dma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_transfer" folder="boards/lpcxpresso51u68/driver_examples/usart/dma_transfer" doc="readme.md">
      <description>The usart_dma example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board willsend back all characters that PC send to the board.Note: The example echo...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_dma_transfer.ewp"/>
        <environment name="csolution" load="usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt" folder="boards/lpcxpresso51u68/driver_examples/usart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/usart_interrupt.ewp"/>
        <environment name="csolution" load="usart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_rb_transfer" folder="boards/lpcxpresso51u68/driver_examples/usart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="usart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_transfer" folder="boards/lpcxpresso51u68/driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling" folder="boards/lpcxpresso51u68/driver_examples/usart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling.uvprojx"/>
        <environment name="iar" load="iar/usart_polling.ewp"/>
        <environment name="csolution" load="usart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_sync_transfer_master" folder="boards/lpcxpresso51u68/driver_examples/usart/sync_transfer/master" doc="readme.md">
      <description>The usart synchronous transfer example shows how to use usart driver in synchronous way.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_sync_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/usart_sync_transfer_master.ewp"/>
        <environment name="csolution" load="usart_sync_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_sync_transfer_slave" folder="boards/lpcxpresso51u68/driver_examples/usart/sync_transfer/slave" doc="readme.md">
      <description>The usart synchronous transfer example shows how to use usart driver in synchronous way.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_sync_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/usart_sync_transfer_slave.ewp"/>
        <environment name="csolution" load="usart_sync_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_wakeup_deepsleep" folder="boards/lpcxpresso51u68/driver_examples/usart/wakeup_deepsleep" doc="readme.md">
      <description>The usart_wakeup_deepsleep example shows how to use usart driver in 32kHz clocking mode to wake up soc from deep sleep. In this example, one usart instance connect to PC, the board will enter deep sleep mode and wake up according to user input.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_wakeup_deepsleep.uvprojx"/>
        <environment name="iar" load="iar/usart_wakeup_deepsleep.ewp"/>
        <environment name="csolution" load="usart_wakeup_deepsleep.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_wakeup" folder="boards/lpcxpresso51u68/demo_apps/utick_wakeup" doc="readme.md">
      <description>The purpose of this demo is to show wakeup from deep sleep mode using MicroTick timer.The demo sets the MicroTick Timer as a wake up source and puts the device in deep-sleep mode. The MicroTick timer wakes up the device. </description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_wakeup.uvprojx"/>
        <environment name="iar" load="iar/utick_wakeup.ewp"/>
        <environment name="csolution" load="utick_wakeup.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_wakeup_peripheral" folder="boards/lpcxpresso51u68/demo_apps/utick_wakeup_peripheral" doc="readme.md">
      <description>The purpose of this demo is to show wakeup from deep sleep mode using MicroTick timer.The demo sets the MicroTick Timer as a wake up source and puts the device in deep-sleep mode. The MicroTick timer wakes up the device. </description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_wakeup_peripheral.uvprojx"/>
        <environment name="iar" load="iar/utick_wakeup_peripheral.ewp"/>
        <environment name="csolution" load="utick_wakeup_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/lpcxpresso51u68/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso51u68" Cversion="1.0.0" condition="BOARD_Project_Template.lpcxpresso51u68.condition_id">
      <description>Board_project_template lpcxpresso51u68</description>
      <files>
        <file category="header" attr="config" name="boards/lpcxpresso51u68/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso51u68/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso51u68/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso51u68/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso51u68/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso51u68/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso51u68/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso51u68/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/lpcxpresso51u68/project_template/"/>
      </files>
    </component>
  </components>
</package>
