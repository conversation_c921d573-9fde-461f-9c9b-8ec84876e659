<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.7.40" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.40/schema/PACK.xsd">
  <name>CMSIS</name>
  <description overview="./CMSIS/Documentation/Overview.md">CMSIS (Common Microcontroller Software Interface Standard)</description>
  <vendor>ARM</vendor>
  <license>LICENSE</license>
  <licenseSets>
    <licenseSet id="all" default="true" gating="true">
      <license name="LICENSE" title="Apache 2.0 open-source license" spdx="Apache-2.0"/>
    </licenseSet>
  </licenseSets>
  <url>https://www.keil.com/pack/</url>

  <releases>
    <release version="6.2.0" date="2025-06-18" tag="v6.2.0">
      - CMSIS-Core: 6.1.1
        - Minor fixes and enhancements
          - Fixed CMSIS_DEPRECATED for IAR
          - Added LAR, LSR register to DWT
          - Add CMSIS-R for IAR
          - Added BPU for Star-MC1
          - Add CPPWR SU10/11 for Cortex-M55/-M85
      - CMSIS-RTOS2: 2.3.0
        - (unchanged)
      - CMSIS-Driver: 2.11.0
        - Added vStream Driver API 1.0.0
    </release>
    <release version="6.1.0" date="2024-05-16" tag="v6.1.0">
      - CMSIS-Core: 6.1.0
        - Added support for Cortex-M52
        - Added deprecated CoreDebug symbols for CMSIS 5 compatibility
        - Added define CMSIS_DISABLE_DEPRECATED to hide deprecated symbols
      - CMSIS-Driver: 2.10.0
        - Updated USB Host API 2.4.0
    </release>
    <release version="6.0.0" date="2023-12-18" tag="v6.0.0">
      - CMSIS-Core: 6.0.0
        - Core(M) and Core(A) joined into single Core component
        - Core header files reworked, aligned with TRMs
        - Previously deprecated features removed
        - Dropped support for Arm Compiler 5
      - CMSIS-DSP: moved into separate pack
      - CMSIS-NN: moved into separate pack
      - CMSIS-RTOS: deprecated and removed
        - RTX4 is deprecated and removed
      - CMSIS-RTOS2: 2.3.0
        - OS Tick API moved from Device to CMSIS class
        - Added provisional support for processor affinity in SMP systems
        - RTX5 is moved into separate CMSIS-RTX pack
      - CMSIS-Driver: 2.9.0
        - Updated VIO API 1.0.0
        - Added GPIO Driver API 1.0.0
      - CMSIS-Pack: moved into Open-CMSIS-Pack project
      - CMSIS-SVD: moved into Open-CMSIS-Pack project
      - CMSIS-DAP: moved into separate repository
      - Devices: moved into separate Cortex_DFP pack
      - Utilities: moved into CMSIS-Toolbox project
    </release>
    <release version="5.9.0" date="2022-05-02">
      CMSIS-Core(M): 5.6.0
       - Arm Cortex-M85 cpu support
       - Arm China STAR-MC1 cpu support
       - Updated system_ARMCM55.c
      CMSIS-DSP: 1.10.0 (see revision history for details)
      CMSIS-NN: 3.1.0 (see revision history for details)
       - Support for int16 convolution and fully connected for reference implementation
       - Support for DSP extension optimization for int16 convolution and fully connected
       - Support dilation for int8 convolution
       - Support dilation for int8 depthwise convolution
       - Support for int16 depthwise conv for reference implementation including dilation
       - Support for int16 average and max pooling for reference implementation
       - Support for elementwise add and mul int16 scalar version
       - Support for softmax int16 scalar version
       - Support for SVDF with 8 bit state tensor
      CMSIS-RTOS2: 2.1.3 (unchanged)
        - RTX 5.5.4 (see revision history for details)
      CMSIS-Pack: deprecated (moved to Open-CMSIS-Pack)
      CMSIS-SVD: 1.3.9 (see revision history for details)
      CMSIS-DAP: 2.1.1 (see revision history for details)
       - Allow default clock frequency to use fast clock mode
      Devices
       - Support for Cortex-M85
      Utilities
        - SVDConv 3.3.42
        - PackChk 1.3.95
    </release>
    <release version="5.8.0" date="2021-06-24">
      CMSIS-Core(M): 5.5.0 (see revision history for details)
        - Updated GCC LinkerDescription, GCC Assembler startup
        - Added Armv8-M Stack Sealing (to linker, startup) for toolchain ARM, GCC
        - Changed C-Startup to default Startup.
        - Updated Armv8-M Assembler startup to use GAS syntax
          Note: Updating existing projects may need manual user interaction!
      CMSIS-Core(A): 1.2.1 (see revision history for details)
        - Bugfixes for Cortex-A32
      CMSIS-DAP: 2.1.0 (see revision history for details)
        - Enhanced DAP_Info
        - Added extra UART support
      CMSIS-DSP: 1.9.0 (see revision history for details)
        - Purged pre-built libs from Git
        - Enhanced support for f16 datatype
        - Fixed couple of GCC issues
      CMSIS-NN: 3.0.0 (see revision history for details including version 2.0.0)
        - Major interface change for functions compatible with TensorFlow Lite for Microcontroller
        - Added optimization for SVDF kernel
        - Improved MVE performance for fully Connected and max pool operator
        - NULL bias support for fully connected operator in non-MVE case(Can affect performance)
        - Expanded existing unit test suite along with support for FVP
        - Removed Examples folder
      CMSIS-RTOS2:
        - RTX 5.5.3 (see revision history for details)
          - CVE-2021-27431 vulnerability mitigation.
          - Enhanced stack overrun checking.
          - Various bug fixes and improvements.
      CMSIS-Pack: 1.7.2 (see revision history for details)
        - Support for Microchip XC32 compiler
        - Support for Custom Datapath Extension
    </release>
    <release version="5.7.0" date="2020-04-09">
      CMSIS-Build: 0.9.0 (beta)
        - Draft for CMSIS Project description (CPRJ)
      CMSIS-Core(M): 5.4.0 (see revision history for details)
        - Cortex-M55 cpu support
        - Enhanced MVE support for Armv8.1-MML
        - Fixed device config define checks.
        - L1 Cache functions for Armv7-M and later
      CMSIS-Core(A): 1.2.0 (see revision history for details)
        - Fixed GIC_SetPendingIRQ to use GICD_SGIR
        - Added missing DSP intrinsics
        - Reworked assembly intrinsics: volatile, barriers and clobber
      CMSIS-DSP: 1.8.0 (see revision history for details)
        - Added new functions and function groups
        - Added MVE support
      CMSIS-NN: 1.3.0 (see revision history for details)
        - Added MVE support
        - Further optimizations for kernels using DSP extension
      CMSIS-RTOS2:
        - RTX 5.5.2 (see revision history for details)
      CMSIS-Driver: 2.8.0
        - Added VIO API 0.1.0 (Preview)
        - removed volatile from status related typedefs in APIs
        - enhanced WiFi Interface API with support for polling Socket Receive/Send
      CMSIS-Pack: 1.6.3 (see revision history for details)
        - deprecating all types specific to cpdsc format. Cpdsc is replaced by Cprj with dedicated schema.
      Devices:
        - ARMCM55 device
        - ARMv81MML startup code recognizing __MVE_USED macro
        - Refactored vector table references for all Cortex-M devices
        - Reworked ARMCM* C-StartUp files.
        - Include L1 Cache functions in ARMv8MML/ARMv81MML devices
      Utilities:
        Attention: Linux binaries moved to Linux64 folder!
        - SVDConv 3.3.35
        - PackChk 1.3.89
    </release>
    <release version="5.6.0" date="2019-07-10">
      CMSIS-Core(M): 5.3.0 (see revision history for details)
        - Added provisions for compiler-independent C startup code.
      CMSIS-Core(A): 1.1.4 (see revision history for details)
        - Fixed __FPU_Enable.
      CMSIS-DSP: 1.7.0 (see revision history for details)
        - New Neon versions of f32 functions
        - Python wrapper
        - Preliminary cmake build
        - Compilation flags for FFTs
        - Changes to arm_math.h
      CMSIS-NN: 1.2.0 (see revision history for details)
        - New function for depthwise convolution with asymmetric quantization.
        - New support functions for requantization.
      CMSIS-RTOS:
        - RTX 4.82.0 (updated provisions for Arm Compiler 6 when using Cortex-M0/M0+)
      CMSIS-RTOS2:
        - RTX 5.5.1 (see revision history for details)
      CMSIS-Driver: 2.7.1
        - WiFi Interface API 1.0.0
      Devices:
        - Generalized C startup code for all Cortex-M family devices.
        - Updated Cortex-A default memory regions and MMU configurations
        - Moved Cortex-A memory and system config files to avoid include path issues
    </release>
    <release version="5.5.1" date="2019-03-20">
      The following folders are deprecated
        - CMSIS/Include/ (superseded by CMSIS/DSP/Include/ and CMSIS/Core/Include/)

      CMSIS-Core(M): 5.2.1 (see revision history for details)
        - Fixed compilation issue in cmsis_armclang_ltm.h
    </release>
    <release version="5.5.0" date="2019-03-18">
      The following folders have been removed:
        - CMSIS/Lib/ (superseded by CMSIS/DSP/Lib/)
        - CMSIS/DSP_Lib/ (superseded by CMSIS/DSP/)
      The following folders are deprecated
        - CMSIS/Include/ (superseded by CMSIS/DSP/Include/ and CMSIS/Core/Include/)

      CMSIS-Core(M): 5.2.0 (see revision history for details)
        - Reworked Stack/Heap configuration for ARM startup files.
        - Added Cortex-M35P device support.
        - Added generic Armv8.1-M Mainline device support.
      CMSIS-Core(A): 1.1.3 (see revision history for details)
      CMSIS-DSP: 1.6.0 (see revision history for details)
        - reworked DSP library source files
        - reworked DSP library documentation
        - Changed DSP folder structure
        - moved DSP libraries to folder ./DSP/Lib
        - ARM DSP Libraries are built with ARMCLANG
        - Added DSP Libraries Source variant
      CMSIS-RTOS2:
        - RTX 5.5.0 (see revision history for details)
      CMSIS-Driver: 2.7.0
        - Added WiFi Interface API 1.0.0-beta
        - Added components for project specific driver implementations
      CMSIS-Pack: 1.6.0 (see revision history for details)
      Devices:
        - Added Cortex-M35P and ARMv81MML device templates.
        - Fixed C-Startup Code for GCC (aligned with other compilers)
      Utilities:
        - SVDConv 3.3.25
        - PackChk 1.3.82
    </release>
    <release version="5.4.0" date="2018-08-01">
      Aligned pack structure with repository.
      The following folders are deprecated:
        - CMSIS/Include/
        - CMSIS/DSP_Lib/

      CMSIS-Core(M): 5.1.2 (see revision history for details)
        - Added Cortex-M1 support (beta).
      CMSIS-Core(A): 1.1.2 (see revision history for details)
      CMSIS-NN: 1.1.0
        - Added new math functions.
      CMSIS-RTOS2:
        - API 2.1.3 (see revision history for details)
        - RTX 5.4.0 (see revision history for details)
          * Updated exception handling on Cortex-A
      CMSIS-Driver:
        - Flash Driver API V2.2.0
      Utilities:
        - SVDConv 3.3.21
        - PackChk 1.3.71
    </release>
    <release version="5.3.0" date="2018-02-22">
      Updated Arm company brand.
      CMSIS-Core(M): 5.1.1 (see revision history for details)
      CMSIS-Core(A): 1.1.1 (see revision history for details)
      CMSIS-DAP: 2.0.0 (see revision history for details)
      CMSIS-NN: 1.0.0
        - Initial contribution of the bare metal Neural Network Library.
      CMSIS-RTOS2:
        - RTX 5.3.0 (see revision history for details)
        - OS Tick API 1.0.1
    </release>
    <release version="5.2.0" date="2017-11-16">
      CMSIS-Core(M): 5.1.0 (see revision history for details)
        - Added MPU Functions for ARMv8-M for Cortex-M23/M33.
        - Added compiler_iccarm.h to replace compiler_iar.h shipped with the compiler.
      CMSIS-Core(A): 1.1.0 (see revision history for details)
        - Added compiler_iccarm.h.
        - Added additional access functions for physical timer.
      CMSIS-DAP: 1.2.0 (see revision history for details)
      CMSIS-DSP: 1.5.2 (see revision history for details)
      CMSIS-Driver: 2.6.0 (see revision history for details)
        - CAN Driver API V1.2.0
        - NAND Driver API V2.3.0
      CMSIS-RTOS:
        - RTX: added variant for Infineon XMC4 series affected by PMU_CM.001 errata.
      CMSIS-RTOS2:
        - API 2.1.2 (see revision history for details)
        - RTX 5.2.3 (see revision history for details)
      Devices:
        - Added GCC startup and linker script for Cortex-A9.
        - Added device ARMCM0plus_MPU for Cortex-M0+ with MPU.
        - Added IAR startup code for Cortex-A9
    </release>
    <release version="5.1.1" date="2017-09-19">
      CMSIS-RTOS2:
      - RTX 5.2.1 (see revision history for details)
    </release>
    <release version="5.1.0" date="2017-08-04">
      CMSIS-Core(M): 5.0.2 (see revision history for details)
      - Changed Version Control macros to be core agnostic.
      - Added MPU Functions for ARMv7-M for Cortex-M0+/M3/M4/M7.
      CMSIS-Core(A): 1.0.0 (see revision history for details)
      - Initial release
      - IRQ Controller API 1.0.0
      CMSIS-Driver: 2.05 (see revision history for details)
      - All typedefs related to status have been made volatile.
      CMSIS-RTOS2:
      - API 2.1.1 (see revision history for details)
      - RTX 5.2.0 (see revision history for details)
      - OS Tick API 1.0.0
      CMSIS-DSP: 1.5.2 (see revision history for details)
      - Fixed GNU Compiler specific diagnostics.
      CMSIS-Pack: 1.5.0 (see revision history for details)
      - added System Description File (*.SDF) Format
      CMSIS-Zone: 0.0.1 (Preview)
      - Initial specification draft
    </release>
    <release version="5.0.1" date="2017-02-03">
      Package Description:
      - added taxonomy for Cclass RTOS
      CMSIS-RTOS2:
      - API 2.1   (see revision history for details)
      - RTX 5.1.0 (see revision history for details)
      CMSIS-Core: 5.0.1 (see revision history for details)
      - Added __PACKED_STRUCT macro
      - Added uVisior support
      - Updated cmsis_armcc.h: corrected macro __ARM_ARCH_6M__
      - Updated template for secure main function (main_s.c)
      - Updated template for Context Management for ARMv8-M TrustZone (tz_context.c)
      CMSIS-DSP: 1.5.1 (see revision history for details)
      - added ARMv8M DSP libraries.
      CMSIS-Pack:1.4.9 (see revision history for details)
      - added Pack Index File specification and schema file
    </release>
    <release version="5.0.0" date="2016-11-11">
      Changed open source license to Apache 2.0
      CMSIS_Core:
       - Added support for Cortex-M23 and Cortex-M33.
       - Added ARMv8-M device configurations for mainline and baseline.
       - Added CMSE support and thread context management for TrustZone for ARMv8-M
       - Added cmsis_compiler.h to unify compiler behaviour.
       - Updated function SCB_EnableICache (for Cortex-M7).
       - Added functions: NVIC_GetEnableIRQ, SCB_GetFPUType
      CMSIS-RTOS:
        - bug fix in RTX 4.82 (see revision history for details)
      CMSIS-RTOS2:
        - new API including compatibility layer to CMSIS-RTOS
        - reference implementation based on RTX5
        - supports all Cortex-M variants including TrustZone for ARMv8-M
      CMSIS-SVD:
       - reworked SVD format documentation
       - removed SVD file database documentation as SVD files are distributed in packs
       - updated SVDConv for Win32 and Linux
      CMSIS-DSP:
       - Moved DSP libraries from CMSIS/DSP/Lib to CMSIS/Lib.
       - Added DSP libraries build projects to CMSIS pack.
    </release>
    <release version="4.5.0" date="2015-10-28">
      - CMSIS-Core     4.30.0  (see revision history for details)
      - CMSIS-DAP      1.1.0   (unchanged)
      - CMSIS-Driver   2.04.0  (see revision history for details)
      - CMSIS-DSP      1.4.7   (no source code change [still labeled 1.4.5], see revision history for details)
      - CMSIS-Pack     1.4.1   (see revision history for details)
      - CMSIS-RTOS     4.80.0  Restored time delay parameter 'millisec' old behavior (prior V4.79) for software compatibility. (see revision history for details)
      - CMSIS-SVD      1.3.1   (see revision history for details)
    </release>
    <release version="4.4.0" date="2015-09-11">
      - CMSIS-Core     4.20   (see revision history for details)
      - CMSIS-DSP      1.4.6  (no source code change [still labeled 1.4.5], see revision history for details)
      - CMSIS-Pack     1.4.0  (adding memory attributes, algorithm style)
      - CMSIS-Driver   2.03.0 (adding CAN [Controller Area Network] API)
      - CMSIS-RTOS
        -- API         1.02   (unchanged)
        -- RTX         4.79   (see revision history for details)
      - CMSIS-SVD      1.3.0  (see revision history for details)
      - CMSIS-DAP      1.1.0  (extended with SWO support)
    </release>
    <release version="4.3.0" date="2015-03-20">
      - CMSIS-Core     4.10   (Cortex-M7 extended Cache Maintenance functions)
      - CMSIS-DSP      1.4.5  (see revision history for details)
      - CMSIS-Driver   2.02   (adding SAI (Serial Audio Interface) API)
      - CMSIS-Pack     1.3.3  (Semantic Versioning, Generator extensions)
      - CMSIS-RTOS
        -- API         1.02   (unchanged)
        -- RTX         4.78   (see revision history for details)
      - CMSIS-SVD      1.2    (unchanged)
    </release>
    <release version="4.2.0" date="2014-09-24">
      Adding Cortex-M7 support
      - CMSIS-Core     4.00  (Cortex-M7 support, corrected C++ include guards in core header files)
      - CMSIS-DSP      1.4.4 (Cortex-M7 support and corrected out of bound issues)
      - CMSIS-Pack     1.3.1 (Cortex-M7 updates, clarification, corrected batch files in Tutorial)
      - CMSIS-SVD      1.2   (Cortex-M7 extensions)
      - CMSIS-RTOS RTX 4.75  (see revision history for details)
    </release>
    <release version="4.1.1" date="2014-06-30">
      - fixed conditions preventing the inclusion of the DSP library in projects for Infineon XMC4000 series devices
    </release>
    <release version="4.1.0" date="2014-06-12">
      - CMSIS-Driver   2.02  (incompatible update)
      - CMSIS-Pack     1.3   (see revision history for details)
      - CMSIS-DSP      1.4.2 (unchanged)
      - CMSIS-Core     3.30  (unchanged)
      - CMSIS-RTOS RTX 4.74  (unchanged)
      - CMSIS-RTOS API 1.02  (unchanged)
      - CMSIS-SVD      1.10  (unchanged)
      PACK:
      - removed G++ specific files from PACK
      - added Component Startup variant "C Startup"
      - added Pack Checking Utility
      - updated conditions to reflect tool-chain dependency
      - added Taxonomy for Graphics
      - updated Taxonomy for unified drivers from "Drivers" to "CMSIS Drivers"
    </release>
    <!-- release version="4.0.0">
      - CMSIS-Driver   2.00  Preliminary (incompatible update)
      - CMSIS-Pack     1.1   Preliminary
      - CMSIS-DSP      1.4.2 (see revision history for details)
      - CMSIS-Core     3.30  (see revision history for details)
      - CMSIS-RTOS RTX 4.74  (see revision history for details)
      - CMSIS-RTOS API 1.02  (unchanged)
      - CMSIS-SVD      1.10  (unchanged)
    </release -->
    <release version="3.20.4" date="2014-02-20">
      - CMSIS-RTOS 4.74 (see revision history for details)
      - PACK Extensions (Boards, Device Features, Flash Programming, Generators, Configuration Wizard). Schema version 1.1.
    </release>
    <!-- release version="3.20.3">
      - CMSIS-Driver API Version 1.10 ARM prefix added (incompatible change)
      - CMSIS-RTOS 4.73 (see revision history for details)
    </release -->
    <!-- release version="3.20.2">
      - CMSIS-Pack documentation has been added
      - CMSIS-Drivers header and documentation have been added to PACK
      - CMSIS-CORE, CMSIS-DSP, CMSIS-RTOS API and CMSIS-SVD remain unchanged
    </release -->
    <!-- release version="3.20.1">
      - CMSIS-RTOS Keil RTX V4.72 has been added to PACK
      - CMSIS-CORE, CMSIS-DSP, CMSIS-RTOS API and CMSIS-SVD remain unchanged
    </release -->
    <!-- release version="3.20.0">
      The software portions that are deployed in the application program are now under a BSD license which allows usage
      of CMSIS components in any commercial or open source projects.  The Pack Description file Arm.CMSIS.pdsc describes the use cases
      The individual components have been update as listed below:
      - CMSIS-CORE adds functions for setting breakpoints, supports the latest GCC Compiler, and contains several corrections.
      - CMSIS-DSP library is optimized for more performance and contains several bug fixes.
      - CMSIS-RTOS API is extended with capabilities for short timeouts, Kernel initialization, and prepared for a C++ interface.
      - CMSIS-SVD is unchanged.
    </release -->
  </releases>

  <taxonomy>
    <description Cclass="Audio">Software components for audio processing</description>
    <description Cclass="Board Support">Generic Interfaces for Evaluation and Development Boards</description>
    <description Cclass="Board Part">Drivers that support an external component available on an evaluation board</description>
    <description Cclass="Compiler">Compiler Software Extensions</description>
    <description Cclass="CMSIS" doc="CMSIS/Documentation/html/General/index.html">Cortex Microcontroller Software Interface Components</description>
    <description Cclass="CMSIS Driver" doc="CMSIS/Documentation/html/Driver/index.html">Unified Device Drivers compliant to CMSIS-Driver Specifications</description>
    <description Cclass="Device" doc="CMSIS/Documentation/html/Core/index.html">Startup, System Setup</description>
    <description Cclass="Data Exchange">Data exchange or data formatter</description>
    <description Cclass="Extension Board">Drivers that support an extension board or shield</description>
    <description Cclass="File System">File Drive Support and File System</description>
    <description Cclass="IoT Client">IoT cloud client connector</description>
    <description Cclass="IoT Service">IoT specific services</description>
    <description Cclass="IoT Utility">IoT specific software utility</description>
    <description Cclass="Graphics">Graphical User Interface</description>
    <description Cclass="Network">Network Stack using Internet Protocols</description>
    <description Cclass="RTOS">Real-time Operating System</description>
    <description Cclass="Security">Encryption for secure communication or storage</description>
    <description Cclass="USB">Universal Serial Bus Stack</description>
    <description Cclass="Utility">Generic software utility components</description>
  </taxonomy>

  <apis>
    <!-- CMSIS Device API -->
    <api Cclass="Device" Cgroup="IRQ Controller" Capiversion="1.0.0" exclusive="1">
      <description>Device interrupt controller interface</description>
      <files>
        <file category="header" name="CMSIS/Core/Include/a-profile/irq_ctrl.h"/>
      </files>
    </api>
    <!-- CMSIS OS Tick API -->
    <api Cclass="CMSIS" Cgroup="OS Tick" Capiversion="1.0.1" exclusive="1">
      <description>RTOS Kernel system tick timer interface</description>
      <files>
        <file category="header" name="CMSIS/RTOS2/Include/os_tick.h"/>
        <file category="doc" name="CMSIS/Documentation/html/RTOS2/group__CMSIS__RTOS__TickAPI.html"/>
      </files>
    </api>
    <!-- CMSIS-RTOS API -->
    <api Cclass="CMSIS" Cgroup="RTOS2" Capiversion="2.3.0" exclusive="1">
      <description>CMSIS-RTOS API for Cortex-M, SC000, and SC300</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/RTOS2/group__CMSIS__RTOS.html"/>
        <file category="header" name="CMSIS/RTOS2/Include/cmsis_os2.h"/>
      </files>
    </api>
    <!-- CMSIS Driver API -->
    <api Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.4.0" exclusive="0">
      <description>USART (Universal Synchronous/Asynchronous Receiver/Transmitter) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__usart__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_USART.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="SPI" Capiversion="2.3.0" exclusive="0">
      <description>SPI (Serial Peripheral Interface) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__spi__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_SPI.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="SAI" Capiversion="1.2.0" exclusive="0">
      <description>SAI (Serial Audio Interface) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__sai__interface__gr.html"/>
        <file category="header" name="CMSIS/Driver/Include/Driver_SAI.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.4.0" exclusive="0">
      <description>I2C (Inter-Integrated Circuit) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__i2c__interface__gr.html"/>
        <file category="header" name="CMSIS/Driver/Include/Driver_I2C.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="CAN" Capiversion="1.3.0" exclusive="0">
      <description>CAN (Controller Area Network) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__can__interface__gr.html"/>
        <file category="header" name="CMSIS/Driver/Include/Driver_CAN.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="Flash" Capiversion="2.3.0" exclusive="0">
      <description>NOR Flash Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__flash__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_Flash.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="MCI" Capiversion="2.4.0" exclusive="0">
      <description>MCI (Memory Card Interface) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__mci__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_MCI.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="NAND" Capiversion="2.4.0" exclusive="0">
      <description>NAND Flash Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__nand__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_NAND.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="Ethernet" Capiversion="2.2.0" exclusive="0">
      <description>Ethernet MAC (Media Access Control) and PHY (Physical layer) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__eth__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_ETH_MAC.h" />
        <file category="header" name="CMSIS/Driver/Include/Driver_ETH_PHY.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="Ethernet MAC" Capiversion="2.2.0" exclusive="0">
      <description>Ethernet MAC (Media Access Control) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__eth__mac__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_ETH_MAC.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Capiversion="2.2.0" exclusive="0">
      <description>Ethernet PHY (Physical layer) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__eth__phy__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_ETH_PHY.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="USB Device" Capiversion="2.3.0" exclusive="0">
      <description>USB (Universal Serial Bus) Device Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__usbd__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_USBD.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="USB Host" Capiversion="2.4.0" exclusive="0">
      <description>USB (Universal Serial Bus) Host Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__usbh__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_USBH.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="GPIO" Capiversion="1.0.0" exclusive="0">
      <description>GPIO (General-Purpose Input/Output) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__gpio__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_GPIO.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="WiFi" Capiversion="1.1.0" exclusive="0">
      <description>WiFi (Wireless network) Driver API</description>
      <files>
        <file category="doc" name="CMSIS/Documentation/html/Driver/group__wifi__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/Include/Driver_WiFi.h" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="VIO" Capiversion="1.0.0" exclusive="1">
      <description>Virtual I/O abstraction for simple peripherals (i.e. button, LED)</description>
      <files>
        <file category="doc"    name="CMSIS/Documentation/html/Driver/group__vio__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/VIO/Include/cmsis_vio.h" />
        <file category="other"  name="CMSIS/Driver/VIO/cmsis_vio.scvd" />
      </files>
    </api>
    <api Cclass="CMSIS Driver" Cgroup="vStream" Capiversion="1.0.0" exclusive="0">
      <description>Virtual Streaming interface Driver API</description>
      <files>
        <file category="doc"    name="CMSIS/Documentation/html/Driver/group__vstream__interface__gr.html" />
        <file category="header" name="CMSIS/Driver/vStream/Include/cmsis_vstream.h" />
      </files>
    </api>
  </apis>

  <!-- conditions are dependency rules that can apply to a component or an individual file -->
  <conditions>
    <!-- Arm architecture -->
    <condition id="ARMv6-M Device">
      <description>Armv6-M architecture based device</description>
      <accept Dcore="Cortex-M0"/>
      <accept Dcore="Cortex-M1"/>
      <accept Dcore="Cortex-M0+"/>
      <accept Dcore="SC000"/>
    </condition>
    <condition id="ARMv7-M Device">
      <description>Armv7-M architecture based device</description>
      <accept Dcore="Cortex-M3"/>
      <accept Dcore="Cortex-M4"/>
      <accept Dcore="Cortex-M7"/>
      <accept Dcore="SC300"/>
    </condition>
    <condition id="ARMv8-MBL Device">
      <description>Armv8-M base line architecture based device</description>
      <accept Dcore="ARMV8MBL"/>
      <accept Dcore="Cortex-M23"/>
    </condition>
    <condition id="ARMv8-MML Device">
      <description>Armv8-M main line architecture based device</description>
      <accept Dcore="ARMV8MML"/>
      <accept Dcore="Cortex-M33"/>
      <accept Dcore="Cortex-M35P"/>
      <accept Dcore="Star-MC1"/>
    </condition>
    <condition id="ARMv81-MML Device">
      <description>Armv8.1-M main line architecture based device</description>
      <accept Dcore="ARMV81MML"/>
      <accept Dcore="Cortex-M52"/>
      <accept Dcore="Cortex-M55"/>
      <accept Dcore="Cortex-M85"/>
    </condition>
    <condition id="ARMv8-M Device">
      <description>Armv8-M architecture based device</description>
      <accept condition="ARMv8-MBL Device"/>
      <accept condition="ARMv8-MML Device"/>
      <accept condition="ARMv81-MML Device"/>
    </condition>
    <condition id="ARMv6_7_8-M Device">
      <description>Armv6_7_8-M architecture based device</description>
      <accept condition="ARMv6-M Device"/>
      <accept condition="ARMv7-M Device"/>
      <accept condition="ARMv8-M Device"/>
    </condition>
    <condition id="ARMv7-A Device">
      <description>Armv7-A architecture based device</description>
      <accept Dcore="Cortex-A5"/>
      <accept Dcore="Cortex-A7"/>
      <accept Dcore="Cortex-A9"/>
    </condition>

    <condition id="TrustZone">
      <description>TrustZone</description>
      <require Dtz="TZ"/>
      <deny Dsecure="TZ-disabled"/>
    </condition>
    <condition id="TZ Secure">
      <description>TrustZone (Secure)</description>
      <require Dtz="TZ"/>
      <accept Dsecure="Secure"/>
      <accept Dsecure="Secure-only"/>
    </condition>

    <!-- OS Tick -->
    <condition id="OS Tick SysTick">
      <description>Components required for OS Tick SysTick Timer</description>
      <require condition="ARMv6_7_8-M Device"/>
    </condition>

    <condition id="OS Tick PTIM">
      <description>Components required for OS Tick Private Timer</description>
      <accept Dcore="Cortex-A5"/>
      <accept Dcore="Cortex-A9"/>
      <require Cclass="Device" Cgroup="IRQ Controller"/>
    </condition>

    <condition id="OS Tick GTIM">
      <description>Components required for OS Tick Generic Physical Timer</description>
      <accept Dcore="Cortex-A7"/>
      <require Cclass="Device" Cgroup="IRQ Controller"/>
    </condition>

  </conditions>

  <components>
    <!-- CMSIS-Core component -->
    <component Cclass="CMSIS" Cgroup="CORE" Cversion="6.1.1"  condition="ARMv6_7_8-M Device" >
      <description>CMSIS-CORE for Cortex-M, SC000, SC300, Star-MC1, ARMv8-M, ARMv8.1-M</description>
      <files>
        <!-- CPU independent -->
        <file category="doc"     name="CMSIS/Documentation/html/Core/index.html"/>
        <file category="include" name="CMSIS/Core/Include/"/>
        <file category="header"  name="CMSIS/Core/Include/tz_context.h" condition="TrustZone"/>
        <!-- Code template -->
        <file category="sourceC" attr="template" condition="TZ Secure" name="CMSIS/Core/Template/ARMv8-M/main_s.c"     version="1.1.1" select="Secure mode 'main' module for ARMv8-M"/>
        <file category="sourceC" attr="template" condition="TZ Secure" name="CMSIS/Core/Template/ARMv8-M/tz_context.c" version="1.1.1" select="RTOS Context Management (TrustZone for ARMv8-M)" />
      </files>
    </component>

    <component Cclass="CMSIS" Cgroup="CORE" Cversion="6.1.1"  condition="ARMv7-A Device" >
      <description>CMSIS-CORE for Cortex-A</description>
      <files>
        <!-- CPU independent -->
        <file category="doc"     name="CMSIS/Documentation/html/Core_A/index.html"/>
        <file category="include" name="CMSIS/Core/Include/"/>
      </files>
    </component>

    <!-- IRQ Controller -->
    <component Cclass="Device" Cgroup="IRQ Controller" Csub="GIC" Capiversion="1.0.0" Cversion="1.2.0" condition="ARMv7-A Device">
      <description>IRQ Controller implementation using GIC</description>
      <files>
        <file category="sourceC" name="CMSIS/Core/Source/irq_ctrl_gic.c"/>
      </files>
    </component>

    <!-- OS Tick -->
    <component Cclass="CMSIS" Cgroup="OS Tick" Csub="SysTick" Capiversion="1.0.1" Cversion="1.0.5" condition="OS Tick SysTick">
      <description>OS Tick implementation using Cortex-M SysTick Timer</description>
      <files>
        <file category="sourceC" name="CMSIS/RTOS2/Source/os_systick.c"/>
      </files>
    </component>

    <component Cclass="CMSIS" Cgroup="OS Tick" Csub="Private Timer" Capiversion="1.0.1" Cversion="1.0.2" condition="OS Tick PTIM">
      <description>OS Tick implementation using Private Timer</description>
      <files>
        <file category="sourceC" name="CMSIS/RTOS2/Source/os_tick_ptim.c"/>
      </files>
    </component>

    <component Cclass="CMSIS" Cgroup="OS Tick" Csub="Generic Physical Timer" Capiversion="1.0.1" Cversion="1.0.1" condition="OS Tick GTIM">
      <description>OS Tick implementation using Generic Physical Timer</description>
      <files>
        <file category="sourceC" name="CMSIS/RTOS2/Source/os_tick_gtim.c"/>
      </files>
    </component>

    <!-- CMSIS-Driver Custom components -->
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="Custom" Cversion="1.0.0" Capiversion="2.4.0" custom="1">
      <description>Access to #include Driver_USART.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_USART.c" select="USART Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="Custom" Cversion="1.0.0" Capiversion="2.3.0" custom="1">
      <description>Access to #include Driver_SPI.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_SPI.c" select="SPI Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SAI" Csub="Custom" Cversion="1.0.0" Capiversion="1.2.0" custom="1">
      <description>Access to #include Driver_SAI.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_SAI.c" select="SAI Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="Custom" Cversion="1.0.0" Capiversion="2.4.0" custom="1">
      <description>Access to #include Driver_I2C.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_I2C.c" select="I2C Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="CAN" Csub="Custom" Cversion="1.0.0" Capiversion="1.3.0" custom="1">
      <description>Access to #include Driver_CAN.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_CAN.c" select="CAN Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="Flash" Csub="Custom" Cversion="1.0.0" Capiversion="2.3.0" custom="1">
      <description>Access to #include Driver_Flash.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_Flash.c" select="Flash Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="MCI" Csub="Custom" Cversion="1.0.0" Capiversion="2.4.0" custom="1">
      <description>Access to #include Driver_MCI.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_MCI.c" select="MCI Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="NAND" Csub="Custom" Cversion="1.0.0" Capiversion="2.4.0" custom="1">
      <description>Access to #include Driver_NAND.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_NAND.c" select="NAND Flash Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="Ethernet" Csub="Custom" Cversion="1.0.0" Capiversion="2.2.0" custom="1">
      <description>Access to #include Driver_ETH_PHY/MAC.h files and code templates for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_ETH_PHY.c" select="Ethernet PHY and MAC Driver"/>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_ETH_MAC.c" select="Ethernet PHY and MAC Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="Ethernet MAC" Csub="Custom" Cversion="1.0.0" Capiversion="2.2.0" custom="1">
      <description>Access to #include Driver_ETH_MAC.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_ETH_MAC.c" select="Ethernet MAC Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="Ethernet PHY" Csub="Custom" Cversion="1.0.0" Capiversion="2.2.0" custom="1">
      <description>Access to #include Driver_ETH_PHY.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_ETH_PHY.c" select="Ethernet PHY Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USB Device" Csub="Custom" Cversion="1.0.0" Capiversion="2.3.0" custom="1">
      <description>Access to #include Driver_USBD.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_USBD.c" select="USB Device Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USB Host" Csub="Custom" Cversion="1.0.0" Capiversion="2.4.0" custom="1">
      <description>Access to #include Driver_USBH.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_USBH.c" select="USB Host Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="GPIO" Csub="Custom" Cversion="1.0.0" Capiversion="1.0.0" custom="1">
      <description>Access to #include Driver_GPIO.h file and code template for custom implementation</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_GPIO.c" select="GPIO Driver"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="WiFi" Csub="Custom" Cversion="1.0.0" Capiversion="1.1.0" custom="1">
      <description>Access to #include Driver_WiFi.h file</description>
      <files>
        <file category="sourceC" attr="template" name="CMSIS/Driver/DriverTemplates/Driver_WiFi.c" select="WiFi Driver"/>
      </files>
    </component>

    <!-- VIO components -->
    <component Cclass="CMSIS Driver" Cgroup="VIO" Csub="Custom" Cversion="1.0.0" Capiversion="1.0.0" custom="1">
      <description>Virtual I/O custom implementation template</description>
      <files>
        <file category="sourceC" name="CMSIS/Driver/VIO/Source/vio.c" attr="template" select="Virtual I/O"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="VIO" Csub="Virtual" Cversion="1.0.0" Capiversion="1.0.0">
      <description>Virtual I/O implementation using memory only</description>
      <files>
        <file category="sourceC" name="CMSIS/Driver/VIO/Source/vio_memory.c"/>
      </files>
    </component>

    <!-- Virtual Streaming custom component -->
    <component Cclass="CMSIS Driver" Cgroup="vStream" Csub="Custom" Cversion="1.0.0" Capiversion="1.0.0" custom="1">
      <description>Virtual Streaming interface Driver custom implementation template</description>
      <files>
        <file category="sourceC" name="CMSIS/Driver/vStream/Template/vstream.c" attr="template" select="vStream Driver"/>
      </files>
    </component>

  </components>

</package>
