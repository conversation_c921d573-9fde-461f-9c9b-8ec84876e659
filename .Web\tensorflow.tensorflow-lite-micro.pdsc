<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.6" 
  xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>tensorflow</vendor>
  <name>tensorflow-lite-micro</name>
  <description overview="Documentation/README.md">LiteRT, formerly known as TensorFlow Lite, is Google's high-performance runtime for on-device AI.</description>

  <url>https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.25.2/</url>
  <license>LICENSE</license>
  <repository type="git">https://github.com/MDK-Packs/tensorflow-pack.git</repository>
  <releases>
    <release version="1.25.2" date="2025-05-09"> Latest release. </release>
    <release version="1.25.2-rc4" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.25.2-rc4/tensorflow.tensorflow-lite-micro.1.25.2-rc4.pack" date="2025-03-26">Based on the latest release from : https://gitlab.arm.com/artificial-intelligence/ethos-u/ethos-u

Introduces Reference Applications</release>
<release version="1.24.11" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.24.11/tensorflow.tensorflow-lite-micro.1.24.11.pack" date="2025-01-09">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/tags/24.11/24.11.json</release>
<release version="1.24.8" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.24.8/tensorflow.tensorflow-lite-micro.1.24.8.pack" date="2024-10-01">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/tags/24.08/24.08.json
</release>
<release version="1.24.2-rc6" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.24.2-rc6/tensorflow.tensorflow-lite-micro.1.24.2-rc6.pack" date="2024-05-08">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u-core-software/+/refs/tags/24.02</release>
<release version="1.23.2" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.23.2/tensorflow.tensorflow-lite-micro.1.23.2.pack" date="2023-06-15">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u-core-software/+/refs/tags/23.02</release>
<release version="1.22.8" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.8/tensorflow.tensorflow-lite-micro.1.22.8.pack" date="2022-12-07">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u-core-software/+/refs/tags/22.08</release>
<release version="1.22.5" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.5/tensorflow.tensorflow-lite-micro.1.22.5.pack" date="2022-10-18">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/heads/master/22.05.json</release>
<release version="1.22.5-rc4" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.5-rc4/tensorflow.tensorflow-lite-micro.1.22.5-rc4.pack" date="2022-06-13">Release candidate for pack based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/heads/master/22.05.json
</release>
<release version="1.22.2" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.2/tensorflow.tensorflow-lite-micro.1.22.2.pack" date="2022-06-07">First release synchronized with released versions from https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/ (rev 22.02)</release>
<release version="0.4.0" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/0.4.0/tensorflow.tensorflow-lite-micro.0.4.0.pack" date="2021-09-30">First release listed on pack repository</release>

  </releases>

  <requirements>
    <packages>
      <package name="CMSIS-NN" vendor="ARM" version="7.0.0:7.99.99"/>
      <package name="gemmlowp" vendor="tensorflow" version="1.25.2:1.25.2"/>
      <package name="flatbuffers" vendor="tensorflow" version="1.25.2:1.25.2"/>
      <package name="kissfft" vendor="tensorflow" version="1.25.2:1.25.2"/>
      <package name="ruy" vendor="tensorflow" version="1.25.2:1.25.2"/>
      <package name="ethos-u-core-driver" vendor="ARM" version="1.25.2:1.25.2"/>
    </packages>
  </requirements>

  <taxonomy>
    <description Cclass="Machine Learning">Software Components for Machine Learning</description>
  </taxonomy>

  <conditions>
    <condition id="CMSIS-NN">
        <require condition="Kernel Utils"/>
        <require condition="3rd Party"/>
        <require Cclass="CMSIS" Cgroup="NN Lib"/>
    </condition>
    <condition id="Reference">
        <require condition="Kernel Utils"/>
        <require condition="3rd Party"/>       
    </condition>
    <condition id="Ethos-U">
        <require condition="Kernel Utils"/>
        <require condition="3rd Party"/>
        <require Cclass="Machine Learning" Cgroup="NPU Support" Csub="Ethos-U Driver"/>
    </condition>
    <condition id="Kernel Utils">
        <require Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Kernel Utils"/>
    </condition>
    <condition id="3rd Party">
      <require Cclass="Data Processing" Cgroup="Math" Csub="kissfft" Cvariant="tensorflow"/>
      <require Cclass="Data Processing" Cgroup="Math" Csub="ruy" Cvariant="tensorflow"/>
      <require Cclass="Data Processing" Cgroup="Math" Csub="gemmlowp fixed-point" Cvariant="tensorflow"/>
      <require Cclass="Data Exchange" Cgroup="Serialization" Csub="flatbuffers" Cvariant="tensorflow"/>
    </condition>    
  </conditions>

  <components>
    <component Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Kernel" Cvariant="Reference" Cversion="1.25.2" condition="Reference">
      <description>TensorFlow Lite Micro Library</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_ML_TF_LITE     /* TF */
      </RTE_Components_h>
      <Pre_Include_Global_h>
        // enabling global pre includes 
        #define TF_LITE_STATIC_MEMORY 1
      </Pre_Include_Global_h>
      <files>
                <file category="sourceCpp" name="tensorflow/lite/kernels/internal/runtime_shape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/tensor_ctypes.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logistic.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/dequantize.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/unpack.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_helpers.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/l2norm.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_log.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/neg.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/circular_buffer_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/kernel_runner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_resource_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/activations_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/expand_dims.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/gather_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/prelu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/assign_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/hexdump.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/non_persistent_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/circular_buffer.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/squared_difference.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/concatenation.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depthwise_conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/squeeze.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reshape_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/softmax_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/shape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mul.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/decompress.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/read_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reshape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/mock_micro_graph.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logistic_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/detection_postprocess.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/sub_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_to_space_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/slice.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/single_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logical.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor_div.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/linear_memory_planner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/non_persistent_buffer_planner_shim.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/dequantize_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/gather.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/quantization_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/div.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fully_connected.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_op_resolver.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/leaky_relu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/unidirectional_sequence_lstm.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/api/flatbuffer_conversions.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/space_to_depth.cpp"/> 
        <file category="sourceCpp" name="tensorflow/compiler/mlir/lite/core/api/error_reporter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/log_softmax.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/select.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depth_to_space.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/maximum_minimum.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/greedy_memory_planner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/zeros_like.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/conv_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/l2_pool_2d.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/kernel_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/split_v.cpp"/> 
        <file category="sourceCpp" name="tensorflow/compiler/mlir/lite/schema/schema_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reduce_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mirror_pad.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/reference/portable_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/hard_swish_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/exp.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/tflite_bridge/micro_error_reporter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logical_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/test_helpers.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/strided_slice_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/arg_min_max.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/sub.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/resize_bilinear.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/transpose_conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depthwise_conv_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cast.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_allocation_info.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/flatbuffer_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mul_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/transpose.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/leaky_relu_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add_n.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/micro_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/persistent_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/reference/comparisons.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/space_to_batch_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/recording_micro_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/lstm_eval.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/elu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reduce.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/hard_swish.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/elementwise.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/call_once.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pooling_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/ethosu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/activations.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/comparisons.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/strided_slice.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cumsum.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/svdf_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fully_connected_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/if.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/softmax.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/c/common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/broadcast_to.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/split.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/round.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/test_helper_custom_ops.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/svdf.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/broadcast_args.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/ceil.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/lstm_eval_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pad.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/embedding_lookup.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/api/tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/fake_micro_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pack.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/resize_nearest_neighbor.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fill.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/var_handle.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/recording_single_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/while.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/tflite_bridge/flatbuffer_conversions_bridge.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_matmul_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/quantize_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_matmul.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor_mod.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pooling.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/portable_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_profiler.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/prelu_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/tanh.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/decompress_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/quantize.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter_graph.cpp"/> 
        <file category="include" name="tensorflow/lite/schema/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/optimized/"/> 
        <file category="include" name="tensorflow/lite/core/c/"/> 
        <file category="include" name="tensorflow/lite/micro/kernels/"/> 
        <file category="include" name="tensorflow/lite/micro/memory_planner/"/> 
        <file category="include" name="tensorflow/lite/micro/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/kernels/internal/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/reference/"/> 
        <file category="include" name="tensorflow/lite/micro/cortex_m_generic/"/> 
        <file category="include" name="tensorflow/lite/core/"/> 
        <file category="include" name="tensorflow/lite/"/> 
        <file category="include" name="tensorflow/lite/c/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/core/c/"/> 
        <file category="include" name="tensorflow/lite/core/api/"/> 
        <file category="include" name="tensorflow/lite/micro/arena_allocator/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/schema/"/> 
        <file category="include" name="tensorflow/lite/kernels/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/core/api/"/> 
        <file category="include" name="tensorflow/lite/micro/tflite_bridge/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/reference/integer_ops/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/"/> 

        <file category="include" name="./"/>
      </files>
    </component>
    <component Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Kernel" Cvariant="CMSIS-NN" Cversion="1.25.2" condition="CMSIS-NN" >
      <description>TensorFlow Lite Micro Library</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_ML_TF_LITE     /* TF */
      </RTE_Components_h>
      <Pre_Include_Global_h>
        // enabling global pre includes 
        #define TF_LITE_STATIC_MEMORY 1
        #define CMSIS_NN
      </Pre_Include_Global_h>
      <files>
                <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/add.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/runtime_shape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/tensor_ctypes.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logistic.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/dequantize.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/unpack.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/depthwise_conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_helpers.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/l2norm.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_log.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/neg.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/circular_buffer_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/kernel_runner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_resource_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/activations_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/expand_dims.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/gather_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/prelu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/maximum_minimum.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/assign_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/hexdump.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/non_persistent_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/circular_buffer.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/squared_difference.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/concatenation.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/transpose_conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/squeeze.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reshape_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/batch_matmul.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/softmax_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/shape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/unidirectional_sequence_lstm.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/mul.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/decompress.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/read_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reshape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/mock_micro_graph.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logistic_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/detection_postprocess.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/sub_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_to_space_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/slice.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/single_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logical.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor_div.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/linear_memory_planner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/fully_connected.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/non_persistent_buffer_planner_shim.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/dequantize_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/gather.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/quantization_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/div.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_op_resolver.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/leaky_relu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/api/flatbuffer_conversions.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/space_to_depth.cpp"/> 
        <file category="sourceCpp" name="tensorflow/compiler/mlir/lite/core/api/error_reporter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/log_softmax.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/select.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depth_to_space.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/greedy_memory_planner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/zeros_like.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/conv_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/l2_pool_2d.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/svdf.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/kernel_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/split_v.cpp"/> 
        <file category="sourceCpp" name="tensorflow/compiler/mlir/lite/schema/schema_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reduce_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mirror_pad.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/reference/portable_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/hard_swish_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/exp.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/tflite_bridge/micro_error_reporter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logical_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/test_helpers.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/strided_slice_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/arg_min_max.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/sub.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/resize_bilinear.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depthwise_conv_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cast.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_allocation_info.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/flatbuffer_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mul_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/transpose.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/leaky_relu_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add_n.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/micro_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/persistent_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/reference/comparisons.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/space_to_batch_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/recording_micro_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/lstm_eval.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/elu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reduce.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/hard_swish.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/elementwise.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/call_once.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pooling_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/ethosu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/pooling.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/activations.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/comparisons.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/strided_slice.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cumsum.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/svdf_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fully_connected_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/if.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/c/common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/broadcast_to.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/split.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/round.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/test_helper_custom_ops.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/broadcast_args.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/ceil.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/lstm_eval_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pad.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/embedding_lookup.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/api/tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/fake_micro_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pack.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/resize_nearest_neighbor.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fill.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/var_handle.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/recording_single_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/softmax.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/while.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/tflite_bridge/flatbuffer_conversions_bridge.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_matmul_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/quantize_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor_mod.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/portable_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_profiler.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/prelu_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/tanh.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/decompress_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/quantize.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter_graph.cpp"/> 
        <file category="include" name="tensorflow/lite/schema/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/optimized/"/> 
        <file category="include" name="tensorflow/lite/core/c/"/> 
        <file category="include" name="tensorflow/lite/micro/kernels/"/> 
        <file category="include" name="tensorflow/lite/micro/memory_planner/"/> 
        <file category="include" name="tensorflow/lite/micro/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/kernels/internal/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/reference/"/> 
        <file category="include" name="tensorflow/lite/micro/cortex_m_generic/"/> 
        <file category="include" name="tensorflow/lite/core/"/> 
        <file category="include" name="tensorflow/lite/"/> 
        <file category="include" name="tensorflow/lite/c/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/core/c/"/> 
        <file category="include" name="tensorflow/lite/core/api/"/> 
        <file category="include" name="tensorflow/lite/micro/arena_allocator/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/schema/"/> 
        <file category="include" name="tensorflow/lite/kernels/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/core/api/"/> 
        <file category="include" name="tensorflow/lite/micro/tflite_bridge/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/reference/integer_ops/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/"/> 

        <file category="include" name="./"/>
      </files>
    </component>
    <component Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Kernel" Cvariant="Ethos-U" Cversion="1.25.2" condition="Ethos-U">
      <description>TensorFlow Lite Micro Library</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_ML_TF_LITE     /* TF */
      </RTE_Components_h>
      <Pre_Include_Global_h>
        // enabling global pre includes 
        #define TF_LITE_STATIC_MEMORY 1
        #define TF_LITE_DISABLE_X86_NEON 1
        //#define TF_LITE_STRIP_ERROR_STRINGS 1
        #define CMSIS_NN
      </Pre_Include_Global_h>
      <files>
                 <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/add.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/runtime_shape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/tensor_ctypes.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logistic.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/dequantize.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/unpack.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/depthwise_conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_helpers.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/l2norm.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_log.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/neg.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/circular_buffer_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/kernel_runner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_resource_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/activations_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/expand_dims.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/gather_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/prelu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/maximum_minimum.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/assign_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/hexdump.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/non_persistent_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/circular_buffer.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/squared_difference.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/concatenation.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/transpose_conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/squeeze.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reshape_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/batch_matmul.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/softmax_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/shape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/unidirectional_sequence_lstm.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/mul.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/decompress.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/read_variable.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reshape.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/mock_micro_graph.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logistic_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/detection_postprocess.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/sub_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/ethos_u/ethosu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_to_space_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/slice.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/single_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logical.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor_div.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/linear_memory_planner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/fully_connected.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/non_persistent_buffer_planner_shim.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/dequantize_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/gather.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/quantization_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/div.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_op_resolver.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/leaky_relu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/api/flatbuffer_conversions.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/space_to_depth.cpp"/> 
        <file category="sourceCpp" name="tensorflow/compiler/mlir/lite/core/api/error_reporter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/log_softmax.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/select.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depth_to_space.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/memory_planner/greedy_memory_planner.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/zeros_like.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/conv_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/l2_pool_2d.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/svdf.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/kernel_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/split_v.cpp"/> 
        <file category="sourceCpp" name="tensorflow/compiler/mlir/lite/schema/schema_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reduce_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mirror_pad.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/reference/portable_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/hard_swish_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/exp.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/tflite_bridge/micro_error_reporter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/logical_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/test_helpers.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/strided_slice_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/arg_min_max.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/sub.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/resize_bilinear.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/depthwise_conv_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cast.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_allocation_info.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/flatbuffer_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/mul_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/transpose.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/leaky_relu_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/add_n.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/micro_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/persistent_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/reference/comparisons.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/space_to_batch_nd.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/recording_micro_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/lstm_eval.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/elu.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/reduce.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/hard_swish.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/elementwise.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/call_once.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pooling_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/pooling.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/activations.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/comparisons.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/strided_slice.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cumsum.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/svdf_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fully_connected_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/if.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/c/common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/broadcast_to.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/split.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/round.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/test_helper_custom_ops.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/broadcast_args.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/ceil.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/lstm_eval_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pad.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/embedding_lookup.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/core/api/tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/fake_micro_context.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/pack.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/resize_nearest_neighbor.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/fill.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/var_handle.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/arena_allocator/recording_single_arena_buffer_allocator.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/conv.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/cmsis_nn/softmax.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/while.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/tflite_bridge/flatbuffer_conversions_bridge.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/batch_matmul_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/quantize_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/floor_mod.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/kernels/internal/portable_tensor_utils.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_profiler.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/prelu_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/tanh.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/decompress_common.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/kernels/quantize.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/micro_interpreter_graph.cpp"/> 
        <file category="include" name="tensorflow/lite/schema/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/optimized/"/> 
        <file category="include" name="tensorflow/lite/core/c/"/> 
        <file category="include" name="tensorflow/lite/micro/kernels/"/> 
        <file category="include" name="tensorflow/lite/micro/memory_planner/"/> 
        <file category="include" name="tensorflow/lite/micro/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/kernels/internal/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/reference/"/> 
        <file category="include" name="tensorflow/lite/micro/cortex_m_generic/"/> 
        <file category="include" name="tensorflow/lite/core/"/> 
        <file category="include" name="tensorflow/lite/"/> 
        <file category="include" name="tensorflow/lite/c/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/core/c/"/> 
        <file category="include" name="tensorflow/lite/core/api/"/> 
        <file category="include" name="tensorflow/lite/micro/arena_allocator/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/schema/"/> 
        <file category="include" name="tensorflow/lite/kernels/"/> 
        <file category="include" name="tensorflow/compiler/mlir/lite/core/api/"/> 
        <file category="include" name="tensorflow/lite/micro/tflite_bridge/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/reference/integer_ops/"/> 
        <file category="include" name="tensorflow/lite/kernels/internal/"/> 

        <file category="include" name="./"/>
      </files>
    </component>
     <!-- this component can be merged into the Kernel components, when duplicate module name issue is solved elsewhere -->
    <component Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Kernel Utils" Cversion="1.25.2">
      <description>TensorFlow Lite Micro Library - Kernel Utilities</description>
      <files>
        <file category="sourceCpp" name="tensorflow/lite/kernels/kernel_util.cpp"/> 
        <file category="sourceCpp" name="tensorflow/lite/micro/system_setup.cpp" version="1.25.2" attr="config"/>
        <file category="sourceCpp" name="tensorflow/lite/micro/cortex_m_generic/micro_time.cpp" version="1.25.2" attr="config"/>
        <file category="sourceCpp" name="tensorflow/lite/micro/cortex_m_generic/debug_log.cpp" version="1.25.2" attr="config"/>
        
      </files>
    </component>
    <component Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Testing" Cversion="1.25.2">
      <description>TensorFlow Lite Micro Test Utilities</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_ML_TF_LITE     /* TF */
      </RTE_Components_h>
      <files>
                <file category="include" name="tensorflow/lite/micro/testing/"/> 

        <file category="include" name="./"/>
      </files>
    </component>
    <component Cclass="Machine Learning" Cgroup="TensorFlow" Csub="Signal Library" Cvariant="Reference" Cversion="1.25.2">
      <description>TensorFlow Lite Micro Signal Library</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_ML_TF_LITE     /* TF */
      </RTE_Components_h>
      <files>
                <file category="sourceCpp" name="signal/micro/kernels/window.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/filter_bank_square_root_common.cpp"/> 
        <file category="sourceCpp" name="signal/src/energy.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/fft_auto_scale_common.cpp"/> 
        <file category="sourceCpp" name="signal/src/kiss_fft_wrappers/kiss_fft_float.cpp"/> 
        <file category="sourceCpp" name="signal/src/kiss_fft_wrappers/kiss_fft_int32.cpp"/> 
        <file category="sourceCpp" name="signal/src/square_root_32.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/framer.cpp"/> 
        <file category="sourceCpp" name="signal/src/irfft_float.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/rfft.cpp"/> 
        <file category="sourceCpp" name="signal/src/pcan_argc_fixed.cpp"/> 
        <file category="sourceCpp" name="signal/src/max_abs.cpp"/> 
        <file category="sourceCpp" name="signal/src/msb_64.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/energy.cpp"/> 
        <file category="sourceCpp" name="signal/src/window.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/filter_bank_log.cpp"/> 
        <file category="sourceCpp" name="signal/src/filter_bank_square_root.cpp"/> 
        <file category="sourceCpp" name="signal/src/rfft_int16.cpp"/> 
        <file category="sourceCpp" name="signal/src/filter_bank.cpp"/> 
        <file category="sourceCpp" name="signal/src/irfft_int32.cpp"/> 
        <file category="sourceCpp" name="signal/micro/kernels/overlap_add.cpp"/> 
        <file category="include" name="signal/src/"/> 
        <file category="include" name="signal/src/kiss_fft_wrappers/"/> 
        <file category="include" name="signal/micro/kernels/"/> 

        <file category="include" name="./"/>
      </files>
    </component>
  </components>

  <examples>
    <example name="Tensorflow LiteRT HelloWorld" doc="README.md" folder="examples/TFLiteRT_HelloWorld">
      <description>TensorFlow LiteRT HelloWorld Example</description>
      <project>
        <environment name="csolution" load="TFLiteRT_HelloWorld.csolution.yml"/>
      </project>
    </example>
  </examples>

</package>
