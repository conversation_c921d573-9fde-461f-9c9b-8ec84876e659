#ifndef MAIN_H
#define MAIN_H

#endif

#include "stdint.h"
#include "stm32f10x.h"
#include "lcd.h"

							/*For AD Convertion*/
//#define OVERCURRENT 	0x424 //22A		
#define OVERCURRENT 	22							
//#define NORMALCURRENT 	0x2D3 //15A	
#define NORMALCURRENT 15							
		


extern uint16_t VREF;           //Reference voltage for ADC in mV
extern const float VREF_4095;   //VREF/4095	(3400/4095)

extern uint16_t ADC_Result_CH0; //AD Convertion of CH0
extern uint16_t ADC_10Results_CH0[];//To store 10 ADC results

extern uint16_t ADC_Result_CH1; //AD Convertion of CH1
extern uint16_t ADC_10Results_CH1;//To store 10 ADC results

extern float ADC_CH0_mV;       //ADC Result in mV of CH0 - Current Sensor
extern uint16_t ADC_Int_CH0;         //Integer part of ADC Result in mV of CH0 - Current Sensor
extern uint16_t ADC_Frac_CH0;        //Fraction part of ADC Result in mV of CH0 - Current Sensor
extern uint8_t ADC_Char_CH0[];    //ADC Result in Chars of CH0 - Current Sensor
extern uint16_t Current;          //To store MSB and LSB of Current value
extern uint8_t CurrentCharBuffer[];//To store chars of MSB and LSB of Current value

extern float ADC_CH1_mV;       //ADC Result in mV of CH1 - Voltage Sensor
extern uint16_t ADC_Int_CH1;      //Integer part of ADC Result in mV of CH1 - Voltage Sensor
extern uint16_t ADC_Frac_CH1;     //Fraction part of ADC Result in mV of CH1 - Voltage Sensor
extern uint8_t ADC_Char_CH1[];  //ADC Result in Chars of CH1 - Voltage Sensor
extern uint16_t Voltage;          //To store MSB and LSB of Voltage value
extern uint8_t VoltageCharBuffer[];//To store chars of MSB and LSB of Voltage value

extern float OUT_Current;
extern uint16_t OUT_Current_Int;
extern uint16_t OUT_Current_Frac; 

extern float OUT_Voltage;
extern uint16_t OUT_Voltage_Int;
extern uint16_t OUT_Voltage_Frac;

extern void ADC1_Config(void);
extern uint16_t ADC1_Connvert(uint8_t );
extern void GetAdc_CH0CH1(void);
extern void AdcToLcd(void);
extern void Current_To_LCD(uint8_t *buf, uint8_t *lcd_buf);

void Signal_3p1D(void);//...-
void Signal_1D1p1D(void);//-.-							 
void Signal_Beep(uint8_t);
void Signal_OK(void);
void Signal_NO(void);

void Send_To_Main(uint8_t* string, int len);//Sends len symbols to UART1, 

#define CW 0 //rotate direction for M3 - M7
#define CCW 1

#define M3_Forward 1
#define M3_Back 2

#define M4_Forward 1
#define M4_Back 2

#define M5_Forward 1
#define M5_Back 2

#define M6_Forward 1 //CW
#define M6_Back 1 //CCW

#define M7_Forward 1
#define M7_Back 2


extern uint16_t Encoders_Angele;
extern uint16_t Encoder1_Position;
extern uint16_t Encoder2_Position;

extern uint16_t M1_Angele;
extern uint16_t M2_Angele;
extern uint16_t Rotate_Angele;

//For Errors:
extern uint8_t M7_Error;
extern uint8_t SensorPositionError;
//uint8_t 

void GetEncoder_1_Angele(void);
void GetEncoder_2_Angele(void);

void RotateM1_D14_Position(void);
void RotateM2_D13_Position(void);

void Rotate_M1_CW(uint16_t);
void Rotate_M2_CW(uint16_t);

void Rotate_M1_CCW(uint16_t);
void Rotate_M2_CCW(uint16_t);

void Rotate_M3(uint8_t);//Forward or Backward

void Rotate_M4(uint8_t);

void Rotate_M5(uint8_t);

void Rotate_M6(uint8_t);

void Rotate_M6_Step(uint8_t);

void Rotate_M7(uint8_t);

void LoadUp(void);

// Additional function declarations for JSON commands
void Return_All_Motors_Home(void);
void Ready_Command(void);
void Show_About_Page(void);
void Play_Piano_Melody(void);
void Rotate_M6_Step_Safe(uint8_t direction);
void Test_All_Motors_Max_Speed(void);
void Move_Motor(uint8_t motor_num, int direction, uint16_t steps);
void Set_Motor_Position(uint8_t motor_num, uint16_t angle);
// Set_Motor_Speed is declared in motor_unified_config.h
void Load_Embedded_Config(void);
void Show_Current_Config(void);
void AutoCalibrate_All_Motors(void);



