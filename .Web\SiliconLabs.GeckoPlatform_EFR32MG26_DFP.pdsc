<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32MG26_DFP</name>
  <description>Silicon Labs EFR32MG26 Mighty Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32MG26</keyword>
    <keyword>EFR32</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32MG26 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
- 32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in QFN packaging;- Integrated PA with up to 19.5 dBm transmit power;- Energy-efficient radio core with low active and sleep currents;- Secure Vault;- AI/ML Hardware Accelerator;- Up to 3200 kB of flash and 256 kB of RAM&#xD;&#xA;
      </description>

      <subFamily DsubFamily="EFR32MG26B211">
        <!-- *************************  Device 'EFR32MG26B211F2048IM68'  ***************************** -->
        <device Dname="EFR32MG26B211F2048IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B211F2048IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B211F2048IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00200000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00200000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00200000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B211F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B211F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B211F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B211F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B221">
        <!-- *************************  Device 'EFR32MG26B221F2048IM68'  ***************************** -->
        <device Dname="EFR32MG26B221F2048IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B221F2048IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B221F2048IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00200000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00200000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00200000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B221F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B221F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B221F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B221F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B311">
        <!-- *************************  Device 'EFR32MG26B311F3200IL136'  ***************************** -->
        <device Dname="EFR32MG26B311F3200IL136">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B311F3200IL136"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B311F3200IL136.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B410">
        <!-- *************************  Device 'EFR32MG26B410F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B410F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B410F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B410F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B410F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B410F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B410F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B410F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B411">
        <!-- *************************  Device 'EFR32MG26B411F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B411F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B411F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B411F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B411F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B411F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B411F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B411F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B420">
        <!-- *************************  Device 'EFR32MG26B420F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B420F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B420F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B420F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B420F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B420F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B420F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B420F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B421">
        <!-- *************************  Device 'EFR32MG26B421F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B421F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B421F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B421F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B421F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B421F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B421F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B421F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B510">
        <!-- *************************  Device 'EFR32MG26B510F3200IL136'  ***************************** -->
        <device Dname="EFR32MG26B510F3200IL136">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B510F3200IL136"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B510F3200IL136.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B510F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B510F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B510F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B510F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B510F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B510F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B510F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B510F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B511">
        <!-- *************************  Device 'EFR32MG26B511F3200IL136'  ***************************** -->
        <device Dname="EFR32MG26B511F3200IL136">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B511F3200IL136"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B511F3200IL136.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B511F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B511F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B511F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B511F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B511F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B511F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B511F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B511F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B520">
        <!-- *************************  Device 'EFR32MG26B520F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B520F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B520F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B520F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B520F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B520F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B520F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B520F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B521">
        <!-- *************************  Device 'EFR32MG26B521F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B521F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B521F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B521F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG26B521F3200IM68'  ***************************** -->
        <device Dname="EFR32MG26B521F3200IM68">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B521F3200IM68"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B521F3200IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG26B610">
        <!-- *************************  Device 'EFR32MG26B610F3200IM48'  ***************************** -->
        <device Dname="EFR32MG26B610F3200IM48">
          <compile header="Device/SiliconLabs/EFR32MG26/Include/em_device.h"  define="EFR32MG26B610F3200IM48"/>
          <debug      svd="SVD/EFR32MG26/EFR32MG26B610F3200IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00060000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32MG26">
      <description>Silicon Labs EFR32MG26 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32MG26*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32MG26">
      <description>System Startup for Silicon Labs EFR32MG26 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32MG26/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32MG26/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32MG26/Source/system_efr32mg26.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
