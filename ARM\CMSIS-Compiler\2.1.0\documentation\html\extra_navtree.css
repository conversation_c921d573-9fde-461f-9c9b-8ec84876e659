:root {
  --arm_light_blue: #00C1DE;
  --arm_blue: #11809F;
  --arm_blue1: #0091BD;
  --arm_dark_blue: #002B49;
  --arm_light_gray: #E5ECEB;
  --arm_light_gray1: #EFF5F4;
  --arm_light_gray2: #EBEBEB;
  --arm_light_gray3: #F7F7F7;
  --arm_dark_gray: #7D868C;
  --arm_black: #333E48;
  --arm_orange: #FF6B00;
}

#nav-tree ul {
  list-style:none outside none;
  margin:0px;
  padding:0px;
}

#nav-tree li {
  white-space:nowrap;
  margin:0px;
  padding:0px;
}

#nav-tree .plus {
  margin:0px;
}

#nav-tree .selected {
  background-image: none;
  background-repeat:no-repeat;
  text-shadow: none;
  border: 1.5px solid var(--arm_blue);
  border-left-width: 5px;
  margin-left:-10px; /*correction to place selection border on the edge screen edge */
}

#nav-tree img {
  margin:0px;
  padding:0px;
  border:0px;
  vertical-align: middle;
}

#nav-tree a {
  color: var(--nav-text-normal-color);
  text-decoration:none;
  padding:0px;
  padding-left:0px;
  margin:0px;
  outline:none;
}

#nav-tree .label {
  margin:0px;
  padding:0px;
  font: 15px  Lato, Calibri, sans-serif;
}

#nav-tree .label:hover {
  color: var(--arm_orange);
}

#nav-tree .label a {
  padding-left:6px;
  line-height: 30px;
}

#nav-tree .selected a {
  font-weight: bold;
}

/*correction for the larger box border on the left (10px-5px) */
#nav-tree .selected .arrow {
  margin-left:5px;
}

#nav-tree .children_ul {
  margin:0px;
  padding:0px;
}

#nav-tree {
  padding: 0px 0px;
  padding-left: 10px;  /*correction to add space before the first arrow in nav-tree */
  overflow:auto;
}

#doc-content {
  overflow:auto;
  display:block;
  padding:0px;
  margin:0px;
  -webkit-overflow-scrolling : touch; /* iOS 5+ */
}

#side-nav {
  padding:0 2px 0 0;
  margin: 0px;
  display:block;
  position: absolute;
  left: 0px;
  width: 330px;
}

.ui-resizable .ui-resizable-handle {
  display:block;
}

.ui-resizable-e {
  background-color: var(--nav-splitbar-color);
  background-repeat:repeat-y;
  background-attachment: scroll;
  cursor:ew-resize;
  height:100%;
  right:0;
  top:0;
  width:1px;
}

.ui-resizable-handle {
  display:none;
  font-size:0.1px;
  position:absolute;
  z-index:1;
}

#nav-tree-contents {
  margin: 6px 0px 0px 0px;
}

#nav-tree {

  -webkit-overflow-scrolling : touch; /* iOS 5+ */
}

#nav-sync {
  position:absolute;
  top:5px;
  right:24px;
  z-index:0;
  opacity:0;
}

#nav-sync img {
  opacity:0;
}

#nav-sync img:hover {
  opacity:0;
}

#nav-tree a:hover {
  color: var(--arm_orange);
}

@media print
{
  #nav-tree { display: none; }
  div.ui-resizable-handle { display: none; position: relative; }
}

