<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss/shared/docs/product/micro/device-family-Pack/</url>
  <name>TXZ4-M4G_DFP</name>
  <description>Toshiba TXZ4 Series TMPM4G Group Device Support</description>

  <releases>
    <release version="1.0.1" date="2018-05-22">
      Correction for SOFTWARE_LICENSE_AGREEMENT.
    </release>
    <release version="1.0.0" date="2018-05-18">
      First Release version of TXZ4 Series TMPM4G Group Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4</keyword>
    <keyword>TXZ4</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4 Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
The TXZ4 microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>
      <!-- ************************  Subfamily 'TMPM4G9x'  **************************** -->
      <subFamily DsubFamily="M4G">

        <!-- ***********************  Device 'TMPM4G9F15x'  ************************* -->
        <device Dname="TMPM4G9F15FG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"   RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4G9F15XBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4G9F10x'  ************************* -->
        <device Dname="TMPM4G9F10FG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4G9F10XBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4G9FEx'  ************************* -->
        <device Dname="TMPM4G9FEFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_768.FLM" start="0x00000000" size="0x000C0000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4G9FEXBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_768.FLM" start="0x00000000" size="0x000C0000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4G9FDx'  ************************* -->
        <device Dname="TMPM4G9FDFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4G9FDXBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G9.h" define="TMPM4G9"/>
          <debug svd="SVD/M4G9.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>

      <!-- ************************  Subfamily 'TMPM4G8x'  **************************** -->

        <!-- ***********************  Device 'TMPM4G8F15x'  ************************* -->
        <device Dname="TMPM4G8F15FG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4G8F15XBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4G8F10x'  ************************* -->
        <device Dname="TMPM4G8F10FG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4G8F10XBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4G8FEx'  ************************* -->
        <device Dname="TMPM4G8FEFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_768.FLM" start="0x00000000" size="0x000C0000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4G8FEXBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_768.FLM" start="0x00000000" size="0x000C0000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4G8FDx'  ************************* -->
        <device Dname="TMPM4G8FDFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4G8FDXBG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G8.h" define="TMPM4G8"/>
          <debug svd="SVD/M4G8.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
      <!-- ************************  Subfamily 'TMPM4G7x'  **************************** -->

        <!-- ***********************  Device 'TMPM4G7F10x'  ************************* -->
        <device Dname="TMPM4G7F10FG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G7.h" define="TMPM4G7"/>
          <debug svd="SVD/M4G7.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="128" name="LQFP 128 14x14x0.4"/>
        </device>
        <!-- ***********************  Device 'TMPM4G7FEx'  ************************* -->
        <device Dname="TMPM4G7FEFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G7.h" define="TMPM4G7"/>
          <debug svd="SVD/M4G7.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_768.FLM" start="0x00000000" size="0x000C0000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="128" name="LQFP 128 14x14x0.4"/>
        </device>
        <!-- ***********************  Device 'TMPM4G7FDx'  ************************* -->
        <device Dname="TMPM4G7FDFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G7.h" define="TMPM4G7"/>
          <debug svd="SVD/M4G7.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="128" name="LQFP 128 14x14x0.4"/>
        </device>
      <!-- ************************  Subfamily 'TMPM4G6x'  **************************** -->

        <!-- ***********************  Device 'TMPM4G6F10x'  ************************* -->
        <device Dname="TMPM4G6F10FG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G6.h" define="TMPM4G6"/>
          <debug svd="SVD/M4G6.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
        <!-- ***********************  Device 'TMPM4G6FEx'  ************************* -->
        <device Dname="TMPM4G6FEFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G6.h" define="TMPM4G6"/>
          <debug svd="SVD/M4G6.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_768.FLM" start="0x00000000" size="0x000C0000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
        <!-- ***********************  Device 'TMPM4G6FDx'  ************************* -->
        <device Dname="TMPM4G6FDFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4G6.h" define="TMPM4G6"/>
          <debug svd="SVD/M4G6.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00020000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="14"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="TMPM4Gx Compiler">
      <accept condition="Compiler ARMCC"/>
      <accept condition="Compiler IAR"/>   
    </condition>

    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="TMPM4G9x CMSIS">
      <description>Toshiba TMPM4G9x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4G9*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4G8x CMSIS">
      <description>Toshiba TMPM4G8x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4G8*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4G7x CMSIS">
      <description>Toshiba TMPM4G7x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4G7*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4G6x CMSIS">
      <description>Toshiba TMPM4G6x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4G6*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>

  </conditions>

  <components>

    <!-- Startup TMPM4G9x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4G9x CMSIS">
      <description>System Startup for Toshiba TMPM4G9x Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4G9x      /* Device Startup for TMPM4G9x */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4G9.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4G9.s" attr="config" version="0.0.1" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="0.0.1" condition="TMPM4Gx Compiler"/>
      </files>
    </component>
    <!-- Startup TMPM4G8x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4G8x CMSIS">
      <description>System Startup for Toshiba TMPM4G8x Devices</description>
      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4G8.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4G8.s" attr="config" version="0.0.1" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="0.0.1" condition="TMPM4Gx Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4G7x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4G7x CMSIS">
      <description>System Startup for Toshiba TMPM4G7x Devices</description>
      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4G7.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4G7.s" attr="config" version="0.0.1" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="0.0.1" condition="TMPM4Gx Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4G6x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4G6x CMSIS">
      <description>System Startup for Toshiba TMPM4G6x Devices</description>
      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4G6.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4G6.s" attr="config" version="0.0.1" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="0.0.1" condition="TMPM4Gx Compiler"/>
      </files>
    </component>

  </components>

</package>
