<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>MCXC041_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MCXC041</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.01' date='2025-07-01'>
      NXP CMSIS Packs based on MCUXpresso SDK 25.06.00 with flashloader display name updated
    </release>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='MCXC041' Dvendor='NXP:11'>
      <description>
        The MCXC041 is a general purpose ultra-low-power MCU family, providing additional memory, communications and analog peripheral.
      </description>
      <device Dname='MCXC041'>
        <processor Dcore='Cortex-M0+' Dfpu='NO_FPU' Dmpu='NO_MPU' Dendian='Little-endian' Dclock='48000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/MCXC041/iar/MCXC041_flash.icf'/>
        </environment>
        <memory name='PROGRAM_FLASH' start='0x00000000' size='0x8000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x1ffffe00' size='0x0800' access='rw' default='1'/>
        <algorithm name='devices/MCXC041/arm/MK_P32_48MHZ_MCXC041.FLM' start='0x00000000' size='0x00008000' RAMstart='0x1FFFFE00' RAMsize='0x800' default='1'/>
        <debug svd='devices/MCXC041/MCXC041.xml'/>
        <variant Dvariant='MCXC041VFK'>
          <compile header='devices/MCXC041/fsl_device_registers.h' define='CPU_MCXC041VFK'/>
        </variant>
        <variant Dvariant='MCXC041VFG'>
          <compile header='devices/MCXC041/fsl_device_registers.h' define='CPU_MCXC041VFG'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.MCXC041.internal_condition'>
      <accept Dname='MCXC041VFG' Dvendor='NXP:11'/>
      <accept Dname='MCXC041VFK' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.device=MCXC041.internal_condition'>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.Legacy_flash_adapter.condition_id'>
      <require condition='allOf.driver.flash, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flash, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flash'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.ak4497_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.button.condition_id'>
      <require condition='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, device=MCXC041.internal_condition'>
      <require condition='anyOf.allOf=component.gpio_adapter, driver.gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.gpio_adapter, driver.gpio.internal_condition'>
      <accept condition='allOf.component.gpio_adapter, driver.gpio.internal_condition'/>
    </condition>
    <condition id='allOf.component.gpio_adapter, driver.gpio.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.codec_adapters.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.codec_i2c.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.cs42448_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.cs42888_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.da7212_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.flash_adapter.condition_id'>
      <require condition='allOf.anyOf=allOf=component.Legacy_flash_adapter, driver.flash, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.Legacy_flash_adapter, driver.flash, device=MCXC041.internal_condition'>
      <require condition='anyOf.allOf=component.Legacy_flash_adapter, driver.flash.internal_condition'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.Legacy_flash_adapter, driver.flash.internal_condition'>
      <accept condition='allOf.component.Legacy_flash_adapter, driver.flash.internal_condition'/>
    </condition>
    <condition id='allOf.component.Legacy_flash_adapter, driver.flash.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='Legacy_flash_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flash'/>
    </condition>
    <condition id='component.gpio_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.port, driver.gpio, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.port, driver.gpio, device=MCXC041.internal_condition'>
      <require condition='anyOf.driver.port.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.port.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='port'/>
    </condition>
    <condition id='component.i2c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.i2c, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.i2c, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.i2c_adapter, driver.i2c, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.i2c_adapter, driver.i2c, device=MCXC041.internal_condition'>
      <require condition='anyOf.allOf=component.i2c_adapter, driver.i2c.internal_condition'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.i2c_adapter, driver.i2c.internal_condition'>
      <accept condition='allOf.component.i2c_adapter, driver.i2c.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter, driver.i2c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.led.condition_id'>
      <require condition='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, driver.common, device=MCXC041.internal_condition'>
      <require condition='anyOf.allOf=component.gpio_adapter, driver.gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.log.condition_id'>
      <require condition='allOf.driver.common, utility.str, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.str, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console_lite.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console_lite, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console_lite, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.log.backend.ringbuffer.condition_id'>
      <require condition='allOf.component.log, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.lptmr_adapter.condition_id'>
      <require condition='allOf.driver.lptmr, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lptmr, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.lpuart_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, device=MCXC041.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.lpuart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MCXC041.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.pcm186x_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.pcm512x_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.pwm_tpm_adapter.condition_id'>
      <require condition='allOf.driver.tpm, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.tpm, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tpm'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=component.software_rng_adapter, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.software_rng_adapter, device=MCXC041.internal_condition'>
      <require condition='anyOf.component.software_rng_adapter.internal_condition'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.component.software_rng_adapter.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.rtc_adapter.condition_id'>
      <require condition='allOf.driver.rtc, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.rtc, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rtc'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MCXC041.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.spi_adapter, driver.spi, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.spi_adapter, driver.spi, device=MCXC041.internal_condition'>
      <require condition='anyOf.allOf=component.spi_adapter, driver.spi.internal_condition'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.spi_adapter, driver.spi.internal_condition'>
      <accept condition='allOf.component.spi_adapter, driver.spi.internal_condition'/>
    </condition>
    <condition id='allOf.component.spi_adapter, driver.spi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.component.lpuart_adapter, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpuart_adapter, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.sgtl_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.spi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.spi, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.spi, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='component.tfa9896_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.tfa9xxx_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lptmr_adapter, driver.lptmr, allOf=component.tpm_adapter, driver.tpm, component.lists, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lptmr_adapter, driver.lptmr, allOf=component.tpm_adapter, driver.tpm, component.lists, driver.common, device=MCXC041.internal_condition'>
      <require condition='anyOf.allOf=component.lptmr_adapter, driver.lptmr, allOf=component.tpm_adapter, driver.tpm.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lptmr_adapter, driver.lptmr, allOf=component.tpm_adapter, driver.tpm.internal_condition'>
      <accept condition='allOf.component.lptmr_adapter, driver.lptmr.internal_condition'/>
      <accept condition='allOf.component.tpm_adapter, driver.tpm.internal_condition'/>
    </condition>
    <condition id='allOf.component.lptmr_adapter, driver.lptmr.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr'/>
    </condition>
    <condition id='allOf.component.tpm_adapter, driver.tpm.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tpm_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tpm'/>
    </condition>
    <condition id='component.tpm_adapter.condition_id'>
      <require condition='allOf.driver.tpm, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.wm8524_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.wm8904_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.wm8960_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='component.wm8962_adapter.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=MCXC041.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='device_id.MCXC041.internal_condition'>
      <accept Dname='MCXC041VFG' Dvendor='NXP:11'/>
      <accept Dname='MCXC041VFK' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MCXC041.internal_condition'>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='device_ids.MCXC041.internal_condition'>
      <accept Dname='MCXC041VFG' Dvendor='NXP:11'/>
      <accept Dname='MCXC041VFK' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=MCXC041.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.MCXC041.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=MCXC041.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.MCXC041.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=MCXC041.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.MCXC041.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.component.lpuart_adapter, device_id=MCXC041, device.startup, driver.clock, driver.common, driver.gpio, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpuart_adapter, device_id=MCXC041, device.startup, driver.clock, driver.common, driver.gpio, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require condition='device_id.MCXC041.internal_condition'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='port'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='smc'/>
      <require condition='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <accept condition='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'/>
      <accept condition='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config'/>
    </condition>
    <condition id='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXC041_system'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXC041_header'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.adc16.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.ak4497.condition_id'>
      <require condition='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cmp.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_gpio.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.GPIO, device.RTE, driver.gpio, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.GPIO, device.RTE, driver.gpio, device_id=MCXC041.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='GPIO' Capiversion='1.0.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_i2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.i2c, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.i2c, device_id=MCXC041.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpuart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MCXC041, device.RTE, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MCXC041, device.RTE, device_id=MCXC041.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpuart, device_id=MCXC041.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpuart, device_id=MCXC041.internal_condition'>
      <accept condition='allOf.driver.lpuart, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpuart, device_id=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_spi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.spi, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.spi, device_id=MCXC041.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require condition='device_id.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.codec.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm0p.condition_id'>
      <require condition='cores.cm0p.internal_condition'/>
    </condition>
    <condition id='cores.cm0p.internal_condition'>
      <accept Dcore='Cortex-M0+'/>
    </condition>
    <condition id='driver.cop.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cs42448.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.cs42888.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.dialog7212.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.ektf2k.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.flash.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.ft3267.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.ft5406.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.ft6x06.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device=MCXC041.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.fxas21002cq.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'>
      <require condition='anyOf.driver.i2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.i2c.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='driver.fxls8974cf.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.fxos8700cq.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.gpio.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.gt911.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.htu21d.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.i2c.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.i2c_edma.condition_id'>
      <require condition='allOf.driver.i2c, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.i2c, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.icm42688p.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.ili9341.condition_id'>
      <require condition='allOf.driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.llwu.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.lptmr.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.lpuart.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.lsm6dso.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.max30101.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.mma8451q.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.mma8652fc.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.mx25r_flash.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.nmh1000.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.p3t1755.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.pcm186x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.pcm512x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.pf1550.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.pf3000.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.pf5020.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.pmc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.port.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.psp27801.condition_id'>
      <require condition='allOf.driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.rcm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.rtc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.sgtl5000.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.sim.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.smc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.spi.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.tfa9896.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx_hal.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.tma525b.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.tpm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.tsl2561.condition_id'>
      <require condition='allOf.anyOf=driver.i2c, driver.common, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.vref.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.wm8524.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.wm8904.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.wm8960.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='driver.wm8962.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.component.lpuart_adapter, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.notifier.condition_id'>
      <require condition='allOf.device=MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=MCXC041.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=MCXC041.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXC041.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=MCXC041.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='Legacy_flash_adapter' Cversion='1.0.0' condition='component.Legacy_flash_adapter.condition_id'>
      <description>Component Legacy_flash_adapter</description>
      <files>
        <file category='header' name='components/internal_flash/fsl_adapter_flash.h' projectpath='component/internal_flash'/>
        <file category='sourceC' name='components/internal_flash/fsl_adapter_flash.c' projectpath='component/internal_flash'/>
        <file category='include' name='components/internal_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter' Cversion='2.2.0' condition='component.ak4497_adapter.condition_id'>
      <description>Component ak4497 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.h' projectpath='codec/port/ak4497'/>
        <file category='sourceC' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.c' projectpath='codec/port/ak4497'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/ak4497/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='button' Cversion='1.0.0' condition='component.button.condition_id'>
      <description>Component button</description>
      <files>
        <file category='header' name='components/button/fsl_component_button.h' projectpath='component/button'/>
        <file category='sourceC' name='components/button/fsl_component_button.c' projectpath='component/button'/>
        <file category='include' name='components/button/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters' Cversion='2.2.0' condition='component.codec_adapters.condition_id'>
      <description>Component codec adapters for multi codec</description>
      <RTE_Components_h>
#ifndef CODEC_MULTI_ADAPTERS
#define CODEC_MULTI_ADAPTERS 1
#endif
</RTE_Components_h>
      <files>
        <file category='sourceC' name='components/codec/port/fsl_codec_adapter.c' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c' Cversion='2.1.0' condition='component.codec_i2c.condition_id'>
      <description>Component codec_i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/i2c/fsl_codec_i2c.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/i2c/fsl_codec_i2c.c' projectpath='codec'/>
        <file category='include' name='components/codec/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter' Cversion='2.2.1' condition='component.cs42448_adapter.condition_id'>
      <description>Component cs42448 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.h' projectpath='codec/port/cs42448'/>
        <file category='sourceC' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.c' projectpath='codec/port/cs42448'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42448/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter' Cversion='2.2.1' condition='component.cs42888_adapter.condition_id'>
      <description>Component cs42888 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.h' projectpath='codec/port/cs42888'/>
        <file category='sourceC' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.c' projectpath='codec/port/cs42888'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42888/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter' Cversion='2.2.0' condition='component.da7212_adapter.condition_id'>
      <description>Component da7212 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/da7212/fsl_codec_da7212_adapter.h' projectpath='codec/port/da7212'/>
        <file category='sourceC' name='components/codec/port/da7212/fsl_codec_da7212_adapter.c' projectpath='codec/port/da7212'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/da7212/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_adapter' Cversion='1.0.0' condition='component.flash_adapter.condition_id'>
      <description>Component flash_adapter</description>
      <files>
        <file category='doc' name='components/internal_flash/component.flash_adapter_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio_adapter' Cversion='1.0.1' condition='component.gpio_adapter.condition_id'>
      <description>Component gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter' Cversion='1.0.0' condition='component.i2c_adapter.condition_id'>
      <description>Component i2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_i2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='led' Cversion='1.0.0' condition='component.led.condition_id'>
      <description>Component led</description>
      <files>
        <file category='header' name='components/led/fsl_component_led.h' projectpath='component/led'/>
        <file category='sourceC' name='components/led/fsl_component_led.c' projectpath='component/led'/>
        <file category='include' name='components/led/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='log' Cversion='1.0.0' condition='component.log.condition_id'>
      <description>Component log</description>
      <files>
        <file category='header' name='components/log/fsl_component_log.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log.c' projectpath='component/log'/>
        <file category='header' name='components/log/fsl_component_log_config.h' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole' Cversion='1.0.0' condition='component.log.backend.debug_console.condition_id'>
      <description>Component log backend debug console</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole_lite' Cversion='1.0.0' condition='component.log.backend.debug_console_lite.condition_id'>
      <description>Component log backend debug console lite</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ringbuffer' Cversion='1.0.0' condition='component.log.backend.ringbuffer.condition_id'>
      <description>Component log backend ring buffer</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_ringbuffer.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_ringbuffer.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr_adapter' Cversion='1.0.0' condition='component.lptmr_adapter.condition_id'>
      <description>Component lptmr_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_LPTMR
#define TIMER_PORT_TYPE_LPTMR 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_lptmr.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter' Cversion='1.0.0' condition='component.lpuart_adapter.condition_id'>
      <description>Component lpuart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_lpuart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter' Cversion='2.0.0' condition='component.pcm186x_adapter.condition_id'>
      <description>Component pcm186x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.h' projectpath='codec/port/pcm186x'/>
        <file category='sourceC' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.c' projectpath='codec/port/pcm186x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm186x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter' Cversion='2.0.0' condition='component.pcm512x_adapter.condition_id'>
      <description>Component pcm512x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.h' projectpath='codec/port/pcm512x'/>
        <file category='sourceC' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.c' projectpath='codec/port/pcm512x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm512x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm_tpm_adapter' Cversion='1.0.0' condition='component.pwm_tpm_adapter.condition_id'>
      <description>Component pwm_tpm_adapter</description>
      <files>
        <file category='header' name='components/pwm/fsl_adapter_pwm.h' projectpath='component/pwm'/>
        <file category='sourceC' name='components/pwm/fsl_adapter_pwm_tpm.c' projectpath='component/pwm'/>
        <file category='include' name='components/pwm/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='component_rtc' Cversion='1.0.0' condition='component.rtc_adapter.condition_id'>
      <description>Component rtc</description>
      <RTE_Components_h>
        #ifndef RTC_LEGACY_FUNCTION_PROTOTYPE
        #define RTC_LEGACY_FUNCTION_PROTOTYPE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/rtc/fsl_adapter_rtc.h' projectpath='component/rtc'/>
        <file category='sourceC' name='components/rtc/fsl_adapter_rtc.c' projectpath='component/rtc'/>
        <file category='include' name='components/rtc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter' Cversion='2.2.0' condition='component.sgtl_adapter.condition_id'>
      <description>Component sgtl5000 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.h' projectpath='codec/port/sgtl5000'/>
        <file category='sourceC' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.c' projectpath='codec/port/sgtl5000'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/sgtl5000/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi_adapter' Cversion='1.0.0' condition='component.spi_adapter.condition_id'>
      <description>Component spi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_spi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter' Cversion='2.2.0' condition='component.tfa9896_adapter.condition_id'>
      <description>Component tfa9896 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.h' projectpath='codec/port/tfa9896'/>
        <file category='sourceC' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.c' projectpath='codec/port/tfa9896'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9896/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter' Cversion='2.2.0' condition='component.tfa9xxx_adapter.condition_id'>
      <description>Component tfa9xxx adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.h' projectpath='codec/port/tfa9xxx'/>
        <file category='sourceC' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.c' projectpath='codec/port/tfa9xxx'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9xxx/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tpm_adapter' Cversion='1.0.0' condition='component.tpm_adapter.condition_id'>
      <description>Component tpm_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_TPM
#define TIMER_PORT_TYPE_TPM 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_tpm.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter' Cversion='2.2.0' condition='component.wm8524_adapter.condition_id'>
      <description>Component wm8524 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.h' projectpath='codec/port/wm8524'/>
        <file category='sourceC' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.c' projectpath='codec/port/wm8524'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8524/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter' Cversion='2.2.0' condition='component.wm8904_adapter.condition_id'>
      <description>Component wm8904 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.h' projectpath='codec/port/wm8904'/>
        <file category='sourceC' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.c' projectpath='codec/port/wm8904'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8904/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter' Cversion='2.2.0' condition='component.wm8960_adapter.condition_id'>
      <description>Component wm8960 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.h' projectpath='codec/port/wm8960'/>
        <file category='sourceC' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.c' projectpath='codec/port/wm8960'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8960/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter' Cversion='2.2.0' condition='component.wm8962_adapter.condition_id'>
      <description>Component wm8962 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.h' projectpath='codec/port/wm8962'/>
        <file category='sourceC' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.c' projectpath='codec/port/wm8962'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8962/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MCXC041_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device MCXC041_cmsis</description>
      <files>
        <file category='header' name='devices/MCXC041/fsl_device_registers.h' projectpath='device'/>
        <file category='header' name='devices/MCXC041/MCXC041.h' projectpath='device'/>
        <file category='header' name='devices/MCXC041/MCXC041_features.h' projectpath='device'/>
        <file category='header' name='devices/MCXC041/MCXC041_COMMON.h' projectpath='device'/>
        <file category='header' name='devices/MCXC041/periph/PERI_ADC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_CMP.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_FGPIO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_FTFA.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_GPIO.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_I2C.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_LLWU.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_LPTMR.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_LPUART.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_MCG.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_MCM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_MTB.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_MTBDWT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_NV.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_OSC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_PMC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_PORT.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_RCM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_RFSYS.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_ROM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_RTC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_SIM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_SMC.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_SPI.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_TPM.h' projectpath='device/periph'/>
        <file category='header' name='devices/MCXC041/periph/PERI_VREF.h' projectpath='device/periph'/>
        <file category='include' name='devices/MCXC041/'/>
        <file category='include' name='devices/MCXC041/periph/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='header' attr='config' name='devices/MCXC041/template/RTE_Device.h' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MCXC041/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='MCXC041_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device MCXC041_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, device_ids=MCXC041.condition_id' category='linkerScript' attr='config' name='devices/MCXC041/arm/MCXC041_flash.scf' version='1.0.0' projectpath='MCXC041/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MCXC041.condition_id' category='linkerScript' attr='config' name='devices/MCXC041/arm/MCXC041_ram.scf' version='1.0.0' projectpath='MCXC041/arm'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MCXC041.condition_id' category='linkerScript' attr='config' name='devices/MCXC041/gcc/MCXC041_flash.ld' version='1.0.0' projectpath='MCXC041/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MCXC041.condition_id' category='linkerScript' attr='config' name='devices/MCXC041/gcc/MCXC041_ram.ld' version='1.0.0' projectpath='MCXC041/gcc'/>
        <file condition='allOf.toolchains=iar, device_ids=MCXC041.condition_id' category='linkerScript' attr='config' name='devices/MCXC041/iar/MCXC041_flash.icf' version='1.0.0' projectpath='MCXC041/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MCXC041.condition_id' category='linkerScript' attr='config' name='devices/MCXC041/iar/MCXC041_ram.icf' version='1.0.0' projectpath='MCXC041/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='MCXC041' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template MCXC041</description>
      <files>
        <file category='header' attr='config' name='devices/MCXC041/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXC041/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXC041/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXC041/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXC041/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXC041/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXC041/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXC041/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MCXC041/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device MCXC041_startup</description>
      <files>
        <file condition='allOf.toolchains=iar.condition_id' category='sourceAsm' attr='config' name='devices/MCXC041/iar/startup_MCXC041.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceAsm' attr='config' name='devices/MCXC041/gcc/startup_MCXC041.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk.condition_id' category='sourceAsm' attr='config' name='devices/MCXC041/arm/startup_MCXC041.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MCXC041_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device MCXC041_system</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/system_MCXC041.c' projectpath='device'/>
        <file category='header' name='devices/MCXC041/system_MCXC041.h' projectpath='device'/>
        <file category='include' name='devices/MCXC041/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adc' Cversion='2.3.0' condition='driver.adc16.condition_id'>
      <description>ADC16 Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_adc16.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_adc16.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497' Cversion='2.1.2' condition='driver.ak4497.condition_id'>
      <description>Driver ak4497</description>
      <RTE_Components_h>
#ifndef CODEC_AK4497_ENABLE
#define CODEC_AK4497_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/ak4497/fsl_ak4497.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/ak4497/fsl_ak4497.c' projectpath='codec'/>
        <file category='include' name='components/codec/ak4497/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.0.0' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cmp' Cversion='2.0.3' condition='driver.cmp.condition_id'>
      <description>CMP Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_cmp.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_cmp.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='GPIO' Csub='gpio_cmsis' Cversion='1.0.0' Capiversion='1.0.0' condition='driver.cmsis_gpio.condition_id'>
      <description>GPIO CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/cmsis_drivers/fsl_gpio_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/cmsis_drivers/fsl_gpio_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='i2c_cmsis' Cversion='2.2.0' Capiversion='2.3.0' condition='driver.cmsis_i2c.condition_id'>
      <description>I2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/cmsis_drivers/fsl_i2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/cmsis_drivers/fsl_i2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='lpuart_cmsis' Cversion='2.7.0' Capiversion='2.3.0' condition='driver.cmsis_lpuart.condition_id'>
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/cmsis_drivers/fsl_lpuart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/cmsis_drivers/fsl_lpuart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='spi_cmsis' Cversion='2.3.0' Capiversion='2.2.0' condition='driver.cmsis_spi.condition_id'>
      <description>SPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/cmsis_drivers/fsl_spi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/cmsis_drivers/fsl_spi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec' Cversion='2.3.1' condition='driver.codec.condition_id'>
      <description>Driver codec</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/fsl_codec_common.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/fsl_codec_common.c' projectpath='codec'/>
        <file category='include' name='components/codec/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm0p.condition_id' category='sourceC' name='devices/MCXC041/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm0p.condition_id' category='header' name='devices/MCXC041/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cop' Cversion='2.0.2' condition='driver.cop.condition_id'>
      <description>COP Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_cop.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_cop.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448' Cversion='2.0.1' condition='driver.cs42448.condition_id'>
      <description>Driver cs42448</description>
      <RTE_Components_h>
#ifndef CODEC_CS42448_ENABLE
#define CODEC_CS42448_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42448/fsl_cs42448.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42448/fsl_cs42448.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42448/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888' Cversion='2.1.3' condition='driver.cs42888.condition_id'>
      <description>Driver cs42888</description>
      <RTE_Components_h>
#ifndef CODEC_CS42888_ENABLE
#define CODEC_CS42888_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42888/fsl_cs42888.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42888/fsl_cs42888.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42888/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dialog7212' Cversion='2.3.1' condition='driver.dialog7212.condition_id'>
      <description>Driver dialog7212</description>
      <RTE_Components_h>
#ifndef CODEC_DA7212_ENABLE
#define CODEC_DA7212_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/da7212/fsl_dialog7212.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/da7212/fsl_dialog7212.c' projectpath='codec'/>
        <file category='include' name='components/codec/da7212/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ektf2k' Cversion='1.0.0' condition='driver.ektf2k.condition_id'>
      <description>Driver ektf2k</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/ektf2k/fsl_ektf2k.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ektf2k/fsl_ektf2k.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ektf2k/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash' Cversion='3.1.3' condition='driver.flash.condition_id'>
      <description>Flash Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_adapter.h' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_utilities.h' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_features.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_ftfx_controller.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_controller.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_ftfx_flash.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_flash.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_ftfx_cache.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_cache.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_ftfx_flexnvm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_ftfx_flexnvm.h' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_flash.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft3267' Cversion='1.0.1' condition='driver.ft3267.condition_id'>
      <description>Touch panel controller FT3267 driver</description>
      <files>
        <file category='header' name='components/touch/ft3267/fsl_ft3267.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft3267/fsl_ft3267.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft3267/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406' Cversion='1.0.0' condition='driver.ft5406.condition_id'>
      <description>Driver ft5406</description>
      <files>
        <file category='header' name='components/touch/ft5406/fsl_ft5406.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406/fsl_ft5406.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft6x06' Cversion='1.0.0' condition='driver.ft6x06.condition_id'>
      <description>Driver ft6x06</description>
      <files>
        <file category='header' name='components/touch/ft6x06/fsl_ft6x06.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft6x06/fsl_ft6x06.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft6x06/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxas21002cq' Cversion='1.0.0' condition='driver.fxas21002cq.condition_id'>
      <description>Driver fxas21002cq</description>
      <files>
        <file category='header' name='components/sensor/fxas21002cq/fsl_fxas.h' projectpath='gyroscope'/>
        <file category='sourceC' name='components/sensor/fxas21002cq/fsl_fxas.c' projectpath='gyroscope'/>
        <file category='include' name='components/sensor/fxas21002cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxls8974cf' Cversion='1.0.0' condition='driver.fxls8974cf.condition_id'>
      <description>Driver fxls8974cf</description>
      <files>
        <file category='header' name='components/sensor/fxls8974cf/fsl_fxls.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxls8974cf/fsl_fxls.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxls8974cf/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxos8700cq' Cversion='1.0.0' condition='driver.fxos8700cq.condition_id'>
      <description>Driver fxos8700cq</description>
      <files>
        <file category='header' name='components/sensor/fxos8700cq/fsl_fxos.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxos8700cq/fsl_fxos.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxos8700cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.8.2' condition='driver.gpio.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gt911' Cversion='1.0.4' condition='driver.gt911.condition_id'>
      <description>Driver gt911</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/gt911/fsl_gt911.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/gt911/fsl_gt911.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/gt911/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='htu21d' Cversion='1.0.0' condition='driver.htu21d.condition_id'>
      <description>Driver htu21d</description>
      <files>
        <file category='header' name='components/sensor/htu21d/fsl_htu21d.h' projectpath='htu'/>
        <file category='sourceC' name='components/sensor/htu21d/fsl_htu21d.c' projectpath='htu'/>
        <file category='include' name='components/sensor/htu21d/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.0.9' condition='driver.i2c.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_i2c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_i2c.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma' Cversion='2.0.9' condition='driver.i2c_edma.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_i2c_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_i2c_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='icm42688p' Cversion='1.0.0' condition='driver.icm42688p.condition_id'>
      <description>Driver icm42688p</description>
      <files>
        <file category='header' name='components/sensor/icm42688p/fsl_icm42688p.h' projectpath='icm42688p'/>
        <file category='sourceC' name='components/sensor/icm42688p/fsl_icm42688p.c' projectpath='icm42688p'/>
        <file category='include' name='components/sensor/icm42688p/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ili9341' Cversion='1.0.2' condition='driver.ili9341.condition_id'>
      <description>Driver ili9341</description>
      <files>
        <file category='header' name='components/display/ili9341/fsl_ili9341.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ili9341/fsl_ili9341.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ili9341/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='llwu' Cversion='2.0.5' condition='driver.llwu.condition_id'>
      <description>LLWU Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_llwu.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_llwu.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr' Cversion='2.2.0' condition='driver.lptmr.condition_id'>
      <description>LPTMR Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_lptmr.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_lptmr.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart' Cversion='2.9.1' condition='driver.lpuart.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_lpuart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_lpuart.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lsm6dso' Cversion='1.0.0' condition='driver.lsm6dso.condition_id'>
      <description>Driver lsm6dso</description>
      <files>
        <file category='header' name='components/sensor/lsm6dso/fsl_lsm.h' projectpath='tilt_pedo'/>
        <file category='sourceC' name='components/sensor/lsm6dso/fsl_lsm.c' projectpath='tilt_pedo'/>
        <file category='include' name='components/sensor/lsm6dso/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='max30101' Cversion='1.0.0' condition='driver.max30101.condition_id'>
      <description>Driver max30101</description>
      <files>
        <file category='header' name='components/sensor/max30101/fsl_max.h' projectpath='heartrate'/>
        <file category='sourceC' name='components/sensor/max30101/fsl_max.c' projectpath='heartrate'/>
        <file category='include' name='components/sensor/max30101/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8451q' Cversion='1.0.0' condition='driver.mma8451q.condition_id'>
      <description>Driver mma8451q</description>
      <files>
        <file category='header' name='components/sensor/mma8451q/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8451q/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8451q/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8652fc' Cversion='1.0.0' condition='driver.mma8652fc.condition_id'>
      <description>Driver mma8652fc</description>
      <files>
        <file category='header' name='components/sensor/mma8652fc/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8652fc/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8652fc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mx25r_flash' Cversion='2.0.0' condition='driver.mx25r_flash.condition_id'>
      <description>Driver mx25r_flash</description>
      <files>
        <file category='header' name='components/mx25r_flash/mx25r_flash.h' projectpath='source'/>
        <file category='sourceC' name='components/mx25r_flash/mx25r_flash.c' projectpath='source'/>
        <file category='include' name='components/mx25r_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nmh1000' Cversion='1.0.0' condition='driver.nmh1000.condition_id'>
      <description>Driver nmh1000</description>
      <files>
        <file category='header' name='components/sensor/nmh1000/fsl_nmh1000.h' projectpath='nmh1000'/>
        <file category='sourceC' name='components/sensor/nmh1000/fsl_nmh1000.c' projectpath='nmh1000'/>
        <file category='include' name='components/sensor/nmh1000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='p3t1755' Cversion='2.0.0' condition='driver.p3t1755.condition_id'>
      <description>Driver p3t1755</description>
      <files>
        <file category='header' name='components/sensor/p3t1755/fsl_p3t1755.h' projectpath='component/p3t1755'/>
        <file category='sourceC' name='components/sensor/p3t1755/fsl_p3t1755.c' projectpath='component/p3t1755'/>
        <file category='include' name='components/sensor/p3t1755/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x' Cversion='2.0.1' condition='driver.pcm186x.condition_id'>
      <description>Driver pcm186x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM186X_ENABLE
#define CODEC_PCM186X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm186x/fsl_pcm186x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm186x/fsl_pcm186x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm186x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x' Cversion='2.0.1' condition='driver.pcm512x.condition_id'>
      <description>Driver pcm512x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM512X_ENABLE
#define CODEC_PCM512X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm512x/fsl_pcm512x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm512x/fsl_pcm512x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm512x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf1550' Cversion='1.0.0' condition='driver.pf1550.condition_id'>
      <description>Driver pf1550</description>
      <files>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550.h' projectpath='component/pmic/pf1550'/>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550_charger.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550_charger.h' projectpath='component/pmic/pf1550'/>
        <file category='include' name='components/pmic/pf1550/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf3000' Cversion='1.0.0' condition='driver.pf3000.condition_id'>
      <description>Driver pf3000</description>
      <files>
        <file category='sourceC' name='components/pmic/pf3000/fsl_pf3000.c' projectpath='component/pmic/pf3000'/>
        <file category='header' name='components/pmic/pf3000/fsl_pf3000.h' projectpath='component/pmic/pf3000'/>
        <file category='include' name='components/pmic/pf3000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf5020' Cversion='2.0.0' condition='driver.pf5020.condition_id'>
      <description>Driver pf5020</description>
      <files>
        <file category='sourceC' name='components/pmic/pf5020/fsl_pf5020.c' projectpath='component/pmic/pf5020'/>
        <file category='header' name='components/pmic/pf5020/fsl_pf5020.h' projectpath='component/pmic/pf5020'/>
        <file category='include' name='components/pmic/pf5020/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pmc' Cversion='2.0.3' condition='driver.pmc.condition_id'>
      <description>PMC Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_pmc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_pmc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='port' Cversion='2.5.1' condition='driver.port.condition_id'>
      <description>PORT Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_port.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='psp27801' Cversion='1.0.0' condition='driver.psp27801.condition_id'>
      <description>Driver psp27801</description>
      <files>
        <file category='header' name='components/display/psp27801/fsl_psp27801.h' projectpath='oled'/>
        <file category='sourceC' name='components/display/psp27801/fsl_psp27801.c' projectpath='oled'/>
        <file category='include' name='components/display/psp27801/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rcm' Cversion='2.0.4' condition='driver.rcm.condition_id'>
      <description>RCM Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_rcm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_rcm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtc' Cversion='2.3.3' condition='driver.rtc.condition_id'>
      <description>RTC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_rtc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_rtc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl5000' Cversion='2.1.1' condition='driver.sgtl5000.condition_id'>
      <description>Driver sgtl5000</description>
      <RTE_Components_h>
#ifndef CODEC_SGTL5000_ENABLE
#define CODEC_SGTL5000_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/sgtl5000/fsl_sgtl5000.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/sgtl5000/fsl_sgtl5000.c' projectpath='codec'/>
        <file category='include' name='components/codec/sgtl5000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sim' Cversion='2.2.0' condition='driver.sim.condition_id'>
      <description>SIM Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_sim.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_sim.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='smc' Cversion='2.0.7' condition='driver.smc.condition_id'>
      <description>SMC Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_smc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_smc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi' Cversion='2.1.4' condition='driver.spi.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_spi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_spi.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896' Cversion='6.0.2' condition='driver.tfa9896.condition_id'>
      <description>Driver tfa9896</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9896_ENABLE
#define CODEC_TFA9896_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896_buffer.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_hal_registers.c' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_tfa9896.c' projectpath='codec'/>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896.h' projectpath='codec'/>
        <file category='doc' name='components/codec/tfa9896/MIMXRT595595-EVK_TFA9896_SW.pdf' projectpath='codec'/>
        <file category='include' name='components/codec/tfa9896/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx' Cversion='2.1.0' condition='driver.tfa9xxx.condition_id'>
      <description>Driver tfa9xxx</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9XXX_ENABLE
#define CODEC_TFA9XXX_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9892N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N2.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx.c' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/fsl_tfa9xxx.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx_IMX.c' projectpath='codec/tfa9xxx'/>
        <file category='doc' name='components/codec/tfa9xxx/README.md' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/config.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dsp_fw.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_init.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa9xxx_parameters.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_container_crc32.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_haptic_fw_defs.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
        <file category='include' name='components/codec/tfa9xxx/vas_tfa_drv/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_hal' Cversion='2.1.0' condition='driver.tfa9xxx_hal.condition_id'>
      <description>Driver tfa9xxx_hal</description>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_device_hal.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/tfa_device_hal.c' projectpath='codec/tfa9xxx'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tma525b' Cversion='1.0.0' condition='driver.tma525b.condition_id'>
      <description>Driver tma525b</description>
      <files>
        <file category='header' name='components/touch/tma525b/fsl_tma525b.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/tma525b/fsl_tma525b.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/tma525b/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tpm' Cversion='2.3.5' condition='driver.tpm.condition_id'>
      <description>TPM Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_tpm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXC041/drivers/fsl_tpm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tsl2561' Cversion='1.0.0' condition='driver.tsl2561.condition_id'>
      <description>Driver tsl2561</description>
      <files>
        <file category='header' name='components/sensor/tsl2561/fsl_tsl2561.h' projectpath='tsl'/>
        <file category='sourceC' name='components/sensor/tsl2561/fsl_tsl2561.c' projectpath='tsl'/>
        <file category='include' name='components/sensor/tsl2561/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='vref' Cversion='2.1.3' condition='driver.vref.condition_id'>
      <description>VREF Driver</description>
      <files>
        <file category='header' name='devices/MCXC041/drivers/fsl_vref.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXC041/drivers/fsl_vref.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXC041/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524' Cversion='2.1.1' condition='driver.wm8524.condition_id'>
      <description>Driver wm8524</description>
      <RTE_Components_h>
#ifndef CODEC_WM8524_ENABLE
#define CODEC_WM8524_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8524/fsl_wm8524.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8524/fsl_wm8524.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8524/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904' Cversion='2.5.1' condition='driver.wm8904.condition_id'>
      <description>Driver wm8904</description>
      <RTE_Components_h>
#ifndef CODEC_WM8904_ENABLE
#define CODEC_WM8904_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8904/fsl_wm8904.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8904/fsl_wm8904.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8904/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960' Cversion='2.2.4' condition='driver.wm8960.condition_id'>
      <description>Driver wm8960</description>
      <RTE_Components_h>
#ifndef CODEC_WM8960_ENABLE
#define CODEC_WM8960_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8960/fsl_wm8960.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8960/fsl_wm8960.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8960/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962' Cversion='2.2.0' condition='driver.wm8962.condition_id'>
      <description>Driver wm8962</description>
      <RTE_Components_h>
#ifndef CODEC_WM8962_ENABLE
#define CODEC_WM8962_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8962/fsl_wm8962.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8962/fsl_wm8962.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8962/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MCXC041/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MCXC041/utilities/fsl_sbrk.c' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/MCXC041/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXC041/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXC041/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/MCXC041/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXC041/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXC041/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/MCXC041/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/MCXC041/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/MCXC041/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/MCXC041/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/MCXC041/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/MCXC041/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/MCXC041/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/MCXC041/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='notifier' Cversion='1.0.0' condition='utility.notifier.condition_id'>
      <description>Utility notifier</description>
      <files>
        <file category='sourceC' name='devices/MCXC041/utilities/fsl_notifier.c' projectpath='utilities/utilities'/>
        <file category='header' name='devices/MCXC041/utilities/fsl_notifier.h' projectpath='utilities/utilities'/>
        <file category='include' name='devices/MCXC041/utilities/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/MCXC041/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXC041/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXC041/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/MCXC041/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/MCXC041/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/MCXC041/utilities/str/'/>
      </files>
    </component>
  </components>
</package>