<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>LPC54S018M_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for LPC54S018M</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version='18.0.0' date='2024-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version='17.0.0' date='2023-07-27'>NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version='16.0.0' date='2023-01-16'>NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version='15.0.0' date='2022-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version='14.0.0' date='2022-01-05'>NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version='13.1.0' date='2021-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version='13.0.0' date='2021-02-05'>
      NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files
    </release>
    <release version='12.3.0' date='2021-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version='12.2.1' date='2020-09-11'>NXP CMSIS Packs based on MCUXpresso SDK 2.8.4</release>
    <release version='12.2.0' date='2020-07-20'>NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version='12.1.0' date='2019-12-19'>NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version='12.0.0' date='2019-06-10'>NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version='11.0.0' date='2019-02-15'>NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='LPC54S018M' Dvendor='NXP:11'>
      <description>
        Power-Efficient Microcontrollers (MCUs) with Advanced Peripherals Based on Arm Cortex-M4 Core and Security
      </description>
      <device Dname='LPC54S018J2M'>
        <processor Dcore='Cortex-M4' Dfpu='SP_FPU' Dmpu='MPU' Dendian='Little-endian' Dclock='180000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/LPC54S018M/iar/LPC54S018J2M_spifi_flash.icf'/>
        </environment>
        <memory name='PROGRAM_FLASH' start='0x10000000' size='0x200000' access='rx' default='1' startup='1'/>
        <memory name='SRAMX' start='0x00000000' size='0x030000' access='rw' default='1'/>
        <memory name='SRAM_0_1_2_3' start='0x20000000' size='0x028000' access='rw' default='1'/>
        <memory name='USB_RAM' start='0x40100000' size='0x2000' access='rw' default='1'/>
        <algorithm name='devices/LPC54S018M/arm/LPC540xx_W25Q32JVWJX.FLM' start='0x10000000' size='0x00200000' RAMstart='0x20000000' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/LPC54S018M/LPC54S018M.xml'/>
        <variant Dvariant='LPC54S018J2MET180'>
          <compile header='devices/LPC54S018M/fsl_device_registers.h' define='CPU_LPC54S018J2MET180'/>
        </variant>
      </device>
      <device Dname='LPC54S018J4M'>
        <processor Dcore='Cortex-M4' Dfpu='SP_FPU' Dmpu='MPU' Dendian='Little-endian' Dclock='180000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/LPC54S018M/iar/LPC54S018J4M_spifi_flash.icf'/>
        </environment>
        <memory name='PROGRAM_FLASH' start='0x10000000' size='0x400000' access='rx' default='1' startup='1'/>
        <memory name='SRAMX' start='0x00000000' size='0x030000' access='rw' default='1'/>
        <memory name='SRAM_0_1_2_3' start='0x20000000' size='0x028000' access='rw' default='1'/>
        <memory name='USB_RAM' start='0x40100000' size='0x2000' access='rw' default='1'/>
        <algorithm name='devices/LPC54S018M/arm/LPC540xx_W25Q32JVWJX.FLM' start='0x10000000' size='0x00400000' RAMstart='0x20000000' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/LPC54S018M/LPC54S018M.xml'/>
        <variant Dvariant='LPC54S018J4MET180'>
          <compile header='devices/LPC54S018M/fsl_device_registers.h' define='CPU_LPC54S018J4MET180'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.LPC54S018M.internal_condition'>
      <accept Dname='LPC54S018J2MET180' Dvendor='NXP:11'/>
      <accept Dname='LPC54S018J4MET180' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.device=LPC54S018M.internal_condition'>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.ak4497_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.audio_flexcomm_i2s_dma_adapter.condition_id'>
      <require condition='allOf.driver.flexcomm_i2s_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_i2s_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2s_dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.button.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter, component.timer_manager, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter, component.timer_manager, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'>
      <accept condition='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
      <accept condition='allOf.anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_gpio_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'>
      <require condition='anyOf.driver.lpc_gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rt_gpio_adapter'/>
    </condition>
    <condition id='anyOf.driver.lpc_gpio.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.codec_adapters.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.codec_i2c.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.cs42448_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.cs42888_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ctimer, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.da7212_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.eth_phy_common.condition_id'>
      <require condition='allOf.driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.flash_nor_spifi.condition_id'>
      <require condition='allOf.driver.spifi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.spifi, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spifi'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.flexcomm_i2c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.flexcomm_i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.flexcomm_i2c, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.flexcomm_spi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm_spi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.flexcomm_spi, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.gint_adapter.condition_id'>
      <require condition='allOf.driver.gint, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.gint, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gint'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c.internal_condition'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.flexcomm_i2c_adapter, driver.flexcomm_i2c.internal_condition'>
      <accept condition='allOf.component.flexcomm_i2c_adapter, driver.flexcomm_i2c.internal_condition'/>
    </condition>
    <condition id='allOf.component.flexcomm_i2c_adapter, driver.flexcomm_i2c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_i2c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.led.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, component.timer_manager, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, component.timer_manager, driver.common, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'>
      <accept condition='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.log.condition_id'>
      <require condition='allOf.driver.common, utility.str, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.str, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console_lite.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console_lite, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console_lite, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.log.backend.ringbuffer.condition_id'>
      <require condition='allOf.component.log, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.lpc_crc_adapter.condition_id'>
      <require condition='allOf.driver.lpc_crc, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_crc, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.lpc_gpio_adapter.condition_id'>
      <require condition='allOf.driver.inputmux, driver.lpc_gpio, driver.pint, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.inputmux, driver.lpc_gpio, driver.pint, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pint'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.lpcrtc.condition_id'>
      <require condition='allOf.driver.lpc_rtc, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_rtc, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_rtc'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.mflash_offchip.condition_id'>
      <require condition='allOf.anyOf=driver.spifi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.spifi, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.driver.spifi.internal_condition'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.spifi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='spifi'/>
    </condition>
    <condition id='component.mflash_onchip_fmu.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.mrt_adapter.condition_id'>
      <require condition='allOf.driver.mrt, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.mrt, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.pcm186x_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.pcm512x_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyaqr113c.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='phy-common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyar8031.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phydp8384x.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phygpy215.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyksz8041.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyksz8081.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phylan8720a.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phylan8741.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyrtl8201.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyrtl8211f.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phytja1100.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phytja1120.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyvsc8541.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.phyyt8521.condition_id'>
      <require condition='allOf.component.eth_phy_common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.pwm_ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, driver.rng, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, driver.rng, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.rng_adapter, driver.rng, component.software_rng_adapter, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.rng_adapter, driver.rng, component.software_rng_adapter, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.allOf=component.rng_adapter, driver.rng, component.software_rng_adapter.internal_condition'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.rng_adapter, driver.rng, component.software_rng_adapter.internal_condition'>
      <accept condition='allOf.component.rng_adapter, driver.rng.internal_condition'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='allOf.component.rng_adapter, driver.rng.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.rt_gpio_adapter.condition_id'>
      <require condition='allOf.driver.lpc_gpio, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_gpio, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'>
      <accept condition='allOf.component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'/>
    </condition>
    <condition id='allOf.component.flexcomm_spi_adapter, driver.flexcomm_spi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_spi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_usart, component.usart_adapter, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_usart, component.usart_adapter, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.driver.flexcomm_usart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.flexcomm_usart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.sgtl_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <accept Dname='LPC54S018J2MET180' Dvendor='NXP:11'/>
      <accept Dname='LPC54S018J4MET180' Dvendor='NXP:11'/>
    </condition>
    <condition id='component.silicon_id.condition_id'>
      <require condition='allOf.anyOf=allOf=driver.iap, device_id=LPC54S018J2M, LPC54S018J4M, driver.common.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=driver.iap, device_id=LPC54S018J2M, LPC54S018J4M, driver.common.internal_condition'>
      <require condition='anyOf.allOf=driver.iap, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
    </condition>
    <condition id='anyOf.allOf=driver.iap, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <accept condition='allOf.driver.iap, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.iap, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='iap'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imx8.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imx93.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imxrt10xx.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imxrt1170.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_imxrt1180.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_mcxn.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_rw610.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.silicon_id_scfw.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.tfa9896_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.tfa9xxx_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, component.lists, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, component.lists, driver.common, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt.internal_condition'>
      <accept condition='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'/>
      <accept condition='allOf.component.mrt_adapter, driver.mrt.internal_condition'/>
    </condition>
    <condition id='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
    </condition>
    <condition id='allOf.component.mrt_adapter, driver.mrt.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt'/>
    </condition>
    <condition id='component.usart_adapter.condition_id'>
      <require condition='allOf.driver.flexcomm, driver.flexcomm_usart, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm, driver.flexcomm_usart, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.wm8524_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.wm8904_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.wm8960_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='component.wm8962_adapter.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=LPC54S018M.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.driver.lpc_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='device_ids.LPC54S018J2M.internal_condition'>
      <accept Dname='LPC54S018J2MET180' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=LPC54S018J2M.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.LPC54S018J2M.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=LPC54S018J2M.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.LPC54S018J2M.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=LPC54S018J2M.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.LPC54S018J2M.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='device_ids.LPC54S018J4M.internal_condition'>
      <accept Dname='LPC54S018J4MET180' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=LPC54S018J4M.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=LPC54S018J4M.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=LPC54S018J4M.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.component.usart_adapter, device_id=LPC54S018J2M, LPC54S018J4M, driver.common, device.startup, driver.clock, driver.power, driver.reset, driver.emc, driver.flexcomm, driver.flexcomm_i2c, driver.flexcomm_spi, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.usart_adapter, device_id=LPC54S018J2M, LPC54S018J4M, driver.common, device.startup, driver.clock, driver.power, driver.reset, driver.emc, driver.flexcomm, driver.flexcomm_i2c, driver.flexcomm_spi, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='reset'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='emc'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='iocon'/>
      <require condition='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <accept condition='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'/>
      <accept condition='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config'/>
    </condition>
    <condition id='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='LPC54S018M_system'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='LPC54S018M_header'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.aes.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.ak4497.condition_id'>
      <require condition='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.ak4497_adapter, component.codec_adapters, component.codec_i2c, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-common.condition_id'>
      <require condition='allOf.driver.video-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.video-common, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ap1302.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.video-i2c, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-common, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-max9286.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-mt9m114.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov5640.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.camera-device-sccb, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7670.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7725.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-sccb.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.driver.flexcomm_i2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.flexcomm_i2c.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='driver.camera-receiver-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.driver.power, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.power, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_flexcomm_i2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.flexcomm_i2c_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.flexcomm_i2c_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_flexcomm_spi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.flexcomm_spi_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.flexcomm_spi_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi_dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_flexcomm_usart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, device.RTE, driver.flexcomm_usart_dma, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, device.RTE, driver.flexcomm_usart_dma, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_dma'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.codec.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm4f.condition_id'>
      <require condition='cores.cm4f.internal_condition'/>
    </condition>
    <condition id='cores.cm4f.internal_condition'>
      <accept Dcore='Cortex-M4'/>
    </condition>
    <condition id='driver.cs42448.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42448_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.cs42888.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.cs42888_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.ctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.dbi.condition_id'>
      <require condition='allOf.driver.video-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dbi_lpc_spi_dma.condition_id'>
      <require condition='allOf.driver.dbi, driver.flexcomm_spi_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, driver.flexcomm_spi_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi_dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-common.condition_id'>
      <require condition='allOf.driver.video-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dbi.condition_id'>
      <require condition='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-ssd1963.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dialog7212.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.da7212_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.display-adv7535.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.video-i2c, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.display-common.condition_id'>
      <require condition='allOf.driver.video-common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.display-it6161.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.display-it6263.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.display-sn65dsi83.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dmic.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.dmic_dma.condition_id'>
      <require condition='allOf.driver.dmic, driver.lpc_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dmic, driver.lpc_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dmic'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.dmic_hwvad.condition_id'>
      <require condition='allOf.driver.dmic, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dmic, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dmic'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.ektf2k.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.emc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2c.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.flexcomm, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2c_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_i2c, driver.lpc_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_i2c, driver.lpc_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2s.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_i2s_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_i2s, driver.lpc_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_i2s, driver.lpc_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2s'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_spi.condition_id'>
      <require condition='allOf.driver.common, driver.flexcomm, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_spi_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_spi, driver.lpc_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_spi, driver.lpc_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_usart.condition_id'>
      <require condition='allOf.driver.flexcomm, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.flexcomm_usart_dma.condition_id'>
      <require condition='allOf.driver.flexcomm_usart, driver.lpc_dma, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexcomm_usart, driver.lpc_dma, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.fmeas.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.fro_calib.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.ft3267.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.ft5406.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.ft6x06.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device=LPC54S018M.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.fxas21002cq.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.fxls8974cf.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.fxos8700cq.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.gint.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.gt911.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.htu21d.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.iap.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.icm42688p.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.ili9341.condition_id'>
      <require condition='allOf.driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.inputmux.condition_id'>
      <require condition='allOf.driver.common, driver.inputmux_connections, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.inputmux_connections, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections'/>
      <require condition='device_id.LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.inputmux_connections.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_adc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_crc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_dma.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_enet.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_gpio.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_iocon.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_lcdc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lpc_rtc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.lsm6dso.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.max30101.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.mcan.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.mma8451q.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.mma8652fc.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.mrt.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.mx25r_flash.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.nmh1000.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.otp.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.p3t1755.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pca9420.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, driver.power, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_i2c, driver.common, driver.power, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.driver.flexcomm_i2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pca9422.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, driver.power, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pcm186x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm186x_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pcm512x.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.pcm512x_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pf1550.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pf3000.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pf5020.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.pint.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.power.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.psp27801.condition_id'>
      <require condition='allOf.driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.puf.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.reset.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.rit.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.rng.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.sctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.sdif.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.sgtl5000.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.sgtl_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.sha.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.spifi.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.spifi_dma.condition_id'>
      <require condition='allOf.driver.lpc_dma, driver.spifi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_dma, driver.spifi, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dma'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spifi'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.ssd1963.condition_id'>
      <require condition='allOf.driver.dbi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.st7796s.condition_id'>
      <require condition='allOf.driver.dbi, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.tfa9896.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9896_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.tfa9xxx_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.tfa9xxx_hal.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, driver.codec, driver.tfa9xxx, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.tma525b.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.tsl2561.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.utick.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='driver.video-common.condition_id'>
      <require condition='allOf.driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.video-i2c.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_i2c, driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.wm8524.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8524_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.wm8904.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8904_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.wm8960.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8960_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.wm8962.condition_id'>
      <require condition='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.codec_adapters, component.codec_i2c, component.wm8962_adapter, driver.codec, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='codec'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='driver.wwdt.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC54S018J2M, LPC54S018J4M.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk, cores=cm4f.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
      <require condition='cores.cm4f.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.flexcomm_usart, driver.common, not=utility.debug_console, utility.str, component.usart_adapter, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.flexcomm_usart, driver.common, not=utility.debug_console, utility.str, component.usart_adapter, device=LPC54S018M.internal_condition'>
      <require condition='anyOf.driver.flexcomm_usart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.debug_console.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='not.utility.debug_console.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='utility.notifier.condition_id'>
      <require condition='allOf.device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=LPC54S018M.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=LPC54S018M.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.LPC54S018M.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=LPC54S018M.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497_adapter' Cversion='2.2.0' condition='component.ak4497_adapter.condition_id'>
      <description>Component ak4497 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.h' projectpath='codec/port/ak4497'/>
        <file category='sourceC' name='components/codec/port/ak4497/fsl_codec_ak4497_adapter.c' projectpath='codec/port/ak4497'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/ak4497/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='audio_flexcomm_i2s_dma_adapter' Cversion='1.0.0' condition='component.audio_flexcomm_i2s_dma_adapter.condition_id'>
      <description>Component flexcomm_i2s_dma_adapter</description>
      <files>
        <file category='header' name='components/audio/fsl_adapter_audio.h' projectpath='component/audio'/>
        <file category='sourceC' name='components/audio/fsl_adapter_flexcomm_i2s.c' projectpath='component/audio'/>
        <file category='include' name='components/audio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='button' Cversion='1.0.0' condition='component.button.condition_id'>
      <description>Component button</description>
      <files>
        <file category='header' name='components/button/fsl_component_button.h' projectpath='component/button'/>
        <file category='sourceC' name='components/button/fsl_component_button.c' projectpath='component/button'/>
        <file category='include' name='components/button/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_adapters' Cversion='2.2.0' condition='component.codec_adapters.condition_id'>
      <description>Component codec adapters for multi codec</description>
      <RTE_Components_h>
#ifndef CODEC_MULTI_ADAPTERS
#define CODEC_MULTI_ADAPTERS 1
#endif
</RTE_Components_h>
      <files>
        <file category='sourceC' name='components/codec/port/fsl_codec_adapter.c' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec_i2c' Cversion='2.1.0' condition='component.codec_i2c.condition_id'>
      <description>Component codec_i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/i2c/fsl_codec_i2c.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/i2c/fsl_codec_i2c.c' projectpath='codec'/>
        <file category='include' name='components/codec/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448_adapter' Cversion='2.2.1' condition='component.cs42448_adapter.condition_id'>
      <description>Component cs42448 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.h' projectpath='codec/port/cs42448'/>
        <file category='sourceC' name='components/codec/port/cs42448/fsl_codec_cs42448_adapter.c' projectpath='codec/port/cs42448'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42448/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888_adapter' Cversion='2.2.1' condition='component.cs42888_adapter.condition_id'>
      <description>Component cs42888 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.h' projectpath='codec/port/cs42888'/>
        <file category='sourceC' name='components/codec/port/cs42888/fsl_codec_cs42888_adapter.c' projectpath='codec/port/cs42888'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/cs42888/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter' Cversion='1.0.0' condition='component.ctimer_adapter.condition_id'>
      <description>Component ctimer_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_CTIMER
#define TIMER_PORT_TYPE_CTIMER 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ctimer.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='da7212_adapter' Cversion='2.2.0' condition='component.da7212_adapter.condition_id'>
      <description>Component da7212 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/da7212/fsl_codec_da7212_adapter.h' projectpath='codec/port/da7212'/>
        <file category='sourceC' name='components/codec/port/da7212/fsl_codec_da7212_adapter.c' projectpath='codec/port/da7212'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/da7212/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-common' Cversion='2.0.0' condition='component.eth_phy_common.condition_id'>
      <description>Driver phy-common</description>
      <files>
        <file category='header' name='components/phy/fsl_phy.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nor_spifi' Cversion='1.0.0' condition='component.flash_nor_spifi.condition_id'>
      <description>Component flash_nor_spifi</description>
      <files>
        <file category='header' name='components/flash/nor/fsl_nor_flash.h' projectpath='nor_flash/nor'/>
        <file category='sourceC' name='components/flash/nor/spifi/fsl_spifi_nor_flash.c' projectpath='nor_flash/nor/spifi'/>
        <file category='header' name='components/flash/nor/spifi/fsl_spifi_nor_flash.h' projectpath='nor_flash/nor/spifi'/>
        <file category='include' name='components/flash/nor/'/>
        <file category='include' name='components/flash/nor/spifi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_i2c_adapter' Cversion='1.0.0' condition='component.flexcomm_i2c_adapter.condition_id'>
      <description>Component flexcomm_i2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_flexcomm_i2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm_spi_adapter' Cversion='1.0.0' condition='component.flexcomm_spi_adapter.condition_id'>
      <description>Component flexcomm_spi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_flexcomm_spi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gint_adapter' Cversion='1.0.1' condition='component.gint_adapter.condition_id'>
      <description>Component gint_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_gint.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='led' Cversion='1.0.0' condition='component.led.condition_id'>
      <description>Component led</description>
      <files>
        <file category='header' name='components/led/fsl_component_led.h' projectpath='component/led'/>
        <file category='sourceC' name='components/led/fsl_component_led.c' projectpath='component/led'/>
        <file category='include' name='components/led/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='log' Cversion='1.0.0' condition='component.log.condition_id'>
      <description>Component log</description>
      <files>
        <file category='header' name='components/log/fsl_component_log.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log.c' projectpath='component/log'/>
        <file category='header' name='components/log/fsl_component_log_config.h' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole' Cversion='1.0.0' condition='component.log.backend.debug_console.condition_id'>
      <description>Component log backend debug console</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole_lite' Cversion='1.0.0' condition='component.log.backend.debug_console_lite.condition_id'>
      <description>Component log backend debug console lite</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ringbuffer' Cversion='1.0.0' condition='component.log.backend.ringbuffer.condition_id'>
      <description>Component log backend ring buffer</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_ringbuffer.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_ringbuffer.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc_adapter' Cversion='1.0.0' condition='component.lpc_crc_adapter.condition_id'>
      <description>Component lpc_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_lpc_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_gpio_adapter' Cversion='1.0.1' condition='component.lpc_gpio_adapter.condition_id'>
      <description>Component lpc_gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_lpc_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpcrtc' Cversion='1.0.0' condition='component.lpcrtc.condition_id'>
      <description>Component lpcrtc</description>
      <RTE_Components_h>
        #ifndef RTC_LEGACY_FUNCTION_PROTOTYPE
        #define RTC_LEGACY_FUNCTION_PROTOTYPE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/rtc/fsl_adapter_rtc.h' projectpath='component/rtc'/>
        <file category='sourceC' name='components/rtc/fsl_adapter_lpcrtc.c' projectpath='component/rtc'/>
        <file category='include' name='components/rtc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_offchip' Cversion='1.0.0' condition='component.mflash_offchip.condition_id'>
      <description>mflash offchip</description>
      <RTE_Components_h>
#ifndef MFLASH_FILE_BASEADDR
#define MFLASH_FILE_BASEADDR 1048576
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/flash/mflash/mflash_common.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mflash_file.c' projectpath='flash/mflash'/>
        <file category='header' name='components/flash/mflash/mflash_file.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/lpc54s018m/mflash_drv.c' projectpath='flash/mflash/lpc54s018m'/>
        <file category='header' name='components/flash/mflash/lpc54s018m/mflash_drv.h' projectpath='flash/mflash/lpc54s018m'/>
        <file category='include' name='components/flash/mflash/'/>
        <file category='include' name='components/flash/mflash/lpc54s018m/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip_fmu' Cversion='1.0.0' condition='component.mflash_onchip_fmu.condition_id'>
      <description>mflash onchip_fmu</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_fmu_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mrt_adapter' Cversion='1.0.0' condition='component.mrt_adapter.condition_id'>
      <description>Component mrt_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_MRT
#define TIMER_PORT_TYPE_MRT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_mrt.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x_adapter' Cversion='2.0.0' condition='component.pcm186x_adapter.condition_id'>
      <description>Component pcm186x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.h' projectpath='codec/port/pcm186x'/>
        <file category='sourceC' name='components/codec/port/pcm186x/fsl_codec_pcm186x_adapter.c' projectpath='codec/port/pcm186x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm186x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x_adapter' Cversion='2.0.0' condition='component.pcm512x_adapter.condition_id'>
      <description>Component pcm512x adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.h' projectpath='codec/port/pcm512x'/>
        <file category='sourceC' name='components/codec/port/pcm512x/fsl_codec_pcm512x_adapter.c' projectpath='codec/port/pcm512x'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/pcm512x/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-aqr113c' Cversion='2.0.0' condition='component.phyaqr113c.condition_id'>
      <description>Driver phy-device-aqr113c</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyaqr113c/fsl_phyaqr113c.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyaqr113c/fsl_phyaqr113c.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyaqr113c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-ar8031' Cversion='2.0.0' condition='component.phyar8031.condition_id'>
      <description>Driver phy-device-ar8031</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyar8031/fsl_phyar8031.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyar8031/fsl_phyar8031.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyar8031/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-dp8384x' Cversion='2.0.0' condition='component.phydp8384x.condition_id'>
      <description>Driver phy-device-dp8384x</description>
      <files>
        <file category='sourceC' name='components/phy/device/phydp8384x/fsl_phydp8384x.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phydp8384x/fsl_phydp8384x.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phydp8384x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-gpy215' Cversion='2.0.0' condition='component.phygpy215.condition_id'>
      <description>Driver phy-device-gpy215</description>
      <files>
        <file category='sourceC' name='components/phy/device/phygpy215/fsl_phygpy215.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phygpy215/fsl_phygpy215.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phygpy215/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-ksz8041' Cversion='2.0.0' condition='component.phyksz8041.condition_id'>
      <description>Driver phy-device-ksz8041</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyksz8041/fsl_phyksz8041.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyksz8041/fsl_phyksz8041.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyksz8041/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-ksz8081' Cversion='2.0.0' condition='component.phyksz8081.condition_id'>
      <description>Driver phy-device-ksz8081</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyksz8081/fsl_phyksz8081.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyksz8081/fsl_phyksz8081.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyksz8081/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-lan8720a' Cversion='2.0.0' condition='component.phylan8720a.condition_id'>
      <description>Driver phy-device-lan8720a</description>
      <files>
        <file category='sourceC' name='components/phy/device/phylan8720a/fsl_phylan8720a.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phylan8720a/fsl_phylan8720a.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phylan8720a/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-lan8741' Cversion='2.0.0' condition='component.phylan8741.condition_id'>
      <description>Driver phy-device-lan8741</description>
      <files>
        <file category='sourceC' name='components/phy/device/phylan8741/fsl_phylan8741.c' projectpath='phy'/>
        <file category='header' name='components/phy/device/phylan8741/fsl_phylan8741.h' projectpath='phy'/>
        <file category='include' name='components/phy/device/phylan8741/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-rtl8201' Cversion='2.0.0' condition='component.phyrtl8201.condition_id'>
      <description>Driver phy-device-rtl8201</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyrtl8201/fsl_phyrtl8201.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyrtl8201/fsl_phyrtl8201.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyrtl8201/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-rtl8211f' Cversion='2.0.0' condition='component.phyrtl8211f.condition_id'>
      <description>Driver phy-device-rtl8211f</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyrtl8211f/fsl_phyrtl8211f.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyrtl8211f/fsl_phyrtl8211f.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyrtl8211f/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-tja1100' Cversion='2.0.0' condition='component.phytja1100.condition_id'>
      <description>Driver phy-device-tja1100</description>
      <files>
        <file category='sourceC' name='components/phy/device/phytja1100/fsl_phytja1100.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phytja1100/fsl_phytja1100.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phytja1100/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-tja1120' Cversion='2.0.0' condition='component.phytja1120.condition_id'>
      <description>Driver phy-device-tja1120</description>
      <files>
        <file category='sourceC' name='components/phy/device/phytja1120/fsl_phytja1120.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phytja1120/fsl_phytja1120.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phytja1120/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-vsc8541' Cversion='2.0.0' condition='component.phyvsc8541.condition_id'>
      <description>Driver phy-device-vsc8541</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyvsc8541/fsl_phyvsc8541.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyvsc8541/fsl_phyvsc8541.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyvsc8541/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='phy-device-yt8521' Cversion='2.0.0' condition='component.phyyt8521.condition_id'>
      <description>Driver phy-device-yt8521</description>
      <files>
        <file category='sourceC' name='components/phy/device/phyyt8521/fsl_phyyt8521.c' projectpath='component/phy'/>
        <file category='header' name='components/phy/device/phyyt8521/fsl_phyyt8521.h' projectpath='component/phy'/>
        <file category='include' name='components/phy/device/phyyt8521/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm_ctimer_adapter' Cversion='1.0.0' condition='component.pwm_ctimer_adapter.condition_id'>
      <description>Component pwm_ctimer_adapter</description>
      <files>
        <file category='header' name='components/pwm/fsl_adapter_pwm.h' projectpath='component/pwm'/>
        <file category='sourceC' name='components/pwm/fsl_adapter_pwm_ctimer.c' projectpath='component/pwm'/>
        <file category='include' name='components/pwm/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter' Cversion='1.0.0' condition='component.rng_adapter.condition_id'>
      <description>Component rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rt_gpio_adapter' Cversion='1.0.1' condition='component.rt_gpio_adapter.condition_id'>
      <description>Component rt_gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_rt_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl_adapter' Cversion='2.2.0' condition='component.sgtl_adapter.condition_id'>
      <description>Component sgtl5000 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.h' projectpath='codec/port/sgtl5000'/>
        <file category='sourceC' name='components/codec/port/sgtl5000/fsl_codec_sgtl_adapter.c' projectpath='codec/port/sgtl5000'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/sgtl5000/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id' Cversion='2.0.1' condition='component.silicon_id.condition_id'>
      <description>Driver silicon_id</description>
      <files>
        <file category='header' name='components/silicon_id/fsl_silicon_id.h' projectpath='component/silicon_id'/>
        <file category='sourceC' name='components/silicon_id/fsl_silicon_id.c' projectpath='component/silicon_id'/>
        <file category='include' name='components/silicon_id/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_imx8' Cversion='2.0.0' condition='component.silicon_id_imx8.condition_id'>
      <description>Driver silicon_id imx8</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imx8/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_imx93' Cversion='2.0.0' condition='component.silicon_id_imx93.condition_id'>
      <description>Driver silicon_id imx93</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imx93/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt10xx' Cversion='2.0.0' condition='component.silicon_id_imxrt10xx.condition_id'>
      <description>Driver silicon_id rt10xx</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/rt10xx/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt1170' Cversion='2.0.0' condition='component.silicon_id_imxrt1170.condition_id'>
      <description>Driver silicon_id imxrt1170</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imxrt1170/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rt1180' Cversion='2.0.0' condition='component.silicon_id_imxrt1180.condition_id'>
      <description>Driver silicon_id imxrt1180</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/imxrt1180/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_mcxn' Cversion='2.0.0' condition='component.silicon_id_mcxn.condition_id'>
      <description>Driver silicon_id mcxn</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/mcxn/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_rw610' Cversion='2.0.0' condition='component.silicon_id_rw610.condition_id'>
      <description>Driver silicon_id rw610</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/rw610/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='silicon_id_scfw' Cversion='2.0.0' condition='component.silicon_id_scfw.condition_id'>
      <description>Driver silicon_id scfw</description>
      <files>
        <file category='sourceC' name='components/silicon_id/socs/scfw/fsl_silicon_id_soc.c' projectpath='component/silicon_id'/>
        <file category='header' name='components/silicon_id/socs/fsl_silicon_id_soc.h' projectpath='component'/>
        <file category='include' name='components/silicon_id/socs/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896_adapter' Cversion='2.2.0' condition='component.tfa9896_adapter.condition_id'>
      <description>Component tfa9896 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.h' projectpath='codec/port/tfa9896'/>
        <file category='sourceC' name='components/codec/port/tfa9896/fsl_codec_tfa9896_adapter.c' projectpath='codec/port/tfa9896'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9896/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_adapter' Cversion='2.2.0' condition='component.tfa9xxx_adapter.condition_id'>
      <description>Component tfa9xxx adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.h' projectpath='codec/port/tfa9xxx'/>
        <file category='sourceC' name='components/codec/port/tfa9xxx/fsl_codec_tfa9xxx_adapter.c' projectpath='codec/port/tfa9xxx'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/tfa9xxx/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart_adapter' Cversion='1.0.0' condition='component.usart_adapter.condition_id'>
      <description>Component usart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_usart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524_adapter' Cversion='2.2.0' condition='component.wm8524_adapter.condition_id'>
      <description>Component wm8524 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.h' projectpath='codec/port/wm8524'/>
        <file category='sourceC' name='components/codec/port/wm8524/fsl_codec_wm8524_adapter.c' projectpath='codec/port/wm8524'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8524/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904_adapter' Cversion='2.2.0' condition='component.wm8904_adapter.condition_id'>
      <description>Component wm8904 adapter for single codec</description>
      <files>
        <file category='header' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.h' projectpath='codec/port/wm8904'/>
        <file category='sourceC' name='components/codec/port/wm8904/fsl_codec_wm8904_adapter.c' projectpath='codec/port/wm8904'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8904/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960_adapter' Cversion='2.2.0' condition='component.wm8960_adapter.condition_id'>
      <description>Component wm8960 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.h' projectpath='codec/port/wm8960'/>
        <file category='sourceC' name='components/codec/port/wm8960/fsl_codec_wm8960_adapter.c' projectpath='codec/port/wm8960'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8960/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962_adapter' Cversion='2.2.0' condition='component.wm8962_adapter.condition_id'>
      <description>Component wm8962 adapter for single codecs</description>
      <files>
        <file category='header' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.h' projectpath='codec/port/wm8962'/>
        <file category='sourceC' name='components/codec/port/wm8962/fsl_codec_wm8962_adapter.c' projectpath='codec/port/wm8962'/>
        <file category='header' name='components/codec/port/fsl_codec_adapter.h' projectpath='codec/port'/>
        <file category='include' name='components/codec/port/wm8962/'/>
        <file category='include' name='components/codec/port/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='LPC54S018M_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device LPC54S018M_cmsis</description>
      <files>
        <file category='header' name='devices/LPC54S018M/fsl_device_registers.h' projectpath='device'/>
        <file category='header' name='devices/LPC54S018M/LPC54S018M.h' projectpath='device'/>
        <file category='header' name='devices/LPC54S018M/LPC54S018M_features.h' projectpath='device'/>
        <file category='header' name='devices/LPC54S018M/LPC54S018M_COMMON.h' projectpath='device'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_ADC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_AES.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_ASYNC_SYSCON.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_CAN.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_CRC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_CTIMER.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_DMA.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_DMIC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_EMC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_ENET.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_FLEXCOMM.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_GINT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_GPIO.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_I2C.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_I2S.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_INPUTMUX.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_IOCON.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_LCD.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_MRT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_OTPC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_PINT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_PUF.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_RIT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_RTC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SCT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SDIF.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SHA.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SMARTCARD.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SPI.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SPIFI.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_SYSCON.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_USART.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_USB.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_USBFSH.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_USBHSD.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_USBHSH.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_UTICK.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC54S018M/periph2/PERI_WWDT.h' projectpath='device/periph2'/>
        <file category='include' name='devices/LPC54S018M/'/>
        <file category='include' name='devices/LPC54S018M/periph2/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='header' attr='config' name='devices/LPC54S018M/template/RTE_Device.h' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/LPC54S018M/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='LPC54S018M_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device LPC54S018M_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, device_ids=LPC54S018J2M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/arm/LPC54S018J2M_ram.scf' version='1.0.0' projectpath='LPC54S018M/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=LPC54S018J2M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/arm/LPC54S018J2M_spifi_flash.scf' version='1.0.0' projectpath='LPC54S018M/arm'/>
        <file condition='allOf.toolchains=iar, device_ids=LPC54S018J2M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/iar/LPC54S018J2M_ram.icf' version='1.0.0' projectpath='LPC54S018M/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=LPC54S018J2M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/iar/LPC54S018J2M_spifi_flash.icf' version='1.0.0' projectpath='LPC54S018M/iar'/>
        <file condition='allOf.toolchains=armgcc, device_ids=LPC54S018J2M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/gcc/LPC54S018J2M_ram.ld' version='1.0.0' projectpath='LPC54S018M/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=LPC54S018J2M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/gcc/LPC54S018J2M_spifi_flash.ld' version='1.0.0' projectpath='LPC54S018M/gcc'/>
        <file condition='allOf.toolchains=mdk, device_ids=LPC54S018J4M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/arm/LPC54S018J4M_ram.scf' version='1.0.0' projectpath='LPC54S018M/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=LPC54S018J4M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/arm/LPC54S018J4M_spifi_flash.scf' version='1.0.0' projectpath='LPC54S018M/arm'/>
        <file condition='allOf.toolchains=iar, device_ids=LPC54S018J4M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/iar/LPC54S018J4M_ram.icf' version='1.0.0' projectpath='LPC54S018M/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=LPC54S018J4M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/iar/LPC54S018J4M_spifi_flash.icf' version='1.0.0' projectpath='LPC54S018M/iar'/>
        <file condition='allOf.toolchains=armgcc, device_ids=LPC54S018J4M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/gcc/LPC54S018J4M_ram.ld' version='1.0.0' projectpath='LPC54S018M/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=LPC54S018J4M.condition_id' category='linkerScript' attr='config' name='devices/LPC54S018M/gcc/LPC54S018J4M_spifi_flash.ld' version='1.0.0' projectpath='LPC54S018M/gcc'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='LPC54S018M' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template LPC54S018M</description>
      <files>
        <file category='header' attr='config' name='devices/LPC54S018M/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC54S018M/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/LPC54S018M/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC54S018M/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/LPC54S018M/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC54S018M/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/LPC54S018M/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC54S018M/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/LPC54S018M/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device LPC54S018M_startup</description>
      <files>
        <file condition='allOf.toolchains=iar.condition_id' category='sourceAsm' attr='config' name='devices/LPC54S018M/iar/startup_LPC54S018M.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceAsm' attr='config' name='devices/LPC54S018M/gcc/startup_LPC54S018M.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk.condition_id' category='sourceAsm' attr='config' name='devices/LPC54S018M/arm/startup_LPC54S018M.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='LPC54S018M_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device LPC54S018M_system</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/system_LPC54S018M.c' projectpath='device'/>
        <file category='header' name='devices/LPC54S018M/system_LPC54S018M.h' projectpath='device'/>
        <file category='include' name='devices/LPC54S018M/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='aes' Cversion='2.0.3' condition='driver.aes.condition_id'>
      <description>AES Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_aes.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_aes.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ak4497' Cversion='2.1.2' condition='driver.ak4497.condition_id'>
      <description>Driver ak4497</description>
      <RTE_Components_h>
#ifndef CODEC_AK4497_ENABLE
#define CODEC_AK4497_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/ak4497/fsl_ak4497.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/ak4497/fsl_ak4497.c' projectpath='codec'/>
        <file category='include' name='components/codec/ak4497/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common' Cversion='1.0.0' condition='driver.camera-common.condition_id'>
      <description>Driver camera-common</description>
      <files>
        <file category='header' name='components/video/camera/fsl_camera.h' projectpath='video'/>
        <file category='include' name='components/video/camera/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ap1302' Cversion='1.0.1' condition='driver.camera-device-ap1302.condition_id'>
      <description>Driver camera-device-ap1302</description>
      <files>
        <file category='header' name='components/video/camera/device/ap1302/fsl_ap1302.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ap1302/fsl_ap1302.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ap1302/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common' Cversion='1.0.0' condition='driver.camera-device-common.condition_id'>
      <description>Driver camera-device-common</description>
      <files>
        <file category='header' name='components/video/camera/device/fsl_camera_device.h' projectpath='video'/>
        <file category='include' name='components/video/camera/device/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-max9286' Cversion='1.0.2' condition='driver.camera-device-max9286.condition_id'>
      <description>Driver camera-device-max9286</description>
      <files>
        <file category='header' name='components/video/camera/device/max9286/fsl_max9286.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/max9286/fsl_max9286.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/max9286/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-mt9m114' Cversion='1.0.2' condition='driver.camera-device-mt9m114.condition_id'>
      <description>Driver camera-device-mt9m114</description>
      <files>
        <file category='header' name='components/video/camera/device/mt9m114/fsl_mt9m114.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/mt9m114/fsl_mt9m114.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/mt9m114/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov5640' Cversion='1.0.1' condition='driver.camera-device-ov5640.condition_id'>
      <description>Driver camera-device-ov5640</description>
      <files>
        <file category='header' name='components/video/camera/device/ov5640/fsl_ov5640.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov5640/fsl_ov5640.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov5640/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7670' Cversion='1.0.2' condition='driver.camera-device-ov7670.condition_id'>
      <description>Driver camera-device-ov7670</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7670/fsl_ov7670.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7670/fsl_ov7670.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7670/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7725' Cversion='1.0.1' condition='driver.camera-device-ov7725.condition_id'>
      <description>Driver camera-device-ov7725</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7725/fsl_ov7725.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7725/fsl_ov7725.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7725/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb' Cversion='1.0.1' condition='driver.camera-device-sccb.condition_id'>
      <description>Driver camera-device-sccb</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/camera/device/sccb/fsl_sccb.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/sccb/fsl_sccb.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/sccb/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-common' Cversion='1.0.0' condition='driver.camera-receiver-common.condition_id'>
      <description>Driver camera-receiver-common</description>
      <files>
        <file category='header' name='components/video/camera/receiver/fsl_camera_receiver.h' projectpath='video'/>
        <file category='include' name='components/video/camera/receiver/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.3.3' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='flexcomm_i2c_cmsis' Cversion='2.3.0' Capiversion='2.3.0' condition='driver.cmsis_flexcomm_i2c.condition_id'>
      <description>I2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/cmsis_drivers/fsl_i2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/cmsis_drivers/fsl_i2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='flexcomm_spi_cmsis' Cversion='2.5.0' Capiversion='2.2.0' condition='driver.cmsis_flexcomm_spi.condition_id'>
      <description>SPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/cmsis_drivers/fsl_spi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/cmsis_drivers/fsl_spi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='flexcomm_usart_cmsis' Cversion='2.4.0' Capiversion='2.3.0' condition='driver.cmsis_flexcomm_usart.condition_id'>
      <description>USART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/cmsis_drivers/fsl_usart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/cmsis_drivers/fsl_usart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='codec' Cversion='2.3.1' condition='driver.codec.condition_id'>
      <description>Driver codec</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/codec/fsl_codec_common.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/fsl_codec_common.c' projectpath='codec'/>
        <file category='include' name='components/codec/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm4f.condition_id' category='sourceC' name='devices/LPC54S018M/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm4f.condition_id' category='header' name='devices/LPC54S018M/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42448' Cversion='2.0.1' condition='driver.cs42448.condition_id'>
      <description>Driver cs42448</description>
      <RTE_Components_h>
#ifndef CODEC_CS42448_ENABLE
#define CODEC_CS42448_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42448/fsl_cs42448.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42448/fsl_cs42448.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42448/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cs42888' Cversion='2.1.3' condition='driver.cs42888.condition_id'>
      <description>Driver cs42888</description>
      <RTE_Components_h>
#ifndef CODEC_CS42888_ENABLE
#define CODEC_CS42888_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/cs42888/fsl_cs42888.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/cs42888/fsl_cs42888.c' projectpath='codec'/>
        <file category='include' name='components/codec/cs42888/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer' Cversion='2.3.3' condition='driver.ctimer.condition_id'>
      <description>CTimer Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_ctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_ctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi' Cversion='1.0.0' condition='driver.dbi.condition_id'>
      <description>Driver dbi</description>
      <files>
        <file category='header' name='components/video/display/dbi/fsl_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/fsl_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi_lpc_spi_dma' Cversion='1.0.0' condition='driver.dbi_lpc_spi_dma.condition_id'>
      <description>Driver dbi_lpc_spi_dma</description>
      <files>
        <file category='header' name='components/video/display/dbi/lpc_spi/fsl_dbi_spi_dma.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/lpc_spi/fsl_dbi_spi_dma.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/lpc_spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common' Cversion='1.0.0' condition='driver.dc-fb-common.condition_id'>
      <description>Driver dc-fb-common</description>
      <files>
        <file category='header' name='components/video/display/dc/fsl_dc_fb.h' projectpath='video'/>
        <file category='include' name='components/video/display/dc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dbi' Cversion='1.0.0' condition='driver.dc-fb-dbi.condition_id'>
      <description>Driver dc-fb-dbi</description>
      <RTE_Components_h>
#ifndef MCUX_DBI_LEGACY
#define MCUX_DBI_LEGACY 0
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-ssd1963' Cversion='1.0.2' condition='driver.dc-fb-ssd1963.condition_id'>
      <description>Driver dc-fb-ssd1963</description>
      <files>
        <file category='header' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dialog7212' Cversion='2.3.1' condition='driver.dialog7212.condition_id'>
      <description>Driver dialog7212</description>
      <RTE_Components_h>
#ifndef CODEC_DA7212_ENABLE
#define CODEC_DA7212_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/da7212/fsl_dialog7212.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/da7212/fsl_dialog7212.c' projectpath='codec'/>
        <file category='include' name='components/codec/da7212/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-adv7535' Cversion='1.0.1' condition='driver.display-adv7535.condition_id'>
      <description>Driver display-adv7535</description>
      <files>
        <file category='header' name='components/video/display/adv7535/fsl_adv7535.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/adv7535/fsl_adv7535.c' projectpath='video'/>
        <file category='include' name='components/video/display/adv7535/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-common' Cversion='1.0.0' condition='driver.display-common.condition_id'>
      <description>Driver display-common</description>
      <files>
        <file category='header' name='components/video/display/fsl_display.h' projectpath='video'/>
        <file category='include' name='components/video/display/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6161' Cversion='1.0.0' condition='driver.display-it6161.condition_id'>
      <description>Driver display-it6161</description>
      <files>
        <file category='header' name='components/video/display/it6161/fsl_it6161.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/fsl_it6161.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/hdmi_tx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/hdmi_tx.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/mipi_rx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/mipi_rx.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6161/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6263' Cversion='1.0.1' condition='driver.display-it6263.condition_id'>
      <description>Driver display-it6263</description>
      <files>
        <file category='header' name='components/video/display/it6263/fsl_it6263.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6263/fsl_it6263.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6263/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-sn65dsi83' Cversion='1.0.0' condition='driver.display-sn65dsi83.condition_id'>
      <description>Driver display-sn65dsi83</description>
      <files>
        <file category='header' name='components/video/display/sn65dsi83/fsl_sn65dsi83.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/sn65dsi83/fsl_sn65dsi83.c' projectpath='video'/>
        <file category='include' name='components/video/display/sn65dsi83/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmic' Cversion='2.3.2' condition='driver.dmic.condition_id'>
      <description>DMIC Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_dmic.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_dmic.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmic_dma' Cversion='2.4.0' condition='driver.dmic_dma.condition_id'>
      <description>DMIC DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_dmic_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_dmic_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dmic_hwvad' Cversion='2.3.0' condition='driver.dmic_hwvad.condition_id'>
      <description>DMIC HWVAD Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_dmic.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_dmic.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ektf2k' Cversion='1.0.0' condition='driver.ektf2k.condition_id'>
      <description>Driver ektf2k</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/ektf2k/fsl_ektf2k.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ektf2k/fsl_ektf2k.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ektf2k/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='emc' Cversion='2.0.4' condition='driver.emc.condition_id'>
      <description>EMC Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_emc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_emc.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcomm' Cversion='2.0.2' condition='driver.flexcomm.condition_id'>
      <description>FLEXCOMM Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_flexcomm.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_flexcomm.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.3.3' condition='driver.flexcomm_i2c.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_i2c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_i2c.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_dma' Cversion='2.3.1' condition='driver.flexcomm_i2c_dma.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_i2c_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_i2c_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2s' Cversion='2.3.2' condition='driver.flexcomm_i2s.condition_id'>
      <description>i2s Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_i2s.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_i2s.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2s_dma' Cversion='2.3.3' condition='driver.flexcomm_i2s_dma.condition_id'>
      <description>i2s Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_i2s_dma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_i2s_dma.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi' Cversion='2.3.2' condition='driver.flexcomm_spi.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_spi.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_spi.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi_dma' Cversion='2.2.1' condition='driver.flexcomm_spi_dma.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_spi_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_spi_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart' Cversion='2.8.5' condition='driver.flexcomm_usart.condition_id'>
      <description>usart Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_usart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_usart.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart_dma' Cversion='2.3.1' condition='driver.flexcomm_usart_dma.condition_id'>
      <description>usart Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_usart_dma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_usart_dma.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fmeas' Cversion='2.1.1' condition='driver.fmeas.condition_id'>
      <description>FMEAS Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_fmeas.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_fmeas.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fro_calib' Cversion='2.0.0' condition='driver.fro_calib.condition_id'>
      <description>USB FRO Calibration Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_fro_calib.h' projectpath='drivers'/>
        <file condition='allOf.toolchains=iar.condition_id' category='library' name='devices/LPC54S018M/iar/iar_lib_fro_calib.a' projectpath='iar'/>
        <file condition='allOf.toolchains=mdk.condition_id' category='library' name='devices/LPC54S018M/arm/keil_lib_fro_calib.lib' projectpath='arm'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='library' name='devices/LPC54S018M/gcc/libfro_calib_hardabi.a' projectpath='gcc'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft3267' Cversion='1.0.1' condition='driver.ft3267.condition_id'>
      <description>Touch panel controller FT3267 driver</description>
      <files>
        <file category='header' name='components/touch/ft3267/fsl_ft3267.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft3267/fsl_ft3267.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft3267/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406' Cversion='1.0.0' condition='driver.ft5406.condition_id'>
      <description>Driver ft5406</description>
      <files>
        <file category='header' name='components/touch/ft5406/fsl_ft5406.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406/fsl_ft5406.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft6x06' Cversion='1.0.0' condition='driver.ft6x06.condition_id'>
      <description>Driver ft6x06</description>
      <files>
        <file category='header' name='components/touch/ft6x06/fsl_ft6x06.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft6x06/fsl_ft6x06.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft6x06/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxas21002cq' Cversion='1.0.0' condition='driver.fxas21002cq.condition_id'>
      <description>Driver fxas21002cq</description>
      <files>
        <file category='header' name='components/sensor/fxas21002cq/fsl_fxas.h' projectpath='gyroscope'/>
        <file category='sourceC' name='components/sensor/fxas21002cq/fsl_fxas.c' projectpath='gyroscope'/>
        <file category='include' name='components/sensor/fxas21002cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxls8974cf' Cversion='1.0.0' condition='driver.fxls8974cf.condition_id'>
      <description>Driver fxls8974cf</description>
      <files>
        <file category='header' name='components/sensor/fxls8974cf/fsl_fxls.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxls8974cf/fsl_fxls.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxls8974cf/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxos8700cq' Cversion='1.0.0' condition='driver.fxos8700cq.condition_id'>
      <description>Driver fxos8700cq</description>
      <files>
        <file category='header' name='components/sensor/fxos8700cq/fsl_fxos.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxos8700cq/fsl_fxos.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxos8700cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gint' Cversion='2.1.1' condition='driver.gint.condition_id'>
      <description>GINT Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_gint.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_gint.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gt911' Cversion='1.0.4' condition='driver.gt911.condition_id'>
      <description>Driver gt911</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/gt911/fsl_gt911.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/gt911/fsl_gt911.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/gt911/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='htu21d' Cversion='1.0.0' condition='driver.htu21d.condition_id'>
      <description>Driver htu21d</description>
      <files>
        <file category='header' name='components/sensor/htu21d/fsl_htu21d.h' projectpath='htu'/>
        <file category='sourceC' name='components/sensor/htu21d/fsl_htu21d.c' projectpath='htu'/>
        <file category='include' name='components/sensor/htu21d/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iap' Cversion='2.0.7' condition='driver.iap.condition_id'>
      <description>IAP Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_iap.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_iap.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='icm42688p' Cversion='1.0.0' condition='driver.icm42688p.condition_id'>
      <description>Driver icm42688p</description>
      <files>
        <file category='header' name='components/sensor/icm42688p/fsl_icm42688p.h' projectpath='icm42688p'/>
        <file category='sourceC' name='components/sensor/icm42688p/fsl_icm42688p.c' projectpath='icm42688p'/>
        <file category='include' name='components/sensor/icm42688p/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ili9341' Cversion='1.0.2' condition='driver.ili9341.condition_id'>
      <description>Driver ili9341</description>
      <files>
        <file category='header' name='components/display/ili9341/fsl_ili9341.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ili9341/fsl_ili9341.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ili9341/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux' Cversion='2.0.9' condition='driver.inputmux.condition_id'>
      <description>INPUTMUX Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_inputmux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_inputmux.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections' Cversion='2.0.0' condition='driver.inputmux_connections.condition_id'>
      <description>Inputmux_connections Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_inputmux_connections.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adc' Cversion='2.6.0' condition='driver.lpc_adc.condition_id'>
      <description>ADC Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_adc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_adc.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc' Cversion='2.1.1' condition='driver.lpc_crc.condition_id'>
      <description>CRC Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_crc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_crc.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dma' Cversion='2.5.3' condition='driver.lpc_dma.condition_id'>
      <description>DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_enet' Cversion='2.3.5' condition='driver.lpc_enet.condition_id'>
      <description>enet Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_enet.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_enet.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.1.7' condition='driver.lpc_gpio.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iocon' Cversion='2.2.0' condition='driver.lpc_iocon.condition_id'>
      <description>IOCON Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_iocon.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lcdc' Cversion='2.0.2' condition='driver.lpc_lcdc.condition_id'>
      <description>LCDC Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_lcdc.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_lcdc.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_rtc' Cversion='2.2.0' condition='driver.lpc_rtc.condition_id'>
      <description>RTC Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_rtc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_rtc.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lsm6dso' Cversion='1.0.0' condition='driver.lsm6dso.condition_id'>
      <description>Driver lsm6dso</description>
      <files>
        <file category='header' name='components/sensor/lsm6dso/fsl_lsm.h' projectpath='tilt_pedo'/>
        <file category='sourceC' name='components/sensor/lsm6dso/fsl_lsm.c' projectpath='tilt_pedo'/>
        <file category='include' name='components/sensor/lsm6dso/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='max30101' Cversion='1.0.0' condition='driver.max30101.condition_id'>
      <description>Driver max30101</description>
      <files>
        <file category='header' name='components/sensor/max30101/fsl_max.h' projectpath='heartrate'/>
        <file category='sourceC' name='components/sensor/max30101/fsl_max.c' projectpath='heartrate'/>
        <file category='include' name='components/sensor/max30101/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mcan' Cversion='2.4.2' condition='driver.mcan.condition_id'>
      <description>MCAN Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_mcan.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_mcan.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8451q' Cversion='1.0.0' condition='driver.mma8451q.condition_id'>
      <description>Driver mma8451q</description>
      <files>
        <file category='header' name='components/sensor/mma8451q/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8451q/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8451q/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8652fc' Cversion='1.0.0' condition='driver.mma8652fc.condition_id'>
      <description>Driver mma8652fc</description>
      <files>
        <file category='header' name='components/sensor/mma8652fc/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8652fc/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8652fc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mrt' Cversion='2.0.5' condition='driver.mrt.condition_id'>
      <description>MRT Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_mrt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_mrt.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mx25r_flash' Cversion='2.0.0' condition='driver.mx25r_flash.condition_id'>
      <description>Driver mx25r_flash</description>
      <files>
        <file category='header' name='components/mx25r_flash/mx25r_flash.h' projectpath='source'/>
        <file category='sourceC' name='components/mx25r_flash/mx25r_flash.c' projectpath='source'/>
        <file category='include' name='components/mx25r_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nmh1000' Cversion='1.0.0' condition='driver.nmh1000.condition_id'>
      <description>Driver nmh1000</description>
      <files>
        <file category='header' name='components/sensor/nmh1000/fsl_nmh1000.h' projectpath='nmh1000'/>
        <file category='sourceC' name='components/sensor/nmh1000/fsl_nmh1000.c' projectpath='nmh1000'/>
        <file category='include' name='components/sensor/nmh1000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='otp' Cversion='2.0.1' condition='driver.otp.condition_id'>
      <description>OTP Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_otp.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='p3t1755' Cversion='2.0.0' condition='driver.p3t1755.condition_id'>
      <description>Driver p3t1755</description>
      <files>
        <file category='header' name='components/sensor/p3t1755/fsl_p3t1755.h' projectpath='component/p3t1755'/>
        <file category='sourceC' name='components/sensor/p3t1755/fsl_p3t1755.c' projectpath='component/p3t1755'/>
        <file category='include' name='components/sensor/p3t1755/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pca9420' Cversion='1.0.0' condition='driver.pca9420.condition_id'>
      <description>Driver pca9420</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/pmic/pca9420/fsl_pca9420.c' projectpath='component/pmic/pca9420'/>
        <file category='header' name='components/pmic/pca9420/fsl_pca9420.h' projectpath='component/pmic/pca9420'/>
        <file category='include' name='components/pmic/pca9420/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pca9422' Cversion='1.0.0' condition='driver.pca9422.condition_id'>
      <description>Driver pca9422</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/pmic/pca9422/fsl_pca9422.c' projectpath='component/pmic/pca9422'/>
        <file category='header' name='components/pmic/pca9422/fsl_pca9422.h' projectpath='component/pmic/pca9422'/>
        <file category='include' name='components/pmic/pca9422/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm186x' Cversion='2.0.1' condition='driver.pcm186x.condition_id'>
      <description>Driver pcm186x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM186X_ENABLE
#define CODEC_PCM186X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm186x/fsl_pcm186x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm186x/fsl_pcm186x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm186x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pcm512x' Cversion='2.0.1' condition='driver.pcm512x.condition_id'>
      <description>Driver pcm512x</description>
      <RTE_Components_h>
#ifndef CODEC_PCM512X_ENABLE
#define CODEC_PCM512X_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/pcm512x/fsl_pcm512x.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/pcm512x/fsl_pcm512x.c' projectpath='codec'/>
        <file category='include' name='components/codec/pcm512x/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf1550' Cversion='1.0.0' condition='driver.pf1550.condition_id'>
      <description>Driver pf1550</description>
      <files>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550.h' projectpath='component/pmic/pf1550'/>
        <file category='sourceC' name='components/pmic/pf1550/fsl_pf1550_charger.c' projectpath='component/pmic/pf1550'/>
        <file category='header' name='components/pmic/pf1550/fsl_pf1550_charger.h' projectpath='component/pmic/pf1550'/>
        <file category='include' name='components/pmic/pf1550/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf3000' Cversion='1.0.0' condition='driver.pf3000.condition_id'>
      <description>Driver pf3000</description>
      <files>
        <file category='sourceC' name='components/pmic/pf3000/fsl_pf3000.c' projectpath='component/pmic/pf3000'/>
        <file category='header' name='components/pmic/pf3000/fsl_pf3000.h' projectpath='component/pmic/pf3000'/>
        <file category='include' name='components/pmic/pf3000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pf5020' Cversion='2.0.0' condition='driver.pf5020.condition_id'>
      <description>Driver pf5020</description>
      <files>
        <file category='sourceC' name='components/pmic/pf5020/fsl_pf5020.c' projectpath='component/pmic/pf5020'/>
        <file category='header' name='components/pmic/pf5020/fsl_pf5020.h' projectpath='component/pmic/pf5020'/>
        <file category='include' name='components/pmic/pf5020/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pint' Cversion='2.2.0' condition='driver.pint.condition_id'>
      <description>PINT Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_pint.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_pint.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='power' Cversion='2.2.0' condition='driver.power.condition_id'>
      <description>Power driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_power.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_power.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='psp27801' Cversion='1.0.0' condition='driver.psp27801.condition_id'>
      <description>Driver psp27801</description>
      <files>
        <file category='header' name='components/display/psp27801/fsl_psp27801.h' projectpath='oled'/>
        <file category='sourceC' name='components/display/psp27801/fsl_psp27801.c' projectpath='oled'/>
        <file category='include' name='components/display/psp27801/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='PUF' Cversion='2.2.0' condition='driver.puf.condition_id'>
      <description>PUF Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_puf.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_puf.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset' Cversion='2.4.0' condition='driver.reset.condition_id'>
      <description>Reset Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_reset.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_reset.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rit' Cversion='2.1.2' condition='driver.rit.condition_id'>
      <description>RIT Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_rit.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_rit.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng' Cversion='2.1.0' condition='driver.rng.condition_id'>
      <description>RNG Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_rng.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_rng.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sctimer' Cversion='2.5.1' condition='driver.sctimer.condition_id'>
      <description>SCT Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_sctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_sctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sdif' Cversion='2.1.0' condition='driver.sdif.condition_id'>
      <description>sdif Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_sdif.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_sdif.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sgtl5000' Cversion='2.1.1' condition='driver.sgtl5000.condition_id'>
      <description>Driver sgtl5000</description>
      <RTE_Components_h>
#ifndef CODEC_SGTL5000_ENABLE
#define CODEC_SGTL5000_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/sgtl5000/fsl_sgtl5000.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/sgtl5000/fsl_sgtl5000.c' projectpath='codec'/>
        <file category='include' name='components/codec/sgtl5000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sha' Cversion='2.3.2' condition='driver.sha.condition_id'>
      <description>SHA Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_sha.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_sha.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spifi' Cversion='2.0.3' condition='driver.spifi.condition_id'>
      <description>SPIFI Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_spifi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_spifi.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spifi_dma' Cversion='2.0.3' condition='driver.spifi_dma.condition_id'>
      <description>SPIFI Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_spifi_dma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_spifi_dma.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963' Cversion='1.2.0' condition='driver.ssd1963.condition_id'>
      <description>Driver ssd1963</description>
      <files>
        <file category='header' name='components/display/ssd1963/fsl_ssd1963.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ssd1963/fsl_ssd1963.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='st7796s' Cversion='1.0.0' condition='driver.st7796s.condition_id'>
      <description>Driver st7796s</description>
      <files>
        <file category='header' name='components/display/st7796s/fsl_st7796s.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/st7796s/fsl_st7796s.c' projectpath='lcdc'/>
        <file category='include' name='components/display/st7796s/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9896' Cversion='6.0.2' condition='driver.tfa9896.condition_id'>
      <description>Driver tfa9896</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9896_ENABLE
#define CODEC_TFA9896_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896_buffer.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_hal_registers.c' projectpath='codec'/>
        <file category='sourceC' name='components/codec/tfa9896/fsl_tfa9896.c' projectpath='codec'/>
        <file category='header' name='components/codec/tfa9896/fsl_tfa9896.h' projectpath='codec'/>
        <file category='doc' name='components/codec/tfa9896/MIMXRT595595-EVK_TFA9896_SW.pdf' projectpath='codec'/>
        <file category='include' name='components/codec/tfa9896/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx' Cversion='2.1.0' condition='driver.tfa9xxx.condition_id'>
      <description>Driver tfa9xxx</description>
      <RTE_Components_h>
#ifndef CODEC_TFA9XXX_ENABLE
#define CODEC_TFA9XXX_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9892N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N1.h' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/tfa_config_TFA9894N2.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx.c' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/fsl_tfa9xxx.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/fsl_tfa9xxx_IMX.c' projectpath='codec/tfa9xxx'/>
        <file category='doc' name='components/codec/tfa9xxx/README.md' projectpath='codec/tfa9xxx'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/config.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_container.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dev.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_dsp_fw.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_haptic.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa2_init.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa9xxx_parameters.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='sourceC' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_container_crc32.c' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='header' name='components/codec/tfa9xxx/vas_tfa_drv/tfa_haptic_fw_defs.h' projectpath='codec/tfa9xxx/vas_tfa_drv'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
        <file category='include' name='components/codec/tfa9xxx/vas_tfa_drv/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tfa9xxx_hal' Cversion='2.1.0' condition='driver.tfa9xxx_hal.condition_id'>
      <description>Driver tfa9xxx_hal</description>
      <files>
        <file category='header' name='components/codec/tfa9xxx/tfa_device_hal.h' projectpath='codec/tfa9xxx'/>
        <file category='sourceC' name='components/codec/tfa9xxx/tfa_device_hal.c' projectpath='codec/tfa9xxx'/>
        <file category='include' name='components/codec/tfa9xxx/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tma525b' Cversion='1.0.0' condition='driver.tma525b.condition_id'>
      <description>Driver tma525b</description>
      <files>
        <file category='header' name='components/touch/tma525b/fsl_tma525b.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/tma525b/fsl_tma525b.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/tma525b/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tsl2561' Cversion='1.0.0' condition='driver.tsl2561.condition_id'>
      <description>Driver tsl2561</description>
      <files>
        <file category='header' name='components/sensor/tsl2561/fsl_tsl2561.h' projectpath='tsl'/>
        <file category='sourceC' name='components/sensor/tsl2561/fsl_tsl2561.c' projectpath='tsl'/>
        <file category='include' name='components/sensor/tsl2561/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='utick' Cversion='2.0.5' condition='driver.utick.condition_id'>
      <description>UTICK Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_utick.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_utick.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-common' Cversion='1.1.0' condition='driver.video-common.condition_id'>
      <description>Driver video-common</description>
      <files>
        <file category='header' name='components/video/fsl_video_common.h' projectpath='video'/>
        <file category='sourceC' name='components/video/fsl_video_common.c' projectpath='video'/>
        <file category='include' name='components/video/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c' Cversion='1.0.1' condition='driver.video-i2c.condition_id'>
      <description>Driver video-i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/i2c/fsl_video_i2c.h' projectpath='video'/>
        <file category='sourceC' name='components/video/i2c/fsl_video_i2c.c' projectpath='video'/>
        <file category='include' name='components/video/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8524' Cversion='2.1.1' condition='driver.wm8524.condition_id'>
      <description>Driver wm8524</description>
      <RTE_Components_h>
#ifndef CODEC_WM8524_ENABLE
#define CODEC_WM8524_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8524/fsl_wm8524.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8524/fsl_wm8524.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8524/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8904' Cversion='2.5.1' condition='driver.wm8904.condition_id'>
      <description>Driver wm8904</description>
      <RTE_Components_h>
#ifndef CODEC_WM8904_ENABLE
#define CODEC_WM8904_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8904/fsl_wm8904.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8904/fsl_wm8904.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8904/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8960' Cversion='2.2.4' condition='driver.wm8960.condition_id'>
      <description>Driver wm8960</description>
      <RTE_Components_h>
#ifndef CODEC_WM8960_ENABLE
#define CODEC_WM8960_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8960/fsl_wm8960.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8960/fsl_wm8960.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8960/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wm8962' Cversion='2.2.0' condition='driver.wm8962.condition_id'>
      <description>Driver wm8962</description>
      <RTE_Components_h>
#ifndef CODEC_WM8962_ENABLE
#define CODEC_WM8962_ENABLE 
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/codec/wm8962/fsl_wm8962.h' projectpath='codec'/>
        <file category='sourceC' name='components/codec/wm8962/fsl_wm8962.c' projectpath='codec'/>
        <file category='include' name='components/codec/wm8962/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wwdt' Cversion='2.1.9' condition='driver.wwdt.condition_id'>
      <description>WWDT Driver</description>
      <files>
        <file category='header' name='devices/LPC54S018M/drivers/fsl_wwdt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC54S018M/drivers/fsl_wwdt.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC54S018M/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/LPC54S018M/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/LPC54S018M/utilities/fsl_sbrk.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc, mdk, cores=cm4f.condition_id' category='sourceAsm' name='devices/LPC54S018M/utilities/fsl_memcpy.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/LPC54S018M/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/LPC54S018M/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/LPC54S018M/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/LPC54S018M/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/LPC54S018M/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/LPC54S018M/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/LPC54S018M/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/LPC54S018M/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/LPC54S018M/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/LPC54S018M/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/LPC54S018M/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/LPC54S018M/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/LPC54S018M/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/LPC54S018M/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='notifier' Cversion='1.0.0' condition='utility.notifier.condition_id'>
      <description>Utility notifier</description>
      <files>
        <file category='sourceC' name='devices/LPC54S018M/utilities/fsl_notifier.c' projectpath='utilities/utilities'/>
        <file category='header' name='devices/LPC54S018M/utilities/fsl_notifier.h' projectpath='utilities/utilities'/>
        <file category='include' name='devices/LPC54S018M/utilities/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/LPC54S018M/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/LPC54S018M/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/LPC54S018M/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/LPC54S018M/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/LPC54S018M/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/LPC54S018M/utilities/str/'/>
      </files>
    </component>
  </components>
</package>