<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-KL27Z_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDMKL27Z</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKL27Z644_DFP" vendor="NXP" version="13.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-KL27Z">
      <description>Freedom Development Board for Kinetis KL17/KL27 (32-64 KB Flash) MCUs</description>
      <mountedDevice Dname="MKL27Z64xxx4" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKL27Z644">
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32xxx4" Dvariant="MKL27Z32VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z32VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VDA4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VFT4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64xxx4" Dvariant="MKL27Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL27Z64VLH4" Dvendor="NXP:11"/>
    </condition>
    <condition id="component.serial_manager_AND_utility.debug_console">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
    </condition>
    <condition id="_component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite">
      <accept condition="component.serial_manager_AND_utility.debug_console"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="device.MKL27Z644_AND___component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite__AND_component.lpuart_adapter_AND_device.MKL27Z644_startup_AND_driver.common_AND_driver.gpio_AND_driver.i2c_AND_driver.lpuart_AND_driver.port_AND_driver.smc">
      <require condition="device.MKL27Z644"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKL27Z644_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require condition="_component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc16_continuous_dma" folder="driver_examples/adc16/continuous_dma" doc="readme.txt">
      <description>The ADC16 continuous DMA demo application demonstrates the usage of the ADC and DMA peripheral while in a continuous mode. TheADC16 is first set to continuous mode. In continuous convert configuration, only the initial rising-edge to launch continuous conversions isobserved, and until conversion is aborted, the ADC16 continues to do conversions on the same SCn register that initiated the conversion. DMA request will be asserted during an ADC16 conversion complete event noted when any of the SC1n[COCO] flags is asserted. DMA will transferADC16 results to memory and if users press any key, demo will average ADC16 results stored in memory and print average on the terminal.  </description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_continuous_dma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_continuous_dma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_interrupt" folder="driver_examples/adc16/interrupt" doc="readme.txt">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value. Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the ADC16 interrupt configuration is set when configuring the ADC16's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing aconversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_async_dma" folder="demo_apps/adc16_low_power_async_dma" doc="readme.txt">
      <description>The ADC Low Power Async DMA demo application demonstrates the usage of the ADC and DMA peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 100 ms, low power timer trigger the ADC module convertvalue on ADC channel. After 16 times(1,6s) the DMA transfer finish interrupt wake up the CPU to process sampled data, print result touser and toggle LED.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_async_dma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_low_power_async_dma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_async_dma_peripheral" folder="demo_apps/adc16_low_power_async_dma_peripheral" doc="readme.txt">
      <description>The ADC Low Power Async DMA demo application demonstrates the usage of the ADC and DMA peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 100 ms, low power timer trigger the ADC module convertvalue on ADC channel. After 16 times(1,6s) the DMA transfer finish interrupt wake up the CPU to process sampled data, print result touser and toggle LED.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_async_dma_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_low_power_async_dma_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_peripheral" folder="demo_apps/adc16_low_power_peripheral" doc="readme.txt">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the ADC module andreads the current temperature of the microcontroller. While the temperature remains within boundaries, both LEDs are on.If the core temperature is higher or lower than average, the LEDs change state respectively. You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_low_power_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="driver_examples/adc16/polling" doc="readme.txt">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.Note, the default setting of initialization for the ADC converter is just an available configuration. User can changethe configuration structure's setting in application to fit the special requirement.The auto-calibration is not essential but strongly recommended. It can help to adjust the converter itself and improvethe ADC16's performance.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral" folder="demo_apps/bubble_peripheral" doc="readme.txt">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis. You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/bubble_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="driver_examples/cmp/interrupt" doc="readme.txt">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input. On the negative side, the internal 6-bit DAC is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user-defined channel's voltage crosses the internal DAC's value. The change ofcomparator's output would generate the falling and rising edge events with their interrupts enabled. When any CMP interrupt happens, the CMP's ISR would turn on the LED light if detecting the output's rising edge, or turn off it whendetecting the output's falling edge.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmp_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="driver_examples/cmp/polling" doc="readme.txt">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input. On the negative side, the internal 6-bit DAC is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user's voltage crosses the internal DAC's value. The endless loop in main() functionwould detect the logic value of comparator's output, and change the LED. The LED would be turned on when the compareoutput is logic one, or turned off when zero.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmp_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_read_accel_value_transfer" folder="cmsis_driver_examples/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_read_accel_value example shows how to use CMSIS I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_dma_transfer" folder="cmsis_driver_examples/lpuart/dma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpuart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.txt">
      <description>The spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'spi_dma_b2b_transfer_master.c' includes the SPI master code.1. SPI master send/received data to/from SPI slave in dma . </description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'spi_dma_b2b_transfer_slave.c' includes the SPI slave code.1. SPI master send/received data to/from SPI slave in dma . </description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_master" folder="cmsis_driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The spi_interrupt_b2b_transfer example shows how to use spi CMSIS driver in interrupt way:In this example , we need two boards, one board used as spi master and another board used as spi slave.The file 'spi_interrupt_b2b_transfer_master.c' includes the spi master code.This example uses the transactional API in spi driver.1. spi master send/received data to/from spi slave in interrupt . </description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_slave" folder="cmsis_driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_interrupt_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'spi_interrupt_b2b_transfer_slave.c' includes the SPI slave code.This example uses the transactional API in SPI driver.1. SPI master send/received data to/from SPI slave in interrupt . </description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_dma_transfer" folder="cmsis_driver_examples/uart/dma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_uart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="cmsis_driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cop" folder="driver_examples/cop" doc="readme.txt">
      <description>The COP Example project is to demonstrate usage of the KSDK cop driver.In this example, after 10 times of refreshing, a timeout reset is generated.Please notice that because COP control registers are write-once only, so the COP_Init function and the COP_Disable function can be called only once after reset.In SystemInit() function which is called in startup code, there is an operation to disable the watchdog if macro DISABLE_WDOG is defined as 0, and by default if the DISABLE_WDOG is not defined, the DISABLE_WDOG is defined as 1. So this example has a special project setting that defines the DISABLE_WDOG macro to 0 so the startup code will skip the disable COP WDOG operation.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cop.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cop.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_link" folder="driver_examples/dma/channel_link" doc="readme.txt">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_link.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_channel_link.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="driver_examples/dma/memory_to_memory" doc="readme.txt">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_ring_buffer" folder="driver_examples/dma/ring_buffer" doc="readme.txt">
      <description>The DMA ring buffer example is a simple demonstration program that uses the SDK software.It demostrates how to implement ring buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_ring_buffer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_ring_buffer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="driver_examples/dma/wrap_transfer" doc="readme.txt">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dma_wrap_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_i2c_transfer" folder="driver_examples/flexio/i2c/interrupt_i2c_transfer" doc="readme.txt">
      <description>The flexio_i2c_interrupt example shows how to use flexio i2c master driver in interrupt way:In this example, a flexio simulated i2c master connect to an I2C slave.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_i2c_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_i2c_interrupt_i2c_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer" folder="driver_examples/flexio/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C  Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use the flexio i2c master driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="driver_examples/flexio/pwm" doc="readme.txt">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_dma_spi_transfer_master" folder="driver_examples/flexio/spi/dma_spi_transfer/master" doc="readme.txt">
      <description>The flexio_spi_master_dma_spi_slave example shows how to use flexio spi master driver in dma way:In this example, a flexio simulated master connect to a spi slave .</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_dma_spi_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_dma_spi_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_dma_spi_transfer_slave" folder="driver_examples/flexio/spi/dma_spi_transfer/slave" doc="readme.txt">
      <description>The flexio_spi_slave_dma_spi_master example shows how to use flexio spi slave driver in dma way:In this example, a flexio simulated slave connect to a spi master.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_dma_spi_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_dma_spi_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_spi_transfer_master" folder="driver_examples/flexio/spi/int_spi_transfer/master" doc="readme.txt">
      <description>The flexio_spi_master_interrupt_spi_slave example shows how to use flexio spi master driver in interrupt way:In this example, a flexio simulated master connect to a spi slave .</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_spi_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_int_spi_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_spi_transfer_slave" folder="driver_examples/flexio/spi/int_spi_transfer/slave" doc="readme.txt">
      <description>The flexio_spi_slave_interrupt_spi_master example shows how to use flexio spi slave driver in interrupt way:In this example, a flexio simulated slave connect to a spi master.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_spi_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_int_spi_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_dma_transfer" folder="driver_examples/flexio/uart/dma_transfer" doc="readme.txt">
      <description>The flexio_uart_dma example shows how to use flexio uart driver in dma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board. Note: two queued transfer in this example, so please input even number characters.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="driver_examples/flexio/uart/int_rb_transfer" doc="readme.txt">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="driver_examples/flexio/uart/interrupt_transfer" doc="readme.txt">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board. Note: two queued transfer in this example, so please input even number characters.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="driver_examples/flexio/uart/polling_transfer" doc="readme.txt">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="driver_examples/gpio/input_interrupt" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example uses the software button to control/toggle the LED.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_input_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="driver_examples/i2c/dma_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt" folder="driver_examples/i2c/interrupt" doc="readme.txt">
      <description>The i2c_functional_interrupt example shows how to use I2C functional driver to build a interrupt based application:In this example , one i2c instance used as I2C master and another i2c instance used as I2C slave .1. I2C master send data to I2C slave in interrupt . (I2C Slave using interrupt to receive the data)2. I2C master read data from I2C slave in interrupt . (I2C Slave using interrupt to send the data)3. The example assumes that the connection is OK between master and slave, so there's NO error handling code.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="driver_examples/i2c/polling_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_read_accel_value_transfer" folder="driver_examples/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>The i2c_read_accel_value example shows how to use I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_control_bm" folder="component_examples/led_control/bm" doc="readme.txt">
      <description>The LED demo is used to demonstrate how to use new components. The main function of the demo is to control the led by using the shell or button. For shell, please enter \"help\" to get the help information firstly. Turn on LED by using command \"led on\". And turn off LED by using command \"led off\". For button, please press the corresponding button to control LED. Turn on LED when the button is pressed with long press or double click event. And turn off LED when the button is pressed with short press or one click event.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_control_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_control_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="log_bm" folder="component_examples/log/bm" doc="readme.txt">
      <description>The log demo is used to demonstrate how to use log component. The main function of the demo is to prompt the LOG level string according to the user input log level command.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/log_bm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/log_bm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="driver_examples/lptmr" doc="readme.txt">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lptmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_dma_rb_transfer" folder="driver_examples/lpuart/dma_rb_transfer" doc="readme.txt">
      <description>The lpuart_dma ring buffer example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_dma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_dma_transfer" folder="driver_examples/lpuart/dma_transfer" doc="readme.txt">
      <description>The lpuart_dma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="driver_examples/lpuart/interrupt" doc="readme.txt">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="driver_examples/lpuart/interrupt_rb_transfer" doc="readme.txt">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="driver_examples/lpuart/polling" doc="readme.txt">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcglite" folder="driver_examples/mcglite" doc="readme.txt">
      <description>The MCG_Lite example shows how to use MCG_Lite driver: 1. How to use the mode functions for MCG_Lite mode switch. 2. How to use the frequency functions to get current MCG_Lite frequency. 3. Work flow  Reset mode --&gt; LIRC8M    LIRC8M --&gt; HIRC    HIRC   --&gt; LIRC2M    LIRC2M --&gt; EXT    EXT    --&gt; HIRC    HIRC   --&gt; LIRC8M    LIRC8M --&gt; EXT    EXT    --&gt; LIRC2M    LIRC2M --&gt; HIRC    HIRC   --&gt; EXT    EXT    --&gt; LIRC8M    LIRC8M --&gt; LIRC2M    LIRC2M --&gt; LIRC8MIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcglite.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mcglite.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="driver_examples/pit" doc="readme.txt">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pit.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="demo_apps/power_manager" doc="readme.txt">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a specific power mode. The usercan also set the wakeup source by following the debug console prompts. The purpose of this demo is to demonstrate theimplementation of a power mode manager. The callback can be registered to the framework. If a power mode transition happens,the callback will be called and user can do something. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/power_manager.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="demo_apps/power_mode_switch" doc="readme.txt">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user can also set the wakeupsource by following the debug console prompts. The purpose of this demo is to show how to switch between different power modes, and how to configure a wakeup source and wakeup the MCU from low power modes. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - Debug pins(e.g SWD_DIO) would consume addtional power, had better to disable related pins or disconnect them. </description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/power_mode_switch.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="driver_examples/rtc" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="demo_apps/rtc_func" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc_func.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func_peripheral" folder="demo_apps/rtc_func_peripheral" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc_func_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="demo_apps/shell" doc="readme.txt">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/shell.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_3wire_interrupt_transfer" folder="driver_examples/spi/3wire_interrupt_transfer" doc="readme.txt">
      <description>The spi_3wire_interrupt_transfer example shows how to use spi driver in 3-wire mode:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct. Actually, spi 3-wire mode can also support DMA transfer.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_3wire_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_3wire_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_master" folder="driver_examples/spi/dma_b2b_transfer/master" doc="readme.txt">
      <description>The spi_dma_board2board_master example shows how to use spi driver as master to do board to board transfer with DMA:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_slave" folder="driver_examples/spi/dma_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_dma_board2board_slave example shows how to use spi driver as slave to do board to board transfer with DMA:In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt" folder="driver_examples/spi/interrupt" doc="readme.txt">
      <description>The spi_interrupt example shows how to use spi functional API to do interrupt transfer:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="driver_examples/spi/interrupt_b2b/master" doc="readme.txt">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from slave,and checkif the data master received is correct. This example needs to work with spi_interrupt_b2b_slave example.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="driver_examples/spi/interrupt_b2b/slave" doc="readme.txt">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to master,and check if the data slave received is correct. This example needs to work with spi_interrupt_b2b_master example.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="driver_examples/spi/polling_b2b_transfer/master" doc="readme.txt">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="driver_examples/spi/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI slave.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_input_capture" folder="driver_examples/tpm/input_capture" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's input capture feature.The example sets up a TPM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_input_capture.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_input_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_output_compare" folder="driver_examples/tpm/output_compare" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's output compare feature.It sets up one TPM channel to toggle the output when a match occurs with the channel value. The usershould probe the TPM output with a oscilloscope to see the signal toggling.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_output_compare.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_output_compare.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_pwm_twochannel" folder="driver_examples/tpm/pwm_twochannel" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The PWM dutycycle on both channelsis manually updated. On boards that have an LED connected to the TPM pins, the user will seea change in LED brightness if user enter different values.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_pwm_twochannel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_pwm_twochannel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_simple_pwm" folder="driver_examples/tpm/simple_pwm" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output a center-aligned PWM signal. The PWM dutycycle is manually updated.On boards that have an LED connected to the TPM pins, the user will see a change in LEDbrightness if user enter different values.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_simple_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer" folder="driver_examples/tpm/timer" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_dma_rb_transfer" folder="driver_examples/uart/dma_rb_transfer" doc="readme.txt">
      <description>The uart_dma example shows how to use uart driver with DMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_dma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_dma_transfer" folder="driver_examples/uart/dma_transfer" doc="readme.txt">
      <description>The uart_dma example shows how to use uart driver with DMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_dma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="driver_examples/uart/interrupt" doc="readme.txt">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="driver_examples/uart/interrupt_rb_transfer" doc="readme.txt">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="driver_examples/uart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="FRDM-KL27Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmkl27z" Cversion="1.0.0" condition="device.MKL27Z644_AND___component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite__AND_component.lpuart_adapter_AND_device.MKL27Z644_startup_AND_driver.common_AND_driver.gpio_AND_driver.i2c_AND_driver.lpuart_AND_driver.port_AND_driver.smc">
      <description>Board_project_template frdmkl27z; {for-development:SDK-Manifest-ID: project_template.frdmkl27z.MKL27Z644}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
