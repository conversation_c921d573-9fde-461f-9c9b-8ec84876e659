<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>HVP-KE18F_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for HVPKE18F</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKE18F16_DFP" vendor="NXP" version="12.2.0"/>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="HVP-KE18F">
      <description>Kinetis KE18 MCU high-voltage development platform</description>
      <mountedDevice Dname="MKE18F512xxx16" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKE18F16">
      <accept Dname="MKE18F512xxx16" Dvariant="MKE18F512VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F512VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F512xxx16" Dvariant="MKE18F512VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F512VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256xxx16" Dvariant="MKE18F256VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256xxx16" Dvariant="MKE18F256VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256VLH16" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MKE18F16_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKE18F16_startup_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_driver.trgmux_AND_utility.debug_console">
      <require condition="device.MKE18F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKE18F16_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="trgmux"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc12_interrupt" folder="driver_examples/adc12/interrupt" doc="readme.txt">
      <description>The adc12_interrupt example shows how to use interrupt with ADC12 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12's sample input.When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value.Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the ADC12 interrupt configuration is set when configuring the ADC12's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing a conversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_lpit_trgmux" folder="driver_examples/adc12/lpit_trgmux" doc="readme.txt">
      <description>The adc12_lpit_trgmux example shows how to use the LPIT and TRGMUX to generate a ADC trigger.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12'ssample input. When run the example, the ADC is triggered by TRGMUX and gets the ADC conversion result in the ADCConversion Complete (COCO) Interrupt. The LPIT is configured as periodic counter which will output pre-trigger andtigger signal to TRGMUX periodically.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_lpit_trgmux.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_lpit_trgmux.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_polling" folder="driver_examples/adc12/polling" doc="readme.txt">
      <description>The adc12_polling example shows the simplest way to use ADC12 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12's sample input.When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.Note, the default setting of initialization for the ADC converter is just an available configuration. User can change theconfiguration structure's setting in application to fit the special requirement.The auto-calibration is not essential but strongly recommended. It can help to adjust the converter itself and improve theADC12's performance.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="cmsis_driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="driver_examples/edma/memory_to_memory" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="driver_examples/edma/scatter_gather" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_scatter_gather.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_dflash" folder="driver_examples/flash/flexnvm_dflash" doc="readme.txt">
      <description>The flexnvm_dflash example shows how to use flash driver to operate data flash:</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_dflash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexnvm_dflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_eeprom" folder="driver_examples/flash/flexnvm_eeprom" doc="readme.txt">
      <description>The flexnvm_eeprom example shows how to use flash driver to operate eeprom:</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_eeprom.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexnvm_eeprom.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="driver_examples/ftm/timer" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_chained_channel" folder="driver_examples/lpit/chained_channel" doc="readme.txt">
      <description>The LPIT chained channel project is a simple example of the SDK LPIT driver. It sets up the LPIT hardware block to trigger a periodic interrupt after every 1 second in the channel No.0, the channel No.1 chained with channel No.0, if LPIT contain more than two channels, the channel No.2 chained with channel No.1....the channel No.N chained with Channel No.N-1. When the LPIT interrupt is triggered.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_chained_channel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpit_chained_channel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_single_channel" folder="driver_examples/lpit/single_channel" doc="readme.txt">
      <description>The LPIT single channel project is a simple example of the SDK LPIT driver. It sets up the LPIThardware block to trigger a periodic interrupt after every 1 second. When the LPIT interrupt is triggereda message a printed on the serial terminal and an LED is toggled on the board.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_single_channel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpit_single_channel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="driver_examples/lptmr" doc="readme.txt">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lptmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="driver_examples/lpuart/interrupt" doc="readme.txt">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="driver_examples/lpuart/interrupt_rb_transfer" doc="readme.txt">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="driver_examples/lpuart/polling" doc="readme.txt">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_adc12_trigger" folder="driver_examples/pdb/adc12_trigger" doc="readme.txt">
      <description>The pdb_adc12_trigger example shows how to use the PDB to generate a ADC trigger.Based on the basic counter, to use the ADC trigger, just to enable the ADC trigger's "milestone" and set the user-defined value for it.After the PDB counter is triggered to start, when the counter pass the "milestone", the ADC's Pre-Trigger would be generated and sentto the ADC12 module.In this example, the ADC12 is configured with hardware trigger and conversion complete interrupt enabled.Once it gets the trigger from the PDB, the conversion goes, then the ISR would be executed.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_adc12_trigger.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pdb_adc12_trigger.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="scg" folder="driver_examples/scg" doc="readme.txt">
      <description>The SCG example shows how to use SCG driver: 1. How to setup the SCG clock source. 2. How to use SCG clock while power mode switch. 3. How to use SCG APIs to get clock frequency.This example prints the clock frequency through the terminal using the SDK driver.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/scg.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/scg.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sysmpu" folder="driver_examples/sysmpu" doc="readme.txt">
      <description>The SYSMPU example defines protected/unprotected memory region for the core access.First, the SYSMPU will capture the hardware information and show it on theterminal. Then, a memory region is configured as the non-writable region. Thebus fault interrupt is enabled to report the memory protection interrupt eventfor this non-writable region. If an operation writes to this region, the busfault interrupt happens. Then the bus fault interrupt handler provides aprevention alert by outputting a message on terminal, then the write rightswill be given to this region for core access. After the write access enabled,the writing to the region becomes successful. When the bus fault happen, thedetails of the error information will be captured and printed on the terminal.This example provides the terminal input control to give the example show for several regions access test. Just press any key to the terminal when theterminal show "Press any key to continue".</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sysmpu.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sysmpu.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog32" folder="driver_examples/wdog32" doc="readme.txt">
      <description>The WDOG32 Example project is to demonstrate usage of the KSDK wdog32 driver.In this example, fast testing is first implemented to test the wdog32.After this, refreshing the watchdog in None-window mode and window mode is executed.Note wdog32 is disabled in SystemInit function which means wdog32 is disabledafter chip emerges from reset.</description>
      <board name="HVP-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog32.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wdog32.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="hvpke18f" Cversion="1.0.0" condition="device.MKE18F16_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKE18F16_startup_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_driver.trgmux_AND_utility.debug_console">
      <description>Board_project_template hvpke18f</description>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
