<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-KE06Z_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDMKE06Z</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKE06Z4_DFP" vendor="NXP" version="14.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-KE06Z">
      <description>Freedom Development Board for Kinetis KE04 (64-128 KB Flash) and KE06 MCUs</description>
      <image small="frdmke06z.png"/>
      <mountedDevice Dname="MKE06Z128xxx4" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKE06Z4">
      <accept Dname="MKE06Z128xxx4" Dvariant="MKE06Z128VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128xxx4" Dvariant="MKE06Z128VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128xxx4" Dvariant="MKE06Z128VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128xxx4" Dvariant="MKE06Z128VLK4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z128VLK4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64xxx4" Dvariant="MKE06Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64xxx4" Dvariant="MKE06Z64VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64xxx4" Dvariant="MKE06Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64xxx4" Dvariant="MKE06Z64VLK4" Dvendor="NXP:11"/>
      <accept Dname="MKE06Z64VLK4" Dvendor="NXP:11"/>
    </condition>
    <condition id="component.serial_manager_AND_utility.debug_console">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
    </condition>
    <condition id="_component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite">
      <accept condition="component.serial_manager_AND_utility.debug_console"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="device.MKE06Z4_AND___component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite__AND_component.uart_adapter_AND_device.MKE06Z4_startup_AND_driver.common_AND_driver.gpio_1_AND_driver.port_ke06_AND_driver.uart">
      <require condition="device.MKE06Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="Startup"/>
      <require condition="_component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_1_interrupt" folder="driver_examples/acmp/interrupt" doc="readme.txt">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison result changed. The purpose of this demo is to show how to use theACMP driver in SDK software by interrupt way. The ACMP can be configured based on defaultconfiguration returned by the API ACMP_GetDefaultConfig().</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_1_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acmp_1_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_1_polling" folder="driver_examples/acmp/polling" doc="readme.txt">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison result changed. The purpose of this demo is to show how to use the ACMP driverin SDK software by polling way. The ACMP can be configured based on default configuration returnedby the API ACMP_GetDefaultConfig(). </description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_1_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acmp_1_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_5v12b_ll18_015_interrupt" folder="driver_examples/adc/interrupt" doc="readme.txt">
      <description>The adc_interrupt example shows how to use interrupt with adc driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the adc'ssample input. When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value. Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the adc interrupt configuration is set when configuring the adc's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing aconversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_5v12b_ll18_015_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc_5v12b_ll18_015_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_5v12b_ll18_015_polling" folder="driver_examples/adc/polling" doc="readme.txt">
      <description>The adc_polling example shows the simplest way to use adc driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the adc'ssample input. When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_5v12b_ll18_015_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc_5v12b_ll18_015_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_read_accel_value_transfer" folder="cmsis_driver_examples/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_read_accel_value example shows how to use CMSIS I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_master" folder="cmsis_driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The spi_interrupt_b2b_transfer example shows how to use spi CMSIS driver in interrupt way:In this example , we need two boards, one board used as spi master and another board used as spi slave.The file 'spi_interrupt_b2b_transfer_master.c' includes the spi master code.This example uses the transactional API in spi driver.1. spi master send/received data to/from spi slave in interrupt . </description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_slave" folder="cmsis_driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_interrupt_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'spi_interrupt_b2b_transfer_slave.c' includes the SPI slave code.This example uses the transactional API in SPI driver.1. SPI master send/received data to/from SPI slave in interrupt . </description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_spi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="cmsis_driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_combine_pwm" folder="driver_examples/ftm/combine_pwm" doc="readme.txt">
      <description>The FTM project is a demonstration program of generating a combined PWM signal by the SDK FTM driver. It sets up the FTMhardware block to output PWM signals on two TPM channels. The example also shows the complementary mode of operationand deadtime insertion.On boards that have 2 LEDs connected to the FTM pins, the user will see a change in LED brightness.And if the board do not support LEDs to show, the outputs can be observed by oscilloscope.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_combine_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_combine_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_dual_edge_capture" folder="driver_examples/ftm/dual_edge_capture" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a FTM channel-pair for dual-edge capture. Once the input signal is received,this example will print the capture values and period of the input signal on the terminal window.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_dual_edge_capture.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_dual_edge_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_input_capture" folder="driver_examples/ftm/input_capture" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's input capture feature.The example sets up a FTM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_input_capture.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_input_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_output_compare" folder="driver_examples/ftm/output_compare" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's output compare feature.It sets up one FTM channel to toggle the output when a match occurs with the channel value. The usershould probe the FTM output with a oscilloscope to see the signal toggling.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_output_compare.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_output_compare.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pwm_twochannel" folder="driver_examples/ftm/pwm_twochannel" doc="readme.txt">
      <description>The FTM pwm two channel Example project is a demonstration program that uses the KSDK software to generate a square pulse PWM on 2 channel to control the LED brightness.- FTM generates a PWM with the increasing and decreasing duty cycle.- LED brightness is increasing and then dimming. This is a continuous process.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pwm_twochannel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_pwm_twochannel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_simple_pwm" folder="driver_examples/ftm/simple_pwm" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver. It sets up the FTMhardware block to output a center-aligned PWM signal. The PWM dutycycle is periodically updated.On boards that have an LED connected to the FTM pins, the user will see a change in LED brightness.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_simple_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="driver_examples/ftm/timer" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_1_led_output" folder="driver_examples/gpio_1/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_1_led_output.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_1_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt" folder="driver_examples/i2c/interrupt" doc="readme.txt">
      <description>The i2c_functional_interrupt example shows how to use I2C functional driver to build a interrupt based application:In this example , one i2c instance used as I2C master and another i2c instance used as I2C slave .1. I2C master send data to I2C slave in interrupt . (I2C Slave using interrupt to receive the data)2. I2C master read data from I2C slave in interrupt . (I2C Slave using interrupt to send the data)3. The example assumes that the connection is OK between master and slave, so there's NO error handling code.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="driver_examples/i2c/polling_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_read_accel_value_transfer" folder="driver_examples/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>The i2c_read_accel_value example shows how to use I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="irq" folder="driver_examples/irq" doc="readme.txt">
      <description>The IRQ Example project is a demonstration program that uses the MCUXpresso SDK software to useIRQ pin interrupt.The example uses the IRQ pin to generate a falling edge interrupt to show the example.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irq.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/irq.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="kbi" folder="driver_examples/kbi" doc="readme.txt">
      <description>The KBI Example project is a demonstration program that uses the KSDK software to usekeyboard interrupt.The example uses one KBI pin to generate a raising edge interrupt to show the example.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kbi.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/kbi.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky_1" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky_1.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_blinky_1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mscan_loopback" folder="driver_examples/mscan/loopback" doc="readme.txt">
      <description>The mscan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will configure one MSCAN Message Txbuffer and Rx buffer with same ID.After that, the example will send a CAN Message from the Tx Buffer to the Rx Bufferthrouth internal loopback interconnect and print out the Message payload to terminal.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mscan_loopback.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mscan_loopback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mscan_loopback_transfer" folder="driver_examples/mscan/loopback_transfer" doc="readme.txt">
      <description>The mscan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one MSCAN MessageBuffer to Rx Message Buffer and the other MSCAN Message Buffer to Tx Message Buffer with same ID.After that, the example will send a CAN Message from the Tx Message Buffer to the Rx Message Bufferthrough internal loopback interconnect and print out the Message payload to terminal.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mscan_loopback_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mscan_loopback_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash_ftmr" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash_ftmr.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pflash_ftmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="driver_examples/pit" doc="readme.txt">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pit.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pwt" folder="driver_examples/pwt" doc="readme.txt">
      <description>The PWT project is a simple demonstration program of the SDK PWT driver. It sets up the PWThardware block to edge detection, capture control part and detects measurement trigger edges andcontrols when and which pulse width register(s) will be updated. Once the input signal is received,this example will print overflow, positive pulse width and negative pulse width.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pwt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_1" folder="driver_examples/rtc" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_1.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc_1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="driver_examples/spi/interrupt_b2b/master" doc="readme.txt">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from slave,and checkif the data master received is correct. This example needs to work with spi_interrupt_b2b_slave example.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="driver_examples/spi/interrupt_b2b/slave" doc="readme.txt">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to master,and check if the data slave received is correct. This example needs to work with spi_interrupt_b2b_master example.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_input_capture" folder="driver_examples/tpm/input_capture" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's input capture feature.The example sets up a TPM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_input_capture.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_input_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_output_compare" folder="driver_examples/tpm/output_compare" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's output compare feature.It sets up one TPM channel to toggle the output when a match occurs with the channel value. The usershould probe the TPM output with a oscilloscope to see the signal toggling.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_output_compare.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_output_compare.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_pwm_twochannel" folder="driver_examples/tpm/pwm_twochannel" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The PWM dutycycle on both channelsis manually updated. On boards that have an LED connected to the TPM pins, the user will seea change in LED brightness if user enter different values.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_pwm_twochannel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_pwm_twochannel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_simple_pwm" folder="driver_examples/tpm/simple_pwm" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output a center-aligned PWM signal. The PWM dutycycle is manually updated.On boards that have an LED connected to the TPM pins, the user will see a change in LEDbrightness if user enter different values.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_simple_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer" folder="driver_examples/tpm/timer" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/tpm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="driver_examples/uart/interrupt" doc="readme.txt">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="driver_examples/uart/interrupt_rb_transfer" doc="readme.txt">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="driver_examples/uart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog8" folder="driver_examples/wdog8" doc="readme.txt">
      <description>The WDOG8 Example project is to demonstrate usage of the wdog8 driver.In this example, fast testing is first implemented to test the wdog8.After this, refreshing the watchdog in None-window mode and window mode is executed.Note wdog8 is disabled in SystemInit function which means wdog8 is disabledafter chip emerges from reset.</description>
      <board name="FRDM-KE06Z" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog8.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wdog8.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmke06z" Cversion="1.0.0" condition="device.MKE06Z4_AND___component.serial_manager_AND_utility.debug_console__OR_utility.debug_console_lite__AND_component.uart_adapter_AND_device.MKE06Z4_startup_AND_driver.common_AND_driver.gpio_1_AND_driver.port_ke06_AND_driver.uart">
      <description>Board_project_template frdmke06z; {for-development:SDK-Manifest-ID: project_template.frdmke06z.MKE06Z4}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
