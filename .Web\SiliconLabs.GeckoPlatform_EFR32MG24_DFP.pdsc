<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32MG24_DFP</name>
  <description>Silicon Labs EFR32MG24 Mighty Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32MG24</keyword>
    <keyword>EFR32</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32MG24 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <book name="Documents/efr32xg24-rm.pdf"  title="EFR32MG24 Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency&#xD;&#xA;- Up to 1536 kB of flash and 256 kB of RAM&#xD;&#xA;- High performance radio with up to +19.5 dBm output power&#xD;&#xA;- Energy efficient design with low active and sleep currents&#xD;&#xA;- Secure Vault&#xD;&#xA;- AI/ML Hardware Accelerator&#xD;&#xA;&#xD;&#xA;The EFR32MG24 Wireless SoCs are ideal for mesh IoT wireless connectivity using Matter, OpenThread and Zigbee.
      </description>

      <subFamily DsubFamily="EFR32MG24A010">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A010 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A010 Errata"/>
        <!-- *************************  Device 'EFR32MG24A010F1024IM40'  ***************************** -->
        <device Dname="EFR32MG24A010F1024IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F1024IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F1024IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F1024IM48'  ***************************** -->
        <device Dname="EFR32MG24A010F1024IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F1024IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F1536GM40'  ***************************** -->
        <device Dname="EFR32MG24A010F1536GM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F1536GM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F1536GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24A010F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24A010F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00030000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24A010F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00030000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F768IM40'  ***************************** -->
        <device Dname="EFR32MG24A010F768IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F768IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F768IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A010F768IM48'  ***************************** -->
        <device Dname="EFR32MG24A010F768IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A010F768IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A010F768IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A020">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A020 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A020 Errata"/>
        <!-- *************************  Device 'EFR32MG24A020F1024IM40'  ***************************** -->
        <device Dname="EFR32MG24A020F1024IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F1024IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F1024IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A020F1024IM48'  ***************************** -->
        <device Dname="EFR32MG24A020F1024IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F1024IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A020F1536GM40'  ***************************** -->
        <device Dname="EFR32MG24A020F1536GM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F1536GM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F1536GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A020F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24A020F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A020F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24A020F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00030000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A020F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24A020F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00030000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A020F768IM40'  ***************************** -->
        <device Dname="EFR32MG24A020F768IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A020F768IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A020F768IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A021">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A021 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A021 Errata"/>
        <!-- *************************  Device 'EFR32MG24A021F1024IM40'  ***************************** -->
        <device Dname="EFR32MG24A021F1024IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A021F1024IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A021F1024IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A110">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A110 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A110 Errata"/>
        <!-- *************************  Device 'EFR32MG24A110F1024IM48'  ***************************** -->
        <device Dname="EFR32MG24A110F1024IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A110F1024IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A110F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A110F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24A110F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A110F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A110F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A111">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A111 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A111 Errata"/>
        <!-- *************************  Device 'EFR32MG24A111F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24A111F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A111F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A111F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A120">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A120 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A120 Errata"/>
        <!-- *************************  Device 'EFR32MG24A120F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24A120F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A120F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A120F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A121">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A121 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A121 Errata"/>
        <!-- *************************  Device 'EFR32MG24A121F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24A121F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A121F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A121F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A410">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A410 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A410 Errata"/>
        <!-- *************************  Device 'EFR32MG24A410F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24A410F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A410F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A410F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A410F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24A410F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A410F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A410F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A420">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A420 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A420 Errata"/>
        <!-- *************************  Device 'EFR32MG24A420F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24A420F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A420F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A420F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24A420F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24A420F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A420F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A420F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A610">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A610 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A610 Errata"/>
        <!-- *************************  Device 'EFR32MG24A610F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24A610F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A610F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A610F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24A620">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24A620 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24A620 Errata"/>
        <!-- *************************  Device 'EFR32MG24A620F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24A620F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24A620F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24A620F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B010">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B010 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B010 Errata"/>
        <!-- *************************  Device 'EFR32MG24B010F1024IM48'  ***************************** -->
        <device Dname="EFR32MG24B010F1024IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B010F1024IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B010F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24B010F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24B010F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B010F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B010F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24B010F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B010F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B010F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B010F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B020">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B020 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B020 Errata"/>
        <!-- *************************  Device 'EFR32MG24B020F1024IM48'  ***************************** -->
        <device Dname="EFR32MG24B020F1024IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B020F1024IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B020F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00020000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24B020F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24B020F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B020F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B020F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24B020F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B020F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B020F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B020F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B110">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B110 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B110 Errata"/>
        <!-- *************************  Device 'EFR32MG24B110F1536GM48'  ***************************** -->
        <device Dname="EFR32MG24B110F1536GM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B110F1536GM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B110F1536GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24B110F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B110F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B110F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B110F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B120">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B120 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B120 Errata"/>
        <!-- *************************  Device 'EFR32MG24B120F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B120F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B120F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B120F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B210">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B210 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B210 Errata"/>
        <!-- *************************  Device 'EFR32MG24B210F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24B210F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B210F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B210F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG24B210F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B210F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B210F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B210F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B220">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B220 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B220 Errata"/>
        <!-- *************************  Device 'EFR32MG24B220F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B220F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B220F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B220F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B310">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B310 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B310 Errata"/>
        <!-- *************************  Device 'EFR32MG24B310F1536IM48'  ***************************** -->
        <device Dname="EFR32MG24B310F1536IM48">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B310F1536IM48"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B310F1536IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG24B610">
        <book         name="Documents/efr32mg24-datasheet.pdf"      title="EFR32MG24B610 Data Sheet"/>
        <book         name="Documents/efr32mg24-errata.pdf"         title="EFR32MG24B610 Errata"/>
        <!-- *************************  Device 'EFR32MG24B610F1536IM40'  ***************************** -->
        <device Dname="EFR32MG24B610F1536IM40">
          <compile header="Device/SiliconLabs/EFR32MG24/Include/em_device.h"  define="EFR32MG24B610F1536IM40"/>
          <debug      svd="SVD/EFR32MG24/EFR32MG24B610F1536IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00028000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32MG24">
      <description>Silicon Labs EFR32MG24 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32MG24*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32MG24">
      <description>System Startup for Silicon Labs EFR32MG24 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32MG24/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32MG24/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32MG24/Source/system_efr32mg24.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
