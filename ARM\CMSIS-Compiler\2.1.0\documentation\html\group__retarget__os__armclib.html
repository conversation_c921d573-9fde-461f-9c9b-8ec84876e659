<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: Arm C Library</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('group__retarget__os__armclib.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">Arm C Library<div class="ingroups"><a class="el" href="group__os__interface__api.html">OS Interface</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Declarations of types and functions for integrating an RTOS with the Arm Standard C Library.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:structrt__mutex__t"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a></td></tr>
<tr class="memdesc:structrt__mutex__t"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mutex type definition.  <a href="group__retarget__os__armclib.html#structrt__mutex__t">More...</a><br /></td></tr>
<tr class="separator:structrt__mutex__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf457d3778f0d2a64a6d028c69f393d61"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__armclib.html#gaf457d3778f0d2a64a6d028c69f393d61">__user_perthread_libspace</a> (void)</td></tr>
<tr class="memdesc:gaf457d3778f0d2a64a6d028c69f393d61"><td class="mdescLeft">&#160;</td><td class="mdescRight">Retrieve thread local storage.  <br /></td></tr>
<tr class="separator:gaf457d3778f0d2a64a6d028c69f393d61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab1e6cdf92dc1e41857e683d358151891"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__armclib.html#gab1e6cdf92dc1e41857e683d358151891">_mutex_initialize</a> (<a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *mutex)</td></tr>
<tr class="memdesc:gab1e6cdf92dc1e41857e683d358151891"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize mutex.  <br /></td></tr>
<tr class="separator:gab1e6cdf92dc1e41857e683d358151891"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaada1d9d5188d36b35e8ea14274010381"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381">_mutex_acquire</a> (<a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *mutex)</td></tr>
<tr class="memdesc:gaada1d9d5188d36b35e8ea14274010381"><td class="mdescLeft">&#160;</td><td class="mdescRight">Acquire mutex.  <br /></td></tr>
<tr class="separator:gaada1d9d5188d36b35e8ea14274010381"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b9b4175427a95bd5f812a736618e63c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__armclib.html#ga5b9b4175427a95bd5f812a736618e63c">_mutex_release</a> (<a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *mutex)</td></tr>
<tr class="memdesc:ga5b9b4175427a95bd5f812a736618e63c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Release mutex.  <br /></td></tr>
<tr class="separator:ga5b9b4175427a95bd5f812a736618e63c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadbed132cb03873020c98e2d95d68c153"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__armclib.html#gadbed132cb03873020c98e2d95d68c153">_mutex_free</a> (<a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *mutex)</td></tr>
<tr class="memdesc:gadbed132cb03873020c98e2d95d68c153"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free mutex.  <br /></td></tr>
<tr class="separator:gadbed132cb03873020c98e2d95d68c153"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Declarations of types and functions for integrating an RTOS with the Arm Standard C Library. </p>
<hr/><h2 class="groupheader">Data Structure Documentation</h2>
<a name="structrt__mutex__t" id="structrt__mutex__t"></a>
<h2 class="memtitle"><span class="permalink"><a href="#structrt__mutex__t">&#9670;&#160;</a></span>rt_mutex_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">struct rt_mutex_t</td>
        </tr>
      </table>
</div><div class="memdoc">
<div class="textblock"><p>Mutex type definition. </p>
<p>The <a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t" title="Mutex type definition.">rt_mutex_t</a> is an incomplete type, an implementation must define it </p>
</div>
</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="gaf457d3778f0d2a64a6d028c69f393d61" name="gaf457d3778f0d2a64a6d028c69f393d61"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf457d3778f0d2a64a6d028c69f393d61">&#9670;&#160;</a></span>__user_perthread_libspace()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void * __user_perthread_libspace </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Retrieve thread local storage. </p>
<p>This function returns a pointer to memory for storing data that is local to a particular thread. This means that <a class="el" href="group__retarget__os__armclib.html#gaf457d3778f0d2a64a6d028c69f393d61" title="Retrieve thread local storage.">__user_perthread_libspace()</a> returns a different address depending on the thread it is called from. </p>

</div>
</div>
<a id="gab1e6cdf92dc1e41857e683d358151891" name="gab1e6cdf92dc1e41857e683d358151891"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab1e6cdf92dc1e41857e683d358151891">&#9670;&#160;</a></span>_mutex_initialize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int _mutex_initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *&#160;</td>
          <td class="paramname"><em>mutex</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize mutex. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">mutex</td><td>Pointer to mutex object </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>nonzero value on success, otherwise the function returns 0</dd></dl>
<p>This function accepts a pointer to a pointer-sized word and initializes it as a valid mutex. By default, <a class="el" href="group__retarget__os__armclib.html#gab1e6cdf92dc1e41857e683d358151891" title="Initialize mutex.">_mutex_initialize()</a> returns zero for a nonthreaded application. Therefore, in a multithreaded application, <a class="el" href="group__retarget__os__armclib.html#gab1e6cdf92dc1e41857e683d358151891" title="Initialize mutex.">_mutex_initialize()</a> must return a nonzero value on success so that at runtime, the library knows that it is being used in a multithreaded environment. Ensure that <a class="el" href="group__retarget__os__armclib.html#gab1e6cdf92dc1e41857e683d358151891" title="Initialize mutex.">_mutex_initialize()</a> initializes the mutex to an unlocked state. </p>

</div>
</div>
<a id="gaada1d9d5188d36b35e8ea14274010381" name="gaada1d9d5188d36b35e8ea14274010381"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaada1d9d5188d36b35e8ea14274010381">&#9670;&#160;</a></span>_mutex_acquire()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void _mutex_acquire </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *&#160;</td>
          <td class="paramname"><em>mutex</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Acquire mutex. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">mutex</td><td>Pointer to mutex object</td></tr>
  </table>
  </dd>
</dl>
<p>This function causes the calling thread to obtain a lock on the supplied mutex. <a class="el" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381" title="Acquire mutex.">_mutex_acquire()</a> returns immediately if the mutex has no owner. If the mutex is owned by another thread, <a class="el" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381" title="Acquire mutex.">_mutex_acquire()</a> must block until it becomes available. <a class="el" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381" title="Acquire mutex.">_mutex_acquire()</a> is not called by the thread that already owns the mutex. </p>

</div>
</div>
<a id="ga5b9b4175427a95bd5f812a736618e63c" name="ga5b9b4175427a95bd5f812a736618e63c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5b9b4175427a95bd5f812a736618e63c">&#9670;&#160;</a></span>_mutex_release()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void _mutex_release </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *&#160;</td>
          <td class="paramname"><em>mutex</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Release mutex. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">mutex</td><td>Pointer to mutex object</td></tr>
  </table>
  </dd>
</dl>
<p>This function causes the calling thread to release the lock on a mutex acquired by <a class="el" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381" title="Acquire mutex.">_mutex_acquire()</a>. The mutex remains in existence, and can be re-locked by a subsequent call to mutex_acquire(). <a class="el" href="group__retarget__os__armclib.html#ga5b9b4175427a95bd5f812a736618e63c" title="Release mutex.">_mutex_release()</a> assumes that the mutex is owned by the calling thread. </p>

</div>
</div>
<a id="gadbed132cb03873020c98e2d95d68c153" name="gadbed132cb03873020c98e2d95d68c153"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadbed132cb03873020c98e2d95d68c153">&#9670;&#160;</a></span>_mutex_free()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void _mutex_free </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__retarget__os__armclib.html#structrt__mutex__t">rt_mutex_t</a> *&#160;</td>
          <td class="paramname"><em>mutex</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free mutex. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">mutex</td><td>Pointer to mutex object</td></tr>
  </table>
  </dd>
</dl>
<p>This function causes the calling thread to free the supplied mutex. Any operating system resources associated with the mutex are freed. The mutex is destroyed and cannot be reused. <a class="el" href="group__retarget__os__armclib.html#gadbed132cb03873020c98e2d95d68c153" title="Free mutex.">_mutex_free()</a> assumes that the mutex is owned by the calling thread. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
