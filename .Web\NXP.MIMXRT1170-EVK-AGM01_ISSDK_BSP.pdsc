<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1170-EVK-AGM01_ISSDK_BSP</name>
  <vendor>NXP</vendor>
  <description>Middleware issdk Board Support Pack for MIMXRT1170-EVK-AGM01</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.1" date="2023-10-25" deprecated="2023-10-25" replacement="NXP.MIMXRT1170-EVK-AGM01_ISSDK_Examples">This pack is no longer maintained. The new pack has been released with "_Examples" suffix instead of "_BSP".</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="ISSDK" vendor="NXP" version="1.0.1"/>
      <package name="MIMXRT1176_DFP" vendor="NXP" version="18.0.0"/>
      <package name="MIMXRT1170-EVK_BSP" vendor="NXP" version="18.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.8.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT1170-EVK-AGM01">
      <description>i.MX RT1170 Evaluation Kit with 9-Axis sensor shield board FRDM-STBC-AGM01 having FXAS21002C Gyroscope &amp; FXOS8700CQ E-compass sensors</description>
      <image small="boards/evkmimxrt1170_agm01/evkmimxrt1170_agm01.png"/>
      <mountedDevice Dname="MIMXRT1176xxxxx" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MIMXRT1176.internal_condition">
      <accept Dname="MIMXRT1176xxxxx" Dvariant="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176xxxxx" Dvariant="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176xxxxx" Dvariant="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.evkmimxrt1170_agm01.condition_id">
      <require condition="allOf.device.MIMXRT1176_startup, driver.lpuart, driver.clock, driver.common, driver.iomuxc, driver.igpio, driver.xip_device, driver.xip_board.evkmimxrt1170, utility.debug_console, component.lpuart_adapter, component.serial_manager_uart, component.serial_manager, driver.pmu_1, driver.dcdc_soc, driver.gpc_3, device=MIMXRT1176.internal_condition"/>
    </condition>
    <condition id="allOf.device.MIMXRT1176_startup, driver.lpuart, driver.clock, driver.common, driver.iomuxc, driver.igpio, driver.xip_device, driver.xip_board.evkmimxrt1170, utility.debug_console, component.lpuart_adapter, component.serial_manager_uart, component.serial_manager, driver.pmu_1, driver.dcdc_soc, driver.gpc_3, device=MIMXRT1176.internal_condition">
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iomuxc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xip_device"/>
      <require Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkmimxrt1170 xip"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="pmu_1"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dcdc_soc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpc_3"/>
      <require condition="device.MIMXRT1176.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="fxas21002_fifo" folder="boards/evkmimxrt1170_agm01/issdk_examples/sensors/fxas21002/fxas21002_fifo/cm7" doc="readme.txt">
      <description>The FXAS21002 FIFO example application demonstrates the use of the FXAS21002 sensor in Buffered (FIFO) Mode.The example demonstrates configuration of all registers reguired to put the sensor in FIFO Mode and read out samples.The sensor reads samples and are buffered upto the configured Water Mark Level and then a Flag is set.The application consistently checks the Flag and when set, reads out all(count=Water Mark Level) samples.</description>
      <board name="MIMXRT1170-EVK-AGM01" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxas21002_fifo.uvprojx"/>
        <environment name="csolution" load="fxas21002_fifo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxas21002_poll" folder="boards/evkmimxrt1170_agm01/issdk_examples/sensors/fxas21002/fxas21002_poll/cm7" doc="readme.txt">
      <description>The FXAS21002 Poll example application demonstrates the use of the FXAS21002 sensor in the Poll (Non-Buffered) Mode. The example demonstrates configuration of all registers reguired to put the sensor into Poll Mode and read out a sample.</description>
      <board name="MIMXRT1170-EVK-AGM01" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxas21002_poll.uvprojx"/>
        <environment name="csolution" load="fxas21002_poll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxos8700_fifo" folder="boards/evkmimxrt1170_agm01/issdk_examples/sensors/fxos8700/fxos8700_fifo/cm7" doc="readme.txt">
      <description>The FXAS8700 FIFO example application demonstrates the use of the FXAS8700 sensor in Buffered (FIFO) Mode.The example demonstrates configuration of all registers reguired to put the sensor in FIFO Mode and read out samples.The sensor reads samples and are buffered upto the configured Water Mark Level and then a Flag is set.The application consistently checks the Flag and when set, reads out all(count=Water Mark Level) samples.</description>
      <board name="MIMXRT1170-EVK-AGM01" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxos8700_fifo.uvprojx"/>
        <environment name="csolution" load="fxos8700_fifo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxos8700_poll" folder="boards/evkmimxrt1170_agm01/issdk_examples/sensors/fxos8700/fxos8700_poll/cm7" doc="readme.txt">
      <description>The FXOS8700 Poll example application demonstrates the use of the FXOS8700 sensor in the Poll (Non-Buffered) Mode. The example demonstrates configuration of all registers reguired to put the sensor into Poll Mode and read out a sample.</description>
      <board name="MIMXRT1170-EVK-AGM01" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxos8700_poll.uvprojx"/>
        <environment name="csolution" load="fxos8700_poll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Sensors" Cgroup="ISSDK project template" Csub="evkmimxrt1170_agm01" Cvariant="evkmimxrt1170_agm01" Cversion="1.0.0" condition="BOARD_Project_Template.evkmimxrt1170_agm01.condition_id">
      <description>Board_project_template evkmimxrt1170_agm01</description>
      <RTE_Components_h>
#ifndef XIP_EXTERNAL_FLASH
#define XIP_EXTERNAL_FLASH 1
#endif
#ifndef XIP_BOOT_HEADER_ENABLE
#define XIP_BOOT_HEADER_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1170_agm01/project_template/board.c" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1170_agm01/project_template/board.h" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1170_agm01/project_template/clock_config.c" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1170_agm01/project_template/clock_config.h" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1170_agm01/project_template/pin_mux.c" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1170_agm01/project_template/pin_mux.h" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1170_agm01/project_template/peripherals.c" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1170_agm01/project_template/peripherals.h" version="1.0.0" projectpath="evkmimxrt1170_agm01/project_template"/>
        <file category="include" name="boards/evkmimxrt1170_agm01/project_template/"/>
      </files>
    </component>
  </components>
</package>
