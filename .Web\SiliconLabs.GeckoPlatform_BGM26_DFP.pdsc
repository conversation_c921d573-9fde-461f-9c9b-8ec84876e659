<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_BGM26_DFP</name>
  <description>Silicon Labs BGM26 Blue Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>BGM26</keyword>
    <keyword>BGM26</keyword>
    <keyword>Blue Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="BGM26 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
- 32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in QFN packaging;- Integrated PA with up to 19.5 dBm transmit power;- Energy-efficient radio core with low active and sleep currents;- Secure Vault;- AI/ML Hardware Accelerator;- Up to 3200 kB of flash and 512 kB of RAM&#xD;&#xA;
      </description>

      <subFamily DsubFamily="BGM260P">
        <!-- *************************  Device 'BGM260PB22VNA'  ***************************** -->
        <device Dname="BGM260PB22VNA">
          <compile header="Device/SiliconLabs/BGM26/Include/em_device.h"  define="BGM260PB22VNA"/>
          <debug      svd="SVD/BGM26/BGM260PB22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'BGM260PB32VNA'  ***************************** -->
        <device Dname="BGM260PB32VNA">
          <compile header="Device/SiliconLabs/BGM26/Include/em_device.h"  define="BGM260PB32VNA"/>
          <debug      svd="SVD/BGM26/BGM260PB32VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00320000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00320000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00320000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="BGM26">
      <description>Silicon Labs BGM26 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="BGM26*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="BGM26">
      <description>System Startup for Silicon Labs BGM26 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/BGM26/Include/"/>
        <file category="header"  name="Device/SiliconLabs/BGM26/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/BGM26/Source/system_bgm26.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
