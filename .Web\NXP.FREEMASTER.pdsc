<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FREEMASTER</name>
  <vendor>NXP</vendor>
  <description>Software Pack for fmstr</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.fmstr.condition_id">
      <require condition="anyOf.middleware.fmstr.platform_gen32le.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.fmstr.platform_gen32le.internal_condition">
      <accept Cclass="FreeMASTER" Cgroup="FreeMASTER Driver" Csub="FreeMASTER 32bit driver" Cvariant="Communication driver 32bit"/>
    </condition>
    <condition id="device_id.K32L2A31xxxxA, K32L2A41xxxxA, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5502, LPC5504, LPC5506, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S04, LPC55S06, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, LPC824, LPC844, LPC845, LPC864, LPC865, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MKE12Z256xxx7, MKE12Z128xxx7, MKE12Z512xxx9, MKE13Z256xxx7, MKE13Z128xxx7, MKE13Z512xxx9, MKE14Z64xxx4, MKE14Z32xxx4, MKE14Z256xxx7, MKE14Z128xxx7, MKE15Z64xxx4, MKE15Z32xxx4, MKE15Z256xxx7, MKE15Z128xxx7, MKE16Z64xxx4, MKE16Z32xxx4, MKE17Z256xxx7, MKE17Z128xxx7, MKE17Z512xxx9, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, RW610, RW612.internal_condition">
      <accept Dname="K32L2A31VLH1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A31VLL1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A41VLH1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2A41VLL1A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="LPC51U68JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC51U68JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54628J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC5502JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5502JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5504JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5504JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5506JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5506JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5512JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5512JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5514JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5516JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S04JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S04JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S06JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S06JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S14JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S16JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC824M201JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC824M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC844M201JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC844M201JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC844M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC844M201JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC864M201JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC864M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC864M201JHI48" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC865M201JHI48" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA144VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA145VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA146VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA154VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA155VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VLL" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXA156VPJ" Dvendor="NXP:11"/>
      <accept Dname="MCXC041VFG" Dvendor="NXP:11"/>
      <accept Dname="MCXC041VFK" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC142VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC243VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN235VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN236VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011CAE4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1011DAE5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1015DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VFM10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VLF10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VFM10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VLF10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VDC10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLL10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VMP10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VMP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VMP12" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z256VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z128VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z128VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z512VLH9" Dvendor="NXP:11"/>
      <accept Dname="MKE12Z512VLL9" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z256VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z128VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z128VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z512VLH9" Dvendor="NXP:11"/>
      <accept Dname="MKE13Z512VLL9" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z64VFP4" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z64VLF4" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z32VFP4" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z32VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z32VLF4" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE14Z128VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z64VFP4" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z64VLF4" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z32VFP4" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z32VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z32VLF4" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z128VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE16Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE16Z64VLF4" Dvendor="NXP:11"/>
      <accept Dname="MKE16Z32VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE16Z32VLF4" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z256VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z128VLF7" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z128VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z512VLH9" Dvendor="NXP:11"/>
      <accept Dname="MKE17Z512VLL9" Dvendor="NXP:11"/>
      <accept Dname="MKM14Z64ACHH5" Dvendor="NXP:11"/>
      <accept Dname="MKM14Z128ACHH5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z64ACLH5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z64ACLL5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z128ACLH5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z128ACLL5" Dvendor="NXP:11"/>
      <accept Dname="MKM34Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM34Z256VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM34Z128ACLL5" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLQ7R" Dvendor="NXP:11"/>
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.fmstr.platform_gen32le.condition_id">
      <require condition="allOf.middleware.fmstr, anyOf=device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5502, LPC5504, LPC5506, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S04, LPC55S06, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, LPC824, LPC844, LPC845, LPC864, LPC865, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MKE12Z256xxx7, MKE12Z128xxx7, MKE12Z512xxx9, MKE13Z256xxx7, MKE13Z128xxx7, MKE13Z512xxx9, MKE14Z64xxx4, MKE14Z32xxx4, MKE14Z256xxx7, MKE14Z128xxx7, MKE15Z64xxx4, MKE15Z32xxx4, MKE15Z256xxx7, MKE15Z128xxx7, MKE16Z64xxx4, MKE16Z32xxx4, MKE17Z256xxx7, MKE17Z128xxx7, MKE17Z512xxx9, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, RW610, RW612.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.fmstr, anyOf=device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5502, LPC5504, LPC5506, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S04, LPC55S06, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, LPC824, LPC844, LPC845, LPC864, LPC865, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MKE12Z256xxx7, MKE12Z128xxx7, MKE12Z512xxx9, MKE13Z256xxx7, MKE13Z128xxx7, MKE13Z512xxx9, MKE14Z64xxx4, MKE14Z32xxx4, MKE14Z256xxx7, MKE14Z128xxx7, MKE15Z64xxx4, MKE15Z32xxx4, MKE15Z256xxx7, MKE15Z128xxx7, MKE16Z64xxx4, MKE16Z32xxx4, MKE17Z256xxx7, MKE17Z128xxx7, MKE17Z512xxx9, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, RW610, RW612.internal_condition">
      <require Cclass="FreeMASTER" Cgroup="FreeMASTER Driver" Csub="Common Driver Code" Cvariant="Communication driver common code"/>
      <require condition="anyOf.device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5502, LPC5504, LPC5506, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S04, LPC55S06, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, LPC824, LPC844, LPC845, LPC864, LPC865, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MKE12Z256xxx7, MKE12Z128xxx7, MKE12Z512xxx9, MKE13Z256xxx7, MKE13Z128xxx7, MKE13Z512xxx9, MKE14Z64xxx4, MKE14Z32xxx4, MKE14Z256xxx7, MKE14Z128xxx7, MKE15Z64xxx4, MKE15Z32xxx4, MKE15Z256xxx7, MKE15Z128xxx7, MKE16Z64xxx4, MKE16Z32xxx4, MKE17Z256xxx7, MKE17Z128xxx7, MKE17Z512xxx9, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, RW610, RW612.internal_condition"/>
    </condition>
    <condition id="anyOf.device_id=K32L2A31xxxxA, K32L2A41xxxxA, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5502, LPC5504, LPC5506, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S04, LPC55S06, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, LPC824, LPC844, LPC845, LPC864, LPC865, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MKE12Z256xxx7, MKE12Z128xxx7, MKE12Z512xxx9, MKE13Z256xxx7, MKE13Z128xxx7, MKE13Z512xxx9, MKE14Z64xxx4, MKE14Z32xxx4, MKE14Z256xxx7, MKE14Z128xxx7, MKE15Z64xxx4, MKE15Z32xxx4, MKE15Z256xxx7, MKE15Z128xxx7, MKE16Z64xxx4, MKE16Z32xxx4, MKE17Z256xxx7, MKE17Z128xxx7, MKE17Z512xxx9, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, RW610, RW612.internal_condition">
      <accept condition="device_id.K32L2A31xxxxA, K32L2A41xxxxA, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, K32L3A60xxx, LPC51U68, LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5502, LPC5504, LPC5506, LPC5512, LPC5514, LPC5516, LPC5526, LPC5528, LPC5534, LPC5536, LPC55S04, LPC55S06, LPC55S14, LPC55S16, LPC55S26, LPC55S28, LPC55S36, LPC55S66, LPC55S69, LPC824, LPC844, LPC845, LPC864, LPC865, MCXA142, MCXA143, MCXA144, MCXA145, MCXA146, MCXA152, MCXA153, MCXA154, MCXA155, MCXA156, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MCXN235, MCXN236, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1011xxxxx, MIMXRT1015xxxxx, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, MKE12Z256xxx7, MKE12Z128xxx7, MKE12Z512xxx9, MKE13Z256xxx7, MKE13Z128xxx7, MKE13Z512xxx9, MKE14Z64xxx4, MKE14Z32xxx4, MKE14Z256xxx7, MKE14Z128xxx7, MKE15Z64xxx4, MKE15Z32xxx4, MKE15Z256xxx7, MKE15Z128xxx7, MKE16Z64xxx4, MKE16Z32xxx4, MKE17Z256xxx7, MKE17Z128xxx7, MKE17Z512xxx9, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, RW610, RW612.internal_condition"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="FreeMASTER" Cgroup="FreeMASTER Driver" Csub="Common Driver Code" Cvariant="Communication driver common code" Cversion="3.0.7" condition="middleware.fmstr.condition_id">
      <description>Common FreeMASTER driver code.</description>
      <files>
        <file category="header" name="middleware/freemaster/src/common/freemaster.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_appcmd.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_defcfg.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_pipes.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_private.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_protocol.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_protocol.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_rec.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_rec.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_scope.c" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_tsa.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_tsa.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_ures.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_ures.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_utils.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_utils.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_can.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_can.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_sha.c" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_pdbdm.c" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_serial.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_serial.h" projectpath="freemaster"/>
        <file category="sourceC" name="middleware/freemaster/src/common/freemaster_net.c" projectpath="freemaster"/>
        <file category="header" name="middleware/freemaster/src/common/freemaster_net.h" projectpath="freemaster"/>
        <file category="include" name="middleware/freemaster/src/common/"/>
      </files>
    </component>
    <component Cclass="FreeMASTER" Cgroup="FreeMASTER Driver" Csub="FreeMASTER 32bit driver" Cvariant="Communication driver 32bit" Cversion="3.0.7" condition="middleware.fmstr.platform_gen32le.condition_id">
      <description>FreeMASTER driver code for 32bit platforms, enabling communication between FreeMASTER or FreeMASTER Lite tools and MCU application. Supports Serial, CAN, USB and BDM/JTAG physical interface.</description>
      <files>
        <file category="header" attr="config" name="middleware/freemaster/src/template/gen32le/freemaster_cfg.h" version="3.0.7" projectpath="source"/>
        <file category="header" name="middleware/freemaster/src/platforms/gen32le/freemaster_gen32le.h" projectpath="freemaster/platforms"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/can/freemaster_flexcan.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/can/freemaster_flexcan.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/can/freemaster_mscan.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/can/freemaster_mscan.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/can/freemaster_mcan.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/can/freemaster_mcan.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_uart.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_uart.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_lpsci.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_lpsci.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_lpuart.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_lpuart.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_usart.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_usart.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_miniusart.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_miniusart.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_usb.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/serial/freemaster_serial_usb.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/network/freemaster_net_segger_rtt.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/network/freemaster_net_segger_rtt.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/network/freemaster_net_lwip_udp.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/network/freemaster_net_lwip_udp.h" projectpath="freemaster/drivers"/>
        <file category="sourceC" name="middleware/freemaster/src/drivers/mcuxsdk/network/freemaster_net_lwip_tcp.c" projectpath="freemaster/drivers"/>
        <file category="header" name="middleware/freemaster/src/drivers/mcuxsdk/network/freemaster_net_lwip_tcp.h" projectpath="freemaster/drivers"/>
        <file category="include" name="middleware/freemaster/src/platforms/gen32le/"/>
        <file category="include" name="middleware/freemaster/src/drivers/mcuxsdk/can/"/>
        <file category="include" name="middleware/freemaster/src/drivers/mcuxsdk/serial/"/>
      </files>
    </component>
  </components>
</package>
