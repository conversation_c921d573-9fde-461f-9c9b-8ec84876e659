<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MKL33Z4_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MKL33Z4</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Project Template">NXP MCUXpresso SDK RTE Device Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="CMSIS Driver">NXP MCUXpresso SDK Peripheral CMSIS Drivers</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="MKL33Z4" Dvendor="NXP:11">
      <description>Kinetis KL3x-48 MHz, Segment LCD Ultra-Low-Power Microcontrollers (MCUs) based on ARM Cortex-M0+ Core</description>
      <device Dname="MKL33Z128xxx4">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="48000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKL33Z128xxx4_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x020000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x1ffff000" size="0x4000" access="rw" default="1"/>
        <algorithm name="arm/MK_P128_48MHZ_KL43.FLM" start="0x00000000" size="0x00020000" RAMstart="0x1ffff000" RAMsize="0x00000800" default="1"/>
        <debug svd="MKL33Z4.xml"/>
        <variant Dvariant="MKL33Z128VLH4">
          <compile header="fsl_device_registers.h" define="CPU_MKL33Z128VLH4"/>
        </variant>
        <variant Dvariant="MKL33Z128VMP4">
          <compile header="fsl_device_registers.h" define="CPU_MKL33Z128VMP4"/>
        </variant>
      </device>
      <device Dname="MKL33Z256xxx4">
        <processor Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="48000000"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKL33Z256xxx4_flash.icf"/>
        </environment>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x040000" access="rx" default="1" startup="1"/>
        <memory name="SRAM" start="0x1fffe000" size="0x8000" access="rw" default="1"/>
        <algorithm name="arm/MK_P256_48MHZ_KL43.FLM" start="0x00000000" size="0x00040000" RAMstart="0x1fffe000" RAMsize="0x00000800" default="1"/>
        <debug svd="MKL33Z4.xml"/>
        <variant Dvariant="MKL33Z256VLH4">
          <compile header="fsl_device_registers.h" define="CPU_MKL33Z256VLH4"/>
        </variant>
        <variant Dvariant="MKL33Z256VMP4">
          <compile header="fsl_device_registers.h" define="CPU_MKL33Z256VMP4"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.MKL33Z4">
      <accept Dname="MKL33Z256xxx4" Dvariant="MKL33Z256VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z256VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z256xxx4" Dvariant="MKL33Z256VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z256VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z128xxx4" Dvariant="MKL33Z128VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z128VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z128xxx4" Dvariant="MKL33Z128VMP4" Dvendor="NXP:11"/>
      <accept Dname="MKL33Z128VMP4" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKL33Z4_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_utility.debug_console">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKL33Z4_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.osa_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="osa"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.flash">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flash"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.gpio_AND_driver.port">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.lptmr">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.lpuart">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.lists_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.pit">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="pit"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.tpm">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="tpm"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.lists_AND_component.serial_manager_uart_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
    </condition>
    <condition id="driver.lpuart_OR_driver.uart">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
    </condition>
    <condition id="device.MKL33Z4_AND__driver.lpuart_OR_driver.uart__AND_component.lpuart_adapter">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="driver.lpuart_OR_driver.uart"/>
    </condition>
    <condition id="component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="tpm_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="pit_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter"/>
    </condition>
    <condition id="device.MKL33Z4_AND__component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter__AND_component.lists_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.uart">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
    </condition>
    <condition id="device.MKL33Z4_AND_CMSIS_Include_core_cm0plus">
      <require condition="device.MKL33Z4"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="armclang_OR_iar">
      <accept Tcompiler="ARMCC" Toptions="AC6"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="device.MKL33Z4_AND__armclang_OR_iar_">
      <require condition="device.MKL33Z4"/>
      <require condition="armclang_OR_iar"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="device.MKL33Z4_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c_dma">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_dma"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL33Z4_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart_dma">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_dma"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL33Z4_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.spi_dma">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi_dma"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" Capiversion="2.2.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL33Z4_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.uart_dma">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_dma"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKL33Z4_AND_device.MKL33Z4_CMSIS_AND_driver.clock">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKL33Z4_header"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.common_AND_driver.dmamux">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.flexio">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.flexio_i2s">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.flexio_uart">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.i2c">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.lpuart">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.sai">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sai"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.spi">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_driver.dma_AND_driver.uart">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.MKL33Z4_AND_utility.debug_console">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.serial_manager_AND_driver.common">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKL33Z4_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKL33Z4"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="MKL33Z4" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKL33Z4_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_utility.debug_console" isDefaultVariant="1">
      <description>Devices_project_template MKL33Z4</description>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.dma">
      <description>Rte_device</description>
      <files>
        <file category="header" attr="config" name="template/RTE_Device.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="button" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component button</description>
      <files>
        <file category="sourceC" name="components/button/button.c"/>
        <file category="header" name="components/button/button.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common_task" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.osa_AND_driver.common">
      <description>Component common_task</description>
      <files>
        <file category="sourceC" name="components/common_task/common_task.c"/>
        <file category="header" name="components/common_task/common_task.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.flash">
      <description>Component flash_adapter</description>
      <files>
        <file category="header" name="components/internal_flash/flash.h"/>
        <file category="sourceC" name="components/internal_flash/flash_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.gpio_AND_driver.port">
      <description>Component gpio_adapter</description>
      <files>
        <file category="header" name="components/gpio/gpio.h"/>
        <file category="sourceC" name="components/gpio/gpio_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="led" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component led</description>
      <files>
        <file category="sourceC" name="components/led/led.c"/>
        <file category="header" name="components/led/led.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common">
      <description>Component lists</description>
      <files>
        <file category="sourceC" name="components/lists/generic_list.c"/>
        <file category="header" name="components/lists/generic_list.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.lptmr">
      <description>Component lptmr_adapter</description>
      <files>
        <file category="sourceC" name="components/timer/lptmr_adapter.c"/>
        <file category="header" name="components/timer/timer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.lpuart">
      <description>Component lpuart_adapter</description>
      <files>
        <file category="sourceC" name="components/uart/lpuart_adapter.c"/>
        <file category="header" name="components/uart/uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.lists_AND_driver.common">
      <description>Component mem_manager</description>
      <files>
        <file category="sourceC" name="components/mem_manager/mem_manager.c"/>
        <file category="header" name="components/mem_manager/mem_manager.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.lists_AND_driver.common">
      <description>Component osa</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa_bm" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.lists_AND_driver.common">
      <description>Component osa_bm</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="panic" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_utility.debug_console">
      <description>Component panic</description>
      <files>
        <file category="sourceC" name="components/panic/panic.c"/>
        <file category="header" name="components/panic/panic.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pit_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.pit">
      <description>Component pit_adapter</description>
      <files>
        <file category="sourceC" name="components/timer/pit_adapter.c"/>
        <file category="header" name="components/timer/timer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pwm_tpm_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.tpm">
      <description>Component pwm_tpm_adapter</description>
      <files>
        <file category="header" name="components/pwm/pwm.h"/>
        <file category="sourceC" name="components/pwm/pwm_tpm_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.lists_AND_component.serial_manager_uart_AND_driver.common">
      <description>Component serial_manager</description>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_manager.c"/>
        <file category="header" name="components/serial_manager/serial_manager.h"/>
        <file category="header" name="components/serial_manager/serial_port_internal.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart" Cversion="1.0.0" condition="device.MKL33Z4_AND__driver.lpuart_OR_driver.uart__AND_component.lpuart_adapter">
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#define SERIAL_PORT_TYPE_UART 1
</RTE_Components_h>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_port_uart.c"/>
        <file category="header" name="components/serial_manager/serial_port_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_crc_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common">
      <description>Component software_crc_adapter</description>
      <files>
        <file category="header" name="components/crc/crc.h"/>
        <file category="sourceC" name="components/crc/software_crc_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager" Cversion="1.0.0" condition="device.MKL33Z4_AND__component.lptmr_adapter_OR_component.pit_adapter_OR_component.tpm_adapter__AND_component.lists_AND_driver.common">
      <description>Component timer_manager</description>
      <files>
        <file category="sourceC" name="components/timer_manager/timer_manager.c"/>
        <file category="header" name="components/timer_manager/timer_manager.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="tpm_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.tpm">
      <description>Component tpm_adapter</description>
      <files>
        <file category="header" name="components/timer/timer.h"/>
        <file category="sourceC" name="components/timer/tpm_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common_AND_driver.uart">
      <description>Component uart_adapter</description>
      <files>
        <file category="header" name="components/uart/uart.h"/>
        <file category="sourceC" name="components/uart/uart_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="MKL33Z4_header" Cversion="1.0.0" condition="device.MKL33Z4_AND_CMSIS_Include_core_cm0plus">
      <description>Device MKL33Z4_cmsis</description>
      <files>
        <file category="header" name="MKL33Z4.h"/>
        <file category="header" name="MKL33Z4_features.h"/>
        <file category="header" name="fsl_device_registers.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Csub="MKL33Z4_startup" Cversion="1.1.0" condition="device.MKL33Z4_AND__armclang_OR_iar_">
      <description>Device MKL33Z4_startup</description>
      <files>
        <file condition="mdk" category="sourceAsm" name="arm/startup_MKL33Z4.S"/>
        <file condition="iar" category="sourceAsm" name="iar/startup_MKL33Z4.s"/>
        <file category="sourceC" name="system_MKL33Z4.c"/>
        <file category="header" name="system_MKL33Z4.h"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL33Z128xxx4_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL33Z128xxx4_ram.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL33Z256xxx4_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKL33Z256xxx4_ram.scf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL33Z128xxx4_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL33Z128xxx4_ram.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL33Z256xxx4_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKL33Z256xxx4_ram.icf" version="1.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="adc" Cversion="2.1.0" condition="device.MKL33Z4_AND_driver.common">
      <description>ADC16 Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_adc16.c"/>
        <file category="header" name="drivers/fsl_adc16.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.3.2" condition="device.MKL33Z4_AND_driver.common">
      <description>Clock Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
        <file category="header" name="drivers/fsl_clock.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cmp" Cversion="2.0.2" condition="device.MKL33Z4_AND_driver.common">
      <description>CMP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cmp.c"/>
        <file category="header" name="drivers/fsl_cmp.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="i2c_cmsis_dma" Cversion="2.0.2" condition="device.MKL33Z4_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.i2c_dma">
      <description>I2C CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_i2c_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_i2c_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="lpuart_cmsis_dma" Cversion="2.0.1" condition="device.MKL33Z4_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart_dma">
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_lpuart_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_lpuart_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="spi_cmsis_dma" Cversion="2.0.0" condition="device.MKL33Z4_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.spi_dma">
      <description>SPI CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_spi_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_spi_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="uart_cmsis_dma" Cversion="2.0.1" condition="device.MKL33Z4_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.uart_dma">
      <description>UART CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_uart_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_uart_cmsis.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.2.4" condition="device.MKL33Z4_AND_device.MKL33Z4_CMSIS_AND_driver.clock">
      <description>COMMON Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_common.c"/>
        <file category="header" name="drivers/fsl_common.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cop" Cversion="2.0.1" condition="device.MKL33Z4_AND_driver.common">
      <description>COP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cop.c"/>
        <file category="header" name="drivers/fsl_cop.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dac" Cversion="2.0.2" condition="device.MKL33Z4_AND_driver.common">
      <description>DAC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dac.c"/>
        <file category="header" name="drivers/fsl_dac.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dma" Cversion="2.0.2" condition="device.MKL33Z4_AND_driver.common_AND_driver.dmamux">
      <description>DMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dma.c"/>
        <file category="header" name="drivers/fsl_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux" Cversion="2.0.4" condition="device.MKL33Z4_AND_driver.common">
      <description>DMAMUX Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dmamux.c"/>
        <file category="header" name="drivers/fsl_dmamux.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash" Cversion="3.0.0" condition="device.MKL33Z4_AND_driver.common">
      <description>Flash Driver</description>
      <files>
        <file category="header" name="drivers/fsl_flash.h"/>
        <file category="header" name="drivers/fsl_ftfx_adapter.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_cache.c"/>
        <file category="header" name="drivers/fsl_ftfx_cache.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_controller.c"/>
        <file category="header" name="drivers/fsl_ftfx_controller.h"/>
        <file category="header" name="drivers/fsl_ftfx_features.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flash.c"/>
        <file category="header" name="drivers/fsl_ftfx_flash.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flexnvm.c"/>
        <file category="header" name="drivers/fsl_ftfx_flexnvm.h"/>
        <file category="header" name="drivers/fsl_ftfx_utilities.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio" Cversion="2.0.3" condition="device.MKL33Z4_AND_driver.common">
      <description>FLEXIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio.c"/>
        <file category="header" name="drivers/fsl_flexio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2c_master" Cversion="2.4.0" condition="device.MKL33Z4_AND_driver.flexio">
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2c_master.c"/>
        <file category="header" name="drivers/fsl_flexio_i2c_master.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s" Cversion="2.2.0" condition="device.MKL33Z4_AND_driver.flexio">
      <description>FLEXIO I2S Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2s.c"/>
        <file category="header" name="drivers/fsl_flexio_i2s.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2s_dma" Cversion="2.1.7" condition="device.MKL33Z4_AND_driver.dma_AND_driver.flexio_i2s">
      <description>FLEXIO I2S DMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2s_dma.c"/>
        <file category="header" name="drivers/fsl_flexio_i2s_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart" Cversion="2.2.0" condition="device.MKL33Z4_AND_driver.flexio">
      <description>FLEXIO UART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart.c"/>
        <file category="header" name="drivers/fsl_flexio_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart_dma" Cversion="2.2.0" condition="device.MKL33Z4_AND_driver.dma_AND_driver.flexio_uart">
      <description>FLEXIO UART DMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart_dma.c"/>
        <file category="header" name="drivers/fsl_flexio_uart_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.5.1" condition="device.MKL33Z4_AND_driver.common">
      <description>GPIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
        <file category="header" name="drivers/fsl_gpio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.0.8" condition="device.MKL33Z4_AND_driver.common">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c.c"/>
        <file category="header" name="drivers/fsl_i2c.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_dma" Cversion="2.0.8" condition="device.MKL33Z4_AND_driver.dma_AND_driver.i2c">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c_dma.c"/>
        <file category="header" name="drivers/fsl_i2c_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="llwu" Cversion="2.0.5" condition="device.MKL33Z4_AND_driver.common">
      <description>LLWU Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_llwu.c"/>
        <file category="header" name="drivers/fsl_llwu.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr" Cversion="2.1.1" condition="device.MKL33Z4_AND_driver.common">
      <description>LPTMR Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lptmr.c"/>
        <file category="header" name="drivers/fsl_lptmr.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart" Cversion="2.3.0" condition="device.MKL33Z4_AND_driver.common">
      <description>LPUART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpuart.c"/>
        <file category="header" name="drivers/fsl_lpuart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_dma" Cversion="2.3.0" condition="device.MKL33Z4_AND_driver.dma_AND_driver.lpuart">
      <description>LPUART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpuart_dma.c"/>
        <file category="header" name="drivers/fsl_lpuart_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pit" Cversion="2.0.3" condition="device.MKL33Z4_AND_driver.common">
      <description>PIT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pit.c"/>
        <file category="header" name="drivers/fsl_pit.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pmc" Cversion="2.0.3" condition="device.MKL33Z4_AND_driver.common">
      <description>PMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pmc.c"/>
        <file category="header" name="drivers/fsl_pmc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="port" Cversion="2.1.0" condition="device.MKL33Z4_AND_driver.common">
      <description>PORT Driver</description>
      <files>
        <file category="header" name="drivers/fsl_port.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rcm" Cversion="2.0.4" condition="device.MKL33Z4_AND_driver.common">
      <description>RCM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rcm.c"/>
        <file category="header" name="drivers/fsl_rcm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rtc" Cversion="2.2.1" condition="device.MKL33Z4_AND_driver.common">
      <description>RTC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rtc.c"/>
        <file category="header" name="drivers/fsl_rtc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sai" Cversion="2.3.1" condition="device.MKL33Z4_AND_driver.common">
      <description>SAI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sai.c"/>
        <file category="header" name="drivers/fsl_sai.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sai_dma" Cversion="2.3.1" condition="device.MKL33Z4_AND_driver.dma_AND_driver.sai">
      <description>SAI DMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sai_dma.c"/>
        <file category="header" name="drivers/fsl_sai_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sim" Cversion="2.1.1" condition="device.MKL33Z4_AND_driver.common">
      <description>SIM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sim.c"/>
        <file category="header" name="drivers/fsl_sim.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="slcd" Cversion="2.0.2" condition="device.MKL33Z4_AND_driver.common">
      <description>SLCD Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_slcd.c"/>
        <file category="header" name="drivers/fsl_slcd.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="smc" Cversion="2.0.7" condition="device.MKL33Z4_AND_driver.common">
      <description>SMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_smc.c"/>
        <file category="header" name="drivers/fsl_smc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi" Cversion="2.1.0" condition="device.MKL33Z4_AND_driver.common">
      <description>SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_spi.c"/>
        <file category="header" name="drivers/fsl_spi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi_dma" Cversion="2.1.0" condition="device.MKL33Z4_AND_driver.dma_AND_driver.spi">
      <description>SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_spi_dma.c"/>
        <file category="header" name="drivers/fsl_spi_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="tpm" Cversion="2.0.7" condition="device.MKL33Z4_AND_driver.common">
      <description>TPM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_tpm.c"/>
        <file category="header" name="drivers/fsl_tpm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart" Cversion="2.3.0" condition="device.MKL33Z4_AND_driver.common">
      <description>UART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_uart.c"/>
        <file category="header" name="drivers/fsl_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="uart_dma" Cversion="2.3.0" condition="device.MKL33Z4_AND_driver.dma_AND_driver.uart">
      <description>UART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_uart_dma.c"/>
        <file category="header" name="drivers/fsl_uart_dma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="vref" Cversion="2.1.2" condition="device.MKL33Z4_AND_driver.common">
      <description>VREF Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_vref.c"/>
        <file category="header" name="drivers/fsl_vref.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert" Cversion="1.0.0" condition="device.MKL33Z4_AND_utility.debug_console">
      <description>Utility assert</description>
      <files>
        <file category="sourceC" name="utilities/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.serial_manager_AND_driver.common">
      <description>Utility debug_console</description>
      <files>
        <file category="sourceC" name="utilities/debug_console/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console.h"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console_conf.h"/>
        <file category="sourceC" name="utilities/str/fsl_str.c"/>
        <file category="header" name="utilities/str/fsl_str.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="notifier" Cversion="1.0.0" condition="device.MKL33Z4_AND_driver.common">
      <description>Utility notifier</description>
      <files>
        <file category="sourceC" name="utilities/fsl_notifier.c"/>
        <file category="header" name="utilities/fsl_notifier.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="shell" Cversion="1.0.0" condition="device.MKL33Z4_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <description>Utility shell</description>
      <RTE_Components_h>
#define DEBUG_CONSOLE_RX_ENABLE 0
</RTE_Components_h>
      <files>
        <file category="sourceC" name="utilities/fsl_shell.c"/>
        <file category="header" name="utilities/fsl_shell.h"/>
      </files>
    </component>
  </components>
</package>
