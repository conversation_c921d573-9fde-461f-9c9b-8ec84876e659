<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXN947_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-MCXN947</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="17.0.0" date="2024-01-29">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCXN947_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-MCXN947">
      <description>FRDM-MCXN947</description>
      <image small="boards/frdmmcxn947/frdmmcxn947.png"/>
      <mountedDevice Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXN947VPB" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MCXN946.internal_condition">
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXN947.internal_condition">
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MCXN947.internal_condition">
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmmcxn947.condition_id">
      <require condition="allOf.board=frdmmcxn947, component.lpuart_adapter, device_id=MCXN947, device.MCXN947_startup, driver.clock, driver.gpio, driver.lpuart, driver.port, driver.mcx_spc, driver.reset, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmmcxn947, component.lpuart_adapter, device_id=MCXN947, device.MCXN947_startup, driver.clock, driver.gpio, driver.lpuart, driver.port, driver.mcx_spc, driver.reset, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.frdmmcxn947.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="device_id.MCXN947.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mcx_spc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmmcxn947.internal_condition">
      <accept condition="device.MCXN946.internal_condition"/>
      <accept condition="device.MCXN947.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="cdog" folder="boards/frdmmcxn947/driver_examples/cdog/cm33_core0" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog.uvprojx"/>
        <environment name="iar" load="iar/cdog.ewp"/>
        <environment name="csolution" load="cdog.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master" folder="boards/frdmmcxn947/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave" folder="boards/frdmmcxn947/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master" folder="boards/frdmmcxn947/cmsis_driver_examples/lpi2c/int_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave" folder="boards/frdmmcxn947/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master" folder="boards/frdmmcxn947/cmsis_driver_examples/lpspi/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave" folder="boards/frdmmcxn947/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master" folder="boards/frdmmcxn947/cmsis_driver_examples/lpspi/int_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave" folder="boards/frdmmcxn947/cmsis_driver_examples/lpspi/int_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="boards/frdmmcxn947/cmsis_driver_examples/lpuart/edma_transfer/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="boards/frdmmcxn947/cmsis_driver_examples/lpuart/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/frdmmcxn947/driver_examples/crc/cm33_core0" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/frdmmcxn947/driver_examples/ctimer/simple_match/cm33_core0" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example.ewp"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/frdmmcxn947/driver_examples/ctimer/simple_match_interrupt/cm33_core0" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/frdmmcxn947/driver_examples/ctimer/simple_pwm/cm33_core0" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/frdmmcxn947/driver_examples/ctimer/simple_pwm_interrupt/cm33_core0" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac14_basic" folder="boards/frdmmcxn947/driver_examples/dac14/dac14_basic/cm33_core0" doc="readme.md">
      <description>The DAC14 basic example shows how to use the DAC module simply as the general DAC converter. No support is needed to be triggered by DAC in the example using the buffer mode. The value written into DAC data register...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac14_basic.uvprojx"/>
        <environment name="iar" load="iar/dac14_basic.ewp"/>
        <environment name="csolution" load="dac14_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac14_interrupt" folder="boards/frdmmcxn947/driver_examples/dac14/dac14_interrupt/cm33_core0" doc="readme.md">
      <description>The DAC14 interrupt example shows how to use the DAC FIFO interrupt. When the application starts to run, it will immediately enter the DAC ISR and write data into the FIFO which is empty at first. Once the DAC FIFO...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac14_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dac14_interrupt.ewp"/>
        <environment name="csolution" load="dac14_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_1_basic" folder="boards/frdmmcxn947/driver_examples/dac/dac_basic/cm33_core0" doc="readme.md">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's buffer feature is not enabled, the first item of the buffer is used as the DAC output data register.The converter...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_1_basic.uvprojx"/>
        <environment name="iar" load="iar/dac_1_basic.ewp"/>
        <environment name="csolution" load="dac_1_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_1_buffer_interrupt" folder="boards/frdmmcxn947/driver_examples/dac/dac_buffer_interrupt/cm33_core0" doc="readme.md">
      <description>The dac_buffer_interrupt example shows how to use DAC FIFO interrupt.When the DAC FIFO empty interrupt is enabled firstly, the application would enter the DAC ISR immediately, since the FIFO is actually empty. Then...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_1_buffer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dac_1_buffer_interrupt.ewp"/>
        <environment name="csolution" load="dac_1_buffer_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_channel_link" folder="boards/frdmmcxn947/driver_examples/edma3/channel_link/cm33_core0" doc="readme.md">
      <description>The EDMA3 channel link example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_channel_link.uvprojx"/>
        <environment name="iar" load="iar/edma3_channel_link.ewp"/>
        <environment name="csolution" load="edma3_channel_link.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_interleave_transfer" folder="boards/frdmmcxn947/driver_examples/edma3/interleave_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA3 interleave example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_interleave_transfer.ewp"/>
        <environment name="csolution" load="edma3_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memory_to_memory" folder="boards/frdmmcxn947/driver_examples/edma3/memory_to_memory/cm33_core0" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/edma3_memory_to_memory.ewp"/>
        <environment name="csolution" load="edma3_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memory_to_memory_transfer" folder="boards/frdmmcxn947/driver_examples/edma3/memory_to_memory_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA3 memory to memory transfer example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA3 drivers.The purpose...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memory_to_memory_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_memory_to_memory_transfer.ewp"/>
        <environment name="csolution" load="edma3_memory_to_memory_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memset" folder="boards/frdmmcxn947/driver_examples/edma3/memset/cm33_core0" doc="readme.md">
      <description>The EDMA3 memset example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA3 drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memset.uvprojx"/>
        <environment name="iar" load="iar/edma3_memset.ewp"/>
        <environment name="csolution" load="edma3_memset.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_ping_pong_transfer" folder="boards/frdmmcxn947/driver_examples/edma3/ping_pong_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA3 ping pong transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_ping_pong_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_ping_pong_transfer.ewp"/>
        <environment name="csolution" load="edma3_ping_pong_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_scatter_gather" folder="boards/frdmmcxn947/driver_examples/edma3/scatter_gather/cm33_core0" doc="readme.md">
      <description>The EDMA3 scatter gather example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA3 drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_scatter_gather.uvprojx"/>
        <environment name="iar" load="iar/edma3_scatter_gather.ewp"/>
        <environment name="csolution" load="edma3_scatter_gather.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_wrap_transfer" folder="boards/frdmmcxn947/driver_examples/edma3/wrap_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA3 wrap transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_wrap_transfer.ewp"/>
        <environment name="csolution" load="edma3_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_hash" folder="boards/frdmmcxn947/els_pkc_examples/els_hash/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of hash algorithms and a crypto library lightweight testing.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_hash.uvprojx"/>
        <environment name="csolution" load="els_hash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_pkc_asymmetric" folder="boards/frdmmcxn947/els_pkc_examples/els_pkc_asymmetric/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of asymmetric algorithms and a crypto library lightweight testing.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_pkc_asymmetric.uvprojx"/>
        <environment name="csolution" load="els_pkc_asymmetric.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_pkc_common" folder="boards/frdmmcxn947/els_pkc_examples/els_pkc_common/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of common features including PRNG and a crypto library lightweight testing.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_pkc_common.uvprojx"/>
        <environment name="csolution" load="els_pkc_common.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_symmetric" folder="boards/frdmmcxn947/els_pkc_examples/els_symmetric/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of symmetric algorithms and a crypto library lightweight testing.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_symmetric.uvprojx"/>
        <environment name="csolution" load="els_symmetric.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="error_recording" folder="boards/frdmmcxn947/driver_examples/erm/error_recording/cm33_core0" doc="readme.md">
      <description>The ERM Single Error project is a simple demonstration program of the SDK ERM driver. It shows how to show error events using the ERM driver.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/error_recording.uvprojx"/>
        <environment name="iar" load="iar/error_recording.ewp"/>
        <environment name="csolution" load="error_recording.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="evtg" folder="boards/frdmmcxn947/driver_examples/evtg/cm33_core0" doc="readme.md">
      <description>The EVTG project is a simple demonstration of the SDK EVTG driver. EVTG example will use SCTimer output 2 PWM sigal and then EVTG will genertae the intersection of PWM0 and PWM1.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/evtg.uvprojx"/>
        <environment name="iar" load="iar/evtg.ewp"/>
        <environment name="csolution" load="evtg.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm" folder="boards/frdmmcxn947/driver_examples/ewm/cm33_core0" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm.uvprojx"/>
        <environment name="iar" load="iar/ewm.ewp"/>
        <environment name="csolution" load="ewm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer" folder="boards/frdmmcxn947/driver_examples/flexcan/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexcan_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback" folder="boards/frdmmcxn947/driver_examples/flexcan/loopback/cm33_core0" doc="readme.md">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback.ewp"/>
        <environment name="csolution" load="flexcan_loopback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer" folder="boards/frdmmcxn947/driver_examples/flexcan/loopback_transfer/cm33_core0" doc="readme.md">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_transfer.ewp"/>
        <environment name="csolution" load="flexcan_loopback_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_ping_pong_buffer_transfer" folder="boards/frdmmcxn947/driver_examples/flexcan/ping_pong_buffer_transfer/cm33_core0" doc="readme.md">
      <description>The flexcan_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CAN frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_ping_pong_buffer_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_ping_pong_buffer_transfer.ewp"/>
        <environment name="csolution" load="flexcan_ping_pong_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_pretended_networking_wakeup" folder="boards/frdmmcxn947/driver_examples/flexcan/pretended_networking_wakeup/cm33_core0" doc="readme.md">
      <description>The flexcan_pretended_networking_wakeup example shows how to wake up FLEXCAN module from Pretended Networking mode:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message to...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_pretended_networking_wakeup.uvprojx"/>
        <environment name="iar" load="iar/flexcan_pretended_networking_wakeup.ewp"/>
        <environment name="csolution" load="flexcan_pretended_networking_wakeup.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/i2c/interrupt_lpi2c_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_mculcd_edma_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/mculcd/edma_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_mculcd_edma_transfer example shows how to use flexio mculcd edma driver to drive MCU LCD panel.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_mculcd_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_mculcd_edma_transfer.ewp"/>
        <environment name="csolution" load="flexio_mculcd_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_mculcd_int_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/mculcd/int_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_mculcd_int_transfer example shows how to use flexio mculcd driver interrupt based transactional APIs to drive MCU LCD panel.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_mculcd_int_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_mculcd_int_transfer.ewp"/>
        <environment name="csolution" load="flexio_mculcd_int_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_mculcd_polling" folder="boards/frdmmcxn947/driver_examples/flexio/mculcd/polling/cm33_core0" doc="readme.md">
      <description>The flexio_mculcd_polling example shows how to use flexio mculcd driver polling APIs to drive MCU LCD panel.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_mculcd_polling.uvprojx"/>
        <environment name="iar" load="iar/flexio_mculcd_polling.ewp"/>
        <environment name="csolution" load="flexio_mculcd_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_mculcd_smartdma_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/mculcd/smartdma_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_mculcd_smartdma_transfer example shows how to use flexio mculcd smartdma driver to drive MCU LCD panel.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_mculcd_smartdma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_mculcd_smartdma_transfer.ewp"/>
        <environment name="csolution" load="flexio_mculcd_smartdma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="boards/frdmmcxn947/driver_examples/flexio/pwm/cm33_core0" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm.ewp"/>
        <environment name="csolution" load="flexio_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/flexio/spi/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_edma example shows how to use flexio spi master  driver in edma way:In this example, a flexio simulated master connect to a flexio simulated spi slave .</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/flexio/spi/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_edma example shows how to use flexio spi slave  driver in dma way:In this example, a flexio simulated slave connect to a flexio simulated spi master.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master" folder="boards/frdmmcxn947/driver_examples/flexio/spi/edma_lpspi_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave" folder="boards/frdmmcxn947/driver_examples/flexio/spi/edma_lpspi_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/flexio/spi/int_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_interrupt example shows how to use flexio spi master  driver in interrupt way:In this example, a flexio simulated master connect to a flexio simulated spi slave .</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/flexio/spi/int_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_interrupt example shows how to use flexio spi slave  driver in interrupt way:In this example, a flexio simulated slave connect to a flexio simulated spi master.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master" folder="boards/frdmmcxn947/driver_examples/flexio/spi/int_lpspi_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave" folder="boards/frdmmcxn947/driver_examples/flexio/spi/int_lpspi_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/uart/edma_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/uart/int_rb_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/uart/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="boards/frdmmcxn947/driver_examples/flexio/uart/polling_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_dma3_transfer" folder="boards/frdmmcxn947/driver_examples/flexspi/octal/edma_transfer/cm33_core0" doc="readme.md">
      <description>The flexspi_octal_dma3_transfer example shows how to use flexspi driver with dma. In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_dma3_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_dma3_transfer.ewp"/>
        <environment name="csolution" load="flexspi_octal_dma3_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_polling_transfer" folder="boards/frdmmcxn947/driver_examples/flexspi/octal/polling_transfer/cm33_core0" doc="readme.md">
      <description>The flexspi_octal_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_polling_transfer.ewp"/>
        <environment name="csolution" load="flexspi_octal_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="freqme_interrupt" folder="boards/frdmmcxn947/driver_examples/freqme/cm33_core0" doc="readme.md">
      <description>The freqme_interrupt is a demonstration program of the SDK LPC_FREQME driver's features. The example demostrate the usage of frequency measurement operate mode and pulse width measurement operate mode.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freqme_interrupt.uvprojx"/>
        <environment name="iar" load="iar/freqme_interrupt.ewp"/>
        <environment name="csolution" load="freqme_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdet" folder="boards/frdmmcxn947/driver_examples/gdet/cm33_core0" doc="readme.md">
      <description>The GDET Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Glitch Detect (GDET) module.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdet.uvprojx"/>
        <environment name="iar" load="iar/gdet.ewp"/>
        <environment name="csolution" load="gdet.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/frdmmcxn947/driver_examples/gpio/input_interrupt/cm33_core0" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear registers for each GPIO pin output register. </description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/frdmmcxn947/driver_examples/gpio/led_output/cm33_core0" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmmcxn947/demo_apps/hello_world/cm33_core0" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_ns" folder="boards/frdmmcxn947/trustzone_examples/hello_world/cm33_core0/hello_world_ns" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. This demo application also utilizes TrustZone, so it demonstrates basic techniques for TrustZone...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_ns.uvprojx"/>
        <environment name="iar" load="iar/hello_world_ns.ewp"/>
        <environment name="csolution" load="../hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_qspi_xip" folder="boards/frdmmcxn947/demo_apps/hello_world_qspi_xip/cm33_core0" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_qspi_xip.uvprojx"/>
        <environment name="iar" load="iar/hello_world_qspi_xip.ewp"/>
        <environment name="csolution" load="hello_world_qspi_xip.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_s" folder="boards/frdmmcxn947/trustzone_examples/hello_world/cm33_core0/hello_world_s" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. This demo application also utilizes TrustZone, so it demonstrates basic techniques for TrustZone...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_s.uvprojx"/>
        <environment name="iar" load="iar/hello_world_s.ewp"/>
        <environment name="csolution" load="../hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/i3c/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_master example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as master and another i3c instance on the other board as slave....See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/i3c/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as slave and another i3c instance on the other board as master....See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/i3c/interrupt_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/i3c/interrupt_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_p3t1755" folder="boards/frdmmcxn947/driver_examples/i3c/master_read_sensor_p3t1755/cm33_core0" doc="readme.md">
      <description>The i3c_master_read_sensor_p3t1755 example shows how to use i3c driver as master to communicate with sensor P3T1755.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_p3t1755.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_p3t1755.ewp"/>
        <environment name="csolution" load="i3c_master_read_sensor_p3t1755.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/i3c/polling_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/i3c/polling_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="intm" folder="boards/frdmmcxn947/driver_examples/intm/cm33_core0" doc="readme.md">
      <description>The INTM project is a simple demonstration of the SDK INTM driver. The role of INTM is to monitor the interrupt response. The main monitoring is whether the interrupt has timed out from the request to the response....See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/intm.uvprojx"/>
        <environment name="iar" load="iar/intm.ewp"/>
        <environment name="csolution" load="intm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="irtc" folder="boards/frdmmcxn947/driver_examples/irtc/cm33_core0" doc="readme.md">
      <description>The IRTC project is a simple demonstration program of the SDK IRTC driver.This example is a low power module that provides time keeping and calendaring functions and additionally providesprotection against tampering,...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irtc.uvprojx"/>
        <environment name="iar" load="iar/irtc.ewp"/>
        <environment name="csolution" load="irtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="itrc" folder="boards/frdmmcxn947/driver_examples/itrc/cm33_core0" doc="readme.md">
      <description>The ITRC Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Intrusion and Tamper Response Controller.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/itrc.uvprojx"/>
        <environment name="iar" load="iar/itrc.ewp"/>
        <environment name="csolution" load="itrc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/frdmmcxn947/demo_apps/led_blinky/cm33_core0" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_edma" folder="boards/frdmmcxn947/driver_examples/lpadc/edma/cm33_core0" doc="readme.md">
      <description>The lpadc_edma example shows how to use ADC to trigger a DMA transfer. In this example, user should indicate a channel to provide a voltage signal(can be controlled by user) as the LPADC's sample input and active the...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_edma.uvprojx"/>
        <environment name="iar" load="iar/lpadc_edma.ewp"/>
        <environment name="csolution" load="lpadc_edma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt" folder="boards/frdmmcxn947/driver_examples/lpadc/interrupt/cm33_core0" doc="readme.md">
      <description>The lpdc_single_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt.ewp"/>
        <environment name="csolution" load="lpadc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling" folder="boards/frdmmcxn947/driver_examples/lpadc/polling/cm33_core0" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling.ewp"/>
        <environment name="csolution" load="lpadc_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_temperature_measurement" folder="boards/frdmmcxn947/driver_examples/lpadc/temperature_measurement/cm33_core0" doc="readme.md">
      <description>The lpadc_temperature_measurement example shows how to measure the temperature within the internal sensor. In this example, the ADC input channel is mapped to an internal temperature sensor. When running the project,...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_temperature_measurement.uvprojx"/>
        <environment name="iar" load="iar/lpadc_temperature_measurement.ewp"/>
        <environment name="csolution" load="lpadc_temperature_measurement.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpcmp_interrupt" folder="boards/frdmmcxn947/driver_examples/lpcmp/interrupt/cm33_core0" doc="readme.md">
      <description>The LPCMP interrupt Example shows how to use interrupt with LPCMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the LPCMP's positive channel...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpcmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpcmp_interrupt.ewp"/>
        <environment name="csolution" load="lpcmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpcmp_polling" folder="boards/frdmmcxn947/driver_examples/lpcmp/polling/cm33_core0" doc="readme.md">
      <description>The LPCMP polling Example shows the simplest way to use LPCMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpcmp_polling.uvprojx"/>
        <environment name="iar" load="iar/lpcmp_polling.ewp"/>
        <environment name="csolution" load="lpcmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpcmp_round_robin" folder="boards/frdmmcxn947/driver_examples/lpcmp/round_robin/cm33_core0" doc="readme.md">
      <description>The LPCMP round robin example is a simple demonstration program to use the LPCMP driver and help user with a quick start.In this example, user needs to specify which port and channel to fixed, users also need to...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpcmp_round_robin.uvprojx"/>
        <environment name="iar" load="iar/lpcmp_round_robin.ewp"/>
        <environment name="csolution" load="lpcmp_round_robin.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpflexcomm_interrupt_transfer" folder="boards/frdmmcxn947/driver_examples/lpflexcomm/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The lpflexcomm_interrupt_transfer example shows how to use lpi2c driver and lpuart driver to build a interrupt based application.In this example, lpi2c and lpuart use same lpflexcomm instance.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpflexcomm_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpflexcomm_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpflexcomm_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/lpi2c/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/lpi2c/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt" folder="boards/frdmmcxn947/driver_examples/lpi2c/interrupt/cm33_core0" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/lpi2c/interrupt_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/lpi2c/interrupt_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master" folder="boards/frdmmcxn947/driver_examples/lpi2c/polling_b2b/master/cm33_core0" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave" folder="boards/frdmmcxn947/driver_examples/lpi2c/polling_b2b/slave/cm33_core0" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/lpspi/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/lpspi/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt" folder="boards/frdmmcxn947/driver_examples/lpspi/interrupt/cm33_core0" doc="readme.md">
      <description>The lpspi_functional_interrupt example shows how to use LPSPI driver in interrupt way:In this example , one lpspi instance used as LPSPI master and another lpspi instance used as LPSPI slave .1. LPSPI master...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt.ewp"/>
        <environment name="csolution" load="lpspi_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master" folder="boards/frdmmcxn947/driver_examples/lpspi/interrupt_b2b/master/cm33_core0" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave" folder="boards/frdmmcxn947/driver_examples/lpspi/interrupt_b2b/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/lpspi/interrupt_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/lpspi/interrupt_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master" folder="boards/frdmmcxn947/driver_examples/lpspi/polling_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave" folder="boards/frdmmcxn947/driver_examples/lpspi/polling_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="boards/frdmmcxn947/driver_examples/lptmr/cm33_core0" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
        <environment name="csolution" load="lptmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_interrupt_transfer" folder="boards/frdmmcxn947/driver_examples/lpuart/9bit_interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_9bit_interrupt_transfer example shows how to use lpuart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_transfer" folder="boards/frdmmcxn947/driver_examples/lpuart/9bit_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_9bit_transfer example shows how to use lpuart driver in 9-bit mode. Only 9bit data sent can be received by itself.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_transfer.ewp"/>
        <environment name="csolution" load="lpuart_9bit_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="boards/frdmmcxn947/driver_examples/lpuart/edma_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/frdmmcxn947/driver_examples/lpuart/interrupt/cm33_core0" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/frdmmcxn947/driver_examples/lpuart/interrupt_rb_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/frdmmcxn947/driver_examples/lpuart/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits" folder="boards/frdmmcxn947/driver_examples/lpuart/interrupt_transfer_seven_bits/cm33_core0" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/frdmmcxn947/driver_examples/lpuart/polling/cm33_core0" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits" folder="boards/frdmmcxn947/driver_examples/lpuart/polling_seven_bits/cm33_core0" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_interrupt_cm33_core0" folder="boards/frdmmcxn947/driver_examples/mailbox/interrupt/cm33_core0" doc="readme.md">
      <description>The primary core writes a value to the secondary core mailbox, it causes mailbox interrupt on the secondary core side. The secondary core reads the value from mailbox, it increments and writes it to mailbox register...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_interrupt_cm33_core0.uvprojx"/>
        <environment name="iar" load="iar/mailbox_interrupt_cm33_core0.ewp"/>
        <environment name="csolution" load="../mailbox_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_mutex_cm33_core0" folder="boards/frdmmcxn947/driver_examples/mailbox/mutex/cm33_core0" doc="readme.md">
      <description>The mailbox_mutex example shows how to use mailbox mutex.In this example:The core 0 sends address of shared variable to core 1 by mailbox.Both cores trying to get mutex in while loop, after that updates shared...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_mutex_cm33_core0.uvprojx"/>
        <environment name="iar" load="iar/mailbox_mutex_cm33_core0.ewp"/>
        <environment name="csolution" load="../mailbox_mutex.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_ptp1588_transfer" folder="boards/frdmmcxn947/driver_examples/enet/txrx_ptp1588_transfer/cm33_core0" doc="readme.md">
      <description>The enet_rxtx_ptp1588_transfer example shows the way to use ENET driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the ENET.2. How to get the time stamp...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_ptp1588_transfer.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_ptp1588_transfer.ewp"/>
        <environment name="csolution" load="enet_txrx_ptp1588_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_rxinterrupt" folder="boards/frdmmcxn947/driver_examples/enet/txrx_rxinterrupt/cm33_core0" doc="readme.md">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET functional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame in...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_rxinterrupt.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_rxinterrupt.ewp"/>
        <environment name="csolution" load="enet_txrx_rxinterrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_rxpoll" folder="boards/frdmmcxn947/driver_examples/enet/txrx_rxpoll/cm33_core0" doc="readme.md">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET functional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame in...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_rxpoll.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_rxpoll.ewp"/>
        <environment name="csolution" load="enet_txrx_rxpoll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer_rxinterrupt" folder="boards/frdmmcxn947/driver_examples/enet/txrx_transfer_rxinterrupt/cm33_core0" doc="readme.md">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET transactional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer_rxinterrupt.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_transfer_rxinterrupt.ewp"/>
        <environment name="csolution" load="enet_txrx_transfer_rxinterrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer_rxpoll" folder="boards/frdmmcxn947/driver_examples/enet/txrx_transfer_rxpoll/cm33_core0" doc="readme.md">
      <description>The enet_rxtx_rxinterrupt example shows the simplest way to use ENET transactional tx/rx API for simple frame receive and transmit.1. This example shows how to initialize the ENET.2. How to use ENET to receive frame...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer_rxpoll.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_transfer_rxpoll.ewp"/>
        <environment name="csolution" load="enet_txrx_transfer_rxpoll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="opamp_basic" folder="boards/frdmmcxn947/driver_examples/opamp/opamp_basic/cm33_core0" doc="readme.md">
      <description>The OPAMP basic example demonstrates how to use the OPAMP driver. In this example, the OPAMP works in the internal gain mode, connect the positive terminal of the OPAMP to the external input source. The OPAMP output...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/opamp_basic.uvprojx"/>
        <environment name="iar" load="iar/opamp_basic.ewp"/>
        <environment name="csolution" load="opamp_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="opamp_dac_adc" folder="boards/frdmmcxn947/driver_examples/opamp/opamp_dac_adc/cm33_core0" doc="readme.md">
      <description>The OPAMP DAC ADC example shows how to use OPAMP PGA mode. in this example, both positive input and negative input should be connected to GND,the positive reference voltage is set as DAC output, the OPAMP output is...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/opamp_dac_adc.uvprojx"/>
        <environment name="iar" load="iar/opamp_dac_adc.ewp"/>
        <environment name="csolution" load="opamp_dac_adc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="opamp_trigger_mode" folder="boards/frdmmcxn947/driver_examples/opamp/opamp_trigger_mode/cm33_core0" doc="readme.md">
      <description>The OPAMP trigger mode example demonstrates how to use SCTIMER to trigger OPAMP. In this example, INP0 and INP1 are connected to different levels. When OPAMP receives a trigger signal, it will switch to different output voltages.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/opamp_trigger_mode.uvprojx"/>
        <environment name="iar" load="iar/opamp_trigger_mode.ewp"/>
        <environment name="csolution" load="opamp_trigger_mode.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcx_secure_gpio_ns" folder="boards/frdmmcxn947/trustzone_examples/secure_gpio/cm33_core0/secure_gpio_ns" doc="readme.md">
      <description>Secure GPIO demo application demonstrates using GPIO which restrict usage of GPIO pins in nonsecure world. This is non-secure part of the application.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcx_secure_gpio_ns.uvprojx"/>
        <environment name="iar" load="iar/mcx_secure_gpio_ns.ewp"/>
        <environment name="csolution" load="../secure_gpio.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcx_secure_gpio_s" folder="boards/frdmmcxn947/trustzone_examples/secure_gpio/cm33_core0/secure_gpio_s" doc="readme.md">
      <description>Secure GPIO demo application demonstrates using GPIO which restrict usage of GPIO pins in nonsecure world. This is secure part of the application.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcx_secure_gpio_s.uvprojx"/>
        <environment name="iar" load="iar/mcx_secure_gpio_s.ewp"/>
        <environment name="csolution" load="../secure_gpio.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_hiding" folder="boards/frdmmcxn947/driver_examples/romapi/flashhiding/cm33_core0" doc="readme.md">
      <description>The FLASH_HIDING project is a simple demonstration program showing flash hiding and customer defined proteted area (CDPA).</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_hiding.uvprojx"/>
        <environment name="iar" load="iar/flash_hiding.ewp"/>
        <environment name="csolution" load="flash_hiding.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flashiap" folder="boards/frdmmcxn947/driver_examples/romapi/flashiap/cm33_core0" doc="readme.md">
      <description>The FLASIAP project is a simple demonstration program of the SDK FLASIAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flashiap.uvprojx"/>
        <environment name="iar" load="iar/flashiap.ewp"/>
        <environment name="csolution" load="flashiap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor" folder="boards/frdmmcxn947/driver_examples/romapi/flexspi_nor/cm33_core0" doc="readme.md">
      <description>The FLASIAP NOR project is a simple demonstration program of the SDK FLASIAP NOR driver. It erases and programs a portion of external Nor flash connected with FLEXSPI. Some simple flash command willbe executed,such...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor.ewp"/>
        <environment name="csolution" load="flexspi_nor.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mem_interface" folder="boards/frdmmcxn947/driver_examples/romapi/mem_interface/cm33_core0" doc="readme.md">
      <description>The mem_interface project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of internal and external flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mem_interface.uvprojx"/>
        <environment name="iar" load="iar/mem_interface.ewp"/>
        <environment name="csolution" load="mem_interface.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="runbootloader" folder="boards/frdmmcxn947/driver_examples/romapi/runbootloader/cm33_core0" doc="readme.md">
      <description>The runbootloader project is a simple demonstration which run into the bootloader with the user parameter. The demo forces the device run into the ISP mode with the specific peripheral.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/runbootloader.uvprojx"/>
        <environment name="iar" load="iar/runbootloader.ewp"/>
        <environment name="csolution" load="runbootloader.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/frdmmcxn947/driver_examples/mrt/cm33_core0" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example" folder="boards/frdmmcxn947/driver_examples/ostimer/cm33_core0" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example.uvprojx"/>
        <environment name="iar" load="iar/ostimer_example.ewp"/>
        <environment name="csolution" load="ostimer_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_edma_transfer" folder="boards/frdmmcxn947/driver_examples/pdm/pdm_edma_transfer/cm33_core0" doc="readme.md">
      <description>pdm_edma_transfer</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/pdm_edma_transfer.ewp"/>
        <environment name="csolution" load="pdm_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_interrupt" folder="boards/frdmmcxn947/driver_examples/pdm/pdm_interrupt/cm33_core0" doc="readme.md">
      <description>The pdm_interrupt example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pdm_interrupt.ewp"/>
        <environment name="csolution" load="pdm_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/frdmmcxn947/driver_examples/pint/pattern_match/cm33_core0" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/frdmmcxn947/driver_examples/pint/pin_interrupt/cm33_core0" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="plu_combination" folder="boards/frdmmcxn947/driver_examples/plu/combination/cm33_core0" doc="readme.md">
      <description>The PLU combination project is a simple demonstration program of the SDK PLU driver. In this example, a number of GPIO pins are connected to PLU inputs and the LED is used to monitor the PLU outputs to demonstrate...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/plu_combination.uvprojx"/>
        <environment name="iar" load="iar/plu_combination.ewp"/>
        <environment name="csolution" load="plu_combination.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager_test_bm" folder="boards/frdmmcxn947/demo_apps/power_manager_test/bm/cm33_core0" doc="readme.md">
      <description>The power manager test application demonstrates the basic usage of power manager framework without RTOS. The demo tests all features of power manager framework, including notification manager, wakeup source manager and so on.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager_test_bm.uvprojx"/>
        <environment name="csolution" load="power_manager_test_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_low_level" folder="boards/frdmmcxn947/demo_apps/power_mode_switch/low_level/cm33_core0" doc="readme.md">
      <description>The power_mode_switch_ll demo application demonstrates the usage of low level power-related drivers(SPC, CMC, VBAT, WUU, and so on) to enter/exit different power modes.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_low_level.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch_low_level.ewp"/>
        <environment name="csolution" load="power_mode_switch_low_level.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="puf_v3" folder="boards/frdmmcxn947/driver_examples/puf_v3/cm33_core0" doc="readme.md">
      <description>This driver example project demonstrates how to use driver for the PUFv3 controller which provides a secure key storage.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/puf_v3.uvprojx"/>
        <environment name="csolution" load="puf_v3.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm" folder="boards/frdmmcxn947/driver_examples/pwm/cm33_core0" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm.uvprojx"/>
        <environment name="iar" load="iar/pwm.ewp"/>
        <environment name="csolution" load="pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qdc_basic" folder="boards/frdmmcxn947/driver_examples/qdc/basic/cm33_core0" doc="readme.md">
      <description>The qdc_basic example shows how to quickly start using QDC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the basic application. When...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qdc_basic.uvprojx"/>
        <environment name="iar" load="iar/qdc_basic.ewp"/>
        <environment name="csolution" load="qdc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qdc_index_interrupt" folder="boards/frdmmcxn947/driver_examples/qdc/index_interrupt/cm33_core0" doc="readme.md">
      <description>The qdc_index_interrupt example shows how to use the interrupt of QDC module with QDC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qdc_index_interrupt.uvprojx"/>
        <environment name="iar" load="iar/qdc_index_interrupt.ewp"/>
        <environment name="csolution" load="qdc_index_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="boards/frdmmcxn947/driver_examples/sctimer/16bit_counter/cm33_core0" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" load="iar/sctimer_16bit_counter.ewp"/>
        <environment name="csolution" load="sctimer_16bit_counter.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="boards/frdmmcxn947/driver_examples/sctimer/multi_state_pwm/cm33_core0" doc="readme.md">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_multi_state_pwm.ewp"/>
        <environment name="csolution" load="sctimer_multi_state_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="boards/frdmmcxn947/driver_examples/sctimer/pwm_with_dutycyle_change/cm33_core0" doc="readme.md">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
        <environment name="csolution" load="sctimer_pwm_with_dutycyle_change.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="boards/frdmmcxn947/driver_examples/sctimer/simple_pwm/cm33_core0" doc="readme.md">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_simple_pwm.ewp"/>
        <environment name="csolution" load="sctimer_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_faults_ns" folder="boards/frdmmcxn947/trustzone_examples/secure_faults/cm33_core0/secure_faults_ns" doc="readme.md">
      <description>The Secure Faults demo application demonstrates handling of different secure faults. This application is based on application Hello World. In addition, user can invoke different secure faults by setting...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_faults_ns.uvprojx"/>
        <environment name="iar" load="iar/secure_faults_ns.ewp"/>
        <environment name="csolution" load="../secure_faults.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_faults_s" folder="boards/frdmmcxn947/trustzone_examples/secure_faults/cm33_core0/secure_faults_s" doc="readme.md">
      <description>The Secure Faults demo application demonstrates handling of different secure faults. This application is based on application Hello World. In addition, user can invoke different secure faults by setting...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_faults_s.uvprojx"/>
        <environment name="iar" load="iar/secure_faults_s.ewp"/>
        <environment name="csolution" load="../secure_faults.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema42_dual_core_core0" folder="boards/frdmmcxn947/driver_examples/sema42/dual_core/core0" doc="readme.md">
      <description>The sema42 example shows how to use SEMA42 driver to lock and unlock a sema gate:In this example:1. Firstly, Core 0 turn on LED and lock a sema gate then boot up Core 1 wake up.2. Core 1 must be wait until Core 0...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema42_dual_core_core0.uvprojx"/>
        <environment name="iar" load="iar/sema42_dual_core_core0.ewp"/>
        <environment name="csolution" load="../sema42_dual_core.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/frdmmcxn947/demo_apps/shell/cm33_core0" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sinc_lpspi" folder="boards/frdmmcxn947/driver_examples/sinc/lpspi/cm33_core0" doc="readme.md">
      <description>The sinc lpspi example shows how to use SINC driver to configure SINC module as single conversion mode to work with on-chip lpspi.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sinc_lpspi.uvprojx"/>
        <environment name="iar" load="iar/sinc_lpspi.ewp"/>
        <environment name="csolution" load="sinc_lpspi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="smartdma_camera_flexio_mculcd" folder="boards/frdmmcxn947/display_examples/smartdma_camera_flexio_mculcd/cm33_core0" doc="readme.md">
      <description>This example uses smartDMA to get image data from camera interface frame by frame captured by OV7670, and uses FlexIO LCD interface to display the captured image on st7796s low cost panel.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/smartdma_camera_flexio_mculcd.uvprojx"/>
        <environment name="csolution" load="smartdma_camera_flexio_mculcd.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="syspm" folder="boards/frdmmcxn947/driver_examples/syspm/cm33_core0" doc="readme.md">
      <description>The System Performance Monitor (SYSPM) is a memory mapped peripheral that enables the user to monitor system and/or CPU events. This demo project obtains the event count value through the system performance monitor, and prints three values.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/syspm.uvprojx"/>
        <environment name="iar" load="iar/syspm.ewp"/>
        <environment name="csolution" load="syspm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tdet" folder="boards/frdmmcxn947/driver_examples/tdet/cm33_core0" doc="readme.md">
      <description>The TDET Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Digital Tamper (TDET) module.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tdet.uvprojx"/>
        <environment name="iar" load="iar/tdet.ewp"/>
        <environment name="csolution" load="tdet.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v6_self_cap" folder="boards/frdmmcxn947/driver_examples/tsi_v6/self_cap/cm33_core0" doc="readme.md">
      <description>The tsi_v6_self_cap example shows how to use TSI_V6 driver in self-cap mode:In this example , we make use of the available electrodes on board to show driver usage.1. Firstly, we get the non-touch calibration results...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v6_self_cap.uvprojx"/>
        <environment name="iar" load="iar/tsi_v6_self_cap.ewp"/>
        <environment name="csolution" load="tsi_v6_self_cap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick" folder="boards/frdmmcxn947/driver_examples/utick/cm33_core0" doc="readme.md">
      <description>The utick project is a simple demonstration program of the SDK utick driver. It sets up the utick hardware block to trigger a periodic interrupt after every 1 second. When the utick interrupt is triggered a message is printed on the UART terminal.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick.uvprojx"/>
        <environment name="iar" load="iar/utick.ewp"/>
        <environment name="csolution" load="utick.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="vref_1_example" folder="boards/frdmmcxn947/driver_examples/vref_1/cm33_core0" doc="readme.md">
      <description>In this example, the adc16 module is initialized and used to measure the VREF output voltage. So, it cannot use internal VREF as the reference voltage. Instead, it can use VDD_ANA or VREFH, for detailed information,...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/vref_1_example.uvprojx"/>
        <environment name="iar" load="iar/vref_1_example.ewp"/>
        <environment name="csolution" load="vref_1_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/frdmmcxn947/driver_examples/wwdt/cm33_core0" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_interrupt_cm33_core1" folder="boards/frdmmcxn947/driver_examples/mailbox/interrupt/cm33_core1" doc="readme.md">
      <description>The primary core writes a value to the secondary core mailbox, it causes mailbox interrupt on the secondary core side. The secondary core reads the value from mailbox, it increments and writes it to mailbox register...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_interrupt_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/mailbox_interrupt_cm33_core1.ewp"/>
        <environment name="csolution" load="../mailbox_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_mutex_cm33_core1" folder="boards/frdmmcxn947/driver_examples/mailbox/mutex/cm33_core1" doc="readme.md">
      <description>The mailbox_mutex example shows how to use mailbox mutex.In this example:The core 0 sends address of shared variable to core 1 by mailbox.Both cores trying to get mutex in while loop, after that updates shared...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_mutex_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/mailbox_mutex_cm33_core1.ewp"/>
        <environment name="csolution" load="../mailbox_mutex.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema42_dual_core_core1" folder="boards/frdmmcxn947/driver_examples/sema42/dual_core/core1" doc="readme.md">
      <description>The sema42 example shows how to use SEMA42 driver to lock and unlock a sema gate:In this example:1. Firstly, Core 0 turn on LED and lock a sema gate then boot up Core 1 wake up.2. Core 1 must be wait until Core 0...See more details in readme document.</description>
      <board name="FRDM-MCXN947" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema42_dual_core_core1.uvprojx"/>
        <environment name="iar" load="iar/sema42_dual_core_core1.ewp"/>
        <environment name="csolution" load="../sema42_dual_core.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmmcxn947" Cversion="1.0.0" condition="BOARD_Project_Template.frdmmcxn947.condition_id">
      <description>Board_project_template frdmmcxn947</description>
      <files>
        <file category="header" attr="config" name="boards/frdmmcxn947/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxn947/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxn947/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxn947/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxn947/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxn947/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxn947/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxn947/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmmcxn947/project_template/"/>
      </files>
    </component>
  </components>
</package>
