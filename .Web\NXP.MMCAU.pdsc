<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MMCAU</name>
  <vendor>NXP</vendor>
  <description>Software Pack for mmcau</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="3.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="2.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="MMCAU Security" Cgroup="MMCAU library">mmCAU NXP</description>
  </taxonomy>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.mmcau.cm0p.condition_id">
      <require condition="allOf.core=cm0p, middleware.mmcau.common_files, driver.clock, driver.common.internal_condition"/>
    </condition>
    <condition id="allOf.core=cm0p, middleware.mmcau.common_files, driver.clock, driver.common.internal_condition">
      <require condition="core.cm0p.internal_condition"/>
      <require Cclass="MMCAU Security" Cgroup="MMCAU library" Csub="common_files"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="core.cm0p.internal_condition">
      <accept Dcore="Cortex-M0+"/>
    </condition>
    <condition id="allOf.toolchains=armgcc, iar.condition_id">
      <require condition="toolchains.armgcc, iar.internal_condition"/>
    </condition>
    <condition id="toolchains.armgcc, iar.internal_condition">
      <accept Tcompiler="GCC"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="allOf.toolchains=mdk.condition_id">
      <require condition="toolchains.mdk.internal_condition"/>
    </condition>
    <condition id="toolchains.mdk.internal_condition">
      <accept Tcompiler="ARMCC"/>
    </condition>
    <condition id="middleware.mmcau.cm4_cm7.condition_id">
      <require condition="allOf.core=cm4f, cm7f, middleware.mmcau.common_files, driver.clock, driver.common.internal_condition"/>
    </condition>
    <condition id="allOf.core=cm4f, cm7f, middleware.mmcau.common_files, driver.clock, driver.common.internal_condition">
      <require condition="core.cm4f, cm7f.internal_condition"/>
      <require Cclass="MMCAU Security" Cgroup="MMCAU library" Csub="common_files"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="core.cm4f, cm7f.internal_condition">
      <accept Dcore="Cortex-M4"/>
      <accept Dcore="Cortex-M7"/>
    </condition>
    <condition id="middleware.mmcau.common_files.condition_id">
      <require condition="allOf.driver.common.internal_condition"/>
    </condition>
    <condition id="allOf.driver.common.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="allOf.cores=cm0p.condition_id">
      <require condition="cores.cm0p.internal_condition"/>
    </condition>
    <condition id="cores.cm0p.internal_condition">
      <accept Dcore="Cortex-M0+"/>
    </condition>
    <condition id="allOf.cores=cm4f, cm7f.condition_id">
      <require condition="cores.cm4f, cm7f.internal_condition"/>
    </condition>
    <condition id="cores.cm4f, cm7f.internal_condition">
      <accept Dcore="Cortex-M4"/>
      <accept Dcore="Cortex-M7"/>
    </condition>
  </conditions>
  <components>
    <bundle Cbundle="mmCAU NXP" Cclass="MMCAU Security" Cversion="2.0.4">
      <description>mmCAU NXP</description>
      <doc>middleware/mmcau/mmCAU NXP_dummy.txt</doc>
      <component Cgroup="MMCAU library" Csub="cm0p" Cversion="2.0.4" condition="middleware.mmcau.cm0p.condition_id">
        <description>MMCAU library for ARM Cortex M0+ core</description>
        <files>
          <file condition="allOf.toolchains=armgcc, iar.condition_id" category="library" name="middleware/mmcau/asm-cm0p/lib_mmcau-cm0p.a" projectpath="mmcau/asm-cm0p"/>
          <file condition="allOf.toolchains=mdk.condition_id" category="library" name="middleware/mmcau/asm-cm0p/lib_mmcau-cm0p.lib" projectpath="mmcau/asm-cm0p"/>
          <file category="include" name="middleware/mmcau/"/>
        </files>
      </component>
      <component Cgroup="MMCAU library" Csub="cm4_cm7" Cversion="2.0.4" condition="middleware.mmcau.cm4_cm7.condition_id">
        <description>MMCAU library for ARM Cortex M4/M7 core</description>
        <files>
          <file condition="allOf.toolchains=armgcc, iar.condition_id" category="library" name="middleware/mmcau/asm-cm4-cm7/lib_mmcau.a" projectpath="mmcau/asm-cm4-cm7"/>
          <file condition="allOf.toolchains=mdk.condition_id" category="library" name="middleware/mmcau/asm-cm4-cm7/lib_mmcau.lib" projectpath="mmcau/asm-cm4-cm7"/>
          <file category="include" name="middleware/mmcau/"/>
        </files>
      </component>
      <component Cgroup="MMCAU library" Csub="common_files" Cversion="2.0.4" condition="middleware.mmcau.common_files.condition_id">
        <description>MMCAU common library source files</description>
        <files>
          <file category="header" name="middleware/mmcau/cau_api.h" projectpath="mmcau"/>
          <file category="header" name="middleware/mmcau/fsl_mmcau.h" projectpath="mmcau"/>
          <file category="sourceC" name="middleware/mmcau/fsl_mmcau.c" projectpath="mmcau"/>
          <file category="include" name="middleware/mmcau/"/>
        </files>
      </component>
      <component Cgroup="MMCAU library" Csub="mmcau_files" Cversion="2.0.4">
        <description>MMCAU security function library source files</description>
        <files>
          <file category="doc" name="middleware/mmcau/ChangeLogKSDK.txt" projectpath="mmcau"/>
          <file category="doc" name="middleware/mmcau/README.txt" projectpath="mmcau"/>
          <file condition="allOf.cores=cm0p.condition_id" category="other" name="middleware/mmcau/asm-cm0p/src/cau2_defines.hdr" projectpath="mmcau/asm-cm0p/src"/>
          <file condition="allOf.cores=cm0p.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm0p/src/mmcau_aes_functions.s" projectpath="mmcau/asm-cm0p/src"/>
          <file condition="allOf.cores=cm0p.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm0p/src/mmcau_des_functions.s" projectpath="mmcau/asm-cm0p/src"/>
          <file condition="allOf.cores=cm0p.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm0p/src/mmcau_md5_functions.s" projectpath="mmcau/asm-cm0p/src"/>
          <file condition="allOf.cores=cm0p.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm0p/src/mmcau_sha1_functions.s" projectpath="mmcau/asm-cm0p/src"/>
          <file condition="allOf.cores=cm0p.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm0p/src/mmcau_sha256_functions.s" projectpath="mmcau/asm-cm0p/src"/>
          <file condition="allOf.cores=cm4f, cm7f.condition_id" category="other" name="middleware/mmcau/asm-cm4-cm7/src/cau2_defines.hdr" projectpath="mmcau/asm-cm4-cm7/src"/>
          <file condition="allOf.cores=cm4f, cm7f.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm4-cm7/src/mmcau_aes_functions.s" projectpath="mmcau/asm-cm4-cm7/src"/>
          <file condition="allOf.cores=cm4f, cm7f.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm4-cm7/src/mmcau_des_functions.s" projectpath="mmcau/asm-cm4-cm7/src"/>
          <file condition="allOf.cores=cm4f, cm7f.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm4-cm7/src/mmcau_md5_functions.s" projectpath="mmcau/asm-cm4-cm7/src"/>
          <file condition="allOf.cores=cm4f, cm7f.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm4-cm7/src/mmcau_sha1_functions.s" projectpath="mmcau/asm-cm4-cm7/src"/>
          <file condition="allOf.cores=cm4f, cm7f.condition_id" category="sourceAsm" name="middleware/mmcau/asm-cm4-cm7/src/mmcau_sha256_functions.s" projectpath="mmcau/asm-cm4-cm7/src"/>
          <file category="doc" name="middleware/mmcau/mmCAU NXP_dummy.txt"/>
        </files>
      </component>
    </bundle>
  </components>
</package>
