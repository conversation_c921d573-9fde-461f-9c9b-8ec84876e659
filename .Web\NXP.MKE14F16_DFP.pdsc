<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MKE14F16_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MKE14F16</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Project Template">NXP MCUXpresso SDK RTE Device Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="CMSIS Driver">NXP MCUXpresso SDK Peripheral CMSIS Drivers</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="MKE14F16" Dvendor="NXP:11">
      <description>Kinetis KE1xF-168MHz, Performance with CAN 5V Microcontrollers based on ARM Cortex-M4</description>
      <device Dname="MKE14F512xxx16">
        <processor Dcore="Cortex-M4" Dfpu="SP_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="*********"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKE14F512xxx16_flash.icf"/>
        </environment>
        <memory name="FLEX_NVM" start="0x10000000" size="0x010000" access="rx" default="1"/>
        <memory name="FLEX_RAM" start="0x14000000" size="0x1000" access="rw" default="1"/>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x080000" access="rx" default="1" startup="1"/>
        <memory name="SRAM_LOWER" start="0x1fff8000" size="0x8000" access="rw" default="1"/>
        <memory name="SRAM_UPPER" start="0x20000000" size="0x8000" access="rw" default="1"/>
        <algorithm name="arm/MKE1x_D64_2KB_SEC.FLM" start="0x10000000" size="0x00010000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <algorithm name="arm/MKE1x_P512_4KB_SEC.FLM" start="0x00000000" size="0x00080000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <debug svd="MKE14F16.xml"/>
        <variant Dvariant="MKE14F512VLL16">
          <compile header="fsl_device_registers.h" define="CPU_MKE14F512VLL16"/>
        </variant>
        <variant Dvariant="MKE14F512VLH16">
          <compile header="fsl_device_registers.h" define="CPU_MKE14F512VLH16"/>
        </variant>
      </device>
      <device Dname="MKE14F256xxx16">
        <processor Dcore="Cortex-M4" Dfpu="SP_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="*********"/>
        <environment name="iar">
          <file category="linkerfile" name="iar/MKE14F256xxx16_flash.icf"/>
        </environment>
        <memory name="FLEX_NVM" start="0x10000000" size="0x010000" access="rx" default="1"/>
        <memory name="FLEX_RAM" start="0x14000000" size="0x1000" access="rw" default="1"/>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x040000" access="rx" default="1" startup="1"/>
        <memory name="SRAM_LOWER" start="0x1fffc000" size="0x4000" access="rw" default="1"/>
        <memory name="SRAM_UPPER" start="0x20000000" size="0x4000" access="rw" default="1"/>
        <algorithm name="arm/MKE1x_D64_2KB_SEC.FLM" start="0x10000000" size="0x00010000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <algorithm name="arm/MKE1x_P256_4KB_SEC.FLM" start="0x00000000" size="0x00040000" RAMstart="0x20000000" RAMsize="0x00001000" default="1"/>
        <debug svd="MKE14F16.xml"/>
        <variant Dvariant="MKE14F256VLL16">
          <compile header="fsl_device_registers.h" define="CPU_MKE14F256VLL16"/>
        </variant>
        <variant Dvariant="MKE14F256VLH16">
          <compile header="fsl_device_registers.h" define="CPU_MKE14F256VLH16"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.MKE14F16">
      <accept Dname="MKE14F256xxx16" Dvariant="MKE14F256VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F256VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F256xxx16" Dvariant="MKE14F256VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F256VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F512xxx16" Dvariant="MKE14F512VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F512VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F512xxx16" Dvariant="MKE14F512VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE14F512VLH16" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKE14F16_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_utility.debug_console">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKE14F16_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.edma">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.osa_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="osa"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.crc">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="crc"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.flash">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flash"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.ftm">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ftm"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.gpio_AND_driver.port">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.lpit">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpit"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.lptmr">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.lpuart">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.lists_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="component.serial_manager_swo_OR_component.serial_manager_uart">
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <accept Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_swo"/>
    </condition>
    <condition id="device.MKE14F16_AND__component.serial_manager_swo_OR_component.serial_manager_uart__AND_component.lists_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.serial_manager_swo_OR_component.serial_manager_uart"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.serial_manager_AND_core_type.cm4f_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Dcore="Cortex-M4"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.lpuart_adapter_AND_driver.lpuart">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="component.ftm_adapter_OR_component.lpit_adapter_OR_component.lptmr_adapter">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="ftm_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="lpit_adapter"/>
    </condition>
    <condition id="device.MKE14F16_AND__component.ftm_adapter_OR_component.lpit_adapter_OR_component.lptmr_adapter__AND_component.lists_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require condition="component.ftm_adapter_OR_component.lpit_adapter_OR_component.lptmr_adapter"/>
    </condition>
    <condition id="device.MKE14F16_AND_CMSIS_Include_core_cm4">
      <require condition="device.MKE14F16"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="armclang_OR_iar">
      <accept Tcompiler="ARMCC" Toptions="AC6"/>
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="device.MKE14F16_AND__armclang_OR_iar_">
      <require condition="device.MKE14F16"/>
      <require condition="armclang_OR_iar"/>
    </condition>
    <condition id="mdk">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="iar">
      <require Tcompiler="IAR"/>
    </condition>
    <condition id="device.MKE14F16_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.lpi2c_edma">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKE14F16_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.lpspi_edma">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpspi_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" Capiversion="2.2.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKE14F16_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart_edma">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_edma"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.MKE14F16_AND_device.MKE14F16_CMSIS_AND_driver.clock">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="MKE14F16_header"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.common_AND_driver.dmamux">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.flexio">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.edma_AND_driver.flexio_spi">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.edma_AND_driver.flexio_uart">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.edma_AND_driver.lpi2c">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.edma_AND_driver.lpspi">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpspi"/>
    </condition>
    <condition id="device.MKE14F16_AND_driver.edma_AND_driver.lpuart">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="edma"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
    </condition>
    <condition id="device.MKE14F16_AND_utility.debug_console">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.serial_manager_AND_driver.common">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.MKE14F16_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <require condition="device.MKE14F16"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="MKE14F16" Cversion="1.0.0" condition="device.MKE14F16_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKE14F16_startup_AND_driver.clock_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_utility.debug_console" isDefaultVariant="1">
      <description>Devices_project_template MKE14F16</description>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.edma">
      <description>Rte_device</description>
      <files>
        <file category="header" attr="config" name="template/RTE_Device.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="button" Cversion="1.0.0" condition="device.MKE14F16_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component button</description>
      <files>
        <file category="sourceC" name="components/button/button.c"/>
        <file category="header" name="components/button/button.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common_task" Cversion="1.0.0" condition="device.MKE14F16_AND_component.osa_AND_driver.common">
      <description>Component common_task</description>
      <files>
        <file category="sourceC" name="components/common_task/common_task.c"/>
        <file category="header" name="components/common_task/common_task.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.crc">
      <description>Component crc_adapter</description>
      <files>
        <file category="header" name="components/crc/crc.h"/>
        <file category="sourceC" name="components/crc/crc_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.flash">
      <description>Component flash_adapter</description>
      <files>
        <file category="header" name="components/internal_flash/flash.h"/>
        <file category="sourceC" name="components/internal_flash/flash_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ftm_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.ftm">
      <description>Component ftm_adapter</description>
      <files>
        <file category="sourceC" name="components/timer/ftm_adapter.c"/>
        <file category="header" name="components/timer/timer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.gpio_AND_driver.port">
      <description>Component gpio_adapter</description>
      <files>
        <file category="header" name="components/gpio/gpio.h"/>
        <file category="sourceC" name="components/gpio/gpio_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="led" Cversion="1.0.0" condition="device.MKE14F16_AND_component.gpio_adapter_AND_component.timer_manager_AND_driver.common">
      <description>Component led</description>
      <files>
        <file category="sourceC" name="components/led/led.c"/>
        <file category="header" name="components/led/led.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common">
      <description>Component lists</description>
      <files>
        <file category="sourceC" name="components/lists/generic_list.c"/>
        <file category="header" name="components/lists/generic_list.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpit_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.lpit">
      <description>Component lpit_adapter</description>
      <files>
        <file category="sourceC" name="components/timer/lpit_adapter.c"/>
        <file category="header" name="components/timer/timer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.lptmr">
      <description>Component lptmr_adapter</description>
      <files>
        <file category="sourceC" name="components/timer/lptmr_adapter.c"/>
        <file category="header" name="components/timer/timer.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.lpuart">
      <description>Component lpuart_adapter</description>
      <files>
        <file category="sourceC" name="components/uart/lpuart_adapter.c"/>
        <file category="header" name="components/uart/uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mem_manager" Cversion="1.0.0" condition="device.MKE14F16_AND_component.lists_AND_driver.common">
      <description>Component mem_manager</description>
      <files>
        <file category="sourceC" name="components/mem_manager/mem_manager.c"/>
        <file category="header" name="components/mem_manager/mem_manager.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa" Cversion="1.0.0" condition="device.MKE14F16_AND_component.lists_AND_driver.common">
      <description>Component osa</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="osa_bm" Cversion="1.0.0" condition="device.MKE14F16_AND_component.lists_AND_driver.common">
      <description>Component osa_bm</description>
      <files>
        <file category="header" name="components/osa/fsl_os_abstraction.h"/>
        <file category="sourceC" name="components/osa/fsl_os_abstraction_bm.c"/>
        <file category="header" name="components/osa/fsl_os_abstraction_bm.h"/>
        <file category="header" name="components/osa/fsl_os_abstraction_config.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="panic" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_utility.debug_console">
      <description>Component panic</description>
      <files>
        <file category="sourceC" name="components/panic/panic.c"/>
        <file category="header" name="components/panic/panic.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pwm_ftm_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common_AND_driver.ftm">
      <description>Component pwm_ftm_adapter</description>
      <files>
        <file category="header" name="components/pwm/pwm.h"/>
        <file category="sourceC" name="components/pwm/pwm_ftm_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager" Cversion="1.0.0" condition="device.MKE14F16_AND__component.serial_manager_swo_OR_component.serial_manager_uart__AND_component.lists_AND_driver.common">
      <description>Component serial_manager</description>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_manager.c"/>
        <file category="header" name="components/serial_manager/serial_manager.h"/>
        <file category="header" name="components/serial_manager/serial_port_internal.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_swo" Cversion="1.0.0" condition="device.MKE14F16_AND_component.serial_manager_AND_core_type.cm4f_AND_driver.common">
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#define SERIAL_PORT_TYPE_SWO 1
</RTE_Components_h>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_port_swo.c"/>
        <file category="header" name="components/serial_manager/serial_port_swo.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart" Cversion="1.0.0" condition="device.MKE14F16_AND_component.lpuart_adapter_AND_driver.lpuart">
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#define SERIAL_PORT_TYPE_UART 1
</RTE_Components_h>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_port_uart.c"/>
        <file category="header" name="components/serial_manager/serial_port_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="software_crc_adapter" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common">
      <description>Component software_crc_adapter</description>
      <files>
        <file category="header" name="components/crc/crc.h"/>
        <file category="sourceC" name="components/crc/software_crc_adapter.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="timer_manager" Cversion="1.0.0" condition="device.MKE14F16_AND__component.ftm_adapter_OR_component.lpit_adapter_OR_component.lptmr_adapter__AND_component.lists_AND_driver.common">
      <description>Component timer_manager</description>
      <files>
        <file category="sourceC" name="components/timer_manager/timer_manager.c"/>
        <file category="header" name="components/timer_manager/timer_manager.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="MKE14F16_header" Cversion="1.0.0" condition="device.MKE14F16_AND_CMSIS_Include_core_cm4">
      <description>Device MKE14F16_cmsis</description>
      <files>
        <file category="header" name="MKE14F16.h"/>
        <file category="header" name="MKE14F16_features.h"/>
        <file category="header" name="fsl_device_registers.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Csub="MKE14F16_startup" Cversion="1.1.0" condition="device.MKE14F16_AND__armclang_OR_iar_">
      <description>Device MKE14F16_startup</description>
      <files>
        <file condition="mdk" category="sourceAsm" name="arm/startup_MKE14F16.S"/>
        <file condition="iar" category="sourceAsm" name="iar/startup_MKE14F16.s"/>
        <file category="sourceC" name="system_MKE14F16.c"/>
        <file category="header" name="system_MKE14F16.h"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKE14F256xxx16_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKE14F256xxx16_ram.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKE14F512xxx16_flash.scf" version="1.1.0"/>
        <file condition="mdk" category="linkerScript" attr="config" name="arm/MKE14F512xxx16_ram.scf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKE14F256xxx16_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKE14F256xxx16_ram.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKE14F512xxx16_flash.icf" version="1.1.0"/>
        <file condition="iar" category="linkerScript" attr="config" name="iar/MKE14F512xxx16_ram.icf" version="1.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="acmp" Cversion="2.0.6" condition="device.MKE14F16_AND_driver.common">
      <description>ACMP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_acmp.c"/>
        <file category="header" name="drivers/fsl_acmp.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="adc" Cversion="2.0.4" condition="device.MKE14F16_AND_driver.common">
      <description>ADC12 Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_adc12.c"/>
        <file category="header" name="drivers/fsl_adc12.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="cache" Cversion="2.0.4" condition="device.MKE14F16_AND_driver.common">
      <description>CACHE LMEM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_cache.c"/>
        <file category="header" name="drivers/fsl_cache.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.3.1" condition="device.MKE14F16_AND_driver.common">
      <description>Clock Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_clock.c"/>
        <file category="header" name="drivers/fsl_clock.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="lpi2c_cmsis" Cversion="2.0.0" condition="device.MKE14F16_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.lpi2c_edma">
      <description>LPI2C CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_lpi2c_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_lpi2c_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="lpspi_cmsis" Cversion="2.0.0" condition="device.MKE14F16_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.lpspi_edma">
      <description>LPSPI CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_lpspi_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_lpspi_cmsis.h"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="lpuart_cmsis_edma" Cversion="2.0.1" condition="device.MKE14F16_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.lpuart_edma">
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_lpuart_cmsis.c"/>
        <file category="header" name="cmsis_drivers/fsl_lpuart_cmsis.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.2.4" condition="device.MKE14F16_AND_device.MKE14F16_CMSIS_AND_driver.clock">
      <description>COMMON Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_common.c"/>
        <file category="header" name="drivers/fsl_common.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="crc" Cversion="2.0.2" condition="device.MKE14F16_AND_driver.common">
      <description>CRC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_crc.c"/>
        <file category="header" name="drivers/fsl_crc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dac" Cversion="2.0.1" condition="device.MKE14F16_AND_driver.common">
      <description>DAC32 Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dac32.c"/>
        <file category="header" name="drivers/fsl_dac32.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dmamux" Cversion="2.0.4" condition="device.MKE14F16_AND_driver.common">
      <description>DMAMUX Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dmamux.c"/>
        <file category="header" name="drivers/fsl_dmamux.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="edma" Cversion="2.3.2" condition="device.MKE14F16_AND_driver.common_AND_driver.dmamux">
      <description>EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_edma.c"/>
        <file category="header" name="drivers/fsl_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ewm" Cversion="2.0.3" condition="device.MKE14F16_AND_driver.common">
      <description>EWM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ewm.c"/>
        <file category="header" name="drivers/fsl_ewm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flash" Cversion="3.0.0" condition="device.MKE14F16_AND_driver.common">
      <description>Flash Driver</description>
      <files>
        <file category="header" name="drivers/fsl_flash.h"/>
        <file category="header" name="drivers/fsl_ftfx_adapter.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_cache.c"/>
        <file category="header" name="drivers/fsl_ftfx_cache.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_controller.c"/>
        <file category="header" name="drivers/fsl_ftfx_controller.h"/>
        <file category="header" name="drivers/fsl_ftfx_features.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flash.c"/>
        <file category="header" name="drivers/fsl_ftfx_flash.h"/>
        <file category="sourceC" name="drivers/fsl_ftfx_flexnvm.c"/>
        <file category="header" name="drivers/fsl_ftfx_flexnvm.h"/>
        <file category="header" name="drivers/fsl_ftfx_utilities.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio" Cversion="2.0.3" condition="device.MKE14F16_AND_driver.common">
      <description>FLEXIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio.c"/>
        <file category="header" name="drivers/fsl_flexio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_i2c_master" Cversion="2.4.0" condition="device.MKE14F16_AND_driver.flexio">
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_i2c_master.c"/>
        <file category="header" name="drivers/fsl_flexio_i2c_master.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi" Cversion="2.2.0" condition="device.MKE14F16_AND_driver.flexio">
      <description>FLEXIO SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_spi.c"/>
        <file category="header" name="drivers/fsl_flexio_spi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_spi_edma" Cversion="2.2.0" condition="device.MKE14F16_AND_driver.edma_AND_driver.flexio_spi">
      <description>FLEXIO SPI EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_spi_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_spi_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart" Cversion="2.2.0" condition="device.MKE14F16_AND_driver.flexio">
      <description>FLEXIO UART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart.c"/>
        <file category="header" name="drivers/fsl_flexio_uart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flexio_uart_edma" Cversion="2.2.0" condition="device.MKE14F16_AND_driver.edma_AND_driver.flexio_uart">
      <description>FLEXIO UART EDMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flexio_uart_edma.c"/>
        <file category="header" name="drivers/fsl_flexio_uart_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ftm" Cversion="2.2.3" condition="device.MKE14F16_AND_driver.common">
      <description>FTM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ftm.c"/>
        <file category="header" name="drivers/fsl_ftm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.5.1" condition="device.MKE14F16_AND_driver.common">
      <description>GPIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_gpio.c"/>
        <file category="header" name="drivers/fsl_gpio.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lmem" Cversion="2.1.2" condition="device.MKE14F16_AND_driver.common">
      <description>LMEM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lmem_cache.c"/>
        <file category="header" name="drivers/fsl_lmem_cache.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.1.11" condition="device.MKE14F16_AND_driver.common">
      <description>LPI2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpi2c.c"/>
        <file category="header" name="drivers/fsl_lpi2c.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_edma" Cversion="2.1.11" condition="device.MKE14F16_AND_driver.edma_AND_driver.lpi2c">
      <description>LPI2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpi2c_edma.c"/>
        <file category="header" name="drivers/fsl_lpi2c_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpit" Cversion="2.0.1" condition="device.MKE14F16_AND_driver.common">
      <description>LPIT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpit.c"/>
        <file category="header" name="drivers/fsl_lpit.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpspi" Cversion="2.0.5" condition="device.MKE14F16_AND_driver.common">
      <description>LPSPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpspi.c"/>
        <file category="header" name="drivers/fsl_lpspi.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpspi_edma" Cversion="2.0.5" condition="device.MKE14F16_AND_driver.edma_AND_driver.lpspi">
      <description>LPSPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpspi_edma.c"/>
        <file category="header" name="drivers/fsl_lpspi_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lptmr" Cversion="2.1.1" condition="device.MKE14F16_AND_driver.common">
      <description>LPTMR Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lptmr.c"/>
        <file category="header" name="drivers/fsl_lptmr.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart" Cversion="2.3.0" condition="device.MKE14F16_AND_driver.common">
      <description>LPUART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpuart.c"/>
        <file category="header" name="drivers/fsl_lpuart.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_edma" Cversion="2.3.0" condition="device.MKE14F16_AND_driver.edma_AND_driver.lpuart">
      <description>LPUART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_lpuart_edma.c"/>
        <file category="header" name="drivers/fsl_lpuart_edma.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pdb" Cversion="2.0.4" condition="device.MKE14F16_AND_driver.common">
      <description>PDB Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pdb.c"/>
        <file category="header" name="drivers/fsl_pdb.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pmc" Cversion="2.0.3" condition="device.MKE14F16_AND_driver.common">
      <description>PMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pmc.c"/>
        <file category="header" name="drivers/fsl_pmc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="port" Cversion="2.1.0" condition="device.MKE14F16_AND_driver.common">
      <description>PORT Driver</description>
      <files>
        <file category="header" name="drivers/fsl_port.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pwt" Cversion="2.0.1" condition="device.MKE14F16_AND_driver.common">
      <description>PWT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pwt.c"/>
        <file category="header" name="drivers/fsl_pwt.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rcm" Cversion="2.0.4" condition="device.MKE14F16_AND_driver.common">
      <description>RCM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rcm.c"/>
        <file category="header" name="drivers/fsl_rcm.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rtc" Cversion="2.2.1" condition="device.MKE14F16_AND_driver.common">
      <description>RTC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rtc.c"/>
        <file category="header" name="drivers/fsl_rtc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sim" Cversion="2.1.1" condition="device.MKE14F16_AND_driver.common">
      <description>SIM Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sim.c"/>
        <file category="header" name="drivers/fsl_sim.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="smc" Cversion="2.0.7" condition="device.MKE14F16_AND_driver.common">
      <description>SMC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_smc.c"/>
        <file category="header" name="drivers/fsl_smc.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sysmpu" Cversion="2.2.3" condition="device.MKE14F16_AND_driver.common">
      <description>SYSMPU Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sysmpu.c"/>
        <file category="header" name="drivers/fsl_sysmpu.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="trgmux" Cversion="2.0.1" condition="device.MKE14F16_AND_driver.common">
      <description>TRGMUX Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_trgmux.c"/>
        <file category="header" name="drivers/fsl_trgmux.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wdog" Cversion="2.0.3" condition="device.MKE14F16_AND_driver.common">
      <description>WDOG32 Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_wdog32.c"/>
        <file category="header" name="drivers/fsl_wdog32.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert" Cversion="1.0.0" condition="device.MKE14F16_AND_utility.debug_console">
      <description>Utility assert</description>
      <files>
        <file category="sourceC" name="utilities/fsl_assert.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console" Cversion="1.0.0" condition="device.MKE14F16_AND_component.serial_manager_AND_driver.common">
      <description>Utility debug_console</description>
      <files>
        <file category="sourceC" name="utilities/debug_console/fsl_debug_console.c"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console.h"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console_conf.h"/>
        <file category="sourceC" name="utilities/str/fsl_str.c"/>
        <file category="header" name="utilities/str/fsl_str.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="notifier" Cversion="1.0.0" condition="device.MKE14F16_AND_driver.common">
      <description>Utility notifier</description>
      <files>
        <file category="sourceC" name="utilities/fsl_notifier.c"/>
        <file category="header" name="utilities/fsl_notifier.h"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="shell" Cversion="1.0.0" condition="device.MKE14F16_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <description>Utility shell</description>
      <RTE_Components_h>
#define DEBUG_CONSOLE_RX_ENABLE 0
</RTE_Components_h>
      <files>
        <file category="sourceC" name="utilities/fsl_shell.c"/>
        <file category="header" name="utilities/fsl_shell.h"/>
      </files>
    </component>
  </components>
</package>
