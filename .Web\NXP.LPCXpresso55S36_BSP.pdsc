<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso55S36_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXpresso55S36</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-31">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="13.0.0" date="2022-04-01">NXP CMSIS Packs based on MCUXpresso SDK 2.10.2</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC55S36_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso55S36">
      <description>LPCXpresso Development Board for LPC55S3x/3x family of MCUs</description>
      <image small="boards/lpcxpresso55s36/lpcxpresso55s36.png"/>
      <mountedDevice Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC5534JHI48" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC5536JHI48" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC5534.internal_condition">
      <accept Dname="LPC5534JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5534JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC5536.internal_condition">
      <accept Dname="LPC5536JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5536JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S36.internal_condition">
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.LPC55S36.internal_condition">
      <accept Dname="LPC55S36JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S36JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.lpcxpresso55s36.condition_id">
      <require condition="allOf.board=lpcxpresso55s36, component.usart_adapter, device_id=LPC55S36, device.LPC55S36_startup, driver.clock, driver.flexcomm_i2c, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, driver.power, driver.reset, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpcxpresso55s36, component.usart_adapter, device_id=LPC55S36, device.LPC55S36_startup, driver.clock, driver.flexcomm_i2c, driver.flexcomm_usart, driver.lpc_gpio, driver.lpc_iocon, driver.power, driver.reset, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.lpcxpresso55s36.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require condition="device_id.LPC55S36.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.lpcxpresso55s36.internal_condition">
      <accept condition="device.LPC5534.internal_condition"/>
      <accept condition="device.LPC5536.internal_condition"/>
      <accept condition="device.LPC55S36.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="aoi_input_mux" folder="boards/lpcxpresso55s36/driver_examples/aoi/input_mux" doc="readme.md">
      <description>The AOI Example shows the simplest way to use AOI driver</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/aoi_input_mux.uvprojx"/>
        <environment name="iar" load="iar/aoi_input_mux.ewp"/>
        <environment name="csolution" load="aoi_input_mux.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cdog" folder="boards/lpcxpresso55s36/driver_examples/cdog" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog.uvprojx"/>
        <environment name="iar" load="iar/cdog.ewp"/>
        <environment name="csolution" load="cdog.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="boards/lpcxpresso55s36/driver_examples/cmp/interrupt" doc="readme.md">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
        <environment name="csolution" load="cmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="boards/lpcxpresso55s36/driver_examples/cmp/polling" doc="readme.md">
      <description>The CMP polling Example shows the simplest way to use CMP driver</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
        <environment name="csolution" load="cmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="boards/lpcxpresso55s36/cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="boards/lpcxpresso55s36/cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/lpcxpresso55s36/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/lpcxpresso55s36/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="boards/lpcxpresso55s36/cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="boards/lpcxpresso55s36/cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_master" folder="boards/lpcxpresso55s36/cmsis_driver_examples/spi/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_slave" folder="boards/lpcxpresso55s36/cmsis_driver_examples/spi/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_dma_transfer" folder="boards/lpcxpresso55s36/cmsis_driver_examples/usart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_interrupt_transfer" folder="boards/lpcxpresso55s36/cmsis_driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/lpcxpresso55s36/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/lpcxpresso55s36/driver_examples/ctimer/simple_match" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example.ewp"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/lpcxpresso55s36/driver_examples/ctimer/simple_match_interrupt" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/lpcxpresso55s36/driver_examples/ctimer/simple_pwm" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/lpcxpresso55s36/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_1_basic" folder="boards/lpcxpresso55s36/driver_examples/dac/dac_basic" doc="readme.md">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's buffer feature is not enabled, the first item of the buffer is used as the DAC output data register.The converter...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_1_basic.uvprojx"/>
        <environment name="iar" load="iar/dac_1_basic.ewp"/>
        <environment name="csolution" load="dac_1_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_1_buffer_interrupt" folder="boards/lpcxpresso55s36/driver_examples/dac/dac_buffer_interrupt" doc="readme.md">
      <description>The dac_buffer_interrupt example shows how to use DAC FIFO interrupt.When the DAC FIFO empty interrupt is enabled firstly, the application would enter the DAC ISR immediately, since the FIFO is actually empty. Then...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_1_buffer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dac_1_buffer_interrupt.ewp"/>
        <environment name="csolution" load="dac_1_buffer_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="boards/lpcxpresso55s36/driver_examples/dma/channel_chain" doc="readme.md">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="iar" load="iar/dma_channel_chain.ewp"/>
        <environment name="csolution" load="dma_channel_chain.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="boards/lpcxpresso55s36/driver_examples/dma/interleave_transfer" doc="readme.md">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_interleave_transfer.ewp"/>
        <environment name="csolution" load="dma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="boards/lpcxpresso55s36/driver_examples/dma/linked_transfer" doc="readme.md">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_linked_transfer.ewp"/>
        <environment name="csolution" load="dma_linked_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/lpcxpresso55s36/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="boards/lpcxpresso55s36/driver_examples/dma/wrap_transfer" doc="readme.md">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes a wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_wrap_transfer.ewp"/>
        <environment name="csolution" load="dma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_dma" folder="boards/lpcxpresso55s36/driver_examples/dmic/dmic_dma" doc="readme.md">
      <description>This example shows how to use DMA to transfer data from DMIC to memory.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_dma.uvprojx"/>
        <environment name="iar" load="iar/dmic_dma.ewp"/>
        <environment name="csolution" load="dmic_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_hwvad" folder="boards/lpcxpresso55s36/driver_examples/dmic/dmic_hwvad" doc="readme.md">
      <description>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~This demo explains how built in HWVAD( HW voice activity detector) in LPC5411x can be used towake up the device from sleep mode</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_hwvad.uvprojx"/>
        <environment name="iar" load="iar/dmic_hwvad.ewp"/>
        <environment name="csolution" load="dmic_hwvad.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_i2s_dma" folder="boards/lpcxpresso55s36/driver_examples/dmic/dmic_i2s_dma" doc="readme.md">
      <description>Demonstrates the DMIC working with I2S. Audio is converted to samples in the DMIC module.Then, the data is placed memory buffer. Last, it is send data to the I2S buffer and send to the CODEC, then the audio data will...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_i2s_dma.uvprojx"/>
        <environment name="csolution" load="dmic_i2s_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_hash" folder="boards/lpcxpresso55s36/els_pkc_examples/els_hash" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of hash algorithms and a crypto library lightweight testing.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_hash.uvprojx"/>
        <environment name="csolution" load="els_hash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_pkc_asymmetric" folder="boards/lpcxpresso55s36/els_pkc_examples/els_pkc_asymmetric" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of asymmetric algorithms and a crypto library lightweight testing.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_pkc_asymmetric.uvprojx"/>
        <environment name="csolution" load="els_pkc_asymmetric.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_pkc_common" folder="boards/lpcxpresso55s36/els_pkc_examples/els_pkc_common" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of common features including PRNG and a crypto library lightweight testing.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_pkc_common.uvprojx"/>
        <environment name="csolution" load="els_pkc_common.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="els_symmetric" folder="boards/lpcxpresso55s36/els_pkc_examples/els_symmetric" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of symmetric algorithms and a crypto library lightweight testing.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_symmetric.uvprojx"/>
        <environment name="csolution" load="els_symmetric.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_basic" folder="boards/lpcxpresso55s36/driver_examples/enc/basic" doc="readme.md">
      <description>The ENC Example shows the simplest way to use ENC driver</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_basic.uvprojx"/>
        <environment name="iar" load="iar/enc_basic.ewp"/>
        <environment name="csolution" load="enc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_index_interrupt" folder="boards/lpcxpresso55s36/driver_examples/enc/index_interrupt" doc="readme.md">
      <description>The ENC Example shows the simplest way to use ENC driver</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_index_interrupt.uvprojx"/>
        <environment name="iar" load="iar/enc_index_interrupt.ewp"/>
        <environment name="csolution" load="enc_index_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_dma_transfer" folder="boards/lpcxpresso55s36/driver_examples/flexspi/octal/dma_transfer" doc="readme.md">
      <description>The flexspi_octal_dma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command willbe...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_dma_transfer.ewp"/>
        <environment name="csolution" load="flexspi_octal_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_polling_transfer" folder="boards/lpcxpresso55s36/driver_examples/flexspi/octal/polling_transfer" doc="readme.md">
      <description>The flexspi_octal_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_polling_transfer.ewp"/>
        <environment name="csolution" load="flexspi_octal_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_power_down" folder="boards/lpcxpresso55s36/driver_examples/flexspi/octal/power_down" doc="readme.md">
      <description>The flexspi_octal_power_down example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command willbe...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_power_down.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_power_down.ewp"/>
        <environment name="csolution" load="flexspi_octal_power_down.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="freqme_interrupt" folder="boards/lpcxpresso55s36/driver_examples/freqme" doc="readme.md">
      <description>The freqme_interrupt is a demonstration program of the SDK LPC_FREQME driver's features. The example demostrate the usage of frequency measurement operate mode and pulse width measurement operate mode.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freqme_interrupt.uvprojx"/>
        <environment name="iar" load="iar/freqme_interrupt.ewp"/>
        <environment name="csolution" load="freqme_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gint" folder="boards/lpcxpresso55s36/driver_examples/gint" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Group GPIO input interrupt peripheral.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gint.uvprojx"/>
        <environment name="iar" load="iar/gint.ewp"/>
        <environment name="csolution" load="gint.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/lpcxpresso55s36/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/lpcxpresso55s36/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_ns" folder="boards/lpcxpresso55s36/trustzone_examples/hello_world/hello_world_ns" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. This demo application also utilizes TrustZone, so it demonstrates basic techniques for TrustZone...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_ns.uvprojx"/>
        <environment name="iar" load="iar/hello_world_ns.ewp"/>
        <environment name="csolution" load="../hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_qspi_xip" folder="boards/lpcxpresso55s36/demo_apps/hello_world_qspi_xip" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_qspi_xip.uvprojx"/>
        <environment name="iar" load="iar/hello_world_qspi_xip.ewp"/>
        <environment name="csolution" load="hello_world_qspi_xip.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_s" folder="boards/lpcxpresso55s36/trustzone_examples/hello_world/hello_world_s" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. This demo application also utilizes TrustZone, so it demonstrates basic techniques for TrustZone...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_s.uvprojx"/>
        <environment name="iar" load="iar/hello_world_s.ewp"/>
        <environment name="csolution" load="../hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_swo" folder="boards/lpcxpresso55s36/demo_apps/hello_world_swo" doc="readme.md">
      <description>The Hello World SWO demo prints the "SWO: Hello World" string to the SWO viewer. The purpose of this demo is to show how to use the swo, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_swo.uvprojx"/>
        <environment name="csolution" load="hello_world_swo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hscmp_interrupt" folder="boards/lpcxpresso55s36/driver_examples/hscmp/interrupt" doc="readme.md">
      <description>The HSCMP interrupt Example shows how to use interrupt with HSCMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the HSCMP's positive channel...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hscmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/hscmp_interrupt.ewp"/>
        <environment name="csolution" load="hscmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hscmp_polling" folder="boards/lpcxpresso55s36/driver_examples/hscmp/polling" doc="readme.md">
      <description>The HSCMP polling Example shows the simplest way to use HSCMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hscmp_polling.uvprojx"/>
        <environment name="iar" load="iar/hscmp_polling.ewp"/>
        <environment name="csolution" load="hscmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_master" folder="boards/lpcxpresso55s36/driver_examples/i2c/polling_b2b/master" doc="readme.md">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_slave" folder="boards/lpcxpresso55s36/driver_examples/i2c/polling_b2b/slave" doc="readme.md">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_transfer" folder="boards/lpcxpresso55s36/driver_examples/i2s/dma_transfer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_transfer.uvprojx"/>
        <environment name="csolution" load="i2s_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_transfer" folder="boards/lpcxpresso55s36/driver_examples/i2s/interrupt_transfer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="i2s_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_master" folder="boards/lpcxpresso55s36/component_examples/i3c_bus/master" doc="readme.md">
      <description>The i3c_bus_master example shows how to use i3c_bus component to create I3C bus and i3c master on bus.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_master.ewp"/>
        <environment name="csolution" load="i3c_bus_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_slave" folder="boards/lpcxpresso55s36/component_examples/i3c_bus/slave" doc="readme.md">
      <description>The i3c_bus_slave example shows how to use i3c_bus component to work as i3c bus slave.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_slave.ewp"/>
        <environment name="csolution" load="i3c_bus_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_dma_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/i3c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i3c_dma_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using dma method. In this example, one i3c instance as master and another i3c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_dma_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/i3c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_dma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a dma master. In this example, one i3c instance as slave and another i3c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/i3c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/i3c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_icm42688p" folder="boards/lpcxpresso55s36/driver_examples/i3c/master_read_sensor_icm42688p" doc="readme.md">
      <description>The i3c_master_read_sensor_icm42688p example shows how to use i3c driver as master to communicate with sensor ICM-42688P.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_icm42688p.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_icm42688p.ewp"/>
        <environment name="csolution" load="i3c_master_read_sensor_icm42688p.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/i3c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/i3c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iped" folder="boards/lpcxpresso55s36/driver_examples/iped" doc="readme.md">
      <description>The IPED Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Inline Prince Encryption Decryption.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iped.uvprojx"/>
        <environment name="iar" load="iar/iped.ewp"/>
        <environment name="csolution" load="iped.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="irtc" folder="boards/lpcxpresso55s36/driver_examples/irtc" doc="readme.md">
      <description>The IRTC project is a simple demonstration program of the SDK IRTC driver.This example is a low power module that provides time keeping and calendaring functions and additionally providesprotection against tampering,...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irtc.uvprojx"/>
        <environment name="iar" load="iar/irtc.ewp"/>
        <environment name="csolution" load="irtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="itrc" folder="boards/lpcxpresso55s36/driver_examples/itrc" doc="readme.md">
      <description>The ITRC Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Intrusion and Tamper Response Controller.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/itrc.uvprojx"/>
        <environment name="iar" load="iar/itrc.ewp"/>
        <environment name="csolution" load="itrc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/lpcxpresso55s36/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_dma" folder="boards/lpcxpresso55s36/driver_examples/lpadc/dma" doc="readme.md">
      <description>The lpdc_dma example shows how to use ADC to trigger a DMA transfer. In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC's sample input. When running...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_dma.uvprojx"/>
        <environment name="iar" load="iar/lpadc_dma.ewp"/>
        <environment name="csolution" load="lpadc_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt" folder="boards/lpcxpresso55s36/driver_examples/lpadc/interrupt" doc="readme.md">
      <description>The lpdc_single_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt.ewp"/>
        <environment name="csolution" load="lpadc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling" folder="boards/lpcxpresso55s36/driver_examples/lpadc/polling" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling.ewp"/>
        <environment name="csolution" load="lpadc_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_temperature_measurement" folder="boards/lpcxpresso55s36/driver_examples/lpadc/temperature_measurement" doc="readme.md">
      <description>The lpadc_temperature_measurement example shows how to measure the temperature within the internal sensor. In this example, the ADC input channel is mapped to an internal temperature sensor. When running the project,...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_temperature_measurement.uvprojx"/>
        <environment name="iar" load="iar/lpadc_temperature_measurement.ewp"/>
        <environment name="csolution" load="lpadc_temperature_measurement.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_opamp" folder="boards/lpcxpresso55s36/driver_examples/opamp" doc="readme.md">
      <description>This project shows how to use the OPAMP driver. In this example, the positive reference voltage is set as DAC output, the OPAMP output is 1X of DAC output. When DAC output changes, the OPAMP changes accordingly.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_opamp.uvprojx"/>
        <environment name="iar" load="iar/lpc_opamp.ewp"/>
        <environment name="csolution" load="lpc_opamp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcan_interrupt_transfer" folder="boards/lpcxpresso55s36/driver_examples/mcan/interrupt_transfer" doc="readme.md">
      <description>The mcan_interrupt example shows how to use MCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when users...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcan_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/mcan_interrupt_transfer.ewp"/>
        <environment name="csolution" load="mcan_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcan_loopback" folder="boards/lpcxpresso55s36/driver_examples/mcan/loopback" doc="readme.md">
      <description>The mcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrate this example, only one board is needed. The example will config Tx Buffer to sendand Rx Fifo to receive....See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcan_loopback.uvprojx"/>
        <environment name="iar" load="iar/mcan_loopback.ewp"/>
        <environment name="csolution" load="mcan_loopback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/lpcxpresso55s36/driver_examples/mrt" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/lpcxpresso55s36/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="iar" load="iar/new_project.ewp"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flashiap" folder="boards/lpcxpresso55s36/driver_examples/flashiap" doc="readme.md">
      <description>The FLASIAP project is a simple demonstration program of the SDK FLASIAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flashiap.uvprojx"/>
        <environment name="iar" load="iar/flashiap.ewp"/>
        <environment name="csolution" load="flashiap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor" folder="boards/lpcxpresso55s36/driver_examples/flexspi_nor" doc="readme.md">
      <description>The FLASIAP NOR project is a simple demonstration program of the SDK FLASIAP NOR driver. It erases and programs a portion of external Nor flash connected with FLEXSPI. Some simple flash command willbe executed,such...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor.ewp"/>
        <environment name="csolution" load="flexspi_nor.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mem_interface" folder="boards/lpcxpresso55s36/driver_examples/mem_interface" doc="readme.md">
      <description>The mem_interface project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of internal and external flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mem_interface.uvprojx"/>
        <environment name="iar" load="iar/mem_interface.ewp"/>
        <environment name="csolution" load="mem_interface.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="runbootloader" folder="boards/lpcxpresso55s36/driver_examples/runbootloader" doc="readme.md">
      <description>The runbootloader project is a simple demonstration which run into the bootloader with the user parameter. The demo forces the device run into the ISP mode with the specific peripheral.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/runbootloader.uvprojx"/>
        <environment name="iar" load="iar/runbootloader.ewp"/>
        <environment name="csolution" load="runbootloader.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="opamp_vref_adc" folder="boards/lpcxpresso55s36/demo_apps/opamp_vref_adc" doc="readme.md">
      <description>This project shows how to use ADC sampling opamp out vaule.In the example opamp,the positive reference voltage is set as VREF output.The OPAMP output is 1X of VREF out.When VREF out change, the OPAMP changes accordingly.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/opamp_vref_adc.uvprojx"/>
        <environment name="iar" load="iar/opamp_vref_adc.ewp"/>
        <environment name="csolution" load="opamp_vref_adc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example" folder="boards/lpcxpresso55s36/driver_examples/ostimer" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example.uvprojx"/>
        <environment name="iar" load="iar/ostimer_example.ewp"/>
        <environment name="csolution" load="ostimer_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/lpcxpresso55s36/driver_examples/pint/pattern_match" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/lpcxpresso55s36/driver_examples/pint/pin_interrupt" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_lpc_1" folder="boards/lpcxpresso55s36/demo_apps/power_mode_switch_lpc" doc="readme.md">
      <description>The power_mode_switch application shows the usage of normal power mode control APIs for entering the three kinds oflow power mode: Sleep mode, Deep Sleep mode, deep power down mode. When the application runs to each...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_lpc_1.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch_lpc_1.ewp"/>
        <environment name="csolution" load="power_mode_switch_lpc_1.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="prince_rom" folder="boards/lpcxpresso55s36/driver_examples/prince_rom" doc="readme.md">
      <description>This driver example demonstrates how to setup PRINCE driver for the on-the-fly encryption/decryption of data stored in the internal flash memory. It shows how to enable encryption/decryption for specified flash...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/prince_rom.uvprojx"/>
        <environment name="csolution" load="prince_rom.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="puf_v3" folder="boards/lpcxpresso55s36/driver_examples/puf_v3" doc="readme.md">
      <description>This driver example project demonstrates how to use driver for the PUFv3 controller which provides a secure key storage.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/puf_v3.uvprojx"/>
        <environment name="csolution" load="puf_v3.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm" folder="boards/lpcxpresso55s36/driver_examples/pwm" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm.uvprojx"/>
        <environment name="iar" load="iar/pwm.ewp"/>
        <environment name="csolution" load="pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="boards/lpcxpresso55s36/driver_examples/sctimer/16bit_counter" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" load="iar/sctimer_16bit_counter.ewp"/>
        <environment name="csolution" load="sctimer_16bit_counter.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="boards/lpcxpresso55s36/driver_examples/sctimer/multi_state_pwm" doc="readme.md">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_multi_state_pwm.ewp"/>
        <environment name="csolution" load="sctimer_multi_state_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="boards/lpcxpresso55s36/driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.md">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
        <environment name="csolution" load="sctimer_pwm_with_dutycyle_change.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="boards/lpcxpresso55s36/driver_examples/sctimer/simple_pwm" doc="readme.md">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_simple_pwm.ewp"/>
        <environment name="csolution" load="sctimer_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_faults_ns" folder="boards/lpcxpresso55s36/trustzone_examples/secure_faults/secure_faults_ns" doc="readme.md">
      <description>The Secure Faults demo application demonstrates handling of different secure faults. This application is based on application Hello World. In addition, user can invoke different secure faults by setting...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_faults_ns.uvprojx"/>
        <environment name="iar" load="iar/secure_faults_ns.ewp"/>
        <environment name="csolution" load="../secure_faults.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_faults_s" folder="boards/lpcxpresso55s36/trustzone_examples/secure_faults/secure_faults_s" doc="readme.md">
      <description>The Secure Faults demo application demonstrates handling of different secure faults. This application is based on application Hello World. In addition, user can invoke different secure faults by setting...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_faults_s.uvprojx"/>
        <environment name="iar" load="iar/secure_faults_s.ewp"/>
        <environment name="csolution" load="../secure_faults.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_gpio_ns" folder="boards/lpcxpresso55s36/trustzone_examples/secure_gpio/secure_gpio_ns" doc="readme.md">
      <description>The Secure GPIO demo application demonstrates using of secure GPIO peripheral and GPIO mask feature in AHB secure controller. This is non-secure part of the application.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_gpio_ns.uvprojx"/>
        <environment name="iar" load="iar/secure_gpio_ns.ewp"/>
        <environment name="csolution" load="../secure_gpio.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_gpio_s" folder="boards/lpcxpresso55s36/trustzone_examples/secure_gpio/secure_gpio_s" doc="readme.md">
      <description>The Secure GPIO demo application demonstrates using of secure GPIO peripheral and GPIO mask feature in AHB secure controller. This is secure part of the application.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_gpio_s.uvprojx"/>
        <environment name="iar" load="iar/secure_gpio_s.ewp"/>
        <environment name="csolution" load="../secure_gpio.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/lpcxpresso55s36/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_HS_LSPI_dma_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/spi/HS_LSPI_dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_HS_LSPI_dma_b2b_transfer_master example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_HS_LSPI_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_HS_LSPI_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_HS_LSPI_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_HS_LSPI_dma_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/spi/HS_LSPI_dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_HS_LSPI_dma_b2b_transfer_slave example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_HS_LSPI_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_HS_LSPI_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_HS_LSPI_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_b2b_transfer_master example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_b2b_transfer_slave example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt" folder="boards/lpcxpresso55s36/driver_examples/spi/interrupt" doc="readme.md">
      <description>The spi_interrupt example shows how to use spi functional API to do interrupt transfer:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt.ewp"/>
        <environment name="csolution" load="spi_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/lpcxpresso55s36/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/lpcxpresso55s36/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/lpcxpresso55s36/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/lpcxpresso55s36/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_double_buffer_transfer" folder="boards/lpcxpresso55s36/driver_examples/usart/dma_double_buffer_transfer" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USARTThe example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_double_buffer_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_dma_double_buffer_transfer.ewp"/>
        <environment name="csolution" load="usart_dma_double_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_transfer" folder="boards/lpcxpresso55s36/driver_examples/usart/dma_transfer" doc="readme.md">
      <description>The usart_dma example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board willsend back all characters that PC send to the board.Note: The example echo...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_dma_transfer.ewp"/>
        <environment name="csolution" load="usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt" folder="boards/lpcxpresso55s36/driver_examples/usart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/usart_interrupt.ewp"/>
        <environment name="csolution" load="usart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_rb_transfer" folder="boards/lpcxpresso55s36/driver_examples/usart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="usart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_transfer" folder="boards/lpcxpresso55s36/driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/usart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling" folder="boards/lpcxpresso55s36/driver_examples/usart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling.uvprojx"/>
        <environment name="iar" load="iar/usart_polling.ewp"/>
        <environment name="csolution" load="usart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick" folder="boards/lpcxpresso55s36/driver_examples/utick" doc="readme.md">
      <description>The utick project is a simple demonstration program of the SDK utick driver. It sets up the utick hardware block to trigger a periodic interrupt after every 1 second. When the utick interrupt is triggered a message is printed on the UART terminal.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick.uvprojx"/>
        <environment name="iar" load="iar/utick.ewp"/>
        <environment name="csolution" load="utick.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_wakeup" folder="boards/lpcxpresso55s36/demo_apps/utick_wakeup" doc="readme.md">
      <description>The purpose of this demo is to show wakeup from deep sleep mode using MicroTick timer.The demo sets the MicroTick Timer as a wake up source and puts the device in deep-sleep mode. The MicroTick timer wakes up the device. </description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_wakeup.uvprojx"/>
        <environment name="iar" load="iar/utick_wakeup.ewp"/>
        <environment name="csolution" load="utick_wakeup.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="vref_1_example" folder="boards/lpcxpresso55s36/driver_examples/vref_1" doc="readme.md">
      <description>In this example, the adc16 module is initialized and used to measure the VREF output voltage. So, it cannot use internal VREF as the reference voltage. Instead, it can use VDD_ANA or VREFH, for detailed information,...See more details in readme document.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/vref_1_example.uvprojx"/>
        <environment name="iar" load="iar/vref_1_example.ewp"/>
        <environment name="csolution" load="vref_1_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/lpcxpresso55s36/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso55S36" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso55s36" Cversion="1.0.0" condition="BOARD_Project_Template.lpcxpresso55s36.condition_id">
      <description>Board_project_template lpcxpresso55s36</description>
      <files>
        <file category="header" attr="config" name="boards/lpcxpresso55s36/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s36/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s36/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s36/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s36/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s36/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso55s36/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso55s36/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/lpcxpresso55s36/project_template/"/>
      </files>
    </component>
  </components>
</package>
