<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: I/O Retarget example project</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('md__home_runner_work_CMSIS_Compiler_CMSIS_Compiler_example_README.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">I/O Retarget example project </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>This project prints "Hello World" and a counter value via the UART output. It is configured for Arm Virtual Hardware, but other target hardware that provides a CMSIS Driver:USART can be easily added.</p>
<h1><a class="anchor" id="autotoc_md1"></a>
Prerequisites</h1>
<h2><a class="anchor" id="autotoc_md2"></a>
Tools</h2>
<ul>
<li><a href="https://github.com/Open-CMSIS-Pack/devtools/releases">CMSIS-Toolbox 2.1.0</a> or higher</li>
<li><a href="https://developer.arm.com/downloads/view/ACOMPE">Arm Compiler for Embedded</a>, or</li>
<li><a href="https://developer.arm.com/downloads/-/arm-gnu-toolchain-downloads">GCC Compiler for Arm bare-metal</a> or</li>
<li><a href="https://github.com/ARM-software/LLVM-embedded-toolchain-for-Arm">Clang Compiler</a> or</li>
<li><a href="https://www.iar.com/products/architectures/arm/iar-build-tools-for-arm/">IAR Build Tools for Arm</a></li>
<li><a href="https://developer.arm.com/Tools%20and%20Software/Arm%20Virtual%20Hardware">Arm Virtual Hardware</a> for local execution</li>
</ul>
<h2><a class="anchor" id="autotoc_md3"></a>
Packs</h2>
<ul>
<li>Required packs are listed in the file <code>retarget.csolution.yml</code></li>
</ul>
<h1><a class="anchor" id="autotoc_md4"></a>
Project Structure</h1>
<p>The project is generated using the <a href="https://github.com/Open-CMSIS-Pack/cmsis-toolbox/blob/main/docs/build-overview.md">CMSIS-Toolbox</a> and is defined in <a href="https://github.com/Open-CMSIS-Pack/cmsis-toolbox/blob/main/docs/YML-Input-Format.md"><code>csolution</code></a> format:</p>
<ul>
<li><code>retarget.csolution.yml</code> lists the required packs and defines the hardware target and build-types (along with the compiler).</li>
<li><code>retarget.cproject.yml</code> defines the source files and the software components.</li>
</ul>
<h1><a class="anchor" id="autotoc_md5"></a>
Build Project</h1>
<p>Use the <code>cbuild</code> command to build the Debug configuration for VHT_CM3 target defined in <code>retarget.csolution.yml</code> solution file.</p>
<div class="fragment"><div class="line">cbuild retarget.csolution.yml -p -r --update-rte -c retarget.Debug+VHT_CM3</div>
</div><!-- fragment --><blockquote class="doxtable">
<p>&zwj;<b>Note</b></p><ul>
<li>During the build process required packs may be downloaded (<code>-p</code> flag). </li>
</ul>
</blockquote>
<p>By default the project is compiled using GCC as set in <code>cdefault.yml</code> file. One can build the project by specifying the toolchain - AC6, GCC, CLANG and IAR are supported.</p>
<h2><a class="anchor" id="autotoc_md6"></a>
To build with Arm Compiler 6:</h2>
<div class="fragment"><div class="line">cbuild retarget.csolution.yml -p -r --update-rte --toolchain AC6 -c retarget.Debug+VHT_CM3</div>
</div><!-- fragment --><h1><a class="anchor" id="autotoc_md7"></a>
Execute Project</h1>
<p>The project is configured for execution on Arm Virtual Hardware which removes the requirement for a physical hardware board.</p>
<p>To execute application image on Arm Virtual Hardware use below command: </p><div class="fragment"><div class="line">FVP_MPS2_Cortex-M3 -f fvp-config.txt out/retarget/VHT_CM3/Debug/retarget.elf</div>
</div><!-- fragment --> <blockquote class="doxtable">
<p>&zwj;<b>Note</b></p><ul>
<li>For Arm Compiler 6, the application image file has extension <code>.axf</code> </li>
</ul>
</blockquote>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
