<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EZR32WG_DFP</name>
  <description>Silicon Labs EZR32WG EZR Wonder Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
    <release version="3.1.0" date="2020-12-11">
      New MCU's families efr32pg22, efr32fg23, efr32zg23 and efm32pg23.
    </release>
    <release version="3.0.0" date="2020-09-14">
      Addition of EFR32ZG13L, EFR32ZG13S and removal of EFR32BG14P device families.
    </release>
    <release version="2.7.3" date="2020-03-23">
      The PRORTC timer peripheral has been added to EFR32xG13 and EFR32xG14 device families.
    </release>
    <release version="2.7.0" date="2019-11-12">
      Changed pack names and version number sequence.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EZR32WG</keyword>
    <keyword>EZR32</keyword>
    <keyword>EZR Wonder Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EZR32WG Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="FPU" Dmpu="MPU" Dtz="NO_TZ" Ddsp="NO_DSP" Dendian="Little-endian" Dclock="48000000"/>
      <book name="Documents/cortex_m4_dgug.pdf"      title="Cortex-M4 Generic User Guide"/>
      <book name="Documents/EZR32WG-RM.pdf"  title="EZR32WG Reference Manual"/>
      <description>
- Silicon Labs first 32-bit Wireless MCU&#xD;&#xA;- Based on ARM Cortex M4 CPU cores&#xD;&#xA;- 256KB of Flash and 32KB RAM&#xD;&#xA;- Best-in-class RF performance with EZradio and EZRadioPRO transceivers&#xD;&#xA;- Ultra-low power wireless MCU&#xD;&#xA;- Low transmit and receive currents&#xD;&#xA;- Ultra-low power standby and sleep modes&#xD;&#xA;- Fast wake-up time&#xD;&#xA;- Low Energy sensor interface (LESENSE)&#xD;&#xA;- Rich set of peripherals including 12-bit ADC and DAC, multiple communication interfaces (USB, UART, LEUART, SPI, I2C), GPIO and timers&#xD;&#xA;- AES Accelerator with 128/256-bit keys&#xD;&#xA;&#xD;&#xA;The EZR32WG Wireless MCUs are the latest in Silicon Labs family of wireless MCUs delivering a high performance, low energy wireless solution integrated into a small form factor package. By combining a high performance sub-GHz RF transceiver with an energy efficient 32-bit MCU, the EZR32WG family provides designers the ultimate in flexibility with a family of pin-compatible devices that scale with 64/128/256 kB of flash and support Silicon Labs EZRadio or EZRadioPRO transceivers. The ultra-low power operating modes and fast wake-up times of the Silicon Labs energy friendly 32-bit MCUs, combined with the low transmit and receive power consumption of the sub-GHz radio, result in a solution optimized for battery powered applications.
      </description>

      <subFamily DsubFamily="EZR32WG230">
        <book         name="Documents/ezr32wg-errata.pdf"         title="EZR32WG230 Errata"/>
        <!-- *************************  Device 'EZR32WG230F128R55'  ***************************** -->
        <device Dname="EZR32WG230F128R55">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R55"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F128R60'  ***************************** -->
        <device Dname="EZR32WG230F128R60">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R60"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F128R61'  ***************************** -->
        <device Dname="EZR32WG230F128R61">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R61"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F128R63'  ***************************** -->
        <device Dname="EZR32WG230F128R63">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R63"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F128R67'  ***************************** -->
        <device Dname="EZR32WG230F128R67">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R67"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F128R68'  ***************************** -->
        <device Dname="EZR32WG230F128R68">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R68"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F128R69'  ***************************** -->
        <device Dname="EZR32WG230F128R69">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F128R69"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F128R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R55'  ***************************** -->
        <device Dname="EZR32WG230F256R55">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R55"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R60'  ***************************** -->
        <device Dname="EZR32WG230F256R60">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R60"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R61'  ***************************** -->
        <device Dname="EZR32WG230F256R61">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R61"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R63'  ***************************** -->
        <device Dname="EZR32WG230F256R63">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R63"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R67'  ***************************** -->
        <device Dname="EZR32WG230F256R67">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R67"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R68'  ***************************** -->
        <device Dname="EZR32WG230F256R68">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R68"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F256R69'  ***************************** -->
        <device Dname="EZR32WG230F256R69">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F256R69"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F256R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R55'  ***************************** -->
        <device Dname="EZR32WG230F64R55">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R55"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R60'  ***************************** -->
        <device Dname="EZR32WG230F64R60">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R60"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R61'  ***************************** -->
        <device Dname="EZR32WG230F64R61">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R61"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R63'  ***************************** -->
        <device Dname="EZR32WG230F64R63">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R63"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R67'  ***************************** -->
        <device Dname="EZR32WG230F64R67">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R67"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R68'  ***************************** -->
        <device Dname="EZR32WG230F64R68">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R68"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG230F64R69'  ***************************** -->
        <device Dname="EZR32WG230F64R69">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG230F64R69"/>
          <debug      svd="SVD/EZR32WG/EZR32WG230F64R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EZR32WG330">
        <book         name="Documents/ezr32wg-errata.pdf"         title="EZR32WG330 Errata"/>
        <!-- *************************  Device 'EZR32WG330F128R55'  ***************************** -->
        <device Dname="EZR32WG330F128R55">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R55"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F128R60'  ***************************** -->
        <device Dname="EZR32WG330F128R60">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R60"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F128R61'  ***************************** -->
        <device Dname="EZR32WG330F128R61">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R61"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F128R63'  ***************************** -->
        <device Dname="EZR32WG330F128R63">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R63"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F128R67'  ***************************** -->
        <device Dname="EZR32WG330F128R67">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R67"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F128R68'  ***************************** -->
        <device Dname="EZR32WG330F128R68">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R68"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F128R69'  ***************************** -->
        <device Dname="EZR32WG330F128R69">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F128R69"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F128R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00020000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R55'  ***************************** -->
        <device Dname="EZR32WG330F256R55">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R55"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R60'  ***************************** -->
        <device Dname="EZR32WG330F256R60">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R60"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R61'  ***************************** -->
        <device Dname="EZR32WG330F256R61">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R61"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R63'  ***************************** -->
        <device Dname="EZR32WG330F256R63">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R63"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R67'  ***************************** -->
        <device Dname="EZR32WG330F256R67">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R67"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R68'  ***************************** -->
        <device Dname="EZR32WG330F256R68">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R68"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F256R69'  ***************************** -->
        <device Dname="EZR32WG330F256R69">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F256R69"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F256R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00040000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00040000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R55'  ***************************** -->
        <device Dname="EZR32WG330F64R55">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R55"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R60'  ***************************** -->
        <device Dname="EZR32WG330F64R60">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R60"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R61'  ***************************** -->
        <device Dname="EZR32WG330F64R61">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R61"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R63'  ***************************** -->
        <device Dname="EZR32WG330F64R63">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R63"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R67'  ***************************** -->
        <device Dname="EZR32WG330F64R67">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R67"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R68'  ***************************** -->
        <device Dname="EZR32WG330F64R68">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R68"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32WG330F64R69'  ***************************** -->
        <device Dname="EZR32WG330F64R69">
          <compile header="Device/SiliconLabs/EZR32WG/Include/em_device.h"  define="EZR32WG330F64R69"/>
          <debug      svd="SVD/EZR32WG/EZR32WG330F64R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32WG.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EZR32WG">
      <description>Silicon Labs EZR32WG device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EZR32WG*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="4.4.0" condition="EZR32WG">
      <description>System Startup for Silicon Labs EZR32WG device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EZR32WG/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EZR32WG/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EZR32WG/Source/GCC/startup_ezr32wg.S" condition="Compiler GCC"   attr="config" version="4.4.0"/>
        <file category="source"  name="Device/SiliconLabs/EZR32WG/Source/IAR/startup_ezr32wg.s" condition="Compiler IAR"   attr="config" version="4.4.0"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EZR32WG/Source/GCC/ezr32wg.ld" condition="Compiler GCC" attr="config" version="4.4.0"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EZR32WG/Source/system_ezr32wg.c" attr="config" version="4.4.0"/>
      </files>
    </component>
  </components>
</package>
