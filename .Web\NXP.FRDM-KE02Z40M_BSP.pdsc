<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-KE02Z40M_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-KE02Z40M</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKE02Z4_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-KE02Z40M">
      <description>Freedom Development Board for Kinetis KE02 (40 MHz) MCUs</description>
      <image small="boards/frdmke02z40m/frdmke02z40m.png"/>
      <book category="overview" name="https://www.nxp.com/pip/FRDM-KE02Z40M" title="Freedom Development Board for Kinetis KE02 (40 MHz) MCUs" public="true"/>
      <mountedDevice Dname="MKE02Z64VQH4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z16VFM4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z16VLC4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z16VLD4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z32VFM4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z32VLC4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z32VLD4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z32VLH4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z32VQH4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z64VFM4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z64VLC4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z64VLD4" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE02Z64VLH4" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKE02Z4.internal_condition">
      <accept Dname="MKE02Z16VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z16VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z16VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VQH4" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MKE02Z64xxx4.internal_condition">
      <accept Dname="MKE02Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VQH4" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmke02z40m.condition_id">
      <require condition="allOf.board=frdmke02z40m, component.uart_adapter, device_id=MKE02Z64xxx4, device.MKE02Z4_startup, driver.common, driver.gpio_1, driver.port_ke02, driver.uart, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmke02z40m, component.uart_adapter, device_id=MKE02Z64xxx4, device.MKE02Z4_startup, driver.common, driver.gpio_1, driver.port_ke02, driver.uart, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.frdmke02z40m.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require condition="device_id.MKE02Z64xxx4.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmke02z40m.internal_condition">
      <accept condition="device.MKE02Z4.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_1_interrupt" folder="boards/frdmke02z40m/driver_examples/acmp/interrupt" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_1_interrupt.uvprojx"/>
        <environment name="iar" load="iar/acmp_1_interrupt.ewp"/>
        <environment name="csolution" load="acmp_1_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_1_polling" folder="boards/frdmke02z40m/driver_examples/acmp/polling" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_1_polling.uvprojx"/>
        <environment name="iar" load="iar/acmp_1_polling.ewp"/>
        <environment name="csolution" load="acmp_1_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_5v12b_ll18_015_interrupt" folder="boards/frdmke02z40m/driver_examples/adc/interrupt" doc="readme.md">
      <description>The adc_interrupt example shows how to use interrupt with adc driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the adc'ssample input. When running the...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_5v12b_ll18_015_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc_5v12b_ll18_015_interrupt.ewp"/>
        <environment name="csolution" load="adc_5v12b_ll18_015_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_5v12b_ll18_015_polling" folder="boards/frdmke02z40m/driver_examples/adc/polling" doc="readme.md">
      <description>The adc_polling example shows the simplest way to use adc driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the adc'ssample input. When running the...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_5v12b_ll18_015_polling.uvprojx"/>
        <environment name="iar" load="iar/adc_5v12b_ll18_015_polling.ewp"/>
        <environment name="csolution" load="adc_5v12b_ll18_015_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/frdmke02z40m/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/frdmke02z40m/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_read_accel_value_transfer" folder="boards/frdmke02z40m/cmsis_driver_examples/i2c/read_accel_value_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_read_accel_value_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_master" folder="boards/frdmke02z40m/cmsis_driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use spi CMSIS driver in interrupt way:In this example , we need two boards, one board used as spi master and another board used as spi slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_slave" folder="boards/frdmke02z40m/cmsis_driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="boards/frdmke02z40m/cmsis_driver_examples/uart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eeprom_ftmr" folder="boards/frdmke02z40m/driver_examples/flash/eeprom" doc="readme.md">
      <description>The eeprom example shows how to use flash driver to operate program eeprom:</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eeprom_ftmr.uvprojx"/>
        <environment name="iar" load="iar/eeprom_ftmr.ewp"/>
        <environment name="csolution" load="eeprom_ftmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_combine_pwm" folder="boards/frdmke02z40m/driver_examples/ftm/combine_pwm" doc="readme.md">
      <description>The FTM project is a demonstration program of generating a combined PWM signal by the SDK FTM driver. It sets up the FTMhardware block to output PWM signals on two TPM channels. The example also shows the...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_combine_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_combine_pwm.ewp"/>
        <environment name="csolution" load="ftm_combine_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_dual_edge_capture" folder="boards/frdmke02z40m/driver_examples/ftm/dual_edge_capture" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a FTM channel-pair for dual-edge capture. Once the...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_dual_edge_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_dual_edge_capture.ewp"/>
        <environment name="csolution" load="ftm_dual_edge_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_input_capture" folder="boards/frdmke02z40m/driver_examples/ftm/input_capture" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's input capture feature.The example sets up a FTM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_input_capture.ewp"/>
        <environment name="csolution" load="ftm_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_output_compare" folder="boards/frdmke02z40m/driver_examples/ftm/output_compare" doc="readme.md">
      <description>The FTM project is a demonstration program of the SDK FTM driver's output compare feature.It sets up one FTM channel to toggle the output when a match occurs with the channel value. The usershould probe the FTM...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/ftm_output_compare.ewp"/>
        <environment name="csolution" load="ftm_output_compare.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pwm_twochannel" folder="boards/frdmke02z40m/driver_examples/ftm/pwm_twochannel" doc="readme.md">
      <description>The FTM pwm two channel Example project is a demonstration program that uses the KSDK software to generate a square pulse PWM on 2 channel to control the LED brightness.- FTM generates a PWM with the increasing and...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/ftm_pwm_twochannel.ewp"/>
        <environment name="csolution" load="ftm_pwm_twochannel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_simple_pwm" folder="boards/frdmke02z40m/driver_examples/ftm/simple_pwm" doc="readme.md">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver. It sets up the FTMhardware block to output a center-aligned PWM signal. The PWM dutycycle is periodically updated.On boards that have an LED...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_simple_pwm.ewp"/>
        <environment name="csolution" load="ftm_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="boards/frdmke02z40m/driver_examples/ftm/timer" doc="readme.md">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" load="iar/ftm_timer.ewp"/>
        <environment name="csolution" load="ftm_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_1_led_output" folder="boards/frdmke02z40m/driver_examples/gpio_1/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_1_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_1_led_output.ewp"/>
        <environment name="csolution" load="gpio_1_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmke02z40m/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/frdmke02z40m/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/frdmke02z40m/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="boards/frdmke02z40m/driver_examples/i2c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="boards/frdmke02z40m/driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_read_accel_value_transfer" folder="boards/frdmke02z40m/driver_examples/i2c/read_accel_value_transfer" doc="readme.md">
      <description>The i2c_read_accel_value example shows how to use I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/i2c_read_accel_value_transfer.ewp"/>
        <environment name="csolution" load="i2c_read_accel_value_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="irq" folder="boards/frdmke02z40m/driver_examples/irq" doc="readme.md">
      <description>The IRQ Example project is a demonstration program that uses the MCUXpresso SDK software to useIRQ pin interrupt.The example uses the IRQ pin to generate a falling edge interrupt to show the example.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irq.uvprojx"/>
        <environment name="iar" load="iar/irq.ewp"/>
        <environment name="csolution" load="irq.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="kbi" folder="boards/frdmke02z40m/driver_examples/kbi" doc="readme.md">
      <description>The KBI Example project is a demonstration program that uses the KSDK software to usekeyboard interrupt.The example uses one KBI pin to generate a raising edge interrupt to show the example.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kbi.uvprojx"/>
        <environment name="iar" load="iar/kbi.ewp"/>
        <environment name="csolution" load="kbi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky_1" folder="boards/frdmke02z40m/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky_1.uvprojx"/>
        <environment name="csolution" load="led_blinky_1.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash_ftmr" folder="boards/frdmke02z40m/driver_examples/flash/pflash" doc="readme.md">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash_ftmr.uvprojx"/>
        <environment name="iar" load="iar/pflash_ftmr.ewp"/>
        <environment name="csolution" load="pflash_ftmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="boards/frdmke02z40m/driver_examples/pit" doc="readme.md">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" load="iar/pit.ewp"/>
        <environment name="csolution" load="pit.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_1" folder="boards/frdmke02z40m/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_1.uvprojx"/>
        <environment name="iar" load="iar/rtc_1.ewp"/>
        <environment name="csolution" load="rtc_1.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/frdmke02z40m/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/frdmke02z40m/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/frdmke02z40m/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/frdmke02z40m/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_input_capture" folder="boards/frdmke02z40m/driver_examples/tpm/input_capture" doc="readme.md">
      <description>The TPM project is a demonstration program of the SDK TPM driver's input capture feature.The example sets up a TPM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/tpm_input_capture.ewp"/>
        <environment name="csolution" load="tpm_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_output_compare" folder="boards/frdmke02z40m/driver_examples/tpm/output_compare" doc="readme.md">
      <description>The TPM project is a demonstration program of the SDK TPM driver's output compare feature.It sets up one TPM channel to toggle the output when a match occurs with the channel value. The usershould probe the TPM...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/tpm_output_compare.ewp"/>
        <environment name="csolution" load="tpm_output_compare.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_pwm_twochannel" folder="boards/frdmke02z40m/driver_examples/tpm/pwm_twochannel" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The PWM dutycycle on both channelsis manually updated. On boards...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/tpm_pwm_twochannel.ewp"/>
        <environment name="csolution" load="tpm_pwm_twochannel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_simple_pwm" folder="boards/frdmke02z40m/driver_examples/tpm/simple_pwm" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output a center-aligned PWM signal. The PWM dutycycle is manually updated.On boards that have an LED...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/tpm_simple_pwm.ewp"/>
        <environment name="csolution" load="tpm_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer" folder="boards/frdmke02z40m/driver_examples/tpm/timer" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer.uvprojx"/>
        <environment name="iar" load="iar/tpm_timer.ewp"/>
        <environment name="csolution" load="tpm_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="boards/frdmke02z40m/driver_examples/uart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt.ewp"/>
        <environment name="csolution" load="uart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="boards/frdmke02z40m/driver_examples/uart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="uart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="boards/frdmke02z40m/driver_examples/uart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="boards/frdmke02z40m/driver_examples/uart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" load="iar/uart_polling.ewp"/>
        <environment name="csolution" load="uart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog8" folder="boards/frdmke02z40m/driver_examples/wdog8" doc="readme.md">
      <description>The WDOG8 Example project is to demonstrate usage of the wdog8 driver.In this example, fast testing is first implemented to test the wdog8.After this, refreshing the watchdog in None-window mode and window mode is...See more details in readme document.</description>
      <board name="FRDM-KE02Z40M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog8.uvprojx"/>
        <environment name="iar" load="iar/wdog8.ewp"/>
        <environment name="csolution" load="wdog8.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmke02z40m" Cversion="1.0.0" condition="BOARD_Project_Template.frdmke02z40m.condition_id">
      <description>Board_project_template frdmke02z40m</description>
      <files>
        <file category="header" attr="config" name="boards/frdmke02z40m/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke02z40m/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmke02z40m/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke02z40m/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmke02z40m/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke02z40m/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmke02z40m/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmke02z40m/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmke02z40m/project_template/"/>
      </files>
    </component>
  </components>
</package>
