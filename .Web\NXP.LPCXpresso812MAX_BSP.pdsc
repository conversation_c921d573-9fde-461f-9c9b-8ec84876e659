<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso812MAX_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXPRESSO812MAX</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-06-21">NXP CMSIS packs based on MCUXpresso SDK 2.4.2. Fixed missing flash algorithms for LPC8xx CMSIS packs.</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC812_DFP" vendor="NXP" version="13.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso812MAX">
      <description>LPCXpresso Development Board for the LPC81x family of MCUs</description>
      <mountedDevice Dname="LPC812" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC812">
      <accept Dname="LPC812" Dvariant="LPC812M101JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC812" Dvariant="LPC812M101JDH16" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JDH16" Dvendor="NXP:11"/>
      <accept Dname="LPC812" Dvariant="LPC812M101JD20" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JD20" Dvendor="NXP:11"/>
      <accept Dname="LPC812" Dvariant="LPC812M101JTB16" Dvendor="NXP:11"/>
      <accept Dname="LPC812M101JTB16" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC812_AND_component.miniusart_adapter_AND_device.LPC812_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_lite_AND_driver.lpc_miniusart_AND_driver.power_no_lib_AND_driver.swm_AND_utility.debug_console_lite">
      <require condition="device.LPC812"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="Startup" Csub="LPC812_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="swm"/>
    </condition>
  </conditions>
  <examples>
    <example name="acomp_basic" folder="driver_examples/acomp/acomp_basic" doc="readme.txt">
      <description>The ACOMP Basic Example shows the simplest way to use ACOMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the ACOMP's negative channel input. On the postive side, the internal voltage ladder is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user's voltage crosses the internal ladder's output. The endless loop in main() functionwould detect the logic value of comparator's output, and change the LED. The LED would be turned on when the compareoutput is logic one, or turned off when zero.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_basic.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acomp_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="acomp_interrupt" folder="driver_examples/acomp/acomp_interrupt" doc="readme.txt">
      <description>The ACOMP Interrupt Example shows how to use interrupt with ACOMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the ACOMP's negative channel input. On the postive side, the internal voltage ladder is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user-defined channel's voltage crosses the internal ladder's output. The change ofcomparator's output would generate the falling and rising edge events with their interrupts enabled. When any ACOMP interrupt happens, the ACOMP's ISR would turn on/off the LED light.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acomp_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_basic" folder="driver_examples/iap/iap_basic" doc="readme.txt">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It reads part id, boot code version, unique id and reinvoke ISP. A message a printed on the UART terminal as various bascial iap operations are performed.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_basic.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/iap_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="driver_examples/iap/iap_flash" doc="readme.txt">
      <description>The IAP Flash project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/iap_flash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_master" folder="driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_slave" folder="driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_master" folder="driver_examples/i2c/polling_b2b/master" doc="readme.txt">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_i2c_polling_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_slave" folder="driver_examples/i2c/polling_b2b/slave" doc="readme.txt">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpc_i2c_polling_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="driver_examples/mrt" doc="readme.txt">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mrt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_lpc" folder="demo_apps/power_mode_switch_lpc" doc="readme.txt">
      <description>The power_mode_switch application shows the usage of normal power mode control APIs for entering the three kinds oflow power mode: Sleep mode, Deep Sleep mode and Power Down mode, deep power down mode. When the application runs to each low powermode, the device would cut off the power for specific modules to save energy. The device can be also waken up by prepared wakeup source from external event. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - In order to meet typedef power consumption of DateSheet manual, Please configure MCU under the following conditions.     鈥? Configure all pins as GPIO with pull-up resistor disabled in the IOCON block.     鈥? Configure GPIO pins as outputs using the GPIO DIR register.     鈥?Write 1 to the GPIO CLR register to drive the outputs LOW.     鈥?All peripherals disabled.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_lpc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/power_mode_switch_lpc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="driver_examples/sctimer/16bit_counter" doc="readme.txt">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_16bit_counter.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.txt">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="driver_examples/sctimer/simple_pwm" doc="readme.txt">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sctimer_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_master" folder="driver_examples/spi/interrupt/master" doc="readme.txt">
      <description>The spi_interrupt_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from slave,and check if the data master received is correct. This example needs to work with spi_interrupt_slave example. </description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_slave" folder="driver_examples/spi/interrupt/slave" doc="readme.txt">
      <description>The spi_interrupt_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. This example should work with spi_interrupt_master example. And this example should start first. </description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_interrupt_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_master" folder="driver_examples/spi/polling/master" doc="readme.txt">
      <description>The spi_polling_transfer_master example shows how to use spi driver as master to do board to boardtransfer with polling:In this example, one spi instance as master and another spi instance on othere board as slave. Mastersends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct. This example need to work with spi_polling_transfer_slave example. </description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_polling_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_slave" folder="driver_examples/spi/polling/slave" doc="readme.txt">
      <description>The spi_polling_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. This example should work with spi_polling_transfer_master example. And this example should start first.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_polling_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_master" folder="driver_examples/spi/transfer_interrupt/master" doc="readme.txt">
      <description>The spi_interrupt_transfer_master example shows how to use spi driver as master to do board to boardtransfer in interrupt way:In this example, one spi instance as master and another spi instance on othere board as slave. Mastersends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct. This example need to work with spi_interrupt_transfer_slave example. </description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_transfer_interrupt_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_slave" folder="driver_examples/spi/transfer_interrupt/slave" doc="readme.txt">
      <description>The spi_interrupt_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. This example should work with spi_interrupt_transfer_master example. And this example should start first. </description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/spi_transfer_interrupt_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling_example" folder="driver_examples/usart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC, the board will send back all characters that PCsend to the board. </description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_polling_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_terminal" folder="driver_examples/usart/terminal" doc="readme.txt">
      <description>This example demonstrate configuration and use of the USART module in interrupt-driven 
asynchronous mode on communication with a terminal emulator calling the USART 
transactional APIs. USART will echo back every character to terminal emulator, and send
back all received characters once users press [Enter] key.
</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_terminal.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_terminal.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_interrupt" folder="driver_examples/usart/transfer_interrupt" doc="readme.txt">
      <description>usart_transfer_interrupt</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_transfer_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_ring_buffer" folder="driver_examples/usart/transfer_ring_buffer" doc="readme.txt">
      <description>The usart_interrupt_rb_transfer example shows how to use usart driver in interrupt way withRX ring buffer enabled.In this example, one uart instance connect to PC through, the board will send back all charactersthat PC send to the board.Note: The example echo every 8 characters.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_ring_buffer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_transfer_ring_buffer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_sync_mode" folder="driver_examples/usart/transfer_sync_mode" doc="readme.txt">
      <description>The usart_interrupt_sync_transfer example shows how to use usart API in synchronous mode:In this example, one usart instance will be selected as master ,and another as slave. The master will send data to slave in polling way, and slave will receive data in nonblocking way.After all data has been received by slave, info will be printed by debug console.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_sync_mode.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/usart_transfer_sync_mode.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wkt_example" folder="driver_examples/wkt" doc="readme.txt">
      <description>The WKT project is a simple demonstration program of the SDK WKT driver. It sets up the WKT hardware block to trigger a periodic interrupt after loading a counter value and counting down to 0. When the WKT interrupt is triggered a message printed on the UART terminal and the LED is toggled on the board.Depending on the clock source, the WKT can be used for waking up the part from any low power mode or for general-purposetiming.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wkt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wkt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="driver_examples/wwdt" doc="readme.txt">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso812MAX" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wwdt_example.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso812max" Cversion="1.0.0" condition="device.LPC812_AND_component.miniusart_adapter_AND_device.LPC812_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_lite_AND_driver.lpc_miniusart_AND_driver.power_no_lib_AND_driver.swm_AND_utility.debug_console_lite">
      <description>Board_project_template lpcxpresso812max; {for-development:SDK-Manifest-ID: project_template.lpcxpresso812max.LPC812}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
