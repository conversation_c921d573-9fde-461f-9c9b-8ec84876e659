<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFM32G_DFP</name>
  <description>Silicon Labs EFM32G Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
    <release version="3.1.0" date="2020-12-11">
      New MCU's families efr32pg22, efr32fg23, efr32zg23 and efm32pg23.
    </release>
    <release version="3.0.0" date="2020-09-14">
      Addition of EFR32ZG13L, EFR32ZG13S and removal of EFR32BG14P device families.
    </release>
    <release version="2.7.3" date="2020-03-23">
      The PRORTC timer peripheral has been added to EFR32xG13 and EFR32xG14 device families.
    </release>
    <release version="2.7.0" date="2019-11-12">
      Changed pack names and version number sequence.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFM32G</keyword>
    <keyword>EFM32</keyword>
    <keyword>Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFM32G Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M3" DcoreVersion="r2p0" Dfpu="NO_FPU" Dmpu="MPU" Dtz="NO_TZ" Ddsp="NO_DSP" Dendian="Little-endian" Dclock="32000000"/>
      <book name="Documents/cortex_m3_dgug.pdf"      title="Cortex-M3 Generic User Guide"/>
      <book name="Documents/efm32-cortex-m3-rm.pdf"  title="EFM32 Cortex-M3 Reference Manual"/>
      <book name="Documents/EFM32G-RM.pdf"  title="EFM32G Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M3 processor running at up to 32 MHz&#xD;&#xA;- Up to 128 kB Flash and 16 kB RAM memory&#xD;&#xA;- Energy efficient and autonomous peripherals&#xD;&#xA;- Ultra low power Energy Modes with sub-uA operation&#xD;&#xA;- Fast wake-up time of only 2 us&#xD;&#xA;&#xD;&#xA;The EFM32G microcontroller series revolutionizes the 8- to 32-bit market with a combination of unmatched performance and ultra low power consumption in both active- and sleep modes. EFM32G devices consume as little as 180 uA/MHz in run mode, and as little as 900 nA with a Real Time Counter running, Brown-out and full RAM and register retention.
      </description>

      <subFamily DsubFamily="EFM32G200">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G200 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G200 Errata"/>
        <!-- *************************  Device 'EFM32G200F16'  ***************************** -->
        <device Dname="EFM32G200F16">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G200F16"/>
          <debug      svd="SVD/EFM32G/EFM32G200F16.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00004000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00004000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00004000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G200F32'  ***************************** -->
        <device Dname="EFM32G200F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G200F32"/>
          <debug      svd="SVD/EFM32G/EFM32G200F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G200F64'  ***************************** -->
        <device Dname="EFM32G200F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G200F64"/>
          <debug      svd="SVD/EFM32G/EFM32G200F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G210">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G210 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G210 Errata"/>
        <!-- *************************  Device 'EFM32G210F128'  ***************************** -->
        <device Dname="EFM32G210F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G210F128"/>
          <debug      svd="SVD/EFM32G/EFM32G210F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G222">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G222 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G222 Errata"/>
        <!-- *************************  Device 'EFM32G222F128'  ***************************** -->
        <device Dname="EFM32G222F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G222F128"/>
          <debug      svd="SVD/EFM32G/EFM32G222F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G222F32'  ***************************** -->
        <device Dname="EFM32G222F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G222F32"/>
          <debug      svd="SVD/EFM32G/EFM32G222F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G222F64'  ***************************** -->
        <device Dname="EFM32G222F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G222F64"/>
          <debug      svd="SVD/EFM32G/EFM32G222F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G230">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G230 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G230 Errata"/>
        <!-- *************************  Device 'EFM32G230F128'  ***************************** -->
        <device Dname="EFM32G230F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G230F128"/>
          <debug      svd="SVD/EFM32G/EFM32G230F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G230F32'  ***************************** -->
        <device Dname="EFM32G230F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G230F32"/>
          <debug      svd="SVD/EFM32G/EFM32G230F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G230F64'  ***************************** -->
        <device Dname="EFM32G230F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G230F64"/>
          <debug      svd="SVD/EFM32G/EFM32G230F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G232">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G232 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G232 Errata"/>
        <!-- *************************  Device 'EFM32G232F128'  ***************************** -->
        <device Dname="EFM32G232F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G232F128"/>
          <debug      svd="SVD/EFM32G/EFM32G232F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G232F32'  ***************************** -->
        <device Dname="EFM32G232F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G232F32"/>
          <debug      svd="SVD/EFM32G/EFM32G232F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G232F64'  ***************************** -->
        <device Dname="EFM32G232F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G232F64"/>
          <debug      svd="SVD/EFM32G/EFM32G232F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G280">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G280 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G280 Errata"/>
        <!-- *************************  Device 'EFM32G280F128'  ***************************** -->
        <device Dname="EFM32G280F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G280F128"/>
          <debug      svd="SVD/EFM32G/EFM32G280F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G280F32'  ***************************** -->
        <device Dname="EFM32G280F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G280F32"/>
          <debug      svd="SVD/EFM32G/EFM32G280F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G280F64'  ***************************** -->
        <device Dname="EFM32G280F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G280F64"/>
          <debug      svd="SVD/EFM32G/EFM32G280F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G290">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G290 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G290 Errata"/>
        <!-- *************************  Device 'EFM32G290F128'  ***************************** -->
        <device Dname="EFM32G290F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G290F128"/>
          <debug      svd="SVD/EFM32G/EFM32G290F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G290F32'  ***************************** -->
        <device Dname="EFM32G290F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G290F32"/>
          <debug      svd="SVD/EFM32G/EFM32G290F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G290F64'  ***************************** -->
        <device Dname="EFM32G290F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G290F64"/>
          <debug      svd="SVD/EFM32G/EFM32G290F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G800">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G800 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G800 Errata"/>
        <!-- *************************  Device 'EFM32G800F128'  ***************************** -->
        <device Dname="EFM32G800F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G800F128"/>
          <debug      svd="SVD/EFM32G/EFM32G800F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G840">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G840 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G840 Errata"/>
        <!-- *************************  Device 'EFM32G840F128'  ***************************** -->
        <device Dname="EFM32G840F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G840F128"/>
          <debug      svd="SVD/EFM32G/EFM32G840F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G840F32'  ***************************** -->
        <device Dname="EFM32G840F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G840F32"/>
          <debug      svd="SVD/EFM32G/EFM32G840F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G840F64'  ***************************** -->
        <device Dname="EFM32G840F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G840F64"/>
          <debug      svd="SVD/EFM32G/EFM32G840F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G842">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G842 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G842 Errata"/>
        <!-- *************************  Device 'EFM32G842F128'  ***************************** -->
        <device Dname="EFM32G842F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G842F128"/>
          <debug      svd="SVD/EFM32G/EFM32G842F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G842F32'  ***************************** -->
        <device Dname="EFM32G842F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G842F32"/>
          <debug      svd="SVD/EFM32G/EFM32G842F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G842F64'  ***************************** -->
        <device Dname="EFM32G842F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G842F64"/>
          <debug      svd="SVD/EFM32G/EFM32G842F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G880">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G880 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G880 Errata"/>
        <!-- *************************  Device 'EFM32G880F128'  ***************************** -->
        <device Dname="EFM32G880F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G880F128"/>
          <debug      svd="SVD/EFM32G/EFM32G880F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G880F32'  ***************************** -->
        <device Dname="EFM32G880F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G880F32"/>
          <debug      svd="SVD/EFM32G/EFM32G880F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G880F64'  ***************************** -->
        <device Dname="EFM32G880F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G880F64"/>
          <debug      svd="SVD/EFM32G/EFM32G880F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32G890">
        <book         name="Documents/efm32g-datasheet.pdf"      title="EFM32G890 Data Sheet"/>
        <book         name="Documents/efm32g-errata.pdf"         title="EFM32G890 Errata"/>
        <!-- *************************  Device 'EFM32G890F128'  ***************************** -->
        <device Dname="EFM32G890F128">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G890F128"/>
          <debug      svd="SVD/EFM32G/EFM32G890F128.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G890F32'  ***************************** -->
        <device Dname="EFM32G890F32">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G890F32"/>
          <debug      svd="SVD/EFM32G/EFM32G890F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00008000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00008000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32G890F64'  ***************************** -->
        <device Dname="EFM32G890F64">
          <compile header="Device/SiliconLabs/EFM32G/Include/em_device.h"  define="EFM32G890F64"/>
          <debug      svd="SVD/EFM32G/EFM32G890F64.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00004000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32.FLM"  start="0x00000000"  size="0x00010000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashEFM32.flash"  start="0x00000000"  size="0x00010000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFM32G">
      <description>Silicon Labs EFM32G device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFM32G*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="4.4.0" condition="EFM32G">
      <description>System Startup for Silicon Labs EFM32G device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFM32G/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFM32G/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFM32G/Source/GCC/startup_efm32g.S" condition="Compiler GCC"   attr="config" version="4.4.0"/>
        <file category="source"  name="Device/SiliconLabs/EFM32G/Source/IAR/startup_efm32g.s" condition="Compiler IAR"   attr="config" version="4.4.0"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFM32G/Source/GCC/efm32g.ld" condition="Compiler GCC" attr="config" version="4.4.0"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFM32G/Source/system_efm32g.c" attr="config" version="4.4.0"/>
      </files>
    </component>
  </components>
</package>
