<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>SDMMC</name>
  <vendor>NXP</vendor>
  <description>Software Pack for sdmmc</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="3.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="2.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.sdmmc.host.sdif.condition_id">
      <require condition="anyOf.middleware.sdmmc.host.sdif.freertos, middleware.sdmmc.host.sdif.interrupt, middleware.sdmmc.host.sdif.polling, middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.sdmmc.host.sdif.freertos, middleware.sdmmc.host.sdif.interrupt, middleware.sdmmc.host.sdif.polling, middleware.sdmmc.host.sdif.azurertos.internal_condition">
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_freertos"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_interrupt"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_polling"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_azurertos"/>
    </condition>
    <condition id="middleware.sdmmc.host.sdif.azurertos.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_azurertos, middleware.sdmmc.sdif.template, driver.sdif, not=middleware.sdmmc.host.sdif.freertos, not=middleware.sdmmc.host.sdif.interrupt, not=middleware.sdmmc.host.sdif.polling.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_azurertos, middleware.sdmmc.sdif.template, driver.sdif, not=middleware.sdmmc.host.sdif.freertos, not=middleware.sdmmc.host.sdif.interrupt, not=middleware.sdmmc.host.sdif.polling.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_azurertos"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="sdif"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdif"/>
      <require condition="not.middleware.sdmmc.host.sdif.freertos.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.interrupt.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.polling.internal_condition"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.sdif.freertos.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_freertos"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.sdif.interrupt.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_interrupt"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.sdif.polling.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_polling"/>
    </condition>
    <condition id="middleware.sdmmc.host.sdif.freertos.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_freertos, middleware.sdmmc.sdif.template, driver.sdif, not=middleware.sdmmc.host.sdif.interrupt, not=middleware.sdmmc.host.sdif.polling, not=middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_freertos, middleware.sdmmc.sdif.template, driver.sdif, not=middleware.sdmmc.host.sdif.interrupt, not=middleware.sdmmc.host.sdif.polling, not=middleware.sdmmc.host.sdif.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_freertos"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="sdif"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdif"/>
      <require condition="not.middleware.sdmmc.host.sdif.interrupt.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.polling.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.sdif.azurertos.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_azurertos"/>
    </condition>
    <condition id="middleware.sdmmc.host.sdif.interrupt.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.sdif, middleware.sdmmc.sdif.template, not=middleware.sdmmc.host.sdif.freertos, not=middleware.sdmmc.host.sdif.polling, not=middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.sdif, middleware.sdmmc.sdif.template, not=middleware.sdmmc.host.sdif.freertos, not=middleware.sdmmc.host.sdif.polling, not=middleware.sdmmc.host.sdif.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_bm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdif"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="sdif"/>
      <require condition="not.middleware.sdmmc.host.sdif.freertos.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.polling.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="middleware.sdmmc.host.sdif.polling.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.sdif, middleware.sdmmc.sdif.template, not=middleware.sdmmc.host.sdif.freertos, not=middleware.sdmmc.host.sdif.interrupt, not=middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.sdif, middleware.sdmmc.sdif.template, not=middleware.sdmmc.host.sdif.freertos, not=middleware.sdmmc.host.sdif.interrupt, not=middleware.sdmmc.host.sdif.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_bm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdif"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="sdif"/>
      <require condition="not.middleware.sdmmc.host.sdif.freertos.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.interrupt.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.sdif.azurertos.internal_condition"/>
    </condition>
    <condition id="middleware.sdmmc.host.usdhc.condition_id">
      <require condition="anyOf.middleware.sdmmc.host.usdhc.freertos, middleware.sdmmc.host.usdhc.interrupt, middleware.sdmmc.host.usdhc.polling, middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.sdmmc.host.usdhc.freertos, middleware.sdmmc.host.usdhc.interrupt, middleware.sdmmc.host.usdhc.polling, middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_freertos"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_interrupt"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_polling"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_azurertos"/>
    </condition>
    <condition id="middleware.sdmmc.host.usdhc.azurertos.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_azurertos, middleware.sdmmc.usdhc.template, driver.usdhc, not=middleware.sdmmc.host.usdhc.freertos, not=middleware.sdmmc.host.usdhc.interrupt, not=middleware.sdmmc.host.usdhc.polling.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_azurertos, middleware.sdmmc.usdhc.template, driver.usdhc, not=middleware.sdmmc.host.usdhc.freertos, not=middleware.sdmmc.host.usdhc.interrupt, not=middleware.sdmmc.host.usdhc.polling.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_azurertos"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="usdhc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdhc"/>
      <require condition="not.middleware.sdmmc.host.usdhc.freertos.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.interrupt.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.polling.internal_condition"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.usdhc.freertos.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_freertos"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.usdhc.interrupt.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_interrupt"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.usdhc.polling.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_polling"/>
    </condition>
    <condition id="device_id.MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition">
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.sdmmc.host.usdhc.cache.condition_id">
      <require condition="anyOf.allOf=driver.cache_armv7_m7, device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, not=device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=driver.cache_armv7_m7, device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, not=device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition">
      <accept condition="allOf.driver.cache_armv7_m7, device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
      <accept condition="not.device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
    </condition>
    <condition id="allOf.driver.cache_armv7_m7, device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="cache"/>
      <require condition="device_id.MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
    </condition>
    <condition id="not.device_id=MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition">
      <deny condition="device_id.MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB.internal_condition"/>
    </condition>
    <condition id="middleware.sdmmc.host.usdhc.freertos.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_freertos, middleware.sdmmc.usdhc.template, middleware.sdmmc.host.usdhc.cache, driver.usdhc, not=middleware.sdmmc.host.usdhc.interrupt, not=middleware.sdmmc.host.usdhc.polling, not=middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_freertos, middleware.sdmmc.usdhc.template, middleware.sdmmc.host.usdhc.cache, driver.usdhc, not=middleware.sdmmc.host.usdhc.interrupt, not=middleware.sdmmc.host.usdhc.polling, not=middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_freertos"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="usdhc"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_cache"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdhc"/>
      <require condition="not.middleware.sdmmc.host.usdhc.interrupt.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.polling.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="not.middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <deny Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_azurertos"/>
    </condition>
    <condition id="middleware.sdmmc.host.usdhc.interrupt.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.usdhc, middleware.sdmmc.usdhc.template, middleware.sdmmc.host.usdhc.cache, not=middleware.sdmmc.host.usdhc.freertos, not=middleware.sdmmc.host.usdhc.polling, not=middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.usdhc, middleware.sdmmc.usdhc.template, middleware.sdmmc.host.usdhc.cache, not=middleware.sdmmc.host.usdhc.freertos, not=middleware.sdmmc.host.usdhc.polling, not=middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_bm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdhc"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="usdhc"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_cache"/>
      <require condition="not.middleware.sdmmc.host.usdhc.freertos.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.polling.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="middleware.sdmmc.host.usdhc.polling.condition_id">
      <require condition="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.usdhc, middleware.sdmmc.usdhc.template, middleware.sdmmc.host.usdhc.cache, not=middleware.sdmmc.host.usdhc.freertos, not=middleware.sdmmc.host.usdhc.interrupt, not=middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, middleware.sdmmc.osa_bm, driver.usdhc, middleware.sdmmc.usdhc.template, middleware.sdmmc.host.usdhc.cache, not=middleware.sdmmc.host.usdhc.freertos, not=middleware.sdmmc.host.usdhc.interrupt, not=middleware.sdmmc.host.usdhc.azurertos.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_bm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="sdhc"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="usdhc"/>
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_cache"/>
      <require condition="not.middleware.sdmmc.host.usdhc.freertos.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.interrupt.internal_condition"/>
      <require condition="not.middleware.sdmmc.host.usdhc.azurertos.internal_condition"/>
    </condition>
    <condition id="middleware.sdmmc.mmc.condition_id">
      <require condition="allOf.middleware.sdmmc.common, anyOf=middleware.sdmmc.host.usdhc, middleware.sdmmc.host.sdif.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.sdmmc.common, anyOf=middleware.sdmmc.host.usdhc, middleware.sdmmc.host.sdif.internal_condition">
      <require Cclass="Memories" Cgroup="SDMMC Stack" Csub="common"/>
      <require condition="anyOf.middleware.sdmmc.host.usdhc, middleware.sdmmc.host.sdif.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.sdmmc.host.usdhc, middleware.sdmmc.host.sdif.internal_condition">
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc"/>
      <accept Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif"/>
    </condition>
    <condition id="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition">
      <accept Dname="LPC54005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54605J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54606J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54607J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54608J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J512ET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54616J256ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512BD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54618J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54628J512ET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S005JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET100" Dvendor="NXP:11"/>
      <accept Dname="LPC54S016JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JBD208" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018JET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J2MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC54S018J4MET180" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5526JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC5528JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S26JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S28JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
      <accept Dname="K32L3A60VPJ1A" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN546VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN547VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN946VPB" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VDF" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VNL" Dvendor="NXP:11"/>
      <accept Dname="MCXN947VPB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAF4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAF5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1021DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1051DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1052DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1064DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT533SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT555SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFAWC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT595SFFOC" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.sdmmc.osa_azurertos.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, component.osa_thread.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, component.osa_thread.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_thread"/>
    </condition>
    <condition id="middleware.sdmmc.osa_bm.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, component.osa_bm.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, component.osa_bm.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_bm"/>
    </condition>
    <condition id="middleware.sdmmc.osa_freertos.condition_id">
      <require condition="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, component.osa_free_rtos.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S, component.osa_free_rtos.internal_condition">
      <require condition="device_id.LPC54005, LPC54016, LPC54018, LPC54018J2M, LPC54018J4M, LPC54605J512, LPC54605J256, LPC54606J512, LPC54606J256, LPC54607J256, LPC54607J512, LPC54608J512, LPC54616J512, LPC54616J256, LPC54618J512, LPC54628J512, LPC54S005, LPC54S016, LPC54S018, LPC54S018J2M, LPC54S018J4M, LPC5526, LPC5528, LPC55S26, LPC55S28, LPC55S66, LPC55S69, K32L3A60xxx, MCXN546, MCXN547, MCXN946, MCXN947, MIMXRT1021xxxxx, MIMXRT1024xxxxx, MIMXRT1041xxxxB, MIMXRT1042xxxxB, MIMXRT1051xxxxB, MIMXRT1052xxxxB, MIMXRT1061xxxxA, MIMXRT1061xxxxB, MIMXRT1062xxxxA, MIMXRT1062xxxxB, MIMXRT1064xxxxA, MIMXRT1064xxxxB, MIMXRT1165xxxxx, MIMXRT1166xxxxx, MIMXRT1171xxxxx, MIMXRT1172xxxxx, MIMXRT1173xxxxx, MIMXRT1175xxxxx, MIMXRT1176xxxxx, MIMXRT1181xxxxx, MIMXRT1182xxxxx, MIMXRT1187xxxxx, MIMXRT1189xxxxx, MIMXRT533S, MIMXRT555S, MIMXRT595S, MIMXRT633S, MIMXRT685S.internal_condition"/>
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_free_rtos"/>
    </condition>
    <condition id="middleware.sdmmc.sd.condition_id">
      <require condition="allOf.middleware.sdmmc.common, anyOf=middleware.sdmmc.host.usdhc, middleware.sdmmc.host.sdif.internal_condition"/>
    </condition>
    <condition id="middleware.sdmmc.sdio.condition_id">
      <require condition="allOf.middleware.sdmmc.common, anyOf=middleware.sdmmc.host.usdhc, middleware.sdmmc.host.sdif.internal_condition"/>
    </condition>
    <condition id="device_id.MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7.internal_condition">
      <accept Dname="MK02FN128VFM10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VLF10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VFM10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VLF10" Dvendor="NXP:11"/>
      <accept Dname="MK02FN64VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VDC10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLH10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VLL10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128VMP10" Dvendor="NXP:11"/>
      <accept Dname="MK22FN128CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256CAH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN256VMP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VMP12" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B11VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B21VMP0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFM0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VFT0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VLH0A" Dvendor="NXP:11"/>
      <accept Dname="K32L2B31VMP0A" Dvendor="NXP:11"/>
      <accept Dname="MCXC041VFG" Dvendor="NXP:11"/>
      <accept Dname="MCXC041VFK" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC141VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC142VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC143VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC144VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC242VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC243VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXC244VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC443VMP" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VLH" Dvendor="NXP:11"/>
      <accept Dname="MCXC444VMP" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z16VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z16VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z16VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z32VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VFM4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLC4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLD4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VLH4" Dvendor="NXP:11"/>
      <accept Dname="MKE02Z64VQH4" Dvendor="NXP:11"/>
      <accept Dname="MKM14Z64ACHH5" Dvendor="NXP:11"/>
      <accept Dname="MKM14Z128ACHH5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z64ACLH5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z64ACLL5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z128ACLH5" Dvendor="NXP:11"/>
      <accept Dname="MKM33Z128ACLL5" Dvendor="NXP:11"/>
      <accept Dname="MKM34Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM34Z256VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM34Z128ACLL5" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLQ7R" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.sdmmc.sdspi.condition_id">
      <require condition="allOf.device_id=MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, anyOf=driver.dspi, driver.spi.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7, anyOf=driver.dspi, driver.spi.internal_condition">
      <require condition="device_id.MK02FN128xxx10, MK02FN64xxx10, MK22FN128xxx10, MK22FN128xxx12, MK22FN256xxx12, MK22FN512xxx12, K32L2B11xxxxA, K32L2B21xxxxA, K32L2B31xxxxA, MCXC041, MCXC141, MCXC142, MCXC143, MCXC144, MCXC242, MCXC243, MCXC244, MCXC443, MCXC444, MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4, MKM14Z64Axxx5, MKM14Z128Axxx5, MKM33Z64Axxx5, MKM33Z128Axxx5, MKM34Z256xxx7, MKM34Z128Axxx5, MKM35Z512xxx7, MKM35Z256xxx7.internal_condition"/>
      <require condition="anyOf.driver.dspi, driver.spi.internal_condition"/>
    </condition>
    <condition id="anyOf.driver.dspi, driver.spi.internal_condition">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="dspi"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="spi"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="common" Cversion="2.3.0">
      <description>Middleware sdmmc common</description>
      <files>
        <file category="header" name="middleware/sdmmc/common/fsl_sdmmc_spec.h" projectpath="sdmmc/inc"/>
        <file category="header" name="middleware/sdmmc/common/fsl_sdmmc_common.h" projectpath="sdmmc/inc"/>
        <file category="sourceC" name="middleware/sdmmc/common/fsl_sdmmc_common.c" projectpath="sdmmc/src"/>
        <file category="include" name="middleware/sdmmc/common/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif" Cversion="2.4.1" condition="middleware.sdmmc.host.sdif.condition_id">
      <description>Middleware sdmmc host sdif</description>
      <files>
        <file category="header" name="middleware/sdmmc/host/sdif/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/sdif/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/sdif/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_azurertos" Cversion="2.4.1" condition="middleware.sdmmc.host.sdif.azurertos.condition_id">
      <description>Middleware sdmmc host sdif azurertos</description>
      <files>
        <file category="header" name="middleware/sdmmc/host/sdif/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="sourceC" name="middleware/sdmmc/host/sdif/non_blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/sdif/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/sdif/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_freertos" Cversion="2.4.1" condition="middleware.sdmmc.host.sdif.freertos.condition_id">
      <description>Middleware sdmmc host sdif freertos</description>
      <files>
        <file category="header" name="middleware/sdmmc/host/sdif/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="sourceC" name="middleware/sdmmc/host/sdif/non_blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/sdif/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/sdif/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_interrupt" Cversion="2.4.1" condition="middleware.sdmmc.host.sdif.interrupt.condition_id">
      <description>Middleware sdmmc host sdif interrupt</description>
      <files>
        <file category="header" name="middleware/sdmmc/host/sdif/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="sourceC" name="middleware/sdmmc/host/sdif/non_blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/sdif/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/sdif/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdif_polling" Cversion="2.4.1" condition="middleware.sdmmc.host.sdif.polling.condition_id">
      <description>Middleware sdmmc host sdif polling</description>
      <files>
        <file category="header" name="middleware/sdmmc/host/sdif/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="sourceC" name="middleware/sdmmc/host/sdif/blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/sdif/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/sdif/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc" Cversion="2.6.3" condition="middleware.sdmmc.host.usdhc.condition_id">
      <description>Middleware sdmmc host usdhc</description>
      <files>
        <file category="header" name="middleware/sdmmc/host/usdhc/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/usdhc/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/usdhc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_azurertos" Cversion="2.6.3" condition="middleware.sdmmc.host.usdhc.azurertos.condition_id">
      <description>Middleware sdmmc host usdhc azurertos</description>
      <files>
        <file category="sourceC" name="middleware/sdmmc/host/usdhc/non_blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="header" name="middleware/sdmmc/host/usdhc/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/usdhc/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/usdhc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_cache" Cversion="1.0.0" condition="middleware.sdmmc.host.usdhc.cache.condition_id">
      <description>SDMMC host controller cache dependency</description>
      <files>
        <file category="doc" name="middleware/sdmmc/host/usdhc/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/usdhc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_freertos" Cversion="2.6.3" condition="middleware.sdmmc.host.usdhc.freertos.condition_id">
      <description>Middleware sdmmc host usdhc freertos</description>
      <files>
        <file category="sourceC" name="middleware/sdmmc/host/usdhc/non_blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="header" name="middleware/sdmmc/host/usdhc/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/usdhc/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/usdhc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_interrupt" Cversion="2.6.3" condition="middleware.sdmmc.host.usdhc.interrupt.condition_id">
      <description>Middleware sdmmc host usdhc interrupt</description>
      <files>
        <file category="sourceC" name="middleware/sdmmc/host/usdhc/non_blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="header" name="middleware/sdmmc/host/usdhc/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/usdhc/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/usdhc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="usdhc_polling" Cversion="2.6.3" condition="middleware.sdmmc.host.usdhc.polling.condition_id">
      <description>Middleware sdmmc host usdhc polling</description>
      <files>
        <file category="sourceC" name="middleware/sdmmc/host/usdhc/blocking/fsl_sdmmc_host.c" projectpath="sdmmc/host"/>
        <file category="header" name="middleware/sdmmc/host/usdhc/fsl_sdmmc_host.h" projectpath="sdmmc/host"/>
        <file category="doc" name="middleware/sdmmc/host/usdhc/ChangeLogKSDK.txt" projectpath="sdmmc/host"/>
        <file category="include" name="middleware/sdmmc/host/usdhc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="mmc" Cversion="2.5.1" condition="middleware.sdmmc.mmc.condition_id">
      <description>Middleware sdmmc mmc</description>
      <Pre_Include_Global_h>
#ifndef MMC_ENABLED
#define MMC_ENABLED 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/sdmmc/mmc/fsl_mmc.c" projectpath="sdmmc/src"/>
        <file category="header" name="middleware/sdmmc/mmc/fsl_mmc.h" projectpath="sdmmc/inc"/>
        <file category="doc" name="middleware/sdmmc/mmc/ChangeLogKSDK.txt" projectpath="sdmmc/mmc"/>
        <file category="include" name="middleware/sdmmc/mmc/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_azurertos" Cversion="2.3.0" condition="middleware.sdmmc.osa_azurertos.condition_id">
      <description>Middleware sdmmc osa_azurertos</description>
      <files>
        <file category="header" name="middleware/sdmmc/osa/fsl_sdmmc_osa.h" projectpath="sdmmc/osa"/>
        <file category="sourceC" name="middleware/sdmmc/osa/fsl_sdmmc_osa.c" projectpath="sdmmc/osa"/>
        <file category="include" name="middleware/sdmmc/osa/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_bm" Cversion="2.3.0" condition="middleware.sdmmc.osa_bm.condition_id">
      <description>Middleware sdmmc osa_bm</description>
      <files>
        <file category="header" name="middleware/sdmmc/osa/fsl_sdmmc_osa.h" projectpath="sdmmc/osa"/>
        <file category="sourceC" name="middleware/sdmmc/osa/fsl_sdmmc_osa.c" projectpath="sdmmc/osa"/>
        <file category="include" name="middleware/sdmmc/osa/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="osa_freertos" Cversion="2.3.0" condition="middleware.sdmmc.osa_freertos.condition_id">
      <description>Middleware sdmmc osa_freertos</description>
      <files>
        <file category="header" name="middleware/sdmmc/osa/fsl_sdmmc_osa.h" projectpath="sdmmc/osa"/>
        <file category="sourceC" name="middleware/sdmmc/osa/fsl_sdmmc_osa.c" projectpath="sdmmc/osa"/>
        <file category="include" name="middleware/sdmmc/osa/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sd" Cversion="2.4.1" condition="middleware.sdmmc.sd.condition_id">
      <description>Middleware sdmmc sd</description>
      <Pre_Include_Global_h>
#ifndef SD_ENABLED
#define SD_ENABLED 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/sdmmc/sd/fsl_sd.c" projectpath="sdmmc/src"/>
        <file category="header" name="middleware/sdmmc/sd/fsl_sd.h" projectpath="sdmmc/inc"/>
        <file category="doc" name="middleware/sdmmc/sd/ChangeLogKSDK.txt" projectpath="sdmmc/sd"/>
        <file category="include" name="middleware/sdmmc/sd/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="sdhc" Cversion="1.0.0">
      <description>Template configuration file to be edited by user.</description>
      <files>
        <file category="sourceC" attr="config" name="middleware/sdmmc/template/sdhc/sdmmc_config.c" version="1.0.0" projectpath="sdmmc/template/sdhc"/>
        <file category="header" attr="config" name="middleware/sdmmc/template/sdhc/sdmmc_config.h" version="1.0.0" projectpath="sdmmc/template/sdhc"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="sdif" Cversion="1.0.0">
      <description>Template configuration file to be edited by user.</description>
      <files>
        <file category="sourceC" attr="config" name="middleware/sdmmc/template/sdif/sdmmc_config.c" version="1.0.0" projectpath="sdmmc/template/sdif"/>
        <file category="header" attr="config" name="middleware/sdmmc/template/sdif/sdmmc_config.h" version="1.0.0" projectpath="sdmmc/template/sdif"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdio" Cversion="2.4.1" condition="middleware.sdmmc.sdio.condition_id">
      <description>Middleware sdmmc sdio</description>
      <Pre_Include_Global_h>
#ifndef SDIO_ENABLED
#define SDIO_ENABLED 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/sdmmc/sdio/fsl_sdio.c" projectpath="sdmmc/src"/>
        <file category="header" name="middleware/sdmmc/sdio/fsl_sdio.h" projectpath="sdmmc/inc"/>
        <file category="doc" name="middleware/sdmmc/sdio/ChangeLogKSDK.txt" projectpath="sdmmc/sdio"/>
        <file category="include" name="middleware/sdmmc/sdio/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="sdspi" Cversion="2.2.1" condition="middleware.sdmmc.sdspi.condition_id">
      <description>Middleware sdmmc sdspi</description>
      <Pre_Include_Global_h>
#ifndef SDSPI_ENABLED
#define SDSPI_ENABLED 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/sdmmc/sdspi/fsl_sdspi.c" projectpath="sdmmc/src"/>
        <file category="header" name="middleware/sdmmc/sdspi/fsl_sdspi.h" projectpath="sdmmc/inc"/>
        <file category="header" name="middleware/sdmmc/common/fsl_sdmmc_spec.h" projectpath="sdmmc/inc"/>
        <file category="doc" name="middleware/sdmmc/sdspi/ChangeLogKSDK.txt" projectpath="sdmmc"/>
        <file category="include" name="middleware/sdmmc/sdspi/"/>
        <file category="include" name="middleware/sdmmc/common/"/>
      </files>
    </component>
    <component Cclass="Memories" Cgroup="SDMMC Stack" Csub="template" Cvariant="usdhc" Cversion="1.0.0">
      <description>Template configuration file to be edited by user.</description>
      <files>
        <file category="sourceC" attr="config" name="middleware/sdmmc/template/usdhc/sdmmc_config.c" version="1.0.0" projectpath="sdmmc/template/usdhc"/>
        <file category="header" attr="config" name="middleware/sdmmc/template/usdhc/sdmmc_config.h" version="1.0.0" projectpath="sdmmc/template/usdhc"/>
      </files>
    </component>
  </components>
</package>
