<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32FG25_DFP</name>
  <description>Silicon Labs EFR32FG25 Flex Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32FG25</keyword>
    <keyword>EFR32</keyword>
    <keyword>Flex Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32FG25 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <book name="Documents/efr32xg25-rm.pdf"  title="EFR32FG25 Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M33 core with 97.5 MHz maximum operating frequency&#xD;&#xA;- Up to 1920 kB of flash and 512 kB of RAM&#xD;&#xA;- Wi-SUN Multi-rate OFDM, FSK, and O- QPSK modulations&#xD;&#xA;- Integrated PA with up to 14 dBm (Sub- GHz) TX power&#xD;&#xA;- Robust peripheral set and up to 37 GPIO&#xD;&#xA;&#xD;&#xA;The EFR32FG25 Flex Gecko SoC is an ideal solution for sub-GHz Wi-SUN applications for metering, lighting, and distribution automation. The high-performance sub-GHz radio provides long range and is not susceptible to 2.4 GHz interference from technologies like Wi-Fi.
      </description>

      <subFamily DsubFamily="EFR32FG25A021">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25A021 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25A021 Errata"/>
        <!-- *************************  Device 'EFR32FG25A021F256IM56'  ***************************** -->
        <device Dname="EFR32FG25A021F256IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A021F256IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A021F256IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25A111">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25A111 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25A111 Errata"/>
        <!-- *************************  Device 'EFR32FG25A111F1152IM56'  ***************************** -->
        <device Dname="EFR32FG25A111F1152IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A111F1152IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A111F1152IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00120000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00120000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00120000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25A121">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25A121 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25A121 Errata"/>
        <!-- *************************  Device 'EFR32FG25A121F1152IM56'  ***************************** -->
        <device Dname="EFR32FG25A121F1152IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A121F1152IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A121F1152IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00120000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00120000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00120000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25A211">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25A211 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25A211 Errata"/>
        <!-- *************************  Device 'EFR32FG25A211F1152IM56'  ***************************** -->
        <device Dname="EFR32FG25A211F1152IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A211F1152IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A211F1152IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00120000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00120000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00120000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG25A211F1920IM56'  ***************************** -->
        <device Dname="EFR32FG25A211F1920IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A211F1920IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A211F1920IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x001E0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x001E0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x001E0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25A221">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25A221 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25A221 Errata"/>
        <!-- *************************  Device 'EFR32FG25A221F1152IM56'  ***************************** -->
        <device Dname="EFR32FG25A221F1152IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A221F1152IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A221F1152IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00120000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00120000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00120000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG25A221F1920IM56'  ***************************** -->
        <device Dname="EFR32FG25A221F1920IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25A221F1920IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25A221F1920IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x001E0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x001E0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x001E0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25B111">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25B111 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25B111 Errata"/>
        <!-- *************************  Device 'EFR32FG25B111F1152IM56'  ***************************** -->
        <device Dname="EFR32FG25B111F1152IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25B111F1152IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25B111F1152IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00120000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00120000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00120000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25B121">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25B121 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25B121 Errata"/>
        <!-- *************************  Device 'EFR32FG25B121F1152IM56'  ***************************** -->
        <device Dname="EFR32FG25B121F1152IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25B121F1152IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25B121F1152IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00120000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00120000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00120000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25B211">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25B211 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25B211 Errata"/>
        <!-- *************************  Device 'EFR32FG25B211F1920IM56'  ***************************** -->
        <device Dname="EFR32FG25B211F1920IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25B211F1920IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25B211F1920IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x001E0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x001E0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x001E0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25B212">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25B212 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25B212 Errata"/>
        <!-- *************************  Device 'EFR32FG25B212F1920IM56'  ***************************** -->
        <device Dname="EFR32FG25B212F1920IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25B212F1920IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25B212F1920IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x001E0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x001E0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x001E0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25B221">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25B221 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25B221 Errata"/>
        <!-- *************************  Device 'EFR32FG25B221F1920IM56'  ***************************** -->
        <device Dname="EFR32FG25B221F1920IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25B221F1920IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25B221F1920IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x001E0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x001E0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x001E0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG25B222">
        <book         name="Documents/efr32fg25-datasheet.pdf"      title="EFR32FG25B222 Data Sheet"/>
        <book         name="Documents/efr32fg25-errata.pdf"         title="EFR32FG25B222 Errata"/>
        <!-- *************************  Device 'EFR32FG25B222F1920IM56'  ***************************** -->
        <device Dname="EFR32FG25B222F1920IM56">
          <compile header="Device/SiliconLabs/EFR32FG25/Include/em_device.h"  define="EFR32FG25B222F1920IM56"/>
          <debug      svd="SVD/EFR32FG25/EFR32FG25B222F1920IM56.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x001E0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00080000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x001E0000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x001E0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32FG25">
      <description>Silicon Labs EFR32FG25 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32FG25*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32FG25">
      <description>System Startup for Silicon Labs EFR32FG25 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32FG25/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32FG25/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32FG25/Source/system_efr32fg25.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
