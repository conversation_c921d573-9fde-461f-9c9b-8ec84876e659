<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32FG23_DFP</name>
  <description>Silicon Labs EFR32FG23 Flex Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
    <release version="3.1.0" date="2020-12-11">
      New MCU's families efr32pg22, efr32fg23, efr32zg23 and efm32pg23.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32FG23</keyword>
    <keyword>EFR32</keyword>
    <keyword>Flex Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32FG23 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <book name="Documents/efr32xg23-rm.pdf"  title="EFR32FG23 Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency&#xD;&#xA;- Up to 512 kB of flash and 64 kB of RAM&#xD;&#xA;- Energy-efficient radio core with low active and sleep currents&#xD;&#xA;- Integrated PA with up to 20 dBm (Sub-GHz) TX power&#xD;&#xA;- Robust peripheral set and up to 31 GPIO&#xD;&#xA;&#xD;&#xA;The EFR32FG23 Flex Gecko SoC is an ideal solution for sub-GHz "Internet of Things" for smart home, security, lighting, building automation, and metering. The high-performance sub-GHz radio provides long range and is not susceptible to 2.4 GHz interference from technologies like Wi-Fi.
      </description>

      <subFamily DsubFamily="EFR32FG23A010">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23A010 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23A010 Errata"/>
        <!-- *************************  Device 'EFR32FG23A010F128GM40'  ***************************** -->
        <device Dname="EFR32FG23A010F128GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A010F128GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A010F128GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A010F256GM40'  ***************************** -->
        <device Dname="EFR32FG23A010F256GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A010F256GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A010F256GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A010F256GM48'  ***************************** -->
        <device Dname="EFR32FG23A010F256GM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A010F256GM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A010F256GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A010F512GM40'  ***************************** -->
        <device Dname="EFR32FG23A010F512GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A010F512GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A010F512GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A010F512GM48'  ***************************** -->
        <device Dname="EFR32FG23A010F512GM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A010F512GM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A010F512GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG23A011">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23A011 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23A011 Errata"/>
        <!-- *************************  Device 'EFR32FG23A011F512GM40'  ***************************** -->
        <device Dname="EFR32FG23A011F512GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A011F512GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A011F512GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG23A020">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23A020 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23A020 Errata"/>
        <!-- *************************  Device 'EFR32FG23A020F128GM40'  ***************************** -->
        <device Dname="EFR32FG23A020F128GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A020F128GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A020F128GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A020F256GM40'  ***************************** -->
        <device Dname="EFR32FG23A020F256GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A020F256GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A020F256GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A020F256GM48'  ***************************** -->
        <device Dname="EFR32FG23A020F256GM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A020F256GM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A020F256GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A020F512GM40'  ***************************** -->
        <device Dname="EFR32FG23A020F512GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A020F512GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A020F512GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23A020F512GM48'  ***************************** -->
        <device Dname="EFR32FG23A020F512GM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A020F512GM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A020F512GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG23A021">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23A021 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23A021 Errata"/>
        <!-- *************************  Device 'EFR32FG23A021F512GM40'  ***************************** -->
        <device Dname="EFR32FG23A021F512GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23A021F512GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23A021F512GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG23B010">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23B010 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23B010 Errata"/>
        <!-- *************************  Device 'EFR32FG23B010F128GM40'  ***************************** -->
        <device Dname="EFR32FG23B010F128GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B010F128GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B010F128GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23B010F512GM48'  ***************************** -->
        <device Dname="EFR32FG23B010F512GM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B010F512GM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B010F512GM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23B010F512IM40'  ***************************** -->
        <device Dname="EFR32FG23B010F512IM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B010F512IM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B010F512IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23B010F512IM48'  ***************************** -->
        <device Dname="EFR32FG23B010F512IM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B010F512IM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B010F512IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG23B020">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23B020 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23B020 Errata"/>
        <!-- *************************  Device 'EFR32FG23B020F128GM40'  ***************************** -->
        <device Dname="EFR32FG23B020F128GM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B020F128GM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B020F128GM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00020000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00008000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00020000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00020000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23B020F512IM40'  ***************************** -->
        <device Dname="EFR32FG23B020F512IM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B020F512IM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B020F512IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23B020F512IM48'  ***************************** -->
        <device Dname="EFR32FG23B020F512IM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B020F512IM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B020F512IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32FG23B021">
        <book         name="Documents/efr32fg23-datasheet.pdf"      title="EFR32FG23B021 Data Sheet"/>
        <book         name="Documents/efr32fg23-errata.pdf"         title="EFR32FG23B021 Errata"/>
        <!-- *************************  Device 'EFR32FG23B021F512IM40'  ***************************** -->
        <device Dname="EFR32FG23B021F512IM40">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B021F512IM40"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B021F512IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32FG23B021F512IM48'  ***************************** -->
        <device Dname="EFR32FG23B021F512IM48">
          <compile header="Device/SiliconLabs/EFR32FG23/Include/em_device.h"  define="EFR32FG23B021F512IM48"/>
          <debug      svd="SVD/EFR32FG23/EFR32FG23B021F512IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x8000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32FG23">
      <description>Silicon Labs EFR32FG23 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32FG23*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32FG23">
      <description>System Startup for Silicon Labs EFR32FG23 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32FG23/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32FG23/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32FG23/Source/system_efr32fg23.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
