<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT685-AUD-EVK_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for MIMXRT685-AUD-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.1.0" date="2022-09-28">NXP CMSIS Packs based on MCUXpresso SDK 2.12.1</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="13.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT685S_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT685-AUD-EVK">
      <description>i.MX RT600 Audio Evaluation Kit</description>
      <image small="boards/mimxrt685audevk/mimxrt685audevk.png"/>
      <mountedDevice Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MIMXRT685S.internal_condition">
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT633S.internal_condition">
      <accept Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MIMXRT685S.internal_condition">
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.mimxrt685audevk.condition_id">
      <require condition="allOf.board=mimxrt685audevk, component.usart_adapter, device_id=MIMXRT685S, device.MIMXRT685S_startup, driver.cache_cache64, driver.clock, driver.common, driver.flash_config.mimxrt685audevk, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.lpc_iopctl, driver.power, driver.reset, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=mimxrt685audevk, component.usart_adapter, device_id=MIMXRT685S, device.MIMXRT685S_startup, driver.cache_cache64, driver.clock, driver.common, driver.flash_config.mimxrt685audevk, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.lpc_iopctl, driver.power, driver.reset, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.mimxrt685audevk.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require condition="device_id.MIMXRT685S.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MIMXRT685S_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="cache"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mimxrt685audevk_flash_config"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iopctl"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.mimxrt685audevk.internal_condition">
      <accept condition="device.MIMXRT685S.internal_condition"/>
      <accept condition="device.MIMXRT633S.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_interrupt" folder="boards/mimxrt685audevk/driver_examples/acmp/interrupt" doc="readme.md">
      <description>The ACMP interrupt project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and print information corresponding to...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt.uvprojx"/>
        <environment name="csolution" load="acmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling" folder="boards/mimxrt685audevk/driver_examples/acmp/polling" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and print information corresponding to different...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling.uvprojx"/>
        <environment name="csolution" load="acmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_round_robin" folder="boards/mimxrt685audevk/driver_examples/acmp/round_robin" doc="readme.md">
      <description>The ACMP Round-Robin project is a simple demonstration program that uses the SDK software. User must set the round-robin mode trigger in specific board properly according to the board resource before running the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_round_robin.uvprojx"/>
        <environment name="csolution" load="acmp_round_robin.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="casper" folder="boards/mimxrt685audevk/driver_examples/casper" doc="readme.md">
      <description>The CASPER Example project is a demonstration program that uses the KSDK software to calculate RSA.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/casper.uvprojx"/>
        <environment name="csolution" load="casper.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="boards/mimxrt685audevk/cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="boards/mimxrt685audevk/cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/mimxrt685audevk/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/mimxrt685audevk/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="boards/mimxrt685audevk/cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="boards/mimxrt685audevk/cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_master" folder="boards/mimxrt685audevk/cmsis_driver_examples/spi/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_slave" folder="boards/mimxrt685audevk/cmsis_driver_examples/spi/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_dma_transfer" folder="boards/mimxrt685audevk/cmsis_driver_examples/usart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_interrupt_transfer" folder="boards/mimxrt685audevk/cmsis_driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/mimxrt685audevk/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/mimxrt685audevk/driver_examples/ctimer/simple_match" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/mimxrt685audevk/driver_examples/ctimer/simple_match_interrupt" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/mimxrt685audevk/driver_examples/ctimer/simple_pwm" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/mimxrt685audevk/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="boards/mimxrt685audevk/driver_examples/dma/channel_chain" doc="readme.md">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="csolution" load="dma_channel_chain.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_hardware_trigger" folder="boards/mimxrt685audevk/driver_examples/dma/hardware_trigger" doc="readme.md">
      <description>The DMA hardware trigger example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers by hardware trigger.The...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_hardware_trigger.uvprojx"/>
        <environment name="csolution" load="dma_hardware_trigger.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="boards/mimxrt685audevk/driver_examples/dma/interleave_transfer" doc="readme.md">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="csolution" load="dma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="boards/mimxrt685audevk/driver_examples/dma/linked_transfer" doc="readme.md">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="csolution" load="dma_linked_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/mimxrt685audevk/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_dma" folder="boards/mimxrt685audevk/driver_examples/dmic/dmic_dma" doc="readme.md">
      <description>This example shows how to use DMA to transfer data from DMIC to memory.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_dma.uvprojx"/>
        <environment name="csolution" load="dmic_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_hwvad" folder="boards/mimxrt685audevk/driver_examples/dmic/dmic_hwvad" doc="readme.md">
      <description>~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~This demo explains how built in HWVAD( HW voice activity detector) in LPC5411x can be used towake up the device from sleep mode</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_hwvad.uvprojx"/>
        <environment name="csolution" load="dmic_hwvad.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_i2s_dma" folder="boards/mimxrt685audevk/driver_examples/dmic/dmic_i2s_dma" doc="readme.md">
      <description>Demonstrates the DMIC working with I2S. Audio is converted to samples in the DMIC module.Then, the data is placed memory buffer. Last, it is send data to the I2S buffer and send to the CODEC, then the audio data will...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_i2s_dma.uvprojx"/>
        <environment name="csolution" load="dmic_i2s_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dmic_multi_channel" folder="boards/mimxrt685audevk/driver_examples/dmic/dmic_multi_channel" doc="readme.md">
      <description>Demonstrates the DMIC multi channel working with I2S. Audio is converted to samples in the DMIC module.Then, the data is placed memory buffer. Last, it is send data to the I2S buffer and send to the CODEC, then the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dmic_multi_channel.uvprojx"/>
        <environment name="csolution" load="dmic_multi_channel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_dma_transfer" folder="boards/mimxrt685audevk/driver_examples/flexspi/nor/dma_transfer" doc="readme.md">
      <description>The flexspi_nor_dma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_dma_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer" folder="boards/mimxrt685audevk/driver_examples/flexspi/nor/polling_transfer" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_psram_dma_transfer" folder="boards/mimxrt685audevk/driver_examples/flexspi/psram/dma_transfer" doc="readme.md">
      <description>The flexspi_psram_dma_transfer example shows how to use flexspi driver with dma: In this example, flexspi will send data and operate the external PSRAM connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_psram_dma_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_psram_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_psram_polling_transfer" folder="boards/mimxrt685audevk/driver_examples/flexspi/psram/polling_transfer" doc="readme.md">
      <description>The flexspi_psram_polling_transfer example shows how to use flexspi driver with polling: In this example, flexspi will send data and operate the external PSRAM connected with FLEXSPI. Some simple flash command will...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_psram_polling_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_psram_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmeas" folder="boards/mimxrt685audevk/driver_examples/fmeas" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Frequency Measure feature of SYSCON module on LPC devices.It shows how to measure a target frequency using a (faster) reference frequency. The example uses the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmeas.uvprojx"/>
        <environment name="csolution" load="fmeas.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/mimxrt685audevk/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear registers for each GPIO pin output register. </description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/mimxrt685audevk/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hashcrypt" folder="boards/mimxrt685audevk/driver_examples/hashcrypt" doc="readme.md">
      <description>The hashcrypt Example project is a demonstration program that uses the KSDK software to encrypt and hash data with hardware acceleration.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hashcrypt.uvprojx"/>
        <environment name="csolution" load="hashcrypt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/mimxrt685audevk/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_hybrid" folder="boards/mimxrt685audevk/demo_apps/hello_world_hybrid" doc="readme.md">
      <description>The HelloWorldHybrid demo prints the "hello world" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to boot from the external flash into the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_hybrid.uvprojx"/>
        <environment name="csolution" load="hello_world_hybrid.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_ns" folder="boards/mimxrt685audevk/trustzone_examples/hello_world/hello_world_ns" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. This demo application also utilizes TrustZone, so it demonstrates basic techniques for TrustZone...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_ns.uvprojx"/>
        <environment name="csolution" load="../hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_s" folder="boards/mimxrt685audevk/trustzone_examples/hello_world/hello_world_s" doc="readme.md">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. This demo application also utilizes TrustZone, so it demonstrates basic techniques for TrustZone...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_s.uvprojx"/>
        <environment name="csolution" load="../hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_swo" folder="boards/mimxrt685audevk/demo_apps/hello_world_swo" doc="readme.md">
      <description>The Hello World SWO demo prints the "SWO: Hello World" string to the SWO viewer. The purpose of this demo is to show how to use the swo, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_swo.uvprojx"/>
        <environment name="csolution" load="hello_world_swo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_master" folder="boards/mimxrt685audevk/driver_examples/i2c/polling_b2b/master" doc="readme.md">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_master.uvprojx"/>
        <environment name="csolution" load="i2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_slave" folder="boards/mimxrt685audevk/driver_examples/i2c/polling_b2b/slave" doc="readme.md">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_slave.uvprojx"/>
        <environment name="csolution" load="i2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_ping_pong_buffer" folder="boards/mimxrt685audevk/driver_examples/i2s/dma_ping_pong_buffer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with WM8904 codec.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_ping_pong_buffer.uvprojx"/>
        <environment name="csolution" load="i2s_dma_ping_pong_buffer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_record_playback" folder="boards/mimxrt685audevk/driver_examples/i2s/dma_record_playback" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_record_playback.uvprojx"/>
        <environment name="csolution" load="i2s_dma_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_tdm_record_playback" folder="boards/mimxrt685audevk/driver_examples/i2s/dma_tdm_record_playback" doc="readme.md">
      <description>The i2s_dma_tdm_record_plyback example shows how to use i2s driver to generate TDM format with DMA:In this example, one i2s instance record and playbacks the audio data in TDM format.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_tdm_record_playback.uvprojx"/>
        <environment name="csolution" load="i2s_dma_tdm_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_dma_transfer" folder="boards/mimxrt685audevk/driver_examples/i2s/dma_transfer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_dma_transfer.uvprojx"/>
        <environment name="csolution" load="i2s_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_record_playback" folder="boards/mimxrt685audevk/driver_examples/i2s/interrupt_record_playback" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_record_playback.uvprojx"/>
        <environment name="csolution" load="i2s_interrupt_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2s_interrupt_transfer" folder="boards/mimxrt685audevk/driver_examples/i2s/interrupt_transfer" doc="readme.md">
      <description>The I2S example project uses one I2S interface to continuously record input sound to a bufferand another I2S interface to playback the buffer to output - digital loopback.It requires NXP Mic/Audio/Oled Shield with...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2s_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="i2s_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_master" folder="boards/mimxrt685audevk/component_examples/i3c_bus/master" doc="readme.md">
      <description>The i3c_bus_master example shows how to use i3c_bus component to create I3C bus and i3c master on bus.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_master.uvprojx"/>
        <environment name="csolution" load="i3c_bus_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_master_read_icm42688p" folder="boards/mimxrt685audevk/component_examples/i3c_bus/read_sensor" doc="readme.md">
      <description>The i3c_bus_master_read_icm42688p example shows how to use i3c_bus component to create I3C bus and i3c master, then use bus master to communicate with slave.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_master_read_icm42688p.uvprojx"/>
        <environment name="csolution" load="i3c_bus_master_read_icm42688p.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_slave" folder="boards/mimxrt685audevk/component_examples/i3c_bus/slave" doc="readme.md">
      <description>The i3c_bus_slave example shows how to use i3c_bus component to work as i3c bus slave.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_slave.uvprojx"/>
        <environment name="csolution" load="i3c_bus_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/i3c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/i3c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_icm42688p" folder="boards/mimxrt685audevk/driver_examples/i3c/master_read_sensor_icm42688p" doc="readme.md">
      <description>The i3c_master_read_sensor_icm42688p example shows how to use i3c driver as master to communicate with sensor ICM-42688P.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_icm42688p.uvprojx"/>
        <environment name="csolution" load="i3c_master_read_sensor_icm42688p.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/i3c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/i3c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_boot" folder="boards/mimxrt685audevk/driver_examples/iap/iap_boot" doc="readme.md">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It invokes into ROM with user specified parameter.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_boot.uvprojx"/>
        <environment name="csolution" load="iap_boot.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="boards/mimxrt685audevk/driver_examples/iap/iap_flash" doc="readme.md">
      <description>The IAP flash project is a simple demonstration program of the SDK IAP driver. It provides some flash operations with ROM API, such as automatically detect flash configuration, erase sectors and program pages. All...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="csolution" load="iap_flash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash_fcb" folder="boards/mimxrt685audevk/driver_examples/iap/flash_fcb" doc="readme.md">
      <description>The IAP flash project is a simple demonstration program of the SDK IAP driver. It provides some flash operations with ROM API, such as using a complete FlexSPI NOR Configuration Block to configure the flash, erasing...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash_fcb.uvprojx"/>
        <environment name="csolution" load="iap_flash_fcb.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_otp" folder="boards/mimxrt685audevk/driver_examples/iap/iap_otp" doc="readme.md">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It reads fuse word with input index. A message a printed on the UART terminal as fuse read iap operations are performed.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_otp.uvprojx"/>
        <environment name="csolution" load="iap_otp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_dma" folder="boards/mimxrt685audevk/driver_examples/lpadc/dma" doc="readme.md">
      <description>The lpdc_dma example shows how to use ADC to trigger a DMA transfer. In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC's sample input. When running...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_dma.uvprojx"/>
        <environment name="csolution" load="lpadc_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt" folder="boards/mimxrt685audevk/driver_examples/lpadc/interrupt" doc="readme.md">
      <description>The lpdc_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When running...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt.uvprojx"/>
        <environment name="csolution" load="lpadc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling" folder="boards/mimxrt685audevk/driver_examples/lpadc/polling" doc="readme.md">
      <description>The lpadc_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When running...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling.uvprojx"/>
        <environment name="csolution" load="lpadc_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_single_interrupt" folder="boards/mimxrt685audevk/driver_examples/lpadc/single_interrupt" doc="readme.md">
      <description>The lpdc_single_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_single_interrupt.uvprojx"/>
        <environment name="csolution" load="lpadc_single_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_single_polling" folder="boards/mimxrt685audevk/driver_examples/lpadc/single_polling" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_single_polling.uvprojx"/>
        <environment name="csolution" load="lpadc_single_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_temperature_measurement" folder="boards/mimxrt685audevk/driver_examples/lpadc/temperature_measurement" doc="readme.md">
      <description>The lpadc_temperature_measurement example shows how to measure the temperature within the internal sensor. In this example, the ADC input channel is mapped to an internal temperature sensor. When running the project,...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_temperature_measurement.uvprojx"/>
        <environment name="csolution" load="lpadc_temperature_measurement.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpi_loader_dsp_hello_world" folder="boards/mimxrt685audevk/component_examples/mpi_loader/dsp_hello_world" doc="readme.md">
      <description>The mpi_loader_dsp_hello_world demo application demonstrates multicore packed image loader to load DSP image to execution place and kick off it. The multicore packed image is created by elftosb tool.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mpi_loader_dsp_hello_world.uvprojx"/>
        <environment name="csolution" load="mpi_loader_dsp_hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpi_loader_extram_hello_world" folder="boards/mimxrt685audevk/component_examples/mpi_loader/extram_hello_world" doc="readme.md">
      <description>The mpi_loader_extram_hello_world example demonstrates an application running in external RAM, and being loaded by mpi_loader_extram_loader. The application binary need to be built together with the mpi_loader_extram_loader binary with elftosb tool.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mpi_loader_extram_hello_world.uvprojx"/>
        <environment name="iar" load="iar/mpi_loader_extram_hello_world.ewp"/>
        <environment name="csolution" load="mpi_loader_extram_hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mpi_loader_extram_loader" folder="boards/mimxrt685audevk/component_examples/mpi_loader/extram_loader" doc="readme.md">
      <description>The mpi_loader_extram_loader demo application demonstrates multicore packed image loader to initialize external RAM first and then load the external RAM image to execute. The multicore packed image is created by elftosb tool.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mpi_loader_extram_loader.uvprojx"/>
        <environment name="iar" load="iar/mpi_loader_extram_loader.ewp"/>
        <environment name="csolution" load="mpi_loader_extram_loader.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/mimxrt685audevk/driver_examples/mrt" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example" folder="boards/mimxrt685audevk/driver_examples/ostimer" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example.uvprojx"/>
        <environment name="csolution" load="ostimer_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="otfad_decrypt" folder="boards/mimxrt685audevk/driver_examples/otfad/decrypt" doc="readme.md">
      <description>The OTFAD project is a simple demonstration program of the SDK OTFAD driver. It support for on the fly AES decryption for the data stored in external memories in an encrypted form.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/otfad_decrypt.uvprojx"/>
        <environment name="csolution" load="otfad_decrypt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pca9420" folder="boards/mimxrt685audevk/driver_examples/pca9420" doc="readme.md">
      <description>The pca9420 driver example demonstrates the usage of pca9420 SDK component driver.The example shows the usage of PCA9420 API to dump mode group settings; Or switch mode group; Or dump PCA9420 register content;</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pca9420.uvprojx"/>
        <environment name="csolution" load="pca9420.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/mimxrt685audevk/driver_examples/pint/pattern_match" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/mimxrt685audevk/driver_examples/pint/pin_interrupt" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="boards/mimxrt685audevk/demo_apps/power_manager" doc="readme.md">
      <description>The power_manager application shows the usage of normal power mode control APIs for entering the four kinds of low power mode: Sleep mode, Deep Sleep mode, Deep Power Down mode and Full Deep Power Down mode. When the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="csolution" load="power_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="puf" folder="boards/mimxrt685audevk/driver_examples/puf" doc="readme.md">
      <description>This driver example project demonstrates how to use driver for the PUF controller which provides a secure key storage.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/puf.uvprojx"/>
        <environment name="csolution" load="puf.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_example" folder="boards/mimxrt685audevk/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_example.uvprojx"/>
        <environment name="csolution" load="rtc_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="boards/mimxrt685audevk/driver_examples/sctimer/16bit_counter" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="csolution" load="sctimer_16bit_counter.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="boards/mimxrt685audevk/driver_examples/sctimer/multi_state_pwm" doc="readme.md">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="csolution" load="sctimer_multi_state_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="boards/mimxrt685audevk/driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.md">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="csolution" load="sctimer_pwm_with_dutycyle_change.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="boards/mimxrt685audevk/driver_examples/sctimer/simple_pwm" doc="readme.md">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="csolution" load="sctimer_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_faults_ns" folder="boards/mimxrt685audevk/trustzone_examples/secure_faults/secure_faults_ns" doc="readme.md">
      <description>The Secure Faults demo application demonstrates handling of different secure faults. This application is based on application Hello World. In addition, user can invoke different secure faults by setting...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_faults_ns.uvprojx"/>
        <environment name="csolution" load="../secure_faults.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_faults_s" folder="boards/mimxrt685audevk/trustzone_examples/secure_faults/secure_faults_s" doc="readme.md">
      <description>The Secure Faults demo application demonstrates handling of different secure faults. This application is based on application Hello World. In addition, user can invoke different secure faults by setting...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_faults_s.uvprojx"/>
        <environment name="csolution" load="../secure_faults.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_gpio_ns" folder="boards/mimxrt685audevk/trustzone_examples/secure_gpio/secure_gpio_ns" doc="readme.md">
      <description>The Secure GPIO demo application demonstrates using of secure GPIO peripheral and GPIO mask feature in AHB secure controller. This is non-secure part of the application.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_gpio_ns.uvprojx"/>
        <environment name="csolution" load="../secure_gpio.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="secure_gpio_s" folder="boards/mimxrt685audevk/trustzone_examples/secure_gpio/secure_gpio_s" doc="readme.md">
      <description>The Secure GPIO demo application demonstrates using of secure GPIO peripheral and GPIO mask feature in AHB secure controller. This is secure part of the application.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/secure_gpio_s.uvprojx"/>
        <environment name="csolution" load="../secure_gpio.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/mimxrt685audevk/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_b2b_transfer_master example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_b2b_transfer_slave example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/mimxrt685audevk/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/mimxrt685audevk/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/mimxrt685audevk/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/mimxrt685audevk/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sx1502_led_control" folder="boards/mimxrt685audevk/component_examples/sx1502_led_control" doc="readme.md">
      <description>The sx1502_led_control demo is used to demonstrate how to use sx1502_led_control component. The main function of the demo is to control the LED of external DMIC board on or off.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sx1502_led_control.uvprojx"/>
        <environment name="csolution" load="sx1502_led_control.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="clockout" folder="boards/mimxrt685audevk/driver_examples/clockout" doc="readme.md">
      <description>The syscon_clockout driver example shows how to output the internal clock signal. In this driver example, users can choose the clock signal to be outputted, and the divider of the output clock signal. By probe the...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/clockout.uvprojx"/>
        <environment name="csolution" load="clockout.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="trng_random" folder="boards/mimxrt685audevk/driver_examples/trng/random" doc="readme.md">
      <description>The True Random Number Generator (TRNG) is a hardware accelerator module that generates a 512-bitentropy as needed by an entropy consuming module or by other post processing functions. The TRNGExample project is a...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/trng_random.uvprojx"/>
        <environment name="csolution" load="trng_random.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_9bit_interrupt_transfer" folder="boards/mimxrt685audevk/driver_examples/usart/9bit_interrupt_transfer" doc="readme.md">
      <description>The usart_9bit_interrupt_transfer example shows how to use usart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it is...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="usart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_double_buffer_transfer" folder="boards/mimxrt685audevk/driver_examples/usart/dma_double_buffer_transfer" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USARTThe example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_double_buffer_transfer.uvprojx"/>
        <environment name="csolution" load="usart_dma_double_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_low_power" folder="boards/mimxrt685audevk/driver_examples/usart/dma_low_power" doc="readme.md">
      <description>The usart_dma_low_power example shows how to use usart to wake up the system in low power modes, and how to wake up for DMA only. In this example, one usart instance connects to PC through usart, the board will start...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_low_power.uvprojx"/>
        <environment name="csolution" load="usart_dma_low_power.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_transfer" folder="boards/mimxrt685audevk/driver_examples/usart/dma_transfer" doc="readme.md">
      <description>The usart_dma example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board willsend back all characters that PC send to the board.Note: The example echo...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt" folder="boards/mimxrt685audevk/driver_examples/usart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt.uvprojx"/>
        <environment name="csolution" load="usart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_rb_transfer" folder="boards/mimxrt685audevk/driver_examples/usart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_rb_transfer.uvprojx"/>
        <environment name="csolution" load="usart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_transfer" folder="boards/mimxrt685audevk/driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling" folder="boards/mimxrt685audevk/driver_examples/usart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling.uvprojx"/>
        <environment name="csolution" load="usart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick" folder="boards/mimxrt685audevk/driver_examples/utick" doc="readme.md">
      <description>The utick project is a simple demonstration program of the SDK utick driver. It sets up the utick hardware block to trigger a periodic interrupt after every 1 second. When the utick interrupt is triggered a message is printed on the UART terminal.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick.uvprojx"/>
        <environment name="csolution" load="utick.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/mimxrt685audevk/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="MIMXRT685-AUD-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="mimxrt685audevk" Cversion="1.0.0" condition="BOARD_Project_Template.mimxrt685audevk.condition_id">
      <description>Board_project_template mimxrt685audevk</description>
      <RTE_Components_h>
#ifndef FSL_SDK_DRIVER_QUICK_ACCESS_ENABLE
#define FSL_SDK_DRIVER_QUICK_ACCESS_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" attr="config" name="boards/mimxrt685audevk/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/mimxrt685audevk/project_template/"/>
      </files>
    </component>
  </components>
</package>
