<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso55S06_AZURE_RTOS_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware azure_rtos Examples Pack for LPCXpresso55S06</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPCXpresso55S06_BSP" vendor="NXP" version="19.0.0"/>
      <package name="AZURE_RTOS" vendor="NXP" version="2.0.0"/>
      <package name="LPC55S06_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="filex_levelx_spiflash" folder="boards/lpcxpresso55s06/azure_rtos_examples/filex_levelx_spiflash" doc="readme.md">
      <description>The filex_levelx_spiflash example.</description>
      <board name="LPCXpresso55S06" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/filex_levelx_spiflash.uvprojx"/>
        <environment name="csolution" load="filex_levelx_spiflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="filex_ram_disk" folder="boards/lpcxpresso55s06/azure_rtos_examples/filex_ram_disk" doc="readme.md">
      <description>The filex_ram_disk example.</description>
      <board name="LPCXpresso55S06" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/filex_ram_disk.uvprojx"/>
        <environment name="csolution" load="filex_ram_disk.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_example" folder="boards/lpcxpresso55s06/azure_rtos_examples/i2c_example" doc="readme.md">
      <description>The example shows I2C communication between two I2C ports.</description>
      <board name="LPCXpresso55S06" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_example.uvprojx"/>
        <environment name="csolution" load="i2c_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_example" folder="boards/lpcxpresso55s06/azure_rtos_examples/spi_example" doc="readme.md">
      <description>The example shows SPI communication between two SPI ports.</description>
      <board name="LPCXpresso55S06" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_example.uvprojx"/>
        <environment name="csolution" load="spi_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="threadx_demo" folder="boards/lpcxpresso55s06/azure_rtos_examples/threadx_demo" doc="readme.md">
      <description>The ThreadX example.</description>
      <board name="LPCXpresso55S06" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/threadx_demo.uvprojx"/>
        <environment name="csolution" load="threadx_demo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_example" folder="boards/lpcxpresso55s06/azure_rtos_examples/uart_example" doc="readme.md">
      <description>The example shows how to use the uart driver in Azure RTOS.</description>
      <board name="LPCXpresso55S06" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_example.uvprojx"/>
        <environment name="csolution" load="uart_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
