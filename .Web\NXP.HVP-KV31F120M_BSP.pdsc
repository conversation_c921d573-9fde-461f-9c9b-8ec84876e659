<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>HVP-KV31F120M_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for HVPKV31F120M</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKV31F51212_DFP" vendor="NXP" version="16.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.8.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="HVP-KV31F120M">
      <description>Kinetis KV31 MCU high-voltage development platform</description>
      <image small="hvpkv31f120m.png"/>
      <mountedDevice Dname="MKV31F512VLL12" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKV31F512VLH12" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKV31F51212">
      <accept Dname="MKV31F512xxx12" Dvariant="MKV31F512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MKV31F512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MKV31F512xxx12" Dvariant="MKV31F512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MKV31F512VLH12" Dvendor="NXP:11"/>
    </condition>
    <condition id="component.serial_manager_AND_utility.assert_AND_utility.debug_console">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="utility.assert_lite_AND_utility.debug_console_lite">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="_component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite_">
      <accept condition="component.serial_manager_AND_utility.assert_AND_utility.debug_console"/>
      <accept condition="utility.assert_lite_AND_utility.debug_console_lite"/>
    </condition>
    <condition id="device.MKV31F51212_AND___component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite___AND_component.uart_adapter_AND_device.MKV31F51212_startup_AND_driver.common_AND_driver.ftm_AND_driver.gpio_AND_driver.i2c_AND_driver.port_AND_driver.smc_AND_driver.uart">
      <require condition="device.MKV31F51212"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require Cclass="Device" Cgroup="Startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ftm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require condition="_component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite_"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc16_interrupt" folder="driver_examples/adc16/interrupt" doc="readme.txt">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value. Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the ADC16 interrupt configuration is set when configuring the ADC16's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing aconversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="driver_examples/adc16/polling" doc="readme.txt">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.Note, the default setting of initialization for the ADC converter is just an available configuration. User can changethe configuration structure's setting in application to fit the special requirement.The auto-calibration is not essential but strongly recommended. It can help to adjust the converter itself and improvethe ADC16's performance.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc16_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_edma_transfer" folder="cmsis_driver_examples/uart/edma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_uart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="cmsis_driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_adc" folder="demo_apps/dac_adc" doc="readme.txt">
      <description>The DAC / ADC demo application demonstrates the use of the DAC and ADC peripherals. This application demonstrates how toconfigure the DAC and set the output on the DAC. This demo also demonstrates how to configure the ADC in 'Blocking Mode'and how to read ADC values.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_adc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dac_adc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_channel_link" folder="driver_examples/edma/channel_link" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_channel_link.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_channel_link.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_interleave_transfer" folder="driver_examples/edma/interleave_transfer" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_interleave_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_interleave_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="driver_examples/edma/memory_to_memory" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_ping_pong_transfer" folder="driver_examples/edma/ping_pong_transfer" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_ping_pong_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_ping_pong_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="driver_examples/edma/scatter_gather" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_scatter_gather.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_wrap_transfer" folder="driver_examples/edma/wrap_transfer" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_wrap_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_wrap_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="driver_examples/ftm/timer" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="driver_examples/lptmr" doc="readme.txt">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lptmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fei_blpi" folder="driver_examples/mcg/fei_blpi" doc="readme.txt">
      <description>The fei_bpli example shows how to use MCG driver to change from FEI mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work flow    Boot to FEI mode from default reset mode    Change mode FEI -&gt; FBI -&gt; BLPI    Change back BLPE -&gt; FBI -&gt; FEI    Get System clock in FEI mode to blink LEDIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fei_blpi.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/mcg_fei_blpi.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_adc16_trigger" folder="driver_examples/pdb/adc16_trigger" doc="readme.txt">
      <description>The pdb_adc16_trigger example shows how to use the PDB to generate a ADC trigger.Based on the basic counter, to use the ADC trigger, just to enable the ADC trigger's "milestone" and set the user-defined value for it.After the PDB counter is triggered to start, when the counter pass the "milestone", the ADC's Pre-Trigger would begenerated and sent to the ADC module.In this example, the ADC16 is configured with hardware trigger and conversion complete interrupt enabled.Once it gets the trigger from the PDB, the conversion goes, then the ISR would be executed.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_adc16_trigger.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pdb_adc16_trigger.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_delay_interrupt" folder="driver_examples/pdb/delay_interrupt" doc="readme.txt">
      <description>The pdb_delay_interrupt example show how to use the PDB as a general programmable interrupt timer.The PDB is triggered by software, and other external triggers are generated from PDB in this project,so that user can see just a general counter is working with interrupt.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_delay_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pdb_delay_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="driver_examples/pit" doc="readme.txt">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pit.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rnga_random" folder="driver_examples/rnga/random" doc="readme.txt">
      <description>The RNGA is a digital integrated circuit capable of generating the 32-bit random numbers. The RNGAExample project is a demonstration program that uses the KSDK software to generate random numbersand prints them to the terminal.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rnga_random.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rnga_random.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_9bit_interrupt_transfer" folder="driver_examples/uart/9bit_interrupt_transfer" doc="readme.txt">
      <description>The uart_9bit_interrupt_transfer example shows how to use uart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it is addressed. In this example, one uart instance is used with address configured. Its TX and RX pins are connected together. First it sends a piece of data out, then addresses itself, after that sends the other piece of data. Only data sent after the address can be received by itself.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_9bit_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_edma_transfer" folder="driver_examples/uart/edma_transfer" doc="readme.txt">
      <description>The uart_edma example shows how to use uart driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="driver_examples/uart/interrupt" doc="readme.txt">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="driver_examples/uart/interrupt_rb_transfer" doc="readme.txt">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="driver_examples/uart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/uart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog" folder="driver_examples/wdog" doc="readme.txt">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 10 times of refreshing the watchdog in None-window mode, a timeout reset is generated.We also try to refresh out of window to trigger reset after 10 times of refreshing in Window mode.</description>
      <board name="HVP-KV31F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wdog.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="hvpkv31f120m" Cversion="1.0.0" condition="device.MKV31F51212_AND___component.serial_manager_AND_utility.assert_AND_utility.debug_console__OR__utility.assert_lite_AND_utility.debug_console_lite___AND_component.uart_adapter_AND_device.MKV31F51212_startup_AND_driver.common_AND_driver.ftm_AND_driver.gpio_AND_driver.i2c_AND_driver.port_AND_driver.smc_AND_driver.uart">
      <description>Board_project_template hvpkv31f120m; {for-development:SDK-Manifest-ID: project_template.hvpkv31f120m.MKV31F51212}</description>
      <files>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
