<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: Overview</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('index.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Overview </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="mainpage"></a> <b>CMSIS-Compiler</b> provides software components that simplify retargeting of standard C run-time library functions.</p>
<p>Application code frequently uses standard C library functions, such as <code>fopen</code>, <code>fwrite</code>, <code>printf()</code>, <code>scanf()</code> and others to perform input/output operations. These functions may as well be used in a multithreaded environment.</p>
<p>The structure of these functions in the standard C run-time library together with the retarget interfaces is:</p>
<div class="image">
<img src="overview.png" alt=""/>
<div class="caption">
Software Structure Overview</div></div>
    <p>Standard C library functions are platform independent and can be easily ported, while the low-level interfaces needs to be tailored to the chosen platform. Generally low-level I/O functions serve to interact with the file system and a serial interface, i.e. terminal. Multithreading support is available for applications that run on top of an RTOS kernel.</p>
<p>CMSIS-Compiler enables platform specific retargeting by providing software components that break down into the following interfaces:</p>
<ul>
<li>File interface that enables reading and writing files</li>
<li>STDIN interface that enables standard input stream redirection</li>
<li>STDOUT interface that enables standard output stream redirection</li>
<li>STDERR interface that enables standard error stream redirection</li>
<li>OS interface that enables multithread safety using an arbitrary RTOS</li>
</ul>
<p>The pages <a class="el" href="rt_io.html">Low-Level I/O Retarget</a> and <a class="el" href="rt_os.html">Multithreading Support Retarget</a> explain the details about how retargeting is done using the provided components.</p>
<p>Using CMSIS-Compiler developers can retarget standard I/O streams and file operations to specific platform and enable thread safe operations using RTOS interface.</p>
<blockquote class="doxtable">
<p>&zwj;<b>Note</b></p><ul>
<li>CMSIS-Compiler replaces and extends retargeting functionality previously provided as part of <em>Keil::ARM_Compiler</em> pack.</li>
<li>See <a href="https://learn.arm.com/learning-paths/microcontrollers/project-migration-cmsis-v6">Migrating projects from CMSIS v5 to CMSIS v6</a> for a guidance on updating existing projects to CMSIS-Compiler. </li>
</ul>
</blockquote>
<h1><a class="anchor" id="toolchains"></a>
Supported Toolchains</h1>
<p>The CMSIS-Compiler component is available for the following toolchains:</p>
<ul>
<li><a href="https://developer.arm.com/Tools%20and%20Software/Arm%20Compiler%20for%20Embedded">Arm Compiler for Embedded</a></li>
<li><a href="https://developer.arm.com/Tools%20and%20Software/GNU%20Toolchain">Arm GNU Toolchain (GCC)</a></li>
</ul>
<p>Support for IAR Compiler support will be added in a future revision of the CMSIS-Compiler.</p>
<h1><a class="anchor" id="compiler_access"></a>
Access to CMSIS-Compiler</h1>
<p>CMSIS-View is actively maintained in <a href="https://github.com/ARM-software/CMSIS-Compiler"><b>CMSIS-Compiler GitHub repository</b></a> and is released as a standalone <a href="https://www.keil.arm.com/packs/cmsis-compiler-arm/versions/"><b>CMSIS-Compiler pack</b></a> in the <a href="https://open-cmsis-pack.github.io/Open-CMSIS-Pack-Spec/main/html/index.html">CMSIS-Pack format</a>.</p>
<p>The table below explains the content of <b>ARM::CMSIS-Compiler</b> pack.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadLeft">Directory   </th><th class="markdownTableHeadLeft">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft">📂 documentation   </td><td class="markdownTableBodyLeft">Folder with this CMSIS-Compiler documenation    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft">📂 example   </td><td class="markdownTableBodyLeft"><a class="el" href="md__home_runner_work_CMSIS_Compiler_CMSIS_Compiler_example_README.html">I/O Retarget example project</a>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft">📂 include   </td><td class="markdownTableBodyLeft">Header files with <a href="modules.html"><b>Retargeting functions</b></a>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft">📂 source   </td><td class="markdownTableBodyLeft">Compiler-specific implementations of regargeting functions    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft">📂 template   </td><td class="markdownTableBodyLeft"><a class="el" href="rt_templates.html">User Template files</a>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft">📄 ARM.CMSIS-Compiler.pdsc   </td><td class="markdownTableBodyLeft">CMSIS-Pack description file    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft">📄 LICENSE   </td><td class="markdownTableBodyLeft">License Agreement (Apache 2.0)   </td></tr>
</table>
<p>See <a href="https://arm-software.github.io/CMSIS_6/">CMSIS Documentation</a> for an overview of CMSIS software components, tools and specifications.</p>
<h1><a class="anchor" id="doc_content"></a>
Documentation Structure</h1>
<p>This documentation contains the following sections:</p>
<ul>
<li><a class="el" href="rev_hist.html">Revision History</a> : lists CMSIS-Compiler releases.</li>
<li><a class="el" href="rt_io.html">Low-Level I/O Retarget</a> : explains low-level I/O retargeting, list available components and describes available user code templates.</li>
<li><a class="el" href="rt_os.html">Multithreading Support Retarget</a> : explains multithreading support retargeting, list available components and describes available user code templates.</li>
<li>The <a class="el" href="md__home_runner_work_CMSIS_Compiler_CMSIS_Compiler_example_README.html">I/O Retarget example project</a> shows how to retarget the output to a UART on an Arm Virtual Hardware model.</li>
<li><a class="el" href="rt_templates.html">Templates</a> : contains the user code template files for the different use cases.</li>
<li><a href="modules.html"><b>API Reference</b></a> describes the API and the functions of the CMSIS-Compiler components in details.</li>
</ul>
<h1><a class="anchor" id="doc_license"></a>
License</h1>
<p>CMSIS-Compiler is provided free of charge by Arm under the <a href="https://raw.githubusercontent.com/ARM-software/CMSIS-Compiler/main/LICENSE">Apache 2.0 License</a> </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
