<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXC041_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-MCXC041</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCXC041_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-MCXC041">
      <description>FRDM-MCXC041</description>
      <image small="boards/frdmmcxc041/frdmmcxc041.png"/>
      <mountedDevice Dname="MCXC041VFK" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXC041VFG" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MCXC041.internal_condition">
      <accept Dname="MCXC041VFG" Dvendor="NXP:11"/>
      <accept Dname="MCXC041VFK" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MCXC041.internal_condition">
      <accept Dname="MCXC041VFG" Dvendor="NXP:11"/>
      <accept Dname="MCXC041VFK" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmmcxc041.condition_id">
      <require condition="allOf.board=frdmmcxc041, component.lpuart_adapter, device_id=MCXC041, device.MCXC041_startup, driver.common, driver.gpio, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmmcxc041, component.lpuart_adapter, device_id=MCXC041, device.MCXC041_startup, driver.common, driver.gpio, driver.lpuart, driver.port, driver.smc, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.frdmmcxc041.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="device_id.MCXC041.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmmcxc041.internal_condition">
      <accept condition="device.MCXC041.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc16_interrupt" folder="boards/frdmmcxc041/driver_examples/adc16/interrupt" doc="readme.md">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc16_interrupt.ewp"/>
        <environment name="csolution" load="adc16_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power" folder="boards/frdmmcxc041/demo_apps/adc16_low_power" doc="readme.md">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power.ewp"/>
        <environment name="csolution" load="adc16_low_power.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="boards/frdmmcxc041/driver_examples/adc16/polling" doc="readme.md">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" load="iar/adc16_polling.ewp"/>
        <environment name="csolution" load="adc16_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble" folder="boards/frdmmcxc041/demo_apps/bubble" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble.uvprojx"/>
        <environment name="iar" load="iar/bubble.ewp"/>
        <environment name="csolution" load="bubble.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="boards/frdmmcxc041/driver_examples/cmp/interrupt" doc="readme.md">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input....See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
        <environment name="csolution" load="cmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="boards/frdmmcxc041/driver_examples/cmp/polling" doc="readme.md">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
        <environment name="csolution" load="cmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="boards/frdmmcxc041/cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_master" folder="boards/frdmmcxc041/cmsis_driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use spi CMSIS driver in interrupt way:In this example , we need two boards, one board used as spi master and another board used as spi slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_slave" folder="boards/frdmmcxc041/cmsis_driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cop" folder="boards/frdmmcxc041/driver_examples/cop" doc="readme.md">
      <description>The COP Example project is to demonstrate usage of the KSDK cop driver.In this example, after 10 times of refreshing, a timeout reset is generated.Please notice that because COP control registers are write-once only,...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cop.uvprojx"/>
        <environment name="iar" load="iar/cop.ewp"/>
        <environment name="csolution" load="cop.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/frdmmcxc041/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/frdmmcxc041/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmmcxc041/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/frdmmcxc041/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/frdmmcxc041/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="boards/frdmmcxc041/driver_examples/i2c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="boards/frdmmcxc041/driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="boards/frdmmcxc041/driver_examples/lptmr" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
        <environment name="csolution" load="lptmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/frdmmcxc041/driver_examples/lpuart/interrupt" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/frdmmcxc041/driver_examples/lpuart/interrupt_rb_transfer" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/frdmmcxc041/driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/frdmmcxc041/driver_examples/lpuart/polling" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcglite" folder="boards/frdmmcxc041/driver_examples/mcglite" doc="readme.md">
      <description>The MCG_Lite example shows how to use MCG_Lite driver: 1. How to use the mode functions for MCG_Lite mode switch. 2. How to use the frequency functions to get current MCG_Lite frequency. 3. Work flow Reset mode --&gt;...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcglite.uvprojx"/>
        <environment name="iar" load="iar/mcglite.ewp"/>
        <environment name="csolution" load="mcglite.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="boards/frdmmcxc041/driver_examples/flash/pflash" doc="readme.md">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" load="iar/pflash.ewp"/>
        <environment name="csolution" load="pflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="boards/frdmmcxc041/demo_apps/power_manager" doc="readme.md">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" load="iar/power_manager.ewp"/>
        <environment name="csolution" load="power_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="boards/frdmmcxc041/demo_apps/power_mode_switch" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="csolution" load="power_mode_switch.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="boards/frdmmcxc041/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" load="iar/rtc.ewp"/>
        <environment name="csolution" load="rtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="boards/frdmmcxc041/demo_apps/rtc_func" doc="readme.md">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar + Get the current date time with Year, Month,...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="csolution" load="rtc_func.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/frdmmcxc041/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/frdmmcxc041/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/frdmmcxc041/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/frdmmcxc041/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/frdmmcxc041/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/frdmmcxc041/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/frdmmcxc041/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_input_capture" folder="boards/frdmmcxc041/driver_examples/tpm/input_capture" doc="readme.md">
      <description>The TPM project is a demonstration program of the SDK TPM driver's input capture feature.The example sets up a TPM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/tpm_input_capture.ewp"/>
        <environment name="csolution" load="tpm_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_output_compare" folder="boards/frdmmcxc041/driver_examples/tpm/output_compare" doc="readme.md">
      <description>The TPM project is a demonstration program of the SDK TPM driver's output compare feature.It sets up one TPM channel to toggle the output when a match occurs with the channel value. The usershould probe the TPM...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/tpm_output_compare.ewp"/>
        <environment name="csolution" load="tpm_output_compare.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_pwm_twochannel" folder="boards/frdmmcxc041/driver_examples/tpm/pwm_twochannel" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The PWM dutycycle on both channelsis manually updated. On boards...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/tpm_pwm_twochannel.ewp"/>
        <environment name="csolution" load="tpm_pwm_twochannel.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_simple_pwm" folder="boards/frdmmcxc041/driver_examples/tpm/simple_pwm" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output a center-aligned PWM signal. The PWM dutycycle is manually updated.On boards that have an LED...See more details in readme document.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/tpm_simple_pwm.ewp"/>
        <environment name="csolution" load="tpm_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer" folder="boards/frdmmcxc041/driver_examples/tpm/timer" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="FRDM-MCXC041" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer.uvprojx"/>
        <environment name="iar" load="iar/tpm_timer.ewp"/>
        <environment name="csolution" load="tpm_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmmcxc041" Cversion="1.0.0" condition="BOARD_Project_Template.frdmmcxc041.condition_id">
      <description>Board_project_template frdmmcxc041</description>
      <files>
        <file category="header" attr="config" name="boards/frdmmcxc041/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc041/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxc041/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc041/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxc041/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc041/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxc041/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxc041/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmmcxc041/project_template/"/>
      </files>
    </component>
  </components>
</package>
