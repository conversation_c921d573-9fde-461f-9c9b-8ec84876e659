<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_MGM24_DFP</name>
  <description>Silicon Labs MGM24 Mighty Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>MGM24</keyword>
    <keyword>MGM24</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="MGM24 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency.
      </description>

      <subFamily DsubFamily="MGM240L">
        <book         name="Documents/mgm240p-datasheet.pdf"      title="MGM240L Data Sheet"/>
        <book         name="Documents/mgm240p-errata.pdf"         title="MGM240L Errata"/>
        <!-- *************************  Device 'MGM240L022RNF'  ***************************** -->
        <device Dname="MGM240L022RNF">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240L022RNF"/>
          <debug      svd="SVD/MGM24/MGM240L022RNF.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00028000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240L022VIF'  ***************************** -->
        <device Dname="MGM240L022VIF">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240L022VIF"/>
          <debug      svd="SVD/MGM24/MGM240L022VIF.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240L022VNF'  ***************************** -->
        <device Dname="MGM240L022VNF">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240L022VNF"/>
          <debug      svd="SVD/MGM24/MGM240L022VNF.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240LA22UIF'  ***************************** -->
        <device Dname="MGM240LA22UIF">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240LA22UIF"/>
          <debug      svd="SVD/MGM24/MGM240LA22UIF.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00030000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240LA22VIF'  ***************************** -->
        <device Dname="MGM240LA22VIF">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240LA22VIF"/>
          <debug      svd="SVD/MGM24/MGM240LA22VIF.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240LD22VIF'  ***************************** -->
        <device Dname="MGM240LD22VIF">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240LD22VIF"/>
          <debug      svd="SVD/MGM24/MGM240LD22VIF.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="MGM240P">
        <book         name="Documents/mgm240p-datasheet.pdf"      title="MGM240P Data Sheet"/>
        <book         name="Documents/mgm240p-errata.pdf"         title="MGM240P Errata"/>
        <!-- *************************  Device 'MGM240PA22VNA'  ***************************** -->
        <device Dname="MGM240PA22VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240PA22VNA"/>
          <debug      svd="SVD/MGM24/MGM240PA22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240PA32VNA'  ***************************** -->
        <device Dname="MGM240PA32VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240PA32VNA"/>
          <debug      svd="SVD/MGM24/MGM240PA32VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240PA32VNN'  ***************************** -->
        <device Dname="MGM240PA32VNN">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240PA32VNN"/>
          <debug      svd="SVD/MGM24/MGM240PA32VNN.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240PB22VNA'  ***************************** -->
        <device Dname="MGM240PB22VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240PB22VNA"/>
          <debug      svd="SVD/MGM24/MGM240PB22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240PB32VNA'  ***************************** -->
        <device Dname="MGM240PB32VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240PB32VNA"/>
          <debug      svd="SVD/MGM24/MGM240PB32VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240PB32VNN'  ***************************** -->
        <device Dname="MGM240PB32VNN">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240PB32VNN"/>
          <debug      svd="SVD/MGM24/MGM240PB32VNN.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="MGM240S">
        <book         name="Documents/mgm240p-datasheet.pdf"      title="MGM240S Data Sheet"/>
        <book         name="Documents/mgm240p-errata.pdf"         title="MGM240S Errata"/>
        <!-- *************************  Device 'MGM240SA22VNA'  ***************************** -->
        <device Dname="MGM240SA22VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240SA22VNA"/>
          <debug      svd="SVD/MGM24/MGM240SA22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240SB22VNA'  ***************************** -->
        <device Dname="MGM240SB22VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240SB22VNA"/>
          <debug      svd="SVD/MGM24/MGM240SB22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM240SD22VNA'  ***************************** -->
        <device Dname="MGM240SD22VNA">
          <compile header="Device/SiliconLabs/MGM24/Include/em_device.h"  define="MGM240SD22VNA"/>
          <debug      svd="SVD/MGM24/MGM240SD22VNA.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00180000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00180000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00180000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="MGM24">
      <description>Silicon Labs MGM24 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="MGM24*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="MGM24">
      <description>System Startup for Silicon Labs MGM24 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/MGM24/Include/"/>
        <file category="header"  name="Device/SiliconLabs/MGM24/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/MGM24/Source/system_mgm24.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
