<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EFR32BG21_DFP</name>
  <description>Silicon Labs EFR32BG21 Blue Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EFR32BG21_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EFR32BG21_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32BG21</keyword>
    <keyword>EFR32</keyword>
    <keyword>Blue Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32BG21 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p3" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="38400000"/>
      <book name="Documents/efr32xg21-rm.pdf"  title="EFR32BG21 Reference Manual"/>
      <description>
32-bit ARM Cortex-M33 core with 80 MHz maximum operating frequency.
      </description>

      <subFamily DsubFamily="EFR32BG21A010">
        <book         name="Documents/efr32bg21-datasheet.pdf"      title="EFR32BG21A010 Data Sheet"/>
        <book         name="Documents/efr32bg21-errata.pdf"         title="EFR32BG21A010 Errata"/>
        <!-- *************************  Device 'EFR32BG21A010F1024IM32'  ***************************** -->
        <device Dname="EFR32BG21A010F1024IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21A010F1024IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21A010F1024IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21A010F512IM32'  ***************************** -->
        <device Dname="EFR32BG21A010F512IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21A010F512IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21A010F512IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21A010F768IM32'  ***************************** -->
        <device Dname="EFR32BG21A010F768IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21A010F768IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21A010F768IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32BG21A020">
        <book         name="Documents/efr32bg21-datasheet.pdf"      title="EFR32BG21A020 Data Sheet"/>
        <book         name="Documents/efr32bg21-errata.pdf"         title="EFR32BG21A020 Errata"/>
        <!-- *************************  Device 'EFR32BG21A020F1024IM32'  ***************************** -->
        <device Dname="EFR32BG21A020F1024IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21A020F1024IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21A020F1024IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21A020F512IM32'  ***************************** -->
        <device Dname="EFR32BG21A020F512IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21A020F512IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21A020F512IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21A020F768IM32'  ***************************** -->
        <device Dname="EFR32BG21A020F768IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21A020F768IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21A020F768IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32BG21B010">
        <book         name="Documents/efr32bg21-datasheet.pdf"      title="EFR32BG21B010 Data Sheet"/>
        <book         name="Documents/efr32bg21-errata.pdf"         title="EFR32BG21B010 Errata"/>
        <!-- *************************  Device 'EFR32BG21B010F1024IM32'  ***************************** -->
        <device Dname="EFR32BG21B010F1024IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21B010F1024IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21B010F1024IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21B010F512IM32'  ***************************** -->
        <device Dname="EFR32BG21B010F512IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21B010F512IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21B010F512IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21B010F768IM32'  ***************************** -->
        <device Dname="EFR32BG21B010F768IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21B010F768IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21B010F768IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32BG21B020">
        <book         name="Documents/efr32bg21-datasheet.pdf"      title="EFR32BG21B020 Data Sheet"/>
        <book         name="Documents/efr32bg21-errata.pdf"         title="EFR32BG21B020 Errata"/>
        <!-- *************************  Device 'EFR32BG21B020F1024IM32'  ***************************** -->
        <device Dname="EFR32BG21B020F1024IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21B020F1024IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21B020F1024IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21B020F512IM32'  ***************************** -->
        <device Dname="EFR32BG21B020F512IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21B020F512IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21B020F512IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32BG21B020F768IM32'  ***************************** -->
        <device Dname="EFR32BG21B020F768IM32">
          <compile header="Device/SiliconLabs/EFR32BG21/Include/em_device.h"  define="EFR32BG21B020F768IM32"/>
          <debug      svd="SVD/EFR32BG21/EFR32BG21B020F768IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x000C0000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x000C0000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x000C0000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32BG21">
      <description>Silicon Labs EFR32BG21 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32BG21*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EFR32BG21">
      <description>System Startup for Silicon Labs EFR32BG21 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32BG21/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32BG21/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFR32BG21/Source/GCC/startup_efr32bg21.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFR32BG21/Source/GCC/efr32bg21.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32BG21/Source/system_efr32bg21.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
