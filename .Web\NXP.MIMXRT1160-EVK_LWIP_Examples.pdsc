<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1160-EVK_LWIP_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware lwip Examples Pack for MIMXRT1160-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1160-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MIMXRT1166_DFP" vendor="NXP" version="19.0.0"/>
      <package name="LWIP" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="COREHTTP" vendor="NXP" version="2.0.0"/>
      <package name="LLHTTP" vendor="NXP" version="2.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="lwip_dhcp_bm_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_dhcp/bm/cm4" doc="readme.md">
      <description>The lwip_dhcp demo application demonstrates a DHCP demo on the lwIP TCP/IP stack.The application acts as a DHCP client and prints the status as it is progressing.Once the interface is being bound to an IP address...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_dhcp_bm_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_dhcp_bm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_dhcp_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_dhcp/freertos/cm4" doc="readme.md">
      <description>The lwip_dhcp demo application demonstrates a DHCP demo on the lwIP TCP/IP stack.The application acts as a DHCP client and prints the status as it is progressing.Once the interface is being bound to an IP address...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_dhcp_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_dhcp_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_https_client_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_https_client/freertos/cm4" doc="readme.md">
      <description>The lwip_https_client_freertos demo application demonstrates downloading of a web page over SSL connection using HTTP GET method.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_https_client_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_https_client_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpsrv_bm_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpsrv/bm/cm4" doc="readme.md">
      <description>The lwip_httpsrv demo application demonstrates an HTTPServer on the lwIP TCP/IP stack with bare metal SDK or FreeRTOS.The user uses an Internet browser to send a request for connection. The board acts as an HTTP server and sends a Webpage back to the PC.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpsrv_bm_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_httpsrv_bm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpsrv_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpsrv/freertos/cm4" doc="readme.md">
      <description>The lwip_httpsrv demo application demonstrates an HTTPServer on the lwIP TCP/IP stack with bare metal SDK or FreeRTOS.The user uses an Internet browser to send a request for connection. The board acts as an HTTP server and sends a Webpage back to the PC.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpsrv_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_httpsrv_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpssrv_mbedTLS_bm_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpssrv_mbedTLS/bm/cm4" doc="readme.md">
      <description>The lwip_httpsrv_mbedTSL_bm demo application demonstrates an HTTPS Serverset up on lwIP TCP/IP and the mbedTLS stack with bare metal. The useruses an Internet browser to send an https request for connection.The board...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpssrv_mbedTLS_bm_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_httpssrv_mbedTLS_bm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpssrv_mbedTLS_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpssrv_mbedTLS/freertos/cm4" doc="readme.md">
      <description>The lwip_httpsrv_mbedTLS demo application demonstrates an HTTPServer set up on lwIP TCP/IP and the MbedTLS stack withFreeRTOS. The user uses an Internet browser to send an https request for connection. The board acts...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpssrv_mbedTLS_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_httpssrv_mbedTLS_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_iperf_bm_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_iperf/bm/cm4" doc="readme.md">
      <description>This is the IPerf server example to check your bandwidth using the network performance measurement IPerf application on a PC as a client.It is currently a minimal implementation providing an IPv4 TCP server...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_iperf_bm_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_iperf_bm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_ipv4_ipv6_echo_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_ipv4_ipv6_echo/freertos/cm4" doc="readme.md">
      <description>The lwip_ipv4_ipv6_echo demo application demonstrates a TCP or UDP echo demo on the lwIP TCP/IP stack with FreeRTOS. The demo uses the TCP or UDP protocol over both IPv4 and IPv6 and acts as an echo server. The...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_ipv4_ipv6_echo_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_ipv4_ipv6_echo_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_mqtt_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_mqtt/freertos/cm4" doc="readme.md">
      <description>The lwip_mqtt demo application demonstrates MQTT client connecting to MQTT broker via unsecured socket. The application obtains IP and DNS addresses from DHCP and resolves broker's hostname if needed. Then it...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_mqtt_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_mqtt_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_ping_bm_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_ping/bm/cm4" doc="readme.md">
      <description>The lwip_ping demo application demonstrates a Ping Demo on the lwIP TCP/IP stack, which uses the ICMP protocol. Theapplication periodically sends the ICMP echo request to a PC, and processes the PC reply. Type "ping...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_ping_bm_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_ping_bm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_ping_freertos_cm4" folder="boards/evkmimxrt1160/lwip_examples/lwip_ping/freertos/cm4" doc="readme.md">
      <description>The lwip_ping demo application demonstrates a Ping Demo on the lwIP TCP/IP stack which using the ICMP protocol. Theapplication periodically sends the ICMP echo request to a PC and processes the PC reply. Type the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_ping_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="lwip_ping_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_dhcp_bm_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_dhcp/bm/cm7" doc="readme.md">
      <description>The lwip_dhcp demo application demonstrates a DHCP demo on the lwIP TCP/IP stack.The application acts as a DHCP client and prints the status as it is progressing.Once the interface is being bound to an IP address...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_dhcp_bm_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_dhcp_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_dhcp_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_dhcp/freertos/cm7" doc="readme.md">
      <description>The lwip_dhcp demo application demonstrates a DHCP demo on the lwIP TCP/IP stack.The application acts as a DHCP client and prints the status as it is progressing.Once the interface is being bound to an IP address...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_dhcp_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_dhcp_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_dhcp_usb_bm" folder="boards/evkmimxrt1160/lwip_examples/lwip_dhcp_usb/bm/cm7" doc="readme.md">
      <description>The lwip_dhcp_usb demo application demonstrates a DHCP demo on the lwIP TCP/IP stack.The application acts as a DHCP client and prints the status as it is progressing.Once the interface is being bound to an IP address...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_dhcp_usb_bm.uvprojx"/>
        <environment name="csolution" load="lwip_dhcp_usb_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_dhcp_usb_freertos" folder="boards/evkmimxrt1160/lwip_examples/lwip_dhcp_usb/freertos/cm7" doc="readme.md">
      <description>The lwip_dhcp demo application demonstrates a DHCP demo on the lwIP TCP/IP stack.The application acts as a DHCP client and prints the status as it is progressing.Once the interface is being bound to an IP address...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_dhcp_usb_freertos.uvprojx"/>
        <environment name="csolution" load="lwip_dhcp_usb_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_https_client_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_https_client/freertos/cm7" doc="readme.md">
      <description>The lwip_https_client_freertos demo application demonstrates downloading of a web page over SSL connection using HTTP GET method.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_https_client_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_https_client_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpsrv_bm_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpsrv/bm/cm7" doc="readme.md">
      <description>The lwip_httpsrv demo application demonstrates an HTTPServer on the lwIP TCP/IP stack with bare metal SDK or FreeRTOS.The user uses an Internet browser to send a request for connection. The board acts as an HTTP server and sends a Webpage back to the PC.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpsrv_bm_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_httpsrv_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpsrv_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpsrv/freertos/cm7" doc="readme.md">
      <description>The lwip_httpsrv demo application demonstrates an HTTPServer on the lwIP TCP/IP stack with bare metal SDK or FreeRTOS.The user uses an Internet browser to send a request for connection. The board acts as an HTTP server and sends a Webpage back to the PC.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpsrv_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_httpsrv_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpssrv_mbedTLS_bm_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpssrv_mbedTLS/bm/cm7" doc="readme.md">
      <description>The lwip_httpsrv_mbedTSL_bm demo application demonstrates an HTTPS Serverset up on lwIP TCP/IP and the mbedTLS stack with bare metal. The useruses an Internet browser to send an https request for connection.The board...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpssrv_mbedTLS_bm_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_httpssrv_mbedTLS_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_httpssrv_mbedTLS_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_httpssrv_mbedTLS/freertos/cm7" doc="readme.md">
      <description>The lwip_httpsrv_mbedTLS demo application demonstrates an HTTPServer set up on lwIP TCP/IP and the MbedTLS stack withFreeRTOS. The user uses an Internet browser to send an https request for connection. The board acts...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_httpssrv_mbedTLS_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_httpssrv_mbedTLS_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_iperf_bm_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_iperf/bm/cm7" doc="readme.md">
      <description>This is the IPerf server example to check your bandwidth using the network performance measurement IPerf application on a PC as a client.It is currently a minimal implementation providing an IPv4 TCP server...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_iperf_bm_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_iperf_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_ipv4_ipv6_echo_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_ipv4_ipv6_echo/freertos/cm7" doc="readme.md">
      <description>The lwip_ipv4_ipv6_echo demo application demonstrates a TCP or UDP echo demo on the lwIP TCP/IP stack with FreeRTOS. The demo uses the TCP or UDP protocol over both IPv4 and IPv6 and acts as an echo server. The...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_ipv4_ipv6_echo_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_ipv4_ipv6_echo_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_mqtt_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_mqtt/freertos/cm7" doc="readme.md">
      <description>The lwip_mqtt demo application demonstrates MQTT client connecting to MQTT broker via unsecured socket. The application obtains IP and DNS addresses from DHCP and resolves broker's hostname if needed. Then it...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_mqtt_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_mqtt_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_ping_bm_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_ping/bm/cm7" doc="readme.md">
      <description>The lwip_ping demo application demonstrates a Ping Demo on the lwIP TCP/IP stack, which uses the ICMP protocol. Theapplication periodically sends the ICMP echo request to a PC, and processes the PC reply. Type "ping...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_ping_bm_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_ping_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lwip_ping_freertos_cm7" folder="boards/evkmimxrt1160/lwip_examples/lwip_ping/freertos/cm7" doc="readme.md">
      <description>The lwip_ping demo application demonstrates a Ping Demo on the lwIP TCP/IP stack which using the ICMP protocol. Theapplication periodically sends the ICMP echo request to a PC and processes the PC reply. Type the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lwip_ping_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="lwip_ping_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
