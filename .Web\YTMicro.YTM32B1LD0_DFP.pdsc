<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.7.26" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="https://raw.githubusercontent.com/Open-CMSIS-Pack/Open-CMSIS-Pack-Spec/v1.7.26/schema/PACK.xsd">
  <vendor>YTMicro</vendor>
  <name>YTM32B1LD0_DFP</name>
  <description>Device Family Pack for YTM32B1LD0</description>
  <url>https://doc.ytmicro.com/keil/</url>

  <releases>
    <release version="1.1.1" date="2023-09-21">
      Updated devices.
    </release>
    <release version="1.1.0" date="2023-06-25">
      Changed company short form and Dvendor ID to 180.
    </release>
    <release version="1.0.4" date="2022-12-20">
      Only include device info and flash download algorithm.
    </release>
  </releases>

  <keywords>
    <keyword>YTMicro</keyword>
    <keyword>YTM32B1LD0</keyword>
    <keyword>Device Family Pack</keyword>
  </keywords>

  <devices>
    <family Dfamily="YTM32Bx" Dvendor="YTMicro:180">

      <!-- ******************************  Subfamily 'YTM32B1LD'  ****************************** -->
      <subFamily DsubFamily="YTM32B1LD0">
        <processor Dcore="Cortex-M0+" DcoreVersion="r0p1" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="48000000" /> 
        <compile header="Device/Include/YTM32B1LD0.h"/>
        <debug svd="SVD/YTM32B1LD0.svd"/>
        <algorithm name="Flash/YTM32B1LD0_Main.FLM" start="0x00000000" size="0x00010000" default="1"/>
        <algorithm name="Flash/YTM32B1LD0_Dflash.FLM" start="0x00400200" size="0x00000800" default="0"/>
        <memory name="IRAM1" access="rwx" start="0x20000000" size="0x2000" default="1"/>
        <memory name="IROM1" access="rx" start="0x00000000" size="0x10000" default="1"/>
        <memory name="IROM2" access="rx" start="0x00400200" size="0x800" default="1"/>
        <description>YTM32B1LD0 48MHz MCU based on ARM Cortex-M0+ Core </description>

        <device Dname="YTM32B1LD04">
          <description>Includes LQFP64/48/32 packages. 64KB PFlash, 2KB DFlash, 8KB SRAM</description>
        </device>
      </subFamily>
    
    </family>
  </devices>

  <taxonomy>
    <description Cclass="Device" Cgroup="Startup">YTMicro SDK Startup</description>
    <description Cclass="Device" Cgroup="SDK Drivers">YTMicro SDK Peripheral Drivers</description>
  </taxonomy>

  <conditions>
    <condition id="YTM32B1LD0">
      <description>YTMicro YTM32B1LD0x device</description>
      <require Dvendor="YTMicro:180" Dname="YTM32B1LD0*"/>
    </condition>

    <!-- Device + Compiler Conditions (GCC) -->
    <condition id="YTM32B1L_GCC">
      <description>YTMicro device and GCC compiler</description>
      <require Tcompiler="GCC"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="1.1.1" condition="YTM32B1LD0">
      <description>Startup for MCU</description>
      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/startup.c" attr="config" version="1.1.1"/>
        <file category="source"  name="Device/Source/system_YTM32B1LD0.c" attr="config" version="1.1.1"/>
        <file category="source"  condition="YTM32B1L_GCC" name="Device/Source/YTM32B1LD0_startup_gcc.S" attr="config" version="1.1.1"/>
      </files>
    </component>
  </components>
</package>
