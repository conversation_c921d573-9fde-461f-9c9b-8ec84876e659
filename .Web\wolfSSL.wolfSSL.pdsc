<?xml version="1.0" encoding="utf-8" standalone="no"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.4" xs:noNamespaceSchemaLocation="PACK.xsd">
  <name>wolfSSL</name>                                                     <!-- name of package -->
  <description>Light weight SSL/TLS and Crypt Library for Embedded Systems</description>  <!-- brief description of PACK -->
  <vendor>wolfSSL</vendor>                                                 <!-- unique vendor/distributor of PACK -->
  <url>http://www.wolfSSL.com/files/ide/</url>                              <!-- Required but NOT USED AT THE MOMENT -->
  <license>wolfssl/IDE/MDK5-ARM/Docs/wolfSSL-License.txt</license>
    <taxonomy>
    <description Cclass="wolfSSL">wolfSSL: SSL/TLS and Crypt Library</description>
    <description Cclass="wolfSSL" Cgroup="wolfSSL">wolfSSL: SSL/TLS Library</description>
    <description Cclass="wolfSSL" Cgroup="wolfCrypt">wolfCrypt: Crypt Library</description>
  </taxonomy>

  <releases>                                                                <!-- contains complete release history of available releases (put most recent first) -->
    <release date="2025-07-17" version="5.8.2">
      July/17/2025,  wolfSSL pack for wolfSSL 5.8.2
    </release>
    <release date="2025-05-05" version="5.8.0">
      May/05/2025,  wolfSSL pack for wolfSSL 5.8.0
    </release>
    <release date="2024-12-31" version="5.7.6">
      December/31/2024,  wolfSSL pack for wolfSSL 5.7.6
    </release>
    <release date="2024-10-24" version="5.7.4">
      October/24/2024,  wolfSSL pack for wolfSSL 5.7.4
    </release>
    <release date="2024-07-08" version="5.7.2">
      July/08/2024,  wolfSSL pack for wolfSSL 5.7.2
    </release>
    <release date="2024-03-20" version="5.7.0">
      March/20/2024,  wolfSSL pack for wolfSSL 5.7.0
    </release>
    <release date="2023-12-19" version="5.6.6">
      December/19/2023,  wolfSSL pack for wolfSSL 5.6.6
    </release>
    <release date="2023-10-30" version="5.6.4">
      October/30/2023,  wolfSSL pack for wolfSSL 5.6.4
    </release>
    <release date="2023-06-16" version="5.6.3">
      June/16/2023,  wolfSSL pack for wolfSSL 5.6.3
    </release>
    <release date="2023-03-24" version="5.6.0">
      March/24/2023,  wolfSSL pack for wolfSSL 5.6.0
    </release>
    <release date="2022-12-21" version="5.5.4">
      December/21/2022,  wolfSSL pack for wolfSSL 5.5.4
    </release>
    <release date="2022-11-02" version="5.5.3">
      November/03/2022,  wolfSSL pack for wolfSSL 5.5.3
    </release>
    <release date="2022-09-28" version="5.5.1">
      September/28/2022,  wolfSSL pack for wolfSSL 5.5.1
    </release>
    <release date="2022-08-30" version="5.5.0">
      August/30/2022,  wolfSSL pack for wolfSSL 5.5.0
    </release>
    <release date="2022-07-11" version="5.4.0">
      July/11/2022,  wolfSSL pack for wolfSSL 5.4.0
    </release>
    <release date="2022-05-03" version="5.3.0">
      May/3/2022,  wolfSSL pack for wolfSSL 5.3.0
    </release>
    <release date="2022-02-21" version="5.2.0">
      February/21/2022,  wolfSSL pack for wolfSSL 5.2.0
    </release>
    <release date="2022-01-03" version="5.1.1">
      January/03/2021,  wolfSSL pack for wolfSSL 5.1.1
    </release>
    <release date="2021-11-01" version="5.0.0">
      November/01/2021,  wolfSSL pack for wolfSSL 5.0.0
    </release>
    <release date="2021-07-16" version="4.8.1">
      July/16/2021,  wolfSSL pack for wolfSSL 4.8.1
    </release>
    <release date="2021-07-12" version="4.8.0">
      July/12/2021,  wolfSSL pack for wolfSSL 4.8.0
    </release>
    <release date="2021-02-16" version="4.7.0">
      February/16/2021,  wolfSSL pack for wolfSSL 4.7.0
    </release>
    <release date="2020-12-15" version="4.6.0">
      December/15/2020,  wolfSSL pack for wolfSSL 4.6.0
    </release>
    <release date="2020-08-19" version="4.5.0">
      August/19/2020,  wolfSSL pack for wolfSSL 4.5.0
    </release>
    <release date="2020-04-24" version="4.4.0">
      April/24/2020,  wolfSSL pack for wolfSSL 4.4.0
    </release>
    <release date="2018-03-13" version="3.14.0">
      March/13/2018,  wolfSSL pack for wolfSSL 3.14.0
    </release>
    <release date="2018-02-09" version="3.13.1">
      February/09/2018,  wolfSSL pack for wolfSSL 3.13.0
      Update for being listed as generic Pack
    </release>
    <release date="2018-01-21" version="3.13.0">
      January/21/2018,  wolfSSL pack for wolfSSL 3.13.0
    </release>
    <release date="2016-03-18" version="3.9.0">
      March/18/2016,  wolfSSL pack for wolfSSL 3.9.0
    </release>
    <release date="2015-12-30" version="3.8.0">
      December/30/2015,  wolfSSL pack for wolfSSL 3.8.0
    </release>
  </releases>

  <keywords>
    <!-- PACK related keywords for search indexing (automatic are vendor and pack name) -->
    <keyword>CyaSSL</keyword>
    <keyword>wolfSSL</keyword>
    <keyword>Security</keyword>
    <keyword>Crypt</keyword>
    <keyword>Cipher</keyword>
    <keyword>SSL</keyword>
    <keyword>TLS</keyword>
  </keywords>

  <conditions>
    <!-- Conditions to express dependencies on devices, processors, software components, toolchains -->
    <condition id="wolfCrypt-Core">
      <require Cclass="wolfSSL" Cgroup="wolfCrypt" Csub="CORE"/>
    </condition>
    <condition id="wolfSSL-Core">
      <require Cclass="wolfSSL" Cgroup="wolfSSL" Csub="Core"/>
      <require condition="wolfCrypt-Core"/>
    </condition>
  </conditions>
  <components>
    <bundle Cbundle="wolfSSL" Cclass="wolfSSL" Cversion="5.8.2">
      <description>wolfSSL: SSL/TLS and Crypt Library</description>
      <doc>wolfssl/IDE/MDK5-ARM/Docs/wolfSSLManual-TableofContents.htm</doc>
      <component Cgroup="wolfSSL" Csub="Core" condition="wolfCrypt-Core">
        <description>wolfSSL, Light weight SSL/TLS library</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="doc" name="wolfssl/IDE/MDK5-ARM/Docs/wolfSSLManual-TableofContents.htm"/>

          <file attr="config" category="header" name="wolfssl/IDE/MDK5-ARM/Conf/user_settings.h" version="5.8.2"/>

          <file category="include" name="wolfssl/IDE/MDK5-ARM/Inc/"/>
          <file category="include" name="wolfssl/"/>

          <file category="source" name="wolfssl/src/bio.c"/>
          <file category="source" name="wolfssl/src/conf.c"/>
          <file category="source" name="wolfssl/src/crl.c"/>
          <file category="source" name="wolfssl/src/dtls.c"/>
          <file category="source" name="wolfssl/src/dtls13.c"/>
          <file category="source" name="wolfssl/src/internal.c"/>
          <file category="source" name="wolfssl/src/keys.c"/>
          <file category="source" name="wolfssl/src/ocsp.c"/>
          <file category="source" name="wolfssl/src/pk.c"/>
          <file category="source" name="wolfssl/src/quic.c"/>
          <file category="source" name="wolfssl/src/sniffer.c"/>
          <file category="source" name="wolfssl/src/ssl.c"/>
          <file category="source" name="wolfssl/src/ssl_asn1.c"/>
          <file category="source" name="wolfssl/src/ssl_bn.c"/>
          <file category="source" name="wolfssl/src/ssl_certman.c"/>
          <file category="source" name="wolfssl/src/ssl_crypto.c"/>
          <file category="source" name="wolfssl/src/ssl_load.c"/>
          <file category="source" name="wolfssl/src/ssl_misc.c"/>
          <file category="source" name="wolfssl/src/ssl_p7p12.c"/>
          <file category="source" name="wolfssl/src/ssl_sess.c"/>
          <file category="source" name="wolfssl/src/tls.c"/>
          <file category="source" name="wolfssl/src/tls13.c"/>
          <file category="source" name="wolfssl/src/wolfio.c"/>
          <file category="source" name="wolfssl/src/x509.c"/>
          <file category="source" name="wolfssl/src/x509_str.c"/>
        </files>
      </component>
      <component Cgroup="wolfCrypt" Csub="CORE" condition="wolfCrypt-Core">
        <description>wolfCrypt Core, Light weight Crypt/Cipher Library</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="doc" name="wolfssl/IDE/MDK5-ARM/Docs/wolfSSLManual-wolfCryptUsageReference.htm"/>

          <file attr="config" category="header" name="wolfssl/IDE/MDK5-ARM/Conf/user_settings.h" version="5.8.2"/>

          <file category="include" name="wolfssl/IDE/MDK5-ARM/Inc/"/>
          <file category="include" name="wolfssl/"/>
          <file category="source" name="wolfssl/wolfcrypt/src/aes.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/arc4.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/asm.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/asn.c"/>
          <!-- <file category="source" name="wolfssl/wolfcrypt/src/async.c" /> -->
          <file category="source" name="wolfssl/wolfcrypt/src/blake2b.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/blake2s.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/camellia.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/chacha.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/chacha20_poly1305.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/cmac.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/coding.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/compress.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/cpuid.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/curve448.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/curve25519.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/des3.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/dh.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/dilithium.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/dsa.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ecc_fp.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ecc.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/eccsi.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ed448.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ed25519.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/error.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/evp.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ext_kyber.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/fe_448.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/fe_low_mem.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/fe_operations.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ge_448.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ge_low_mem.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ge_operations.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/hash.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/hmac.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/hpke.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/integer.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/kdf.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/logging.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/md2.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/md4.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/md5.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/memory.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/misc.c" />
          <file category="source" name="wolfssl/wolfcrypt/src/pkcs7.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/pkcs12.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/poly1305.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/pwdbased.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/random.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/ripemd.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/rsa.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sakke.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sha.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sha256.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sha3.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sha512.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/signature.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_arm32.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_arm64.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_armthumb.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_c32.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_c64.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_cortexm.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_dsp32.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_int.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/sp_x86_64.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/srp.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/tfm.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/wc_dsp.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/wc_encrypt.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/wc_pkcs11.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/wc_port.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/wolfevent.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/wolfmath.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-32-curve25519_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-32-sha512-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-aes.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-chacha.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-curve25519_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-poly1305.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-sha256.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/armv8-sha512.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-aes-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-chacha-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-chacha.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-curve25519_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-kyber-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-poly1305-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-poly1305.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-sha256-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-sha3-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/arm/thumb2-sha512-asm_c.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/atmel/atmel.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/caam_aes.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/caam_driver.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/caam_error.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/caam_integrity.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/caam_qnx.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/caam_sha.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/wolfcaam_cmac.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/wolfcaam_ecdsa.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/wolfcaam_init.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/caam/wolfcaam_qnx.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/nxp/ksdk_port.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/pic32/pic32mz-crypt.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/st/stm32.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/st/stsafe.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/ti/ti-aes.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/ti/ti-ccm.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/ti/ti-des3.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/ti/ti-hash.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/xilinx/xil-aesgcm.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/xilinx/xil-sha3.c"/>
          <file category="source" name="wolfssl/wolfcrypt/src/port/nrf51.c"/>
        </files>
      </component>
      <component Cgroup="wolfCrypt" Csub="Dummy" condition="wolfCrypt-Core">
        <description>Dummy file for Crypt alone use</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/IDE/MDK5-ARM/Src/ssl-dummy.c"/>
        </files>
      </component>
      <component Cgroup="wolfCrypt" Csub="Test" condition="wolfCrypt-Core">
        <description>wolfCrypt Test suite</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/wolfcrypt/test/test.c"/>
        </files>
      </component>
      <component Cgroup="wolfCrypt" Csub="Benchmark" condition="wolfCrypt-Core">
        <description>wolfCrypt Benchmark Suite</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/wolfcrypt/benchmark/benchmark.c"/>
        </files>
      </component>
      <component Cgroup="wolfSSL" Csub="echoClient" condition="wolfSSL-Core">
        <description>TLS echo Client</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/examples/echoclient/echoclient.c"/>
        </files>
      </component>
      <component Cgroup="wolfSSL" Csub="echoServer" condition="wolfSSL-Core">
        <description>TLS echo Server</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/examples/echoserver/echoserver.c"/>
        </files>
      </component>
      <component Cgroup="wolfSSL" Csub="SimpleClient" condition="wolfSSL-Core">
        <description>simple TLS Client</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/examples/client/client.c"/>
        </files>
      </component>
      <component Cgroup="wolfSSL" Csub="SimpleServer" condition="wolfSSL-Core">
        <description>simple TLS Server</description>
        <RTE_Components_h>
        </RTE_Components_h>
        <files>
          <file category="source" name="wolfssl/examples/server/server.c"/>
        </files>
      </component>
    </bundle>
  </components>

  <examples>
    <example doc="Abstract.txt" folder="wolfssl/IDE/MDK5-ARM/Projects/CryptTest" name="wolfSSL 2: wolfCrypt test">
      <description>wolfCrypt Simple Test Suite</description>
      <board name="uVision Simulator" vendor="Keil"/>
      <project>
        <environment load="CryptTest.uvprojx" name="uv"/>
      </project>
      <attributes>
        <component Cclass="wolfSSL" Cgroup="wolfCrypt" Csub="Test"/>
        <component Cclass="wolfSSL" Cgroup="wolfCrypt" Csub="Dummy"/>
        <category>Middleware</category>
        <category>Security</category>
        <keyword>Crypt</keyword>
        <keyword>Cipher</keyword>
      </attributes>
    </example>
    <example doc="Abstract.txt" folder="wolfssl/IDE/MDK5-ARM/Projects/CryptBenchmark" name="wolfSSL 3: wolfCrypt Benchmark">
      <description>wolfCrypt Benchmark Suite</description>
      <board name="uVision Simulator" vendor="Keil"/>
      <project>
        <environment load="CryptBenchmark.uvprojx" name="uv"/>
      </project>
      <attributes>
        <component Cclass="wolfSSL" Cgroup="wolfCrypt" Csub="Benchmark"/>
        <component Cclass="wolfSSL" Cgroup="wolfCrypt" Csub="Dummy"/>
        <category>Middleware</category>
        <category>Security</category>
        <keyword>Crypt</keyword>
        <keyword>Cipher</keyword>
      </attributes>
    </example>
    <example doc="Abstract.txt" folder="wolfssl/IDE/MDK5-ARM/Projects/SimpleClient" name="wolfSSL 4: Simple Client">
      <description>wolfSSL Simple Client Example</description>
      <board name="uVision Simulator" vendor="Keil"/>
      <project>
        <environment load="SimpleClient.uvprojx" name="uv"/>
      </project>
      <attributes>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="Core"/>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="SimpleClient"/>
        <category>Middleware</category>
        <category>Security</category>
        <keyword>Crypt</keyword>
        <keyword>Cipher</keyword>
      </attributes>
    </example>
    <example doc="Abstract.txt" folder="wolfssl/IDE/MDK5-ARM/Projects/SimpleServer" name="wolfSSL 5: Simple Server">
      <description>wolfSSL Simple Server Example</description>
      <board name="uVision Simulator" vendor="Keil"/>
      <project>
        <environment load="SimpleServer.uvprojx" name="uv"/>
      </project>
      <attributes>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="Core"/>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="SimpleServer"/>
        <category>Middleware</category>
        <category>Security</category>
        <keyword>Crypt</keyword>
        <keyword>Cipher</keyword>
      </attributes>
    </example>
    <example doc="Abstract.txt" folder="wolfssl/IDE/MDK5-ARM/Projects/EchoClient" name="wolfSSL 6: Echo Client">
      <description>wolfSSL Echo Client Example</description>
      <board name="uVision Simulator" vendor="Keil"/>
      <project>
        <environment load="EchoClient.uvprojx" name="uv"/>
      </project>
      <attributes>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="Core"/>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="EchoClient"/>
        <category>Middleware</category>
        <category>Security</category>
        <keyword>Crypt</keyword>
        <keyword>Cipher</keyword>
      </attributes>
    </example>
    <example doc="Abstract.txt" folder="wolfssl/IDE/MDK5-ARM/Projects/EchoServer" name="wolfSSL 7: Echo Server">
      <description>wolfSSL Echo Server Example</description>
      <board name="uVision Simulator" vendor="Keil"/>
      <project>
        <environment load="EchoServer.uvprojx" name="uv"/>
      </project>
      <attributes>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="Core"/>
        <component Cclass="wolfSSL" Cgroup="wolfSSL" Csub="EchoServer"/>
        <category>Middleware</category>
        <category>Security</category>
        <keyword>Crypt</keyword>
        <keyword>Cipher</keyword>
      </attributes>
    </example>
</examples>
</package>
