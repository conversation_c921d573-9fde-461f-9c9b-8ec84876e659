<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: Flash</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('driver_Flash.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Flash </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="driver_Flash_devices"></a>
Driver Implementations</h1>
<p >The <a class="el" href="index.html#driver_pack_content">Pack Content</a> contains implementations of <a href="https://arm-software.github.io/CMSIS_6/latest/Driver/group__flash__interface__gr.html" target="_blank"><b>CMSIS-Flash drivers</b></a> for the following devices:</p>
<table class="cmtable" summary="Flash Drivers">
<tr>
<th>Driver </th><th>Description  <a class="anchor" id="driver_AM29x800BB"></a> </th></tr>
<tr>
<td><b>AM29x800BB</b>  </td><td>Flash interface for Parallel NOR Flash <b>AM29x800BB</b>. This product is discontinued.  <a class="anchor" id="driver_AT45DB641E"></a> </td></tr>
<tr>
<td><b>AT45DB641E</b>  </td><td>Flash interface for SPI Serial DataFlash <a href="https://www.renesas.com/br/en/document/dst/at45db641e-datasheet" target="_blank"><b>AT45DB641E</b></a>.   <a class="anchor" id="driver_AT45DB642D"></a> </td></tr>
<tr>
<td><b>AT45DB642D</b>  </td><td>Flash interface for SPI Serial DataFlash <b>AT45DB642D</b>.   <a class="anchor" id="driver_M29EW28F128"></a> </td></tr>
<tr>
<td><b>M29EW28F128</b>  </td><td>Flash interface for Parallel NOR Flash <b>M29EW28F128</b>.   <a class="anchor" id="driver_M29W640FB"></a> </td></tr>
<tr>
<td><b>M29W640FB</b>  </td><td>Flash interface for Parallel NOR Flash <b>M29W640FB</b>.   <a class="anchor" id="driver_N25Q032A"></a> </td></tr>
<tr>
<td><b>N25Q032A</b>  </td><td>Flash interface for Serial NOR Flash <b>N25Q032A</b>.   <a class="anchor" id="driver_S29GL064Nx2"></a> </td></tr>
<tr>
<td><b>S29GL064Nx2</b>  </td><td>Flash interface for Parallel NOR Flash <a href="http://www.cypress.com/documentation/datasheets/s29gl064n-s29gl032n-64-mbit-32-mbit-3-v-page-mode-mirrorbit-flash" target="_blank"><b>S29GL064N</b></a>.  </td></tr>
</table>
<h1><a class="anchor" id="driver_Flash_multiple"></a>
Multiple Driver Instances</h1>
<p >CMSIS-Driver API supports multiple driver instances. The Flash drivers are implemented within a single C module and several driver instances of the same type can be used in a project as follows: </p><ul>
<li>
Add the first driver instance to the project. In IDEs with CMSIS-pack management support this can be done from the Run-Time Environment (RTE). </li>
<li>
Create a copy of the driver's .c file with a different file name and add it to the project. This will be the second driver instance. For example, copy <code>AT45DB641E.c</code> file as <code>AT45DB641E_2.c</code>. </li>
<li>
Copy the driver's .h file to the project or add the driver's folder to the compiler include search path. </li>
<li>
Specify the driver parameters for the second instance based on the hardware design. For example, in <code>AT45DB641E_2.c</code> the values for the flash driver number and SPI driver number need to be configured as shown below: <div class="fragment"><div class="line"><span class="preprocessor">#define DRIVER_FLASH_NUM        1</span></div>
<div class="line"><span class="preprocessor">#define DRIVER_SPI_NUM          1</span></div>
</div><!-- fragment -->  </li>
<li>
Now both Flash instances can be accessed from the application. For example: <div class="fragment"><div class="line"><span class="preprocessor">#include &quot;Driver_Flash.h&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Flash driver instances */</span></div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_FLASH Driver_Flash0;</div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_FLASH Driver_Flash1;</div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#define flash0  (&amp;Driver_Flash0)</span></div>
<div class="line"><span class="preprocessor">#define flash1  (&amp;Driver_Flash1)</span></div>
</div><!-- fragment -->  </li>
</ul>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
