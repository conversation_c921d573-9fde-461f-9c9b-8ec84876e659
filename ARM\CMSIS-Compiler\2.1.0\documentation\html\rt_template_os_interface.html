<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: OS Interface</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('rt_template_os_interface.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">OS Interface </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="md_src_templates_os"></a> <a class="el" href="group__os__interface__api.html">OS Interface</a> provides <b>Custom</b> component that can be used to implement its functionality using code templates below.</p>
<p>Exposed OS Interface API is toolchain specific therefore make sure to select correct code template:</p>
<ul>
<li><a class="el" href="rt_template_os_interface.html#retarget_os_c_armcc">Arm Compiler</a></li>
<li><a class="el" href="rt_template_os_interface.html#retarget_os_gcc">GCC Newlib</a></li>
</ul>
<h1><a class="anchor" id="retarget_os_c_armcc"></a>
Arm Compiler</h1>
<div class="fragment"><div class="line"><span class="comment">/*-----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Name:    retarget_os.c</span></div>
<div class="line"><span class="comment"> * Purpose: OS Interface Retarget Template</span></div>
<div class="line"><span class="comment"> * Rev.:    1.0.0</span></div>
<div class="line"><span class="comment"> *-----------------------------------------------------------------------------*/</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/*</span></div>
<div class="line"><span class="comment"> * Copyright (C) 2023 ARM Limited or its affiliates. All rights reserved.</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * SPDX-License-Identifier: Apache-2.0</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Licensed under the Apache License, Version 2.0 (the License); you may</span></div>
<div class="line"><span class="comment"> * not use this file except in compliance with the License.</span></div>
<div class="line"><span class="comment"> * You may obtain a copy of the License at</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><span class="comment"> * distributed under the License is distributed on an AS IS BASIS, WITHOUT</span></div>
<div class="line"><span class="comment"> * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><span class="comment"> * See the License for the specific language governing permissions and</span></div>
<div class="line"><span class="comment"> * limitations under the License.</span></div>
<div class="line"><span class="comment"> */</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Mutex identifier */</span></div>
<div class="line"><span class="keyword">typedef</span> <span class="keywordtype">void</span> *mutex;</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Mutex function prototypes */</span></div>
<div class="line"><span class="keywordtype">int</span>  <a class="code hl_function" href="group__retarget__os__armclib.html#gab1e6cdf92dc1e41857e683d358151891" title="Initialize mutex.">_mutex_initialize</a>(mutex *m);</div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381" title="Acquire mutex.">_mutex_acquire</a>   (mutex *m);</div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__armclib.html#ga5b9b4175427a95bd5f812a736618e63c" title="Release mutex.">_mutex_release</a>   (mutex *m);</div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__armclib.html#gadbed132cb03873020c98e2d95d68c153" title="Free mutex.">_mutex_free</a>      (mutex *m);</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Initialize mutex */</span></div>
<div class="line"><span class="keywordtype">int</span> <a class="code hl_function" href="group__retarget__os__armclib.html#gab1e6cdf92dc1e41857e683d358151891" title="Initialize mutex.">_mutex_initialize</a>(mutex *m) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">  <span class="keywordflow">return</span> 0;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Acquire mutex */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__armclib.html#gaada1d9d5188d36b35e8ea14274010381" title="Acquire mutex.">_mutex_acquire</a>(mutex *m) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Release mutex */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__armclib.html#ga5b9b4175427a95bd5f812a736618e63c" title="Release mutex.">_mutex_release</a>(mutex *m) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Free mutex */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__armclib.html#gadbed132cb03873020c98e2d95d68c153" title="Free mutex.">_mutex_free</a>(mutex *m) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
</div><!-- fragment --><h1><a class="anchor" id="retarget_os_gcc"></a>
GCC Newlib</h1>
<p><b>Code template used to retarget locking routines</b></p>
<div class="fragment"><div class="line"><span class="comment">/*-----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Name:    retarget_lock.c</span></div>
<div class="line"><span class="comment"> * Purpose: Locking Routines Retarget Template</span></div>
<div class="line"><span class="comment"> * Rev.:    1.0.0</span></div>
<div class="line"><span class="comment"> *-----------------------------------------------------------------------------*/</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/*</span></div>
<div class="line"><span class="comment"> * Copyright (C) 2023 ARM Limited or its affiliates. All rights reserved.</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * SPDX-License-Identifier: Apache-2.0</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Licensed under the Apache License, Version 2.0 (the License); you may</span></div>
<div class="line"><span class="comment"> * not use this file except in compliance with the License.</span></div>
<div class="line"><span class="comment"> * You may obtain a copy of the License at</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><span class="comment"> * distributed under the License is distributed on an AS IS BASIS, WITHOUT</span></div>
<div class="line"><span class="comment"> * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><span class="comment"> * See the License for the specific language governing permissions and</span></div>
<div class="line"><span class="comment"> * limitations under the License.</span></div>
<div class="line"><span class="comment"> */</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Lock structure definition */</span></div>
<div class="line"><span class="keyword">struct </span>__lock {</div>
<div class="line">  <span class="comment">/* ... */</span></div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Newlib mutexes */</span></div>
<div class="line"><span class="keyword">struct </span>__lock __lock___sinit_recursive_mutex  = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___sfp_recursive_mutex    = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___atexit_recursive_mutex = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___at_quick_exit_mutex    = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___malloc_recursive_mutex = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___env_recursive_mutex    = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___tz_mutex               = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___dd_hash_mutex          = {0};</div>
<div class="line"><span class="keyword">struct </span>__lock __lock___arc4random_mutex       = {0};</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Allocate lock related resources */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#gade69e629dfa01c6be1668603c0382899" title="Allocate lock related resources.">__retarget_lock_init</a> (_LOCK_T *lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Allocate recursive lock related resources */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#gaa2fc9199a1a415d4fb558b21a30f607d" title="Allocate recursive lock related resources.">__retarget_lock_init_recursive</a> (_LOCK_T *lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Free lock related resources */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#gabfff184006f7f1316140e4f022656765" title="Free lock related resources.">__retarget_lock_close</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Free recursive lock related resources */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#gaffb9501f6f4829dc0e2983e9858a1fed" title="Free recursive lock related resources.">__retarget_lock_close_recursive</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Acquire lock immediately after the lock object is available */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#ga1ab8f0b321a80a6484e40b98fb3b61e7" title="Acquire lock immediately after the lock object is available.">__retarget_lock_acquire</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Acquire recursive lock immediately after the lock object is available */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#ga8c117a73524578f221e6bbf414ccc6c9" title="Acquire recursive lock immediately after the lock object is available.">__retarget_lock_acquire_recursive</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Acquire lock if the lock object is available */</span></div>
<div class="line"><span class="keywordtype">int</span> <a class="code hl_function" href="group__retarget__os__newlib.html#gaf4754bc767666414bfaf4b401b2be4bc" title="Acquire lock if the lock object is available.">__retarget_lock_try_acquire</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Acquire recursive lock if the lock object is available */</span></div>
<div class="line"><span class="keywordtype">int</span> <a class="code hl_function" href="group__retarget__os__newlib.html#ga2f8b2d66fbcf1de096cb9655ae78cd09" title="Acquire recursive lock if the lock object is available.">__retarget_lock_try_acquire_recursive</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Relinquish the lock ownership */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#gadec4c3df8d3bb9590d575ee500cd8611" title="Relinquish the lock ownership.">__retarget_lock_release</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Relinquish the recursive lock ownership */</span></div>
<div class="line"><span class="keywordtype">void</span> <a class="code hl_function" href="group__retarget__os__newlib.html#ga8be1e6784fb33f579d4944cbbad1a65a" title="Relinquish the recursive lock ownership.">__retarget_lock_release_recursive</a> (_LOCK_T lock) {</div>
<div class="line">  <span class="comment">/* .. */</span></div>
<div class="line">}</div>
</div><!-- fragment --><p><b>Code template used to retarget system calls</b></p>
<blockquote class="doxtable">
<p>&zwj;<b>Note</b></p><ul>
<li>All system call functions are provided in this template although there may not be necessary to reimplement them all. Functions that are not necessary may be removed or kept depending on the linker settings.</li>
<li>Reimplementing functions like <code>_open</code>, <code>_close</code>,<code>_write</code>, etc. breaks compatibility with the CMSIS-Compiler:IO component. Make sure to remove them from custom implementation when application does not require to reimplement them. </li>
</ul>
</blockquote>
<div class="fragment"><div class="line"><span class="comment">/*-----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Name:    retarget_syscalls.c</span></div>
<div class="line"><span class="comment"> * Purpose: System Call Routines Retarget Template</span></div>
<div class="line"><span class="comment"> * Rev.:    1.0.0</span></div>
<div class="line"><span class="comment"> *-----------------------------------------------------------------------------*/</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/*</span></div>
<div class="line"><span class="comment"> * Copyright (C) 2023 ARM Limited or its affiliates. All rights reserved.</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * SPDX-License-Identifier: Apache-2.0</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Licensed under the Apache License, Version 2.0 (the License); you may</span></div>
<div class="line"><span class="comment"> * not use this file except in compliance with the License.</span></div>
<div class="line"><span class="comment"> * You may obtain a copy of the License at</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><span class="comment"> *</span></div>
<div class="line"><span class="comment"> * Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><span class="comment"> * distributed under the License is distributed on an AS IS BASIS, WITHOUT</span></div>
<div class="line"><span class="comment"> * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><span class="comment"> * See the License for the specific language governing permissions and</span></div>
<div class="line"><span class="comment"> * limitations under the License.</span></div>
<div class="line"><span class="comment"> */</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &lt;unistd.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;fcntl.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &lt;errno.h&gt;</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Open file */</span></div>
<div class="line"><span class="keywordtype">int</span> _open (<span class="keyword">const</span> <span class="keywordtype">char</span> *path, <span class="keywordtype">int</span> oflag, ...) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Close a file descriptor */</span></div>
<div class="line"><span class="keywordtype">int</span> _close (<span class="keywordtype">int</span> fildes) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Write on a file */</span></div>
<div class="line">ssize_t _write (<span class="keywordtype">int</span> fildes, <span class="keyword">const</span> <span class="keywordtype">void</span> *buf, <span class="keywordtype">size_t</span> nbyte) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Read from a file */</span></div>
<div class="line">ssize_t _read (<span class="keywordtype">int</span> fildes, <span class="keywordtype">void</span> *buf, <span class="keywordtype">size_t</span> nbyte) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Move the read/write file offset */</span></div>
<div class="line">off_t _lseek (<span class="keywordtype">int</span> fildes, off_t offset, <span class="keywordtype">int</span> whence) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Test for a terminal device */</span></div>
<div class="line"><span class="keywordtype">int</span> _isatty (<span class="keywordtype">int</span> fildes) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (0);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Get file status */</span></div>
<div class="line"><span class="keywordtype">int</span> _fstat (<span class="keywordtype">int</span> fildes, <span class="keyword">struct</span> stat *buf) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Link one file to another file */</span></div>
<div class="line"><span class="keywordtype">int</span> _link(<span class="keyword">const</span> <span class="keywordtype">char</span> *path1, <span class="keyword">const</span> <span class="keywordtype">char</span> *path2) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Remove a directory entry */</span></div>
<div class="line"><span class="keywordtype">int</span> _unlink (<span class="keyword">const</span> <span class="keywordtype">char</span> *path) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> (-1);</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Execute a file */</span></div>
<div class="line"><span class="keywordtype">int</span> _execve(<span class="keyword">const</span> <span class="keywordtype">char</span> *path, <span class="keywordtype">char</span> *<span class="keyword">const</span> argv[], <span class="keywordtype">char</span> *<span class="keyword">const</span> envp[]) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Create a new process */</span></div>
<div class="line">pid_t fork (<span class="keywordtype">void</span>) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Terminate a process */</span></div>
<div class="line"><span class="keywordtype">void</span> _exit (<span class="keywordtype">int</span> status) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Send a signal to a process or a group of processes */</span></div>
<div class="line"><span class="keywordtype">int</span> _kill (pid_t pid, <span class="keywordtype">int</span> sig) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Get the current process id */</span></div>
<div class="line">pid_t _getpid (<span class="keywordtype">void</span>) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> 0;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Wait for a child process to stop or terminate */</span></div>
<div class="line">pid_t _wait (<span class="keywordtype">int</span> *stat_loc) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Extend heap space by incr bytes */</span></div>
<div class="line"><span class="keywordtype">void</span> *_sbrk (ptrdiff_t incr) {</div>
<div class="line">  <span class="comment">// ...</span></div>
<div class="line">  <span class="keywordflow">return</span> -1;</div>
<div class="line">}</div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
