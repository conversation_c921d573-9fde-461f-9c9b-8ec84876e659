<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: I2C</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('driver_I2C.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">I2C </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p >The I2C MultiSlave wrapper (I2C_MultiSlave.c) resides on top of an arbitrary I2C CMSIS-Driver and exports maximum of eight I2C CMSIS-Drivers (with I2C Master functionality only). Slave functionalities are disabled and calling slave functions will return with an error. An I2C slave device connects to one of the exported driver and uses it as any other CMSIS I2C driver (in master mode only). The wrapper provides multi-thread protection.</p>
<p >Each slave can use its own bus speed configuration, but the MultiSlave wrapper will limit the bus speed to the lowest requested frequency (assuming that three slaves are present and if two slaves configure bus speed <code>ARM_I2C_BUS_SPEED_FAST</code> (400 KHz) and one slave <code>ARM_I2C_BUS_SPEED_STANDARD</code> (100 kHz), then the actual bus speed will be <code>ARM_I2C_BUS_SPEED_STANDARD</code>).</p>
<p >The wrapper is configured using the I2C_MultiSlave_Config.h file, which contains the following options:</p><ul>
<li><code>#define</code> <code>I2C_DRIVER</code> specifies the underlying I2C CMSIS-Driver, which controls the I2C peripheral and accesses the bus. The wrapper connects to that driver.</li>
<li><code>#define</code> <code>I2C_ENABLE_SLAVE_x</code> enables each connected slave on the I2C bus. This basically means that the driver control block <code>Driver_I2Cn</code> will be exported by the wrapper for each particular slave.</li>
<li><code>#define</code> <code>I2C_DRIVER_SLAVE_x</code> sets the exported control block number n, for example <code>Driver_I2Cn</code>. The user application connects to this driver.</li>
</ul>
<p ><b>Code example</b></p>
<p >This is a demo application which demonstrates the usage of the I2C MultiSlave driver wrapper. It consists of two threads that periodically access two I2C slave devices.</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &lt;string.h&gt;</span></div>
<div class="line"><span class="preprocessor">#include &quot;cmsis_os2.h&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &quot;RTE_Components.h&quot;</span></div>
<div class="line"><span class="preprocessor">#include  CMSIS_device_header</span></div>
<div class="line"> </div>
<div class="line"><span class="preprocessor">#include &quot;Driver_I2C.h&quot;</span>                 <span class="comment">// ::CMSIS Driver:I2C</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/* Thread prototypes */</span></div>
<div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> Thread_A (<span class="keywordtype">void</span> *argument);</div>
<div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> Thread_B (<span class="keywordtype">void</span> *argument);</div>
<div class="line"><span class="keyword">static</span> <span class="keywordtype">void</span> app_main (<span class="keywordtype">void</span> *argument);</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* A and B Thread IDs */</span></div>
<div class="line"><span class="keyword">static</span> osThreadId_t ThreadId_A;</div>
<div class="line"><span class="keyword">static</span> osThreadId_t ThreadId_B;</div>
<div class="line"> </div>
<div class="line"><span class="comment">/* I2C A Driver, controls Slave Device 0, uses underlying Driver_I2C1 (see I2C_MultiSlave_Config.h) */</span></div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_I2C         Driver_I2C10;</div>
<div class="line"><span class="preprocessor">#define I2C_A               (&amp;Driver_I2C10)</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/* I2C B Driver, controls Slave Device 1, uses underlying Driver_I2C1 (see I2C_MultiSlave_Config.h) */</span></div>
<div class="line"><span class="keyword">extern</span> ARM_DRIVER_I2C         Driver_I2C11;</div>
<div class="line"><span class="preprocessor">#define I2C_B               (&amp;Driver_I2C11)</span></div>
<div class="line"> </div>
<div class="line"><span class="comment">/*----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * I2C Thread A</span></div>
<div class="line"><span class="comment"> *---------------------------------------------------------------------------*/</span></div>
<div class="line">__NO_RETURN <span class="keyword">static</span> <span class="keywordtype">void</span> Thread_A (<span class="keywordtype">void</span> *argument) {</div>
<div class="line">  uint8_t addr;</div>
<div class="line">  uint8_t reg;</div>
<div class="line">  uint8_t val;</div>
<div class="line">  (void)argument;</div>
<div class="line"> </div>
<div class="line">  <span class="comment">/* Initialize and configure I2C */</span></div>
<div class="line">  I2C_A-&gt;Initialize  (NULL);</div>
<div class="line">  I2C_A-&gt;PowerControl(ARM_POWER_FULL);</div>
<div class="line">  I2C_A-&gt;Control     (ARM_I2C_BUS_SPEED, ARM_I2C_BUS_SPEED_FAST);</div>
<div class="line">  I2C_A-&gt;Control     (ARM_I2C_BUS_CLEAR, 0);</div>
<div class="line"> </div>
<div class="line">  <span class="comment">/* Periodically read device register at address 0x0F */</span></div>
<div class="line">  addr = 0x68;</div>
<div class="line">  reg  = 0x0F;</div>
<div class="line"> </div>
<div class="line">  <span class="keywordflow">while</span>(1) {</div>
<div class="line">    I2C_A-&gt;MasterTransmit(addr, &amp;reg, 1, <span class="keyword">true</span>);</div>
<div class="line">    <span class="keywordflow">while</span> (I2C_A-&gt;GetStatus().busy);</div>
<div class="line"> </div>
<div class="line">    I2C_A-&gt;MasterReceive (addr, &amp;val, 1, <span class="keyword">false</span>);</div>
<div class="line">    <span class="keywordflow">while</span> (I2C_B-&gt;GetStatus().busy);</div>
<div class="line"> </div>
<div class="line">    osDelay(10);</div>
<div class="line">  }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/*----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * I2C Thread B</span></div>
<div class="line"><span class="comment"> *---------------------------------------------------------------------------*/</span></div>
<div class="line">__NO_RETURN <span class="keyword">static</span> <span class="keywordtype">void</span> Thread_B (<span class="keywordtype">void</span> *argument) {</div>
<div class="line">  uint8_t addr;</div>
<div class="line">  uint8_t reg;</div>
<div class="line">  uint8_t val;</div>
<div class="line">  (void)argument;</div>
<div class="line"> </div>
<div class="line">  <span class="comment">/* Initialize and configure I2C */</span></div>
<div class="line">  I2C_B-&gt;Initialize  (NULL);</div>
<div class="line">  I2C_B-&gt;PowerControl(ARM_POWER_FULL);</div>
<div class="line">  I2C_B-&gt;Control     (ARM_I2C_BUS_SPEED, ARM_I2C_BUS_SPEED_STANDARD);</div>
<div class="line">  I2C_B-&gt;Control     (ARM_I2C_BUS_CLEAR, 0);</div>
<div class="line"> </div>
<div class="line">  <span class="comment">/* Periodically write device register at address 0x02 */</span></div>
<div class="line">  addr = 0x44;</div>
<div class="line">  reg  = 0x02;</div>
<div class="line">  val  = 0xA5;</div>
<div class="line"> </div>
<div class="line">  <span class="keywordflow">while</span>(1) {</div>
<div class="line">    I2C_A-&gt;MasterTransmit(addr, &amp;reg, 1, <span class="keyword">true</span>);</div>
<div class="line">    <span class="keywordflow">while</span> (I2C_A-&gt;GetStatus().busy);</div>
<div class="line"> </div>
<div class="line">    I2C_A-&gt;MasterTransmit(addr, &amp;val, 1, <span class="keyword">false</span>);</div>
<div class="line">    <span class="keywordflow">while</span> (I2C_B-&gt;GetStatus().busy);</div>
<div class="line"> </div>
<div class="line">    osDelay(10);</div>
<div class="line">  }</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="comment">/*----------------------------------------------------------------------------</span></div>
<div class="line"><span class="comment"> * Application main thread</span></div>
<div class="line"><span class="comment"> *---------------------------------------------------------------------------*/</span></div>
<div class="line">__NO_RETURN <span class="keyword">static</span> <span class="keywordtype">void</span> app_main (<span class="keywordtype">void</span> *argument) {</div>
<div class="line">  (void)argument;</div>
<div class="line"> </div>
<div class="line">  <span class="comment">/* Create SPI threads */</span></div>
<div class="line">  ThreadId_A = osThreadNew(Thread_A, NULL, NULL);</div>
<div class="line">  ThreadId_B = osThreadNew(Thread_B, NULL, NULL);</div>
<div class="line"> </div>
<div class="line">  osDelay(osWaitForever);</div>
<div class="line"> </div>
<div class="line">  <span class="keywordflow">for</span> (;;) {}</div>
<div class="line">}</div>
<div class="line"> </div>
<div class="line"><span class="keywordtype">int</span> main (<span class="keywordtype">void</span>) {</div>
<div class="line"> </div>
<div class="line">  <span class="comment">// System Initialization</span></div>
<div class="line">  SystemCoreClockUpdate();</div>
<div class="line"> </div>
<div class="line">  osKernelInitialize();                 <span class="comment">// Initialize CMSIS-RTOS</span></div>
<div class="line">  osThreadNew(app_main, NULL, NULL);    <span class="comment">// Create application main thread</span></div>
<div class="line">  osKernelStart();                      <span class="comment">// Start thread execution</span></div>
<div class="line">  <span class="keywordflow">for</span> (;;) {}</div>
<div class="line">}</div>
</div><!-- fragment --> </div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
