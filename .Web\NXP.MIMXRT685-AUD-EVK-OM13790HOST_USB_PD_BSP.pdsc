<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT685-AUD-EVK-OM13790HOST_USB_PD_BSP</name>
  <vendor>NXP</vendor>
  <description>Middleware usb_pd Board Support Pack for MIMXRT685-AUD-EVK-OM13790HOST</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.1" date="2023-10-25" deprecated="2023-10-25" replacement="NXP.MIMXRT685-AUD-EVK-OM13790HOST_USB_PD_Examples">This pack is no longer maintained. The new pack has been released with "_Examples" suffix instead of "_BSP".</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT685S_DFP" vendor="NXP" version="18.0.0"/>
      <package name="USB_PD" vendor="NXP" version="1.0.1"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="1.0.1"/>
      <package name="CMSIS" vendor="ARM" version="5.8.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="1.0.1"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT685-AUD-EVK-OM13790HOST">
      <description>i.MX RT600 Audio Evaluation Kit with USB Type-C Host Shield Board Gen 2 with Display Port Alt Mode</description>
      <image small="boards/mimxrt685audevk_om13790host/mimxrt685audevk_om13790host.png"/>
      <mountedDevice Dname="MIMXRT685S" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT633SFAWBR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT633SFFOB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT633SFVKB" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MIMXRT685S.internal_condition">
      <accept Dname="MIMXRT685S" Dvariant="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685S" Dvariant="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFFOB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685S" Dvariant="MIMXRT685SFVKB" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT685SFVKB" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.mimxrt685audevk_om13790host.condition_id">
      <require condition="allOf.component.serial_manager, component.serial_manager_uart, component.usart_adapter, device_id=MIMXRT685S, device.MIMXRT685S_startup, driver.cache_cache64, driver.clock, driver.common, driver.flash_config.mimxrt685audevk, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.lpc_iopctl, driver.power, driver.reset, kit=mimxrt685audevk_om13790host, utility.debug_console.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, component.serial_manager_uart, component.usart_adapter, device_id=MIMXRT685S, device.MIMXRT685S_startup, driver.cache_cache64, driver.clock, driver.common, driver.flash_config.mimxrt685audevk, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.lpc_iopctl, driver.power, driver.reset, kit=mimxrt685audevk_om13790host, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require condition="device_id.MIMXRT685S.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MIMXRT685S_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="cache"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mimxrt685audevk_flash_config"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iopctl"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
  </conditions>
  <examples>
    <example name="usb_pd_alt_mode_dp_host_bm" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_alt_mode_dp_host/bm" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the shield host board (om13790host) to implement the DisplayPort alternate mode.It recognize attached video adapters (like "Type-C to DisplayPort" or "Type-C to HDMI"), and drive the adapter to work.</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_alt_mode_dp_host_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_alt_mode_dp_host_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_bm" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd/bm" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the board keys and debug console to test the PD functions. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_charger_battery_bm" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_charger_battery/bm" doc="readme.txt">
      <description>the application simulate battery product , it prints the battery percent continually. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_charger_battery_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_charger_battery_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_sink_battery_bm" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_sink_battery/bm" doc="readme.txt">
      <description>The application simulate battery product , it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_sink_battery_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_sink_battery_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_source_charger_bm" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_source_charger/bm" doc="readme.txt">
      <description>The application simulate charger product. The demo only works as source and is external powered</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_source_charger_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_source_charger_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_alt_mode_dp_host_freertos" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_alt_mode_dp_host/freertos" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the shield host board (om13790host) to implement the DisplayPort alternate mode.It recognize attached video adapters (like "Type-C to DisplayPort" or "Type-C to HDMI"), and drive the adapter to work.</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_alt_mode_dp_host_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_alt_mode_dp_host_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_charger_battery_freertos" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_charger_battery/freertos" doc="readme.txt">
      <description>The application simulate battery product, it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_charger_battery_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_charger_battery_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_freertos" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd/freertos" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the board keys and debug console to test the PD functions. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_sink_battery_freertos" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_sink_battery/freertos" doc="readme.txt">
      <description>The application simulate battery product, it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_sink_battery_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_sink_battery_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_source_charger_freertos" folder="boards/mimxrt685audevk_om13790host/usb_examples/usb_pd_source_charger/freertos" doc="readme.txt">
      <description>The application simulate charger product. The demo only works as source and is external powered</description>
      <board name="MIMXRT685-AUD-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_source_charger_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_source_charger_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="USB" Cgroup="USB PD Project Template" Csub="mimxrt685audevk_om13790host" Cvariant="mimxrt685audevk_om13790host" Cversion="1.0.0" condition="BOARD_Project_Template.mimxrt685audevk_om13790host.condition_id">
      <description>BOARD_Project_Template mimxrt685audevk_om13790host</description>
      <files>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/board.c" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/board.h" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/clock_config.c" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/clock_config.h" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/pin_mux.c" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/pin_mux.h" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/peripherals.c" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/mimxrt685audevk_om13790host/project_template/peripherals.h" version="1.0.0" projectpath="mimxrt685audevk_om13790host/project_template"/>
        <file category="include" name="boards/mimxrt685audevk_om13790host/project_template/"/>
      </files>
    </component>
  </components>
</package>
