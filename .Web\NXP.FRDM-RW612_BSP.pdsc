<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-RW612_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-RW612</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="RW612_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-RW612">
      <description>FRDM-RW612</description>
      <mountedDevice Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <compatibleDevice Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <compatibleDevice Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <compatibleDevice Dname="RW610UKA2I" Dvendor="NXP:11"/>
      <compatibleDevice Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <compatibleDevice Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.RW610.internal_condition">
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.RW612.internal_condition">
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.RW612.internal_condition">
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmrw612.condition_id">
      <require condition="allOf.board=frdmrw612, component.usart_adapter, device_id=RW612, device.RW612_startup, driver.cache_cache64, driver.clock, driver.cns_io_mux, driver.common, driver.flash_config.frdmrw612, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.power, driver.reset, driver.ocotp, component.els_pkc.platform.rw61x_standalone_clib_gdet_sensor, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=frdmrw612, component.usart_adapter, device_id=RW612, device.RW612_startup, driver.cache_cache64, driver.clock, driver.cns_io_mux, driver.common, driver.flash_config.frdmrw612, driver.flexcomm_usart, driver.flexspi, driver.lpc_gpio, driver.power, driver.reset, driver.ocotp, component.els_pkc.platform.rw61x_standalone_clib_gdet_sensor, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.frdmrw612.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_adapter"/>
      <require condition="device_id.RW612.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="cache"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="io_mux"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="frdmrw612_flash_config"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flexspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ocotp"/>
      <require Cclass="RW612 els_pkc" Cgroup="els_pkc_platform" Csub="rw61x_clib_gdet_sensor"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmrw612.internal_condition">
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="cdog" folder="boards/frdmrw612/driver_examples/cdog" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog.uvprojx"/>
        <environment name="csolution" load="cdog.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="boards/frdmrw612/cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="boards/frdmrw612/cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/frdmrw612/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/frdmrw612/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="boards/frdmrw612/cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="boards/frdmrw612/cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_master" folder="boards/frdmrw612/cmsis_driver_examples/spi/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_int_b2b_transfer_slave" folder="boards/frdmrw612/cmsis_driver_examples/spi/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_dma_transfer" folder="boards/frdmrw612/cmsis_driver_examples/usart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_usart_interrupt_transfer" folder="boards/frdmrw612/cmsis_driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_usart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acomp_basic" folder="boards/frdmrw612/driver_examples/acomp/basic" doc="readme.md">
      <description>The acomp basic driver example demostrates the basic usage of the ACOMP module. This example compares the user input analog signal with interanl reference voltage(VDDIO_3 * 0.5) and toggle the LED when the result...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_basic.uvprojx"/>
        <environment name="csolution" load="acomp_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_software_trigger" folder="boards/frdmrw612/driver_examples/adc/software_trigger" doc="readme.md">
      <description>The adc_software_trigger example shows how to software trigger ADC conversion. In this example, ADC resolution is set as 16bit, the reference voltage is selected as the internal 1.8V bandgap, and input gain is set as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_software_trigger.uvprojx"/>
        <environment name="csolution" load="adc_software_trigger.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_normal" folder="boards/frdmrw612/driver_examples/dac/normal" doc="readme.md">
      <description>The DAC normal driver example demostrates the basic useage of DAC module. In this example, users input value from the terminal, and then the related voltage will be output through DAC output pin.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_normal.uvprojx"/>
        <environment name="csolution" load="dac_normal.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_waveform_generator" folder="boards/frdmrw612/driver_examples/dac/waveform_generator" doc="readme.md">
      <description>The DAC waveform generator driver example demonstrates how to use the DAC module to generate different types of waveforms (including triangle waveform, sawtooth waveform, sine waveform, and noise). After starting...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_waveform_generator.uvprojx"/>
        <environment name="csolution" load="dac_waveform_generator.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/frdmrw612/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_capture" folder="boards/frdmrw612/driver_examples/ctimer/capture" doc="readme.md">
      <description>This example shows how to use CTimer to capture the edge. In this example, CTimer timer counter uses APB clock as clock source, and CTimer monitors capture pin. When rising edge detected on the pin, CTimer saves the timer counter value to capture register.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_capture.uvprojx"/>
        <environment name="csolution" load="ctimer_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/frdmrw612/driver_examples/ctimer/simple_match" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/frdmrw612/driver_examples/ctimer/simple_match_interrupt" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/frdmrw612/driver_examples/ctimer/simple_pwm" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/frdmrw612/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="boards/frdmrw612/driver_examples/dma/channel_chain" doc="readme.md">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="csolution" load="dma_channel_chain.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="boards/frdmrw612/driver_examples/dma/interleave_transfer" doc="readme.md">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="csolution" load="dma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="boards/frdmrw612/driver_examples/dma/linked_transfer" doc="readme.md">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="csolution" load="dma_linked_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/frdmrw612/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_ptp1588_transfer" folder="boards/frdmrw612/driver_examples/enet/txrx_ptp1588_transfer" doc="readme.md">
      <description>The enet_rxtx_ptp1588 example shows the way to use ENET driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_ptp1588_transfer.uvprojx"/>
        <environment name="csolution" load="enet_txrx_ptp1588_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer" folder="boards/frdmrw612/driver_examples/enet/txrx_transfer" doc="readme.md">
      <description>The enet_rxtx example shows the simplest way to use ENET driver for simple frame receive and transmit.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive and transmit frame.The...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer.uvprojx"/>
        <environment name="csolution" load="enet_txrx_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_nor" folder="boards/frdmrw612/component_examples/flash_component/flexspi_nor" doc="readme.md">
      <description>nor flash demo shows the use of nor flash component to erase, program, and read an external nor flash device.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_nor.uvprojx"/>
        <environment name="csolution" load="flash_component_nor.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_dma_transfer" folder="boards/frdmrw612/driver_examples/flexspi/nor/dma_transfer" doc="readme.md">
      <description>The flexspi_nor_dma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_dma_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer" folder="boards/frdmrw612/driver_examples/flexspi/nor/polling_transfer" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_psram_dma_transfer" folder="boards/frdmrw612/driver_examples/flexspi/psram/dma_transfer" doc="readme.md">
      <description>The flexspi_psram_dma_transfer example shows how to use flexspi driver with dma: In this example, flexspi will send data and operate the external PSRAM connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_psram_dma_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_psram_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_psram_polling_transfer" folder="boards/frdmrw612/driver_examples/flexspi/psram/polling_transfer" doc="readme.md">
      <description>The flexspi_psram_polling_transfer example shows how to use flexspi driver with polling: In this example, flexspi will send data and operate the external PSRAM connected with FLEXSPI. Some simple flash command will...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_psram_polling_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_psram_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fmeas" folder="boards/frdmrw612/driver_examples/fmeas" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Frequency Measure feature of SYSCON module on LPC devices.It shows how to measure a target frequency using a (faster) reference frequency. The example uses the...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmeas.uvprojx"/>
        <environment name="csolution" load="fmeas.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdma_basic" folder="boards/frdmrw612/driver_examples/gdma/basic" doc="readme.md">
      <description>This example shows how to use GDMA functional APIs to do memory to memory data copy.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdma_basic.uvprojx"/>
        <environment name="csolution" load="gdma_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdma_lli" folder="boards/frdmrw612/driver_examples/gdma/lli" doc="readme.md">
      <description>This project shows how to use GDMA functional APIs to do the memory to memory data transfer with link list feature.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdma_lli.uvprojx"/>
        <environment name="csolution" load="gdma_lli.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdma_transfer_basic" folder="boards/frdmrw612/driver_examples/gdma/transfer_basic" doc="readme.md">
      <description>This example shows how to use GDMA transactional APIs to do memory to memory data copy.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdma_transfer_basic.uvprojx"/>
        <environment name="csolution" load="gdma_transfer_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdma_transfer_lli" folder="boards/frdmrw612/driver_examples/gdma/transfer_lli" doc="readme.md">
      <description>This project shows how to use GDMA transactional APIs to do the memory to memory data transfer with link list feature.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdma_transfer_lli.uvprojx"/>
        <environment name="csolution" load="gdma_transfer_lli.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdma_transfer_wrap" folder="boards/frdmrw612/driver_examples/gdma/transfer_wrap" doc="readme.md">
      <description>This project shows how to use GDMA transactional APIs to do the wrap transfer.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdma_transfer_wrap.uvprojx"/>
        <environment name="csolution" load="gdma_transfer_wrap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gdma_wrap" folder="boards/frdmrw612/driver_examples/gdma/wrap" doc="readme.md">
      <description>This project shows how to use GDMA functional APIs to do the wrap transfer.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdma_wrap.uvprojx"/>
        <environment name="csolution" load="gdma_wrap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmrw612/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="boards/frdmrw612/driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="boards/frdmrw612/driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/frdmrw612/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/frdmrw612/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_master" folder="boards/frdmrw612/driver_examples/i2c/polling_b2b/master" doc="readme.md">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_master.uvprojx"/>
        <environment name="csolution" load="i2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_slave" folder="boards/frdmrw612/driver_examples/i2c/polling_b2b/slave" doc="readme.md">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_slave.uvprojx"/>
        <environment name="csolution" load="i2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="itrc" folder="boards/frdmrw612/driver_examples/itrc" doc="readme.md">
      <description>The ITRC Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Intrusion and Tamper Response Controller.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/itrc.uvprojx"/>
        <environment name="csolution" load="itrc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdic_dma" folder="boards/frdmrw612/driver_examples/lcdic/dma" doc="readme.md">
      <description>This example shows how to use the lcdic DMA APIs.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdic_dma.uvprojx"/>
        <environment name="csolution" load="lcdic_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdic_interrupt" folder="boards/frdmrw612/driver_examples/lcdic/interrupt" doc="readme.md">
      <description>This example shows how to use the lcdic interrupt APIs.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdic_interrupt.uvprojx"/>
        <environment name="csolution" load="lcdic_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdic_polling" folder="boards/frdmrw612/driver_examples/lcdic/polling" doc="readme.md">
      <description>This example shows how to use the lcdic polling APIs.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdic_polling.uvprojx"/>
        <environment name="csolution" load="lcdic_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/frdmrw612/driver_examples/mrt" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example" folder="boards/frdmrw612/driver_examples/ostimer" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example.uvprojx"/>
        <environment name="csolution" load="ostimer_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager_test_bm" folder="boards/frdmrw612/demo_apps/power_manager_test/bm" doc="readme.md">
      <description>The power manager test application demonstrates the basic usage of power manager framework without RTOS. The demo tests all features of power manager framework, including notification manager, wakeup source manager and so on.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager_test_bm.uvprojx"/>
        <environment name="csolution" load="power_manager_test_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_bootloader" folder="boards/frdmrw612/driver_examples/romapi/bootloader" doc="readme.md">
      <description>Rom API driver example for bootloader functions.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="romapi_bootloader.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_flexspi" folder="boards/frdmrw612/driver_examples/romapi/flexspi" doc="readme.md">
      <description>Rom API driver example for FlexSPI interface.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="romapi_flexspi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_iap" folder="boards/frdmrw612/driver_examples/romapi/iap" doc="readme.md">
      <description>Rom API driver example for IAP interface.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="romapi_iap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_nboot" folder="boards/frdmrw612/driver_examples/romapi/nboot" doc="readme.md">
      <description>Rom API driver example for NBOOT interface.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="romapi_nboot.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_otp" folder="boards/frdmrw612/driver_examples/romapi/otp" doc="readme.md">
      <description>Rom API driver example for OCOTP interface.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="romapi_otp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_example" folder="boards/frdmrw612/driver_examples/rtc" doc="readme.md">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_example.uvprojx"/>
        <environment name="csolution" load="rtc_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="boards/frdmrw612/driver_examples/sctimer/16bit_counter" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="csolution" load="sctimer_16bit_counter.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_input_capture" folder="boards/frdmrw612/driver_examples/sctimer/input_capture" doc="readme.md">
      <description>The SCTimer project is a demonstration program of the SDK SCTimer driver's input capture feature.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_input_capture.uvprojx"/>
        <environment name="csolution" load="sctimer_input_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="boards/frdmrw612/driver_examples/sctimer/multi_state_pwm" doc="readme.md">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="csolution" load="sctimer_multi_state_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="boards/frdmrw612/driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.md">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="csolution" load="sctimer_pwm_with_dutycyle_change.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="boards/frdmrw612/driver_examples/sctimer/simple_pwm" doc="readme.md">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="csolution" load="sctimer_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_master" folder="boards/frdmrw612/driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_b2b_transfer_master example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_slave" folder="boards/frdmrw612/driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_b2b_transfer_slave example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/frdmrw612/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/frdmrw612/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/frdmrw612/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/frdmrw612/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/frdmrw612/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/frdmrw612/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="temperature_measurement" folder="boards/frdmrw612/demo_apps/temperature_measurement" doc="readme.md">
      <description>The temperature measurement demo is used to measure the temperature, note that this case just support RW61x platform at present.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/temperature_measurement.uvprojx"/>
        <environment name="csolution" load="temperature_measurement.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="trng_random" folder="boards/frdmrw612/driver_examples/trng/random" doc="readme.md">
      <description>The True Random Number Generator (TRNG) is a hardware accelerator module that generates entropy for consuming module or for other post processing functions. The TRNGExample project is a demonstration program that...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="trng_random.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_double_buffer_transfer" folder="boards/frdmrw612/driver_examples/usart/dma_double_buffer_transfer" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USARTThe example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_double_buffer_transfer.uvprojx"/>
        <environment name="csolution" load="usart_dma_double_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_dma_transfer" folder="boards/frdmrw612/driver_examples/usart/dma_transfer" doc="readme.md">
      <description>The usart_dma example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board willsend back all characters that PC send to the board.Note: The example echo...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="usart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt" folder="boards/frdmrw612/driver_examples/usart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt.uvprojx"/>
        <environment name="csolution" load="usart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_rb_transfer" folder="boards/frdmrw612/driver_examples/usart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_rb_transfer.uvprojx"/>
        <environment name="csolution" load="usart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_interrupt_transfer" folder="boards/frdmrw612/driver_examples/usart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="usart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling" folder="boards/frdmrw612/driver_examples/usart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling.uvprojx"/>
        <environment name="csolution" load="usart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick" folder="boards/frdmrw612/driver_examples/utick" doc="readme.md">
      <description>The utick project is a simple demonstration program of the SDK utick driver. It sets up the utick hardware block to trigger a periodic interrupt after every 1 second. When the utick interrupt is triggered a message is printed on the UART terminal.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick.uvprojx"/>
        <environment name="csolution" load="utick.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/frdmrw612/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="FRDM-RW612" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmrw612" Cversion="1.0.0" condition="BOARD_Project_Template.frdmrw612.condition_id">
      <description>Board_project_template frdmrw612</description>
      <RTE_Components_h>
#ifndef FSL_SDK_DRIVER_QUICK_ACCESS_ENABLE
#define FSL_SDK_DRIVER_QUICK_ACCESS_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" attr="config" name="boards/frdmrw612/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmrw612/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmrw612/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmrw612/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmrw612/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmrw612/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmrw612/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmrw612/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmrw612/project_template/"/>
      </files>
    </component>
  </components>
</package>
