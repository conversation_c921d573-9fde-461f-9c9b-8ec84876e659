<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss/shared/docs/product/micro/device-family-Pack/</url>
  <name>TXZ4-M4L1_DFP</name>
  <description>Toshiba TXZ4 Series TMPM4L Group (1) Device Support</description>

  <releases>
    <release version="1.0.0" date="2019-03-25">
      First Release version of TXZ4 Series TMPM4L Group(1) Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4</keyword>
    <keyword>TXZ4</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4 Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
The TXZ4 microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>

      <!-- ************************  Subfamily 'TMPM4Lx'  **************************** -->
      <subFamily DsubFamily="M4L(1)">

        <!-- ***********************  Device 'TMPM4L2'  ************************* -->
        <device Dname="TMPM4L2FWDUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4L2.h" define="TMPM4L2"/>
          <debug svd="SVD/M4L2.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Lx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x1800"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="4"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="3"/>
          <feature type="Other"           n="3"                           name="TSPI(SIO)"/>
          <feature type="ADC"           n="7"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="8"/>
          <feature type="IOs"           n="37"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="48" name="LQFP48-P-0707-0.50D"/>
        </device>
        <!-- ***********************  Device 'TMPM4L1'  ************************* -->
        <device Dname="TMPM4L1FWUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4L1.h" define="TMPM4L1"/>
          <debug svd="SVD/M4L1.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Lx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x1800"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="4"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="3"/>
          <feature type="Other"           n="3"                           name="TSPI(SIO)"/>
          <feature type="ADC"           n="6"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="7"/>
          <feature type="IOs"           n="33"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="44" name="LQFP44-P-1010-0.80B"/>
        </device>

      </subFamily>
    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <!-- Device + CMSIS Conditions -->

    <condition id="TMPM4L2x CMSIS">
      <description>Toshiba TMPM4L2x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4L2*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4L1x CMSIS">
      <description>Toshiba TMPM4L1x Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4L1*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>

  </conditions>

  <components>
    <!-- Startup TMPM4L2x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4L2x CMSIS">
      <description>System Startup for Toshiba TMPM4L2x Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4L2x      /* Device Startup for TMPM4L2x */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4L2.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/system_TMPM4Lx.c"      attr="config" version="1.0.0"/>
      </files>
    </component>

    <!-- Startup TMPM4L1x -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4L1x CMSIS">
      <description>System Startup for Toshiba TMPM4L1x Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4L1x      /* Device Startup for TMPM4L1x */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4L1.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/system_TMPM4Lx.c"      attr="config" version="1.0.0"/>
      </files>
    </component>
  </components>

</package>
