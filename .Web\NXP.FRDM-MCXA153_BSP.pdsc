<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXA153_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for FRDM-MCXA153</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="17.0.0" date="2024-01-26">NXP CMSIS Packs based on MCUXpresso SDK 2.14.2</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCXA153_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-MCXA153">
      <description>MCXA153 Evaluation Kit</description>
      <image small="boards/frdmmcxa153/frdmmcxa153.png"/>
      <mountedDevice Dname="MCXA153VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA142VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA142VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA142VLF" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA142VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA143VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA143VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA143VLF" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA143VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA152VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA152VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA152VLF" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA152VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA153VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA153VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA153VLF" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA132VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA132VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA133VFM" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA133VFT" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MCXA153.internal_condition">
      <accept Dname="MCXA153VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXA142.internal_condition">
      <accept Dname="MCXA142VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA142VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXA143.internal_condition">
      <accept Dname="MCXA143VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA143VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXA152.internal_condition">
      <accept Dname="MCXA152VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA152VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXA153.internal_condition">
      <accept Dname="MCXA153VFM" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VFT" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLF" Dvendor="NXP:11"/>
      <accept Dname="MCXA153VLH" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MCXA132.internal_condition"/>
    <condition id="device.MCXA133.internal_condition"/>
    <condition id="BOARD_Project_Template.frdmmcxa153.condition_id">
      <require condition="allOf.component.lpuart_adapter, driver.lpuart, driver.gpio, driver.reset, driver.clock, driver.mcx_spc, driver.port, device_id=MCXA153, device.MCXA153_startup, board=frdmmcxa153, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.lpuart_adapter, driver.lpuart, driver.gpio, driver.reset, driver.clock, driver.mcx_spc, driver.port, device_id=MCXA153, device.MCXA153_startup, board=frdmmcxa153, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="mcx_spc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require condition="device_id.MCXA153.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require condition="board.frdmmcxa153.internal_condition"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.frdmmcxa153.internal_condition">
      <accept condition="device.MCXA142.internal_condition"/>
      <accept condition="device.MCXA143.internal_condition"/>
      <accept condition="device.MCXA152.internal_condition"/>
      <accept condition="device.MCXA153.internal_condition"/>
      <accept condition="device.MCXA132.internal_condition"/>
      <accept condition="device.MCXA133.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
  </conditions>
  <examples>
    <example name="aoi_io_and" folder="boards/frdmmcxa153/driver_examples/aoi/io_and" doc="readme.md">
      <description>This example demos the AOI uses two IO, the AOI_OUT = (IO0 &amp; IO1).</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/aoi_io_and.uvprojx"/>
        <environment name="iar" load="iar/aoi_io_and.ewp"/>
        <environment name="csolution" load="aoi_io_and.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cdog" folder="boards/frdmmcxa153/driver_examples/cdog" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog.uvprojx"/>
        <environment name="iar" load="iar/cdog.ewp"/>
        <environment name="csolution" load="cdog.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master" folder="boards/frdmmcxa153/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave" folder="boards/frdmmcxa153/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master" folder="boards/frdmmcxa153/cmsis_driver_examples/lpi2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave" folder="boards/frdmmcxa153/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master" folder="boards/frdmmcxa153/cmsis_driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave" folder="boards/frdmmcxa153/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master" folder="boards/frdmmcxa153/cmsis_driver_examples/lpspi/int_b2b_transfer/master" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave" folder="boards/frdmmcxa153/cmsis_driver_examples/lpspi/int_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="boards/frdmmcxa153/cmsis_driver_examples/lpuart/edma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="boards/frdmmcxa153/cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/frdmmcxa153/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/frdmmcxa153/driver_examples/ctimer/simple_match" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example.ewp"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/frdmmcxa153/driver_examples/ctimer/simple_match_interrupt" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/frdmmcxa153/driver_examples/ctimer/simple_pwm" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/frdmmcxa153/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_channel_link" folder="boards/frdmmcxa153/driver_examples/edma3/channel_link" doc="readme.md">
      <description>The EDMA3 channel link example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_channel_link.uvprojx"/>
        <environment name="iar" load="iar/edma3_channel_link.ewp"/>
        <environment name="csolution" load="edma3_channel_link.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_interleave_transfer" folder="boards/frdmmcxa153/driver_examples/edma3/interleave_transfer" doc="readme.md">
      <description>The EDMA3 interleave example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_interleave_transfer.ewp"/>
        <environment name="csolution" load="edma3_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memory_to_memory" folder="boards/frdmmcxa153/driver_examples/edma3/memory_to_memory" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/edma3_memory_to_memory.ewp"/>
        <environment name="csolution" load="edma3_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memory_to_memory_transfer" folder="boards/frdmmcxa153/driver_examples/edma3/memory_to_memory_transfer" doc="readme.md">
      <description>The EDMA3 memory to memory transfer example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA3 drivers.The purpose...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memory_to_memory_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_memory_to_memory_transfer.ewp"/>
        <environment name="csolution" load="edma3_memory_to_memory_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memset" folder="boards/frdmmcxa153/driver_examples/edma3/memset" doc="readme.md">
      <description>The EDMA3 memset example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA3 drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memset.uvprojx"/>
        <environment name="iar" load="iar/edma3_memset.ewp"/>
        <environment name="csolution" load="edma3_memset.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_ping_pong_transfer" folder="boards/frdmmcxa153/driver_examples/edma3/ping_pong_transfer" doc="readme.md">
      <description>The EDMA3 ping pong transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_ping_pong_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_ping_pong_transfer.ewp"/>
        <environment name="csolution" load="edma3_ping_pong_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_scatter_gather" folder="boards/frdmmcxa153/driver_examples/edma3/scatter_gather" doc="readme.md">
      <description>The EDMA3 scatter gather example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA3 drivers.The purpose of this...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_scatter_gather.uvprojx"/>
        <environment name="iar" load="iar/edma3_scatter_gather.ewp"/>
        <environment name="csolution" load="edma3_scatter_gather.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_wrap_transfer" folder="boards/frdmmcxa153/driver_examples/edma3/wrap_transfer" doc="readme.md">
      <description>The EDMA3 wrap transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA3 and to provide a simple example for debugging and further development.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma3_wrap_transfer.ewp"/>
        <environment name="csolution" load="edma3_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eqdc_basic" folder="boards/frdmmcxa153/driver_examples/eqdc/basic" doc="readme.md">
      <description>The EQDC project shows how to sample the shaft position based on quadrature signal (PHASEA and PHASEB).</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eqdc_basic.uvprojx"/>
        <environment name="iar" load="iar/eqdc_basic.ewp"/>
        <environment name="csolution" load="eqdc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eqdc_index_interrupt" folder="boards/frdmmcxa153/driver_examples/eqdc/index_interrupt" doc="readme.md">
      <description>The EQDC project shows how to sample the shaft position based on quadrature signal (PHASEA and PHASEB).</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eqdc_index_interrupt.uvprojx"/>
        <environment name="iar" load="iar/eqdc_index_interrupt.ewp"/>
        <environment name="csolution" load="eqdc_index_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="error_recording" folder="boards/frdmmcxa153/driver_examples/erm/error_recording" doc="readme.md">
      <description>The ERM Single Error project is a simple demonstration program of the SDK ERM driver. It shows how to show error events using the ERM driver.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/error_recording.uvprojx"/>
        <environment name="iar" load="iar/error_recording.ewp"/>
        <environment name="csolution" load="error_recording.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="freqme_interrupt" folder="boards/frdmmcxa153/driver_examples/freqme" doc="readme.md">
      <description>The freqme_interrupt is a demonstration program of the SDK LPC_FREQME driver's features. The example demostrate the usage of frequency measurement operate mode and pulse width measurement operate mode.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freqme_interrupt.uvprojx"/>
        <environment name="iar" load="iar/freqme_interrupt.ewp"/>
        <environment name="csolution" load="freqme_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="glikey" folder="boards/frdmmcxa153/driver_examples/glikey" doc="readme.md">
      <description>The GLIKEY Example project is a demonstration program that uses the MCUX SDK software to show funcionality of GLIKEY IP.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/glikey.uvprojx"/>
        <environment name="iar" load="iar/glikey.ewp"/>
        <environment name="csolution" load="glikey.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/frdmmcxa153/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear registers for each GPIO pin output register. </description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/frdmmcxa153/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/frdmmcxa153/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_master" folder="boards/frdmmcxa153/component_examples/i3c_bus/master" doc="readme.md">
      <description>The i3c_bus_master example shows how to use i3c_bus component to create I3C bus and i3c master on bus.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_master.ewp"/>
        <environment name="csolution" load="i3c_bus_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_slave" folder="boards/frdmmcxa153/component_examples/i3c_bus/slave" doc="readme.md">
      <description>The i3c_bus_slave example shows how to use i3c_bus component to work as i3c bus slave.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_slave.ewp"/>
        <environment name="csolution" load="i3c_bus_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/i3c/edma_b2b_transfer/master" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_master example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as master and another i3c instance on the other board as slave....See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/i3c/edma_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as slave and another i3c instance on the other board as master....See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/i3c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/i3c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_p3t1755" folder="boards/frdmmcxa153/driver_examples/i3c/master_read_sensor_p3t1755" doc="readme.md">
      <description>The i3c_master_read_sensor_p3t1755 example shows how to use i3c driver as master to communicate with sensor P3T1755.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_p3t1755.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_p3t1755.ewp"/>
        <environment name="csolution" load="i3c_master_read_sensor_p3t1755.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/i3c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/i3c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/frdmmcxa153/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt" folder="boards/frdmmcxa153/driver_examples/lpadc/interrupt" doc="readme.md">
      <description>The lpdc_single_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt.ewp"/>
        <environment name="csolution" load="lpadc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling" folder="boards/frdmmcxa153/driver_examples/lpadc/polling" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling.ewp"/>
        <environment name="csolution" load="lpadc_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_temperature_measurement" folder="boards/frdmmcxa153/driver_examples/lpadc/temperature_measurement" doc="readme.md">
      <description>The lpadc_temperature_measurement example shows how to measure the temperature within the internal sensor. In this example, the ADC input channel is mapped to an internal temperature sensor. When running the project,...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_temperature_measurement.uvprojx"/>
        <environment name="iar" load="iar/lpadc_temperature_measurement.ewp"/>
        <environment name="csolution" load="lpadc_temperature_measurement.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_trigger_exception" folder="boards/frdmmcxa153/driver_examples/lpadc/trigger_exception" doc="readme.md">
      <description>The lpadc_trigger_exception example demonstrates the usage of trigger exception feature.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_trigger_exception.uvprojx"/>
        <environment name="iar" load="iar/lpadc_trigger_exception.ewp"/>
        <environment name="csolution" load="lpadc_trigger_exception.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpcmp_interrupt" folder="boards/frdmmcxa153/driver_examples/lpcmp/interrupt" doc="readme.md">
      <description>The LPCMP interrupt Example shows how to use interrupt with LPCMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the LPCMP's positive channel...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpcmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpcmp_interrupt.ewp"/>
        <environment name="csolution" load="lpcmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpcmp_polling" folder="boards/frdmmcxa153/driver_examples/lpcmp/polling" doc="readme.md">
      <description>The LPCMP polling Example shows the simplest way to use LPCMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpcmp_polling.uvprojx"/>
        <environment name="iar" load="iar/lpcmp_polling.ewp"/>
        <environment name="csolution" load="lpcmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpcmp_round_robin" folder="boards/frdmmcxa153/driver_examples/lpcmp/round_robin" doc="readme.md">
      <description>The LPCMP round robin example is a simple demonstration program to use the LPCMP driver and help user with a quick start.In this example, user needs to specify which port and channel to fixed, users also need to...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpcmp_round_robin.uvprojx"/>
        <environment name="iar" load="iar/lpcmp_round_robin.ewp"/>
        <environment name="csolution" load="lpcmp_round_robin.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/lpi2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/lpi2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master" folder="boards/frdmmcxa153/driver_examples/lpi2c/polling_b2b/master" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave" folder="boards/frdmmcxa153/driver_examples/lpi2c/polling_b2b/slave" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt" folder="boards/frdmmcxa153/driver_examples/lpspi/interrupt" doc="readme.md">
      <description>The lpspi_functional_interrupt example shows how to use LPSPI driver in interrupt way:In this example , one lpspi instance used as LPSPI master and another lpspi instance used as LPSPI slave .1. LPSPI master...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt.ewp"/>
        <environment name="csolution" load="lpspi_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master" folder="boards/frdmmcxa153/driver_examples/lpspi/interrupt_b2b/master" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave" folder="boards/frdmmcxa153/driver_examples/lpspi/interrupt_b2b/slave" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/lpspi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/lpspi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master" folder="boards/frdmmcxa153/driver_examples/lpspi/polling_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave" folder="boards/frdmmcxa153/driver_examples/lpspi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="boards/frdmmcxa153/driver_examples/lptmr" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
        <environment name="csolution" load="lptmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="boards/frdmmcxa153/driver_examples/lpuart/edma_transfer" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/frdmmcxa153/driver_examples/lpuart/interrupt" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/frdmmcxa153/driver_examples/lpuart/interrupt_rb_transfer" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/frdmmcxa153/driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits" folder="boards/frdmmcxa153/driver_examples/lpuart/interrupt_transfer_seven_bits" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/frdmmcxa153/driver_examples/lpuart/polling" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits" folder="boards/frdmmcxa153/driver_examples/lpuart/polling_seven_bits" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example" folder="boards/frdmmcxa153/driver_examples/ostimer" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example.uvprojx"/>
        <environment name="iar" load="iar/ostimer_example.ewp"/>
        <environment name="csolution" load="ostimer_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_ll" folder="boards/frdmmcxa153/demo_apps/power_mode_switch/low_level" doc="readme.md">
      <description>Low level power_mode_switch demo.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_ll.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch_ll.ewp"/>
        <environment name="csolution" load="power_mode_switch_ll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm" folder="boards/frdmmcxa153/driver_examples/pwm" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm.uvprojx"/>
        <environment name="iar" load="iar/pwm.ewp"/>
        <environment name="csolution" load="pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flashiap" folder="boards/frdmmcxa153/driver_examples/romapi/flashiap" doc="readme.md">
      <description>The FLASIAP project is a simple demonstration program of the SDK FLASIAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flashiap.uvprojx"/>
        <environment name="iar" load="iar/flashiap.ewp"/>
        <environment name="csolution" load="flashiap.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="runbootloader" folder="boards/frdmmcxa153/driver_examples/romapi/runbootloader" doc="readme.md">
      <description>The runbootloader project is a simple demonstration which run into the bootloader with the user parameter. The demo forces the device run into the ISP mode with the specific peripheral.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/runbootloader.uvprojx"/>
        <environment name="iar" load="iar/runbootloader.ewp"/>
        <environment name="csolution" load="runbootloader.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="utick" folder="boards/frdmmcxa153/driver_examples/utick" doc="readme.md">
      <description>The utick project is a simple demonstration program of the SDK utick driver. It sets up the utick hardware block to trigger a periodic interrupt after every 1 second. When the utick interrupt is triggered a message is printed on the UART terminal.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick.uvprojx"/>
        <environment name="iar" load="iar/utick.ewp"/>
        <environment name="csolution" load="utick.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="waketimer_example" folder="boards/frdmmcxa153/driver_examples/waketimer" doc="readme.md">
      <description>The WAKETIMER project is a simple demonstration program of the SDK WAKETIMER driver. WAKETIMER will trigger every 1 second.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/waketimer_example.uvprojx"/>
        <environment name="iar" load="iar/waketimer_example.ewp"/>
        <environment name="csolution" load="waketimer_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/frdmmcxa153/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="frdmmcxa153" Cversion="1.0.0" condition="BOARD_Project_Template.frdmmcxa153.condition_id">
      <description>Board_project_template frdmmcxa153</description>
      <files>
        <file category="header" attr="config" name="boards/frdmmcxa153/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxa153/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxa153/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxa153/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxa153/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxa153/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/frdmmcxa153/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/frdmmcxa153/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/frdmmcxa153/project_template/"/>
      </files>
    </component>
  </components>
</package>
