<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: GCC Newlib</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('group__retarget__os__newlib.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">GCC Newlib<div class="ingroups"><a class="el" href="group__os__interface__api.html">OS Interface</a></div></div></div>
</div><!--header-->
<div class="contents">

<p>Declarations of types and functions for integrating an RTOS with the GCC Newlib.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gade69e629dfa01c6be1668603c0382899"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#gade69e629dfa01c6be1668603c0382899">__retarget_lock_init</a> (_LOCK_T *lock)</td></tr>
<tr class="memdesc:gade69e629dfa01c6be1668603c0382899"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allocate lock related resources.  <br /></td></tr>
<tr class="separator:gade69e629dfa01c6be1668603c0382899"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa2fc9199a1a415d4fb558b21a30f607d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#gaa2fc9199a1a415d4fb558b21a30f607d">__retarget_lock_init_recursive</a> (_LOCK_T *lock)</td></tr>
<tr class="memdesc:gaa2fc9199a1a415d4fb558b21a30f607d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allocate recursive lock related resources.  <br /></td></tr>
<tr class="separator:gaa2fc9199a1a415d4fb558b21a30f607d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabfff184006f7f1316140e4f022656765"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#gabfff184006f7f1316140e4f022656765">__retarget_lock_close</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:gabfff184006f7f1316140e4f022656765"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free lock related resources.  <br /></td></tr>
<tr class="separator:gabfff184006f7f1316140e4f022656765"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaffb9501f6f4829dc0e2983e9858a1fed"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#gaffb9501f6f4829dc0e2983e9858a1fed">__retarget_lock_close_recursive</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:gaffb9501f6f4829dc0e2983e9858a1fed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free recursive lock related resources.  <br /></td></tr>
<tr class="separator:gaffb9501f6f4829dc0e2983e9858a1fed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ab8f0b321a80a6484e40b98fb3b61e7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#ga1ab8f0b321a80a6484e40b98fb3b61e7">__retarget_lock_acquire</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:ga1ab8f0b321a80a6484e40b98fb3b61e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Acquire lock immediately after the lock object is available.  <br /></td></tr>
<tr class="separator:ga1ab8f0b321a80a6484e40b98fb3b61e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c117a73524578f221e6bbf414ccc6c9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#ga8c117a73524578f221e6bbf414ccc6c9">__retarget_lock_acquire_recursive</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:ga8c117a73524578f221e6bbf414ccc6c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Acquire recursive lock immediately after the lock object is available.  <br /></td></tr>
<tr class="separator:ga8c117a73524578f221e6bbf414ccc6c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4754bc767666414bfaf4b401b2be4bc"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#gaf4754bc767666414bfaf4b401b2be4bc">__retarget_lock_try_acquire</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:gaf4754bc767666414bfaf4b401b2be4bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Acquire lock if the lock object is available.  <br /></td></tr>
<tr class="separator:gaf4754bc767666414bfaf4b401b2be4bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f8b2d66fbcf1de096cb9655ae78cd09"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#ga2f8b2d66fbcf1de096cb9655ae78cd09">__retarget_lock_try_acquire_recursive</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:ga2f8b2d66fbcf1de096cb9655ae78cd09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Acquire recursive lock if the lock object is available.  <br /></td></tr>
<tr class="separator:ga2f8b2d66fbcf1de096cb9655ae78cd09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadec4c3df8d3bb9590d575ee500cd8611"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#gadec4c3df8d3bb9590d575ee500cd8611">__retarget_lock_release</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:gadec4c3df8d3bb9590d575ee500cd8611"><td class="mdescLeft">&#160;</td><td class="mdescRight">Relinquish the lock ownership.  <br /></td></tr>
<tr class="separator:gadec4c3df8d3bb9590d575ee500cd8611"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8be1e6784fb33f579d4944cbbad1a65a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__retarget__os__newlib.html#ga8be1e6784fb33f579d4944cbbad1a65a">__retarget_lock_release_recursive</a> (_LOCK_T lock)</td></tr>
<tr class="memdesc:ga8be1e6784fb33f579d4944cbbad1a65a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Relinquish the recursive lock ownership.  <br /></td></tr>
<tr class="separator:ga8be1e6784fb33f579d4944cbbad1a65a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<p>Declarations of types and functions for integrating an RTOS with the GCC Newlib. </p>
<h2 class="groupheader">Function Documentation</h2>
<a id="gade69e629dfa01c6be1668603c0382899" name="gade69e629dfa01c6be1668603c0382899"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gade69e629dfa01c6be1668603c0382899">&#9670;&#160;</a></span>__retarget_lock_init()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_init </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T *&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Allocate lock related resources. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>pointer to user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="gaa2fc9199a1a415d4fb558b21a30f607d" name="gaa2fc9199a1a415d4fb558b21a30f607d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa2fc9199a1a415d4fb558b21a30f607d">&#9670;&#160;</a></span>__retarget_lock_init_recursive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_init_recursive </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T *&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Allocate recursive lock related resources. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>pointer to user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="gabfff184006f7f1316140e4f022656765" name="gabfff184006f7f1316140e4f022656765"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabfff184006f7f1316140e4f022656765">&#9670;&#160;</a></span>__retarget_lock_close()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_close </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free lock related resources. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="gaffb9501f6f4829dc0e2983e9858a1fed" name="gaffb9501f6f4829dc0e2983e9858a1fed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaffb9501f6f4829dc0e2983e9858a1fed">&#9670;&#160;</a></span>__retarget_lock_close_recursive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_close_recursive </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free recursive lock related resources. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga1ab8f0b321a80a6484e40b98fb3b61e7" name="ga1ab8f0b321a80a6484e40b98fb3b61e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga1ab8f0b321a80a6484e40b98fb3b61e7">&#9670;&#160;</a></span>__retarget_lock_acquire()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_acquire </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Acquire lock immediately after the lock object is available. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga8c117a73524578f221e6bbf414ccc6c9" name="ga8c117a73524578f221e6bbf414ccc6c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8c117a73524578f221e6bbf414ccc6c9">&#9670;&#160;</a></span>__retarget_lock_acquire_recursive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_acquire_recursive </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Acquire recursive lock immediately after the lock object is available. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="gaf4754bc767666414bfaf4b401b2be4bc" name="gaf4754bc767666414bfaf4b401b2be4bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaf4754bc767666414bfaf4b401b2be4bc">&#9670;&#160;</a></span>__retarget_lock_try_acquire()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int __retarget_lock_try_acquire </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Acquire lock if the lock object is available. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>zero for success and non-zero to indicate that the lock cannot be acquired </dd></dl>

</div>
</div>
<a id="ga2f8b2d66fbcf1de096cb9655ae78cd09" name="ga2f8b2d66fbcf1de096cb9655ae78cd09"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga2f8b2d66fbcf1de096cb9655ae78cd09">&#9670;&#160;</a></span>__retarget_lock_try_acquire_recursive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int __retarget_lock_try_acquire_recursive </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Acquire recursive lock if the lock object is available. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>zero for success and non-zero to indicate that the lock cannot be acquired </dd></dl>

</div>
</div>
<a id="gadec4c3df8d3bb9590d575ee500cd8611" name="gadec4c3df8d3bb9590d575ee500cd8611"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gadec4c3df8d3bb9590d575ee500cd8611">&#9670;&#160;</a></span>__retarget_lock_release()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_release </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Relinquish the lock ownership. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ga8be1e6784fb33f579d4944cbbad1a65a" name="ga8be1e6784fb33f579d4944cbbad1a65a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga8be1e6784fb33f579d4944cbbad1a65a">&#9670;&#160;</a></span>__retarget_lock_release_recursive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void __retarget_lock_release_recursive </td>
          <td>(</td>
          <td class="paramtype">_LOCK_T&#160;</td>
          <td class="paramname"><em>lock</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Relinquish the recursive lock ownership. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">lock</td><td>user defined lock object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
