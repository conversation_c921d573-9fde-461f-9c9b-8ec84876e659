<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: Revision History</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('rev_hist.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Revision History </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><table class="cmtable" summary="Revision History">
<tr>
<th>Version </th><th>Description  </th></tr>
<tr>
<td>v2.10.0 </td><td><ul>
<li>Added WiFi Shield layers   </li>
</ul>
</td></tr>
<tr>
<td>v2.9.0 </td><td><ul>
<li>Added USB Host EHCI (TT) driver</li>
<li>Added USB Host OHCI driver   </li>
</ul>
</td></tr>
<tr>
<td>v2.8.0 </td><td><ul>
<li>Align with CMSIS v6   </li>
</ul>
</td></tr>
<tr>
<td>v2.7.2 </td><td><ul>
<li>Added Renesas DA16200 WiFi driver</li>
<li>Updated Inventek ISM43362 WiFi driver (version 1.14.0):<ul>
<li>Added statically allocated control block for asynchronous thread if FreeRTOS is used   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.7.1 </td><td><ul>
<li>Added support for Arm Cortex-M85 processor based devices</li>
<li>Added support for Arm China Star-MC1 processor based devices   </li>
</ul>
</td></tr>
<tr>
<td>v2.7.0 </td><td><ul>
<li>Added PHY_LAN8740A driver</li>
<li>Added ETH_LAN91C111 driver</li>
<li>Updated ETH_KSZ8851SNL and ETH_LAN9220 drivers:<ul>
<li>Corrected invalid power status in MAC_PowerControl</li>
</ul>
</li>
<li>Updated ESP8266 WiFi driver:<ul>
<li>Fixed Activate to use BSSID specified in SetOption with ARM_WIFI_BSSID</li>
</ul>
</li>
<li>Updated ESP8266, ESP32 and WizFi360 WiFi drivers:<ul>
<li>Fixed return string null terminator in GetModuleInfo</li>
<li>Fixed SocketSendTo for stream socket lengths above 2048 bytes</li>
<li>Enabled placement of USART transfer buffers in appropriate RAM by using section ".bss.driver.usartn" (n = USART driver instance number) in the linker scatter file</li>
</ul>
</li>
<li>Updated Inventek ISM43362 WiFi driver (version 1.13.0):<ul>
<li>Supports also ISM43340 Module</li>
<li>Added configuration for asynchronous thread stack size</li>
<li>Added support for 5 GHz channels on Access Point</li>
<li>Fixed socket connect operation for non-blocking mode</li>
<li>Enabled placement of SPI transfer buffers in appropriate RAM by using section ".bss.driver.spin" (n = SPI driver instance number) in the linker scatter file</li>
<li>Detected ISM43362 Module on STMicroelectronics B-L475E-IOT01A1 firmware limitation: SocketConnect does not work if first or last octet of IP address is 0 (for example IPs 0.x.y.z or x.y.z.0 do not work)</li>
<li>Detected shield firmware limitation: SocketConnect does not work if certain IP address octets contain value 0 or 255 (combinations that do not work: 0.x.y.z, x.y.z.0, 255.x.y.z)</li>
<li>Updated CMSIS Driver Validation test results   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.6.1 </td><td><ul>
<li>Updated ESP8266, ESP32 and WizFi360 WiFi drivers:<ul>
<li>Added auto protocol selection in SocketCreate</li>
<li>Fixed socket default timeout (zero == no time out)</li>
<li>Fixed SocketRecv/RecvFrom non blocking mode when received less than buffer length</li>
</ul>
</li>
<li>Updated Inventek ISM43362 WiFi driver (version 1.9.0):<ul>
<li>Corrected Initialize function failure if called shortly after reset</li>
<li>Corrected default protocol selection in SocketCreate function</li>
<li>Detected STM firmware limitation: SocketConnect does not work if any of IP address octets is 255 (for example IP like x.y.z.255)   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.6.0 </td><td><ul>
<li>Updated ESP8266, ESP32 and WizFi360 WiFi drivers:<ul>
<li>API V1.1: SocketSend/SendTo and SocketRecv/RecvFrom (support for polling)</li>
<li>Added DHCP setting before station Activate</li>
<li>Added read of DHCP assigned IPs after station activate</li>
<li>Fixed serial tx busy flag handling</li>
<li>Fixed function AT_Resp_ConnectAP for NULL argument</li>
<li>Enhanced serial communication startup procedure</li>
</ul>
</li>
<li>Updated Inventek ISM43362 WiFi driver (version 1.8.0):<ul>
<li>API V1.1: SocketSend/SendTo and SocketRecv/RecvFrom (support for polling)</li>
<li>Corrected GetModuleInfo return string termination</li>
<li>Corrected functionality when DATARDY line is used in polling mode</li>
<li>Corrected SocketConnect function never returning 0 in non-blocking mode</li>
<li>Corrected SocketRecv/SocketRecvFrom function polling if called without previous Bind</li>
<li>Corrected delay after module reset</li>
<li>For non-STM firmware variant only firmware version ******* is supported</li>
</ul>
</li>
<li>Documented firmware update procedure for Inventek ISMART43362-E WiFi Shield   </li>
</ul>
</td></tr>
<tr>
<td>v2.5.0 </td><td><ul>
<li>Added Espressif ESP32 WiFi driver</li>
<li>Added Espressif ESP8266 WiFi driver</li>
<li>Added Wiznet WizFi360 WiFi driver</li>
<li>Updated Inventek ISM43362 WiFi driver (version 1.3.0):<ul>
<li>Fixed SocketClose functionality</li>
<li>Fixed Activate function not to set password if OPEN security is used</li>
<li>Updated Initialization function to handle unavailable reset pin</li>
<li>Added debug of SPI traffic with Event Recorder</li>
<li>Added CMSIS Driver Validation test results   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.4.1 </td><td><ul>
<li>Updated Inventek ISM43362 WiFi driver</li>
<li>Corrected PHY_LAN8742A power down bit definition   </li>
</ul>
</td></tr>
<tr>
<td>v2.4.0 </td><td><ul>
<li>Added CMSIS-Driver WiFi implementation for Inventek ISM43362 via SPI (CMSIS-Driver SPI)   </li>
</ul>
</td></tr>
<tr>
<td>v2.3.0 </td><td><ul>
<li>Updated PHY_KSZ8081RNA:<ul>
<li>Added support for 50MHz reference clock</li>
</ul>
</li>
<li>Updated NAND MemoryBus driver:<ul>
<li>Fixed Ready/Busy configuration handling</li>
<li>Enhanced compatibility for ARM Compiler 6</li>
</ul>
</li>
<li>Updated Flash Drivers:<ul>
<li>Added data cache handling to memory bus based drivers</li>
<li>Fixed warnings flagged by ARM Compiler 6   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.2.0 </td><td><ul>
<li>Cvendor explicitly set to "Keil" to ease project migration</li>
<li>doxygen based documentation:<ul>
<li>added links to hardware parts</li>
<li>added Multi-Slave I2C and SPI usage instructions   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.1.0 </td><td><ul>
<li>Added LAN9220 Ethernet MAC+PHY driver</li>
<li>Updated I2C and SPI multi-slave wrapper:<ul>
<li>configuration options moved into separate file   </li>
</ul>
</li>
</ul>
</td></tr>
<tr>
<td>v2.0.0 </td><td>CMSIS-Driver implementation for non-MCU devices from MDK-Middleware pack have been moved to git-hub and are now shipped as a separate pack.   </td></tr>
</table>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
