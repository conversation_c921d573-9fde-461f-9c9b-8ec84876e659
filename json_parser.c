#include "json_parser.h"
#include "main.h"
#include "lcd.h"
#include "motor_unified_config.h"
#include "motor_config.h"
#include "motor_autocalibration.h"
// UserFunction.h уже включен через main.h

// Простые строковые функции
static uint8_t simple_strlen(const char* str)
{
    uint8_t len = 0;
    while(str[len] && len < 255) len++;
    return len;
}

static uint8_t simple_strcmp(const char* str1, const char* str2)
{
    while(*str1 && *str2) {
        if(*str1 != *str2) return 0;
        str1++;
        str2++;
    }
    return (*str1 == *str2);
}



// Преобразование строки команды в enum
JSON_Command_t JSON_StringToCommand(const char* cmd_str)
{
    if(simple_strcmp(cmd_str, "reset")) return CMD_RESET;
    if(simple_strcmp(cmd_str, "ready")) return CMD_READY;
    if(simple_strcmp(cmd_str, "about")) return CMD_ABOUT;
    if(simple_strcmp(cmd_str, "play_melody")) return CMD_PLAY_MELODY;
    if(simple_strcmp(cmd_str, "fire")) return CMD_FIRE;
    if(simple_strcmp(cmd_str, "test_m6")) return CMD_TEST_M6;
    if(simple_strcmp(cmd_str, "test_all")) return CMD_TEST_ALL;
    if(simple_strcmp(cmd_str, "move_motor")) return CMD_MOVE_MOTOR;
    if(simple_strcmp(cmd_str, "set_speed")) return CMD_SET_SPEED;
    if(simple_strcmp(cmd_str, "load_config")) return CMD_LOAD_CONFIG;
    if(simple_strcmp(cmd_str, "show_config")) return CMD_SHOW_CONFIG;
    if(simple_strcmp(cmd_str, "get_status")) return CMD_GET_STATUS;
    if(simple_strcmp(cmd_str, "stop_all")) return CMD_STOP_ALL;
    if(simple_strcmp(cmd_str, "aim")) return CMD_AIM;
    if(simple_strcmp(cmd_str, "set_position")) return CMD_SET_POSITION;
    if(simple_strcmp(cmd_str, "auto_calibrate") || simple_strcmp(cmd_str, "ac")) return CMD_AUTO_CALIBRATE;
    return CMD_UNKNOWN;
}

// Получить имя команды
const char* JSON_GetCommandName(JSON_Command_t cmd)
{
    switch(cmd) {
        case CMD_RESET: return "reset";
        case CMD_READY: return "ready";
        case CMD_ABOUT: return "about";
        case CMD_PLAY_MELODY: return "play_melody";
        case CMD_FIRE: return "fire";
        case CMD_TEST_M6: return "test_m6";
        case CMD_TEST_ALL: return "test_all";
        case CMD_MOVE_MOTOR: return "move_motor";
        case CMD_SET_SPEED: return "set_speed";
        case CMD_LOAD_CONFIG: return "load_config";
        case CMD_SHOW_CONFIG: return "show_config";
        case CMD_GET_STATUS: return "get_status";
        case CMD_STOP_ALL: return "stop_all";
        case CMD_AIM: return "aim";
        case CMD_SET_POSITION: return "set_position";
        case CMD_AUTO_CALIBRATE: return "auto_calibrate";
        default: return "unknown";
    }
}

// Простой поиск значения по ключу в JSON строке
JSON_Result_t JSON_GetStringValue(const char* json, const char* key, char* value, uint8_t max_len)
{
    // Ищем "key":"value"
    const char* key_pos = json;
    uint8_t key_len = simple_strlen(key);
    
    while(*key_pos) {
        // Ищем ключ в кавычках
        if(*key_pos == '"') {
            key_pos++;
            if(simple_strlen(key_pos) >= key_len) {
                uint8_t match = 1;
                for(uint8_t i = 0; i < key_len; i++) {
                    if(key_pos[i] != key[i]) {
                        match = 0;
                        break;
                    }
                }
                if(match && key_pos[key_len] == '"') {
                    // Нашли ключ, ищем значение
                    key_pos += key_len + 1; // пропускаем ключ и "
                    while(*key_pos && (*key_pos == ' ' || *key_pos == ':')) key_pos++;
                    if(*key_pos == '"') {
                        key_pos++; // пропускаем "
                        uint8_t i = 0;
                        while(*key_pos && *key_pos != '"' && i < (max_len - 1)) {
                            value[i++] = *key_pos++;
                        }
                        value[i] = '\0';
                        return JSON_OK;
                    }
                }
            }
        }
        key_pos++;
    }
    return JSON_ERROR;
}

// Получить числовое значение
int32_t JSON_GetIntValue(const char* json, const char* key)
{
    char value_str[20];
    if(JSON_GetStringValue(json, key, value_str, sizeof(value_str)) == JSON_OK) {
        // Простое преобразование строки в число
        int32_t result = 0;
        uint8_t negative = 0;
        uint8_t i = 0;
        
        if(value_str[0] == '-') {
            negative = 1;
            i = 1;
        }
        
        while(value_str[i] >= '0' && value_str[i] <= '9') {
            result = result * 10 + (value_str[i] - '0');
            i++;
        }
        
        return negative ? -result : result;
    }
    return 0;
}

// Основная функция парсинга JSON
JSON_Result_t JSON_Parse(const char* json_string, JSON_ParsedCommand_t* cmd)
{
    if(!json_string || !cmd) return JSON_INVALID_FORMAT;
    
    // Очищаем структуру команды
    cmd->command = CMD_UNKNOWN;
    cmd->motor[0] = '\0';
    cmd->direction[0] = '\0';
    cmd->steps = 0;
    cmd->speed = 0;
    cmd->step_delay = 0;
    cmd->pulse_width = 0;
    cmd->duration = 0;
    cmd->angle = 0;
    cmd->horizontal = 0;
    cmd->vertical = 0;
    
    // Получаем команду
    char cmd_str[JSON_MAX_CMD_LEN];
    if(JSON_GetStringValue(json_string, "cmd", cmd_str, sizeof(cmd_str)) != JSON_OK) {
        return JSON_INVALID_FORMAT;
    }
    
    cmd->command = JSON_StringToCommand(cmd_str);
    if(cmd->command == CMD_UNKNOWN) {
        return JSON_UNKNOWN_COMMAND;
    }
    
    // Получаем дополнительные параметры в зависимости от команды
    JSON_GetStringValue(json_string, "motor", cmd->motor, sizeof(cmd->motor));
    JSON_GetStringValue(json_string, "direction", cmd->direction, sizeof(cmd->direction));
    cmd->steps = JSON_GetIntValue(json_string, "steps");
    cmd->speed = JSON_GetIntValue(json_string, "speed");
    cmd->step_delay = JSON_GetIntValue(json_string, "step_delay");
    cmd->pulse_width = JSON_GetIntValue(json_string, "pulse_width");
    cmd->duration = JSON_GetIntValue(json_string, "duration");
    cmd->angle = JSON_GetIntValue(json_string, "angle");
    cmd->horizontal = JSON_GetIntValue(json_string, "horizontal");
    cmd->vertical = JSON_GetIntValue(json_string, "vertical");
    
    return JSON_OK;
}

// Выполнение команды
JSON_Result_t JSON_ExecuteCommand(const JSON_ParsedCommand_t* cmd)
{
    if(!cmd) return JSON_ERROR;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    
    switch(cmd->command) {
        case CMD_RESET:
            LCD_SendString((uint8_t *)"JSON: RESET command ",20);
            Return_All_Motors_Home();
            break;

        case CMD_READY:
            LCD_SendString((uint8_t *)"JSON: READY command ",20);
            Ready_Command();
            // Отправляем JSON ответ
            Send_To_Main((uint8_t*)"{\"status\":\"ready\",\"command\":\"ready\",\"system\":\"cordon-82\"}\r\n", 58);
            break;

        case CMD_ABOUT:
            LCD_SendString((uint8_t *)"JSON: ABOUT command ",20);
            Show_About_Page();
            // Отправляем JSON ответ
            Send_To_Main((uint8_t*)"{\"status\":\"ok\",\"command\":\"about\",\"version\":\"1.0\",\"device\":\"CORDON-82\"}\r\n", 75);
            break;

        case CMD_PLAY_MELODY:
            LCD_SendString((uint8_t *)"JSON: MELODY command",20);
            Play_Piano_Melody();
            break;

        case CMD_FIRE:
            LCD_SendString((uint8_t *)"JSON: FIRE command  ",20);
            // TODO: реализовать команду fire
            break;
            
        case CMD_TEST_M6:
            LCD_SendString((uint8_t *)"JSON: TEST M6 SAFE  ",20);
            Rotate_M6_Step_Safe(M6_Forward);
            // Отправляем JSON ответ
            Send_To_Main((uint8_t*)"{\"status\":\"ok\",\"command\":\"test_m6\",\"result\":\"executed\"}\r\n", 60);
            break;
            
        case CMD_TEST_ALL:
            LCD_SendString((uint8_t *)"JSON: TEST ALL      ",20);
            Test_All_Motors_Max_Speed();
            break;
            
        case CMD_SET_SPEED:
            LCD_SendString((uint8_t *)"JSON: SET SPEED     ",20);
            // cmd->motor - строка, преобразуем в номер мотора
            // cmd->speed - значение скорости
            if(cmd->motor[0] >= '1' && cmd->motor[0] <= '7') {
                uint8_t motor_num = cmd->motor[0] - '1'; // 0-based
                Set_Motor_Speed(motor_num, cmd->speed);
                Send_To_Main((uint8_t*)"{\"status\":\"ok\",\"command\":\"set_speed\"}\r\n", 39);
            } else {
                Send_To_Main((uint8_t*)"{\"status\":\"error\",\"message\":\"bad motor\"}\r\n", 47);
            }
            break;

        case CMD_MOVE_MOTOR:
            LCD_SendString((uint8_t *)"JSON: MOVE MOTOR    ",20);
            // cmd->motor, cmd->direction, cmd->steps
            if(cmd->motor[0] >= '1' && cmd->motor[0] <= '7') {
                uint8_t motor_num = cmd->motor[0] - '1';
                int dir = 0;
                if(cmd->direction[0] == 'r' || cmd->direction[0] == 'R')
                    dir = 1;
                else if(cmd->direction[0] == 'l' || cmd->direction[0] == 'L')
                    dir = 0;
                else
                    dir = 0;
                Move_Motor(motor_num, dir, cmd->steps);
                Send_To_Main((uint8_t*)"{\"status\":\"ok\",\"command\":\"move_motor\"}\r\n", 41);
            } else {
                Send_To_Main((uint8_t*)"{\"status\":\"error\",\"message\":\"bad motor\"}\r\n", 47);
            }
            break;

        case CMD_SET_POSITION:
            LCD_SendString((uint8_t *)"JSON: SET POSITION  ",20);
            // cmd->motor, cmd->angle
            if(cmd->motor[0] >= '1' && cmd->motor[0] <= '7') {
                uint8_t motor_num = cmd->motor[0] - '1';
                Set_Motor_Position(motor_num, cmd->angle);
                Send_To_Main((uint8_t*)"{\"status\":\"ok\",\"command\":\"set_position\"}\r\n", 44);
            } else {
                Send_To_Main((uint8_t*)"{\"status\":\"error\",\"message\":\"bad motor\"}\r\n", 47);
            }
            break;

        case CMD_LOAD_CONFIG:
            LCD_SendString((uint8_t *)"JSON: LOAD CONFIG   ",20);
            Load_Embedded_Config();
            break;

        case CMD_SHOW_CONFIG:
            LCD_SendString((uint8_t *)"JSON: SHOW CONFIG   ",20);
            Show_Current_Config();
            break;
            
        case CMD_STOP_ALL:
            LCD_SendString((uint8_t *)"JSON: STOP ALL      ",20);
            // Останавливаем все моторы
            M7_Stop;
            break;
            
        case CMD_AUTO_CALIBRATE:
            LCD_SendString((uint8_t *)"JSON: AUTO CALIBRATE",20);
            Auto_Calibrate_All_Motors();
            break;
            
        default:
            LCD_SendString((uint8_t *)"JSON: Unknown cmd   ",20);
            // Отправляем JSON ответ об ошибке
            Send_To_Main((uint8_t*)"{\"status\":\"error\",\"message\":\"unknown command\"}\r\n", 50);
            return JSON_UNKNOWN_COMMAND;
    }
    
    return JSON_OK;
}


