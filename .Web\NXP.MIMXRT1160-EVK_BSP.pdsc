<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1160-EVK_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for MIMXRT1160-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.1.0" date="2022-09-28">NXP CMSIS Packs based on MCUXpresso SDK 2.12.1</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-05-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1166_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT1160-EVK">
      <description>MIMXRT1160-EVK: i.MX RT1160 Evaluation Kit</description>
      <image small="boards/evkmimxrt1160/evkmimxrt1160.png"/>
      <book category="overview" name="https://www.nxp.com/pip/MIMXRT1160-EVK" title="MIMXRT1160-EVK: i.MX RT1160 Evaluation Kit" public="true"/>
      <mountedDevice Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MIMXRT1166xxxxx.internal_condition">
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1165.internal_condition">
      <accept Dname="MIMXRT1165CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1165XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1166.internal_condition">
      <accept Dname="MIMXRT1166CVM5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166DVM6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1166XVM5A" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.evkmimxrt1160.condition_id">
      <require condition="allOf.component.lpuart_adapter, device_id=MIMXRT1166xxxxx, device.MIMXRT1166_startup, driver.clock, driver.common, driver.dcdc_soc, driver.igpio, driver.iomuxc, driver.lpuart, driver.nic301, driver.pmu_1, driver.xip_board.evkmimxrt1160, driver.xip_device, driver.xmcd.evkmimxrt1160, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite, board=evkmimxrt1160.internal_condition"/>
    </condition>
    <condition id="allOf.component.lpuart_adapter, device_id=MIMXRT1166xxxxx, device.MIMXRT1166_startup, driver.clock, driver.common, driver.dcdc_soc, driver.igpio, driver.iomuxc, driver.lpuart, driver.nic301, driver.pmu_1, driver.xip_board.evkmimxrt1160, driver.xip_device, driver.xmcd.evkmimxrt1160, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite, board=evkmimxrt1160.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="device_id.MIMXRT1166xxxxx.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dcdc_soc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iomuxc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="nic301"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="pmu_1"/>
      <require Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkmimxrt1160 xip"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xip_device"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="evkmimxrt1160 xmcd"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
      <require condition="board.evkmimxrt1160.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="board.evkmimxrt1160.internal_condition">
      <accept condition="device.MIMXRT1165.internal_condition"/>
      <accept condition="device.MIMXRT1166.internal_condition"/>
    </condition>
    <condition id="allOf.cores=cm7f.condition_id">
      <require condition="cores.cm7f.internal_condition"/>
    </condition>
    <condition id="cores.cm7f.internal_condition">
      <accept Dcore="Cortex-M7"/>
    </condition>
    <condition id="allOf.cores=cm4f.condition_id">
      <require condition="cores.cm4f.internal_condition"/>
    </condition>
    <condition id="cores.cm4f.internal_condition">
      <accept Dcore="Cortex-M4"/>
    </condition>
    <condition id="driver.xip_board.evkmimxrt1160.condition_id">
      <require condition="allOf.driver.common, device=MIMXRT1166.internal_condition"/>
    </condition>
    <condition id="allOf.driver.common, device=MIMXRT1166.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require condition="device.MIMXRT1166.internal_condition"/>
    </condition>
    <condition id="driver.xmcd.evkmimxrt1160.condition_id">
      <require condition="allOf.driver.common, board=evkmimxrt1160, device=MIMXRT1166.internal_condition"/>
    </condition>
    <condition id="allOf.driver.common, board=evkmimxrt1160, device=MIMXRT1166.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require condition="board.evkmimxrt1160.internal_condition"/>
      <require condition="device.MIMXRT1166.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/acmp/interrupt/cm4" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/acmp_interrupt_cm4.ewp"/>
        <environment name="csolution" load="acmp_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling_cm4" folder="boards/evkmimxrt1160/driver_examples/acmp/polling/cm4" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling_cm4.uvprojx"/>
        <environment name="iar" load="iar/acmp_polling_cm4.ewp"/>
        <environment name="csolution" load="acmp_polling_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_etc_hardware_trigger_conv_cm4" folder="boards/evkmimxrt1160/driver_examples/adc_etc/adc_etc_hardware_trigger_conv/cm4" doc="readme.md">
      <description>The adc_etc_software_trigger_conv example shows how to use the ADC_ETC to generate a ADC trigger by PIT channel0 trigger.Every 1 second, PIT channel0 would send a trigger signal to ADC_ETC, which can arbitrate and...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_etc_hardware_trigger_conv_cm4.uvprojx"/>
        <environment name="iar" load="iar/adc_etc_hardware_trigger_conv_cm4.ewp"/>
        <environment name="csolution" load="adc_etc_hardware_trigger_conv_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_etc_software_trigger_conv_cm4" folder="boards/evkmimxrt1160/driver_examples/adc_etc/adc_etc_software_trigger_conv/cm4" doc="readme.md">
      <description>The adc_etc_software_trigger_conv example shows how to use the ADC_ETC to generate a ADC trigger by software trigger.In this example, the ADC is configured with hardware trigger. Once ADC gets the trigger from the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_etc_software_trigger_conv_cm4.uvprojx"/>
        <environment name="iar" load="iar/adc_etc_software_trigger_conv_cm4.ewp"/>
        <environment name="csolution" load="adc_etc_software_trigger_conv_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral_cm4" folder="boards/evkmimxrt1160/demo_apps/bubble_peripheral/cm4" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis....See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral_cm4.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral_cm4.ewp"/>
        <environment name="csolution" load="bubble_peripheral_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="caam_cm4" folder="boards/evkmimxrt1160/driver_examples/caam/cm4" doc="readme.md">
      <description>The CAAM Example project is a demonstration program that uses the KSDK software to encrypt plain textand decrypt it back using AES algorithm. CBC and GCM modes are demonstrated.The symmetric key is generated at random, using CAAM's random number generator.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/caam_cm4.uvprojx"/>
        <environment name="csolution" load="caam_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cache_cm4" folder="boards/evkmimxrt1160/driver_examples/cache/cm4" doc="readme.md">
      <description>The cache example shows how to use memory cache driver.In this example, many memory (such as SDRAM, etc) and DMA will be used to show the example.Those memory is both accessible for cpu and DMA. For the memory data...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cache_cm4.uvprojx"/>
        <environment name="iar" load="iar/cache_cm4.ewp"/>
        <environment name="csolution" load="cache_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/canfd/interrupt_transfer/cm4" doc="readme.md">
      <description>The canfd_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_interrupt_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/canfd_interrupt_transfer_cm4.ewp"/>
        <environment name="csolution" load="canfd_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_cm4" folder="boards/evkmimxrt1160/driver_examples/canfd/loopback/cm4" doc="readme.md">
      <description>The canfd_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_cm4.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_cm4.ewp"/>
        <environment name="csolution" load="canfd_loopback_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/canfd/loopback_transfer/cm4" doc="readme.md">
      <description>The canfd_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_transfer_cm4.ewp"/>
        <environment name="csolution" load="canfd_loopback_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_ping_pong_buffer_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/canfd/ping_pong_buffer_transfer/cm4" doc="readme.md">
      <description>The canfd_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CANFD frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_ping_pong_buffer_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/canfd_ping_pong_buffer_transfer_cm4.ewp"/>
        <environment name="csolution" load="canfd_ping_pong_buffer_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_enet_txrx_transfer_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/enet/txrx_transfer/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_enet_txrx_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_enet_txrx_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/int_b2b_transfer/master/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/edma_b2b_transfer/master/cm4" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave/cm4" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/int_b2b_transfer/master/cm4" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/int_b2b_transfer/slave/cm4" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpuart/edma_transfer/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpuart/interrupt_transfer/cm4" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_mipi_rgb_cm4" folder="boards/evkmimxrt1160/driver_examples/csi/mipi_rgb/cm4" doc="readme.md">
      <description>This project shows how to use the CSI with MIPI_CSI. In this example, the camera device output pixel format is RGB565, the MIPI_CSI converts it to RGB888 internally and sends to CSI. In other words, the CSI input...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_mipi_rgb_cm4.uvprojx"/>
        <environment name="csolution" load="csi_mipi_rgb_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_mipi_yuv_cm4" folder="boards/evkmimxrt1160/driver_examples/csi/mipi_yuv/cm4" doc="readme.md">
      <description>This project shows how to use the CSI with MIPI_CSI. In this example, the camera device output pixel format is YUYV, the MIPI_CSI converts it to YUV internally and sends to CSI. In other words, the CSI input data bus...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_mipi_yuv_cm4.uvprojx"/>
        <environment name="csolution" load="csi_mipi_yuv_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_basic_cm4" folder="boards/evkmimxrt1160/driver_examples/dac12/basic/cm4" doc="readme.md">
      <description>The dac12_basic example shows how to use DAC12 module simply as the general DAC12 converter.When the DAC12's fifo feature is not enabled, Any write to the DATA register will replace thedata in the buffer and push...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_basic_cm4.uvprojx"/>
        <environment name="iar" load="iar/dac12_basic_cm4.ewp"/>
        <environment name="csolution" load="dac12_basic_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_fifo_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/dac12/fifo_interrupt/cm4" doc="readme.md">
      <description>The dac12_fifo_interrupt example shows how to use DAC12 FIFO interrupt.When the DAC12 FIFO watermark interrupt is enabled firstly, the application would enter the DAC12 ISR immediately, since remaining FIFO data is...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_fifo_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/dac12_fifo_interrupt_cm4.ewp"/>
        <environment name="csolution" load="dac12_fifo_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dcic_cm4" folder="boards/evkmimxrt1160/driver_examples/dcic/cm4" doc="readme.md">
      <description>This example shows how to use DCIC to monitor the display content integrity.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dcic_cm4.uvprojx"/>
        <environment name="iar" load="iar/dcic_cm4.ewp"/>
        <environment name="csolution" load="dcic_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ecompass_cm4" folder="boards/evkmimxrt1160/demo_apps/ecompass/cm4" doc="readme.md">
      <description>The E-Compass demo application demonstrates the use of the FXOS8700 sensor. The tilt-compensated algorithm calculatesall three angles (pitch, roll, and yaw or compass heading).</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecompass_cm4.uvprojx"/>
        <environment name="iar" load="iar/ecompass_cm4.ewp"/>
        <environment name="csolution" load="ecompass_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_channel_link_cm4" folder="boards/evkmimxrt1160/driver_examples/edma/channel_link/cm4" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_channel_link_cm4.uvprojx"/>
        <environment name="iar" load="iar/edma_channel_link_cm4.ewp"/>
        <environment name="csolution" load="edma_channel_link_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_interleave_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/edma/interleave_transfer/cm4" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_interleave_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/edma_interleave_transfer_cm4.ewp"/>
        <environment name="csolution" load="edma_interleave_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory_cm4" folder="boards/evkmimxrt1160/driver_examples/edma/memory_to_memory/cm4" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory_cm4.uvprojx"/>
        <environment name="iar" load="iar/edma_memory_to_memory_cm4.ewp"/>
        <environment name="csolution" load="edma_memory_to_memory_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_ping_pong_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/edma/ping_pong_transfer/cm4" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_ping_pong_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/edma_ping_pong_transfer_cm4.ewp"/>
        <environment name="csolution" load="edma_ping_pong_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather_cm4" folder="boards/evkmimxrt1160/driver_examples/edma/scatter_gather/cm4" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather_cm4.uvprojx"/>
        <environment name="iar" load="iar/edma_scatter_gather_cm4.ewp"/>
        <environment name="csolution" load="edma_scatter_gather_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_wrap_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/edma/wrap_transfer/cm4" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_wrap_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/edma_wrap_transfer_cm4.ewp"/>
        <environment name="csolution" load="edma_wrap_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="elcdif_lut_cm4" folder="boards/evkmimxrt1160/driver_examples/elcdif/lut/cm4" doc="readme.md">
      <description>The ELCDIF LUT project shows how to use the ELCDIF LUT to convert 8-bit input pixelto 24-bit output pixel. There are two LUT memories, this project uses one inputframe buffer, and swithes between the two memories, so...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/elcdif_lut_cm4.uvprojx"/>
        <environment name="iar" load="iar/elcdif_lut_cm4.ewp"/>
        <environment name="csolution" load="elcdif_lut_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="elcdif_rgb_cm4" folder="boards/evkmimxrt1160/driver_examples/elcdif/rgb/cm4" doc="readme.md">
      <description>The ELCDIF RGB project shows how to drive the RGB interface LCD using eLCDIF driver.If this example runs correctly, a rectangle is moving in the screen, and the colorchanges every time it reaches the edges of the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/elcdif_rgb_cm4.uvprojx"/>
        <environment name="iar" load="iar/elcdif_rgb_cm4.ewp"/>
        <environment name="csolution" load="elcdif_rgb_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_basic_cm4" folder="boards/evkmimxrt1160/driver_examples/enc/basic/cm4" doc="readme.md">
      <description>The enc_basic example shows how to quickly start using ENC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the basic application. When...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_basic_cm4.uvprojx"/>
        <environment name="iar" load="iar/enc_basic_cm4.ewp"/>
        <environment name="csolution" load="enc_basic_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_index_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/enc/index_interrupt/cm4" doc="readme.md">
      <description>The enc_index_interrupt example shows how to use the interrupt of ENC module with ENC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_index_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/enc_index_interrupt_cm4.ewp"/>
        <environment name="csolution" load="enc_index_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_1g_txrx_multiring_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/enet_1g/txrx_multiring_transfer/cm4" doc="readme.md">
      <description>The enet_1g_txrx_multiring_transfer example shows the way to use ENET_1G driver to receive and transmit avb frame in the avb feature supported multi-ring platforms. this example is only supported in multi-ring...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_1g_txrx_multiring_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/enet_1g_txrx_multiring_transfer_cm4.ewp"/>
        <environment name="csolution" load="enet_1g_txrx_multiring_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_ptp1588_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/enet/txrx_ptp1588_transfer/cm4" doc="readme.md">
      <description>The enet_rxtx_ptp1588 example shows the way to use ENET driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_ptp1588_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_ptp1588_transfer_cm4.ewp"/>
        <environment name="csolution" load="enet_txrx_ptp1588_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/enet/txrx_transfer/cm4" doc="readme.md">
      <description>The enet_rxtx example shows the simplest way to use ENET driver for simple frame receive and transmit.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive and transmit frame.The...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_transfer_cm4.ewp"/>
        <environment name="csolution" load="enet_txrx_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm_cm4" folder="boards/evkmimxrt1160/driver_examples/ewm/cm4" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm_cm4.uvprojx"/>
        <environment name="iar" load="iar/ewm_cm4.ewp"/>
        <environment name="csolution" load="ewm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexcan/interrupt_transfer/cm4" doc="readme.md">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexcan_interrupt_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexcan_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_cm4" folder="boards/evkmimxrt1160/driver_examples/flexcan/loopback/cm4" doc="readme.md">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_cm4.ewp"/>
        <environment name="csolution" load="flexcan_loopback_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexcan/loopback_transfer/cm4" doc="readme.md">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexcan_loopback_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_ping_pong_buffer_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexcan/ping_pong_buffer_transfer/cm4" doc="readme.md">
      <description>The flexcan_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CAN frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_ping_pong_buffer_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexcan_ping_pong_buffer_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexcan_ping_pong_buffer_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/i2c/interrupt_lpi2c_transfer/cm4" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/i2c/read_accel_value_transfer/cm4" doc="readme.md">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_read_accel_value_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexio_i2c_read_accel_value_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/i2s/edma_transfer/cm4" doc="readme.md">
      <description>The flexio_i2s_EDMA example shows how to use flexio_i2s driver with EDMA:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using EDMA.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_edma_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/i2s/interrupt_transfer/cm4" doc="readme.md">
      <description>The flexio_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_interrupt_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/pwm/cm4" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm_cm4.ewp"/>
        <environment name="csolution" load="flexio_pwm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/edma_lpspi_transfer/master/cm4" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/edma_lpspi_transfer/slave/cm4" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/edma_lpspi_transfer/slave_dynamic/cm4" doc="readme.md">
      <description>The flexio_spi_slave_dynamic example shows how to use flexio spi slave driver in edma way, In this example, a flexio simulated slave connect to a lpspi master. The CS signal remains low during transfer, after master...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm4.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/int_lpspi_transfer/master/cm4" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/int_lpspi_transfer/slave/cm4" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_polling_lpspi_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/polling_lpspi_transfer/master/cm4" doc="readme.md">
      <description>The flexio_spi_master_pooling_lpspi_slave example shows how to use flexio spi master driver in polling way. In this example, a flexio simulated master connect to a lpspi slave.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_polling_lpspi_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_polling_lpspi_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="flexio_spi_polling_lpspi_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/edma_transfer/cm4" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/int_rb_transfer/cm4" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/interrupt_transfer/cm4" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/polling_transfer/cm4" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexspi/nor/edma_transfer/cm4" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_edma_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/flexspi/nor/polling_transfer/cm4" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor_polling_transfer_cm4.ewp"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_xecc_multi_error_cm4" folder="boards/evkmimxrt1160/driver_examples/xecc/flexspi/xecc_multi_error/cm4" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_xecc_multi_error_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexspi_xecc_multi_error_cm4.ewp"/>
        <environment name="csolution" load="flexspi_xecc_multi_error_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_xecc_single_error_cm4" folder="boards/evkmimxrt1160/driver_examples/xecc/flexspi/xecc_single_error/cm4" doc="readme.md">
      <description>The XECC Single Error project is a simple demonstration program of the SDK XECC driver. It supports Single Error Correction ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_xecc_single_error_cm4.uvprojx"/>
        <environment name="iar" load="iar/flexspi_xecc_single_error_cm4.ewp"/>
        <environment name="csolution" load="flexspi_xecc_single_error_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fsl_romapi_cm4" folder="boards/evkmimxrt1160/driver_examples/fsl_romapi/cm4" doc="readme.md">
      <description>The fsl_romapi example shows how to use flexspi rom api In this example, rom api will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command willbe executed, such as Write...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fsl_romapi_cm4.uvprojx"/>
        <environment name="iar" load="iar/fsl_romapi_cm4.ewp"/>
        <environment name="csolution" load="fsl_romapi_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_compare_cm4" folder="boards/evkmimxrt1160/driver_examples/gpt/compare/cm4" doc="readme.md">
      <description>The gpt_compare project is a simple demonstration program of the SDK GPT driver's output compare feature. Once content of an OCRx matches the value in GPT_CNT, output compare timer pin is toggled.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_compare_cm4.uvprojx"/>
        <environment name="iar" load="iar/gpt_compare_cm4.ewp"/>
        <environment name="csolution" load="gpt_compare_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_timer_cm4" folder="boards/evkmimxrt1160/driver_examples/gpt/timer/cm4" doc="readme.md">
      <description>The gpt_timer project is a simple demonstration program of the SDK GPT driver. It sets up the GPThardware block to trigger a periodic interrupt after every 1 second. When the GPT interrupt is triggereda message a printed on the UART terminal.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_timer_cm4.uvprojx"/>
        <environment name="iar" load="iar/gpt_timer_cm4.ewp"/>
        <environment name="csolution" load="gpt_timer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_demo_cm4" folder="boards/evkmimxrt1160/demo_apps/hello_world/cm4" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_demo_cm4.uvprojx"/>
        <environment name="iar" load="iar/hello_world_demo_cm4.ewp"/>
        <environment name="csolution" load="hello_world_demo_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_secondary_core_boot_cm4" folder="boards/evkmimxrt1160/demo_apps/hello_world_secondary_core_boot/cm4" doc="readme.md">
      <description>The hello_world_secondary_core_boot demo use cm4 core to prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use cm7 core...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_secondary_core_boot_cm4.uvprojx"/>
        <environment name="iar" load="iar/hello_world_secondary_core_boot_cm4.ewp"/>
        <environment name="csolution" load="hello_world_secondary_core_boot_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iee_apc_cm4" folder="boards/evkmimxrt1160/demo_apps/iee_apc/cm4" doc="readme.md">
      <description>The IEE APC demo application demonstrates usage of the IEE and IEE APC driver. The Inline Encryption Engine (IEE) together with IEE APC provides a means to perform inline encryption and decryption of information...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iee_apc_cm4.uvprojx"/>
        <environment name="csolution" load="iee_apc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_input_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/gpio/input_interrupt/cm4" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear registers for each GPIO pin output register. </description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_input_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/igpio_input_interrupt_cm4.ewp"/>
        <environment name="csolution" load="igpio_input_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_led_output_cm4" folder="boards/evkmimxrt1160/driver_examples/gpio/led_output/cm4" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_led_output_cm4.uvprojx"/>
        <environment name="iar" load="iar/igpio_led_output_cm4.ewp"/>
        <environment name="csolution" load="igpio_led_output_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iled_blinky_cm4" folder="boards/evkmimxrt1160/demo_apps/led_blinky/cm4" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iled_blinky_cm4.uvprojx"/>
        <environment name="csolution" load="iled_blinky_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="key_manager_cm4" folder="boards/evkmimxrt1160/driver_examples/key_manager/cm4" doc="readme.md">
      <description>The Key Manager example shows how to configure Key Manager to select PUF key as Master Key and locks this setting</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/key_manager_cm4.uvprojx"/>
        <environment name="iar" load="iar/key_manager_cm4.ewp"/>
        <environment name="csolution" load="key_manager_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="kpp_cm4" folder="boards/evkmimxrt1160/driver_examples/kpp/cm4" doc="readme.md">
      <description>The KPP Example project is a demonstration program that uses the KSDK software to manipulate the Keypad MATRIX.The example is use the continuous column and rows as 4*4 or 8*8 matrix to show the example.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kpp_cm4.uvprojx"/>
        <environment name="iar" load="iar/kpp_cm4.ewp"/>
        <environment name="csolution" load="kpp_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_alpha_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/alpha/cm4" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 layer blend feature. In this example, two layers are enabled. For each layer, a global alpha value 50% (128) is used. When the example runs, two rectangles are shown,...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_alpha_cm4.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_alpha_cm4.ewp"/>
        <environment name="csolution" load="lcdifv2_alpha_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_csc_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/csc/cm4" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 driver to do the YUV to RGB conversion. In this example, the frame buffer pixel format is YUV, the byte order in frame buffer is Y U Y V Y U Y V ... The LCDIV v2...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_csc_cm4.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_csc_cm4.ewp"/>
        <environment name="csolution" load="lcdifv2_csc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_embedded_alpha_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/embedded_alpha/cm4" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 multiple layer blend using the alpha embedded in pixel.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_embedded_alpha_cm4.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_embedded_alpha_cm4.ewp"/>
        <environment name="csolution" load="lcdifv2_embedded_alpha_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_lut_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/lut/cm4" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 color palette (LUT). In this example the pixel format is 8-bit LUT index. Although the LUT supports 256 items in this example only items 0-7 are used, because the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_lut_cm4.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_lut_cm4.ewp"/>
        <environment name="csolution" load="lcdifv2_lut_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_multi_layer_alpha_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/multi_layer_alpha/cm4" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 API to get the global alpha based on desired blended alpha, for the multi-layer blend case.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_multi_layer_alpha_cm4.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_multi_layer_alpha_cm4.ewp"/>
        <environment name="csolution" load="lcdifv2_multi_layer_alpha_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_porter_duff_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/porter_duff/cm4" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 porter duff feature. In this example, two layers are enabled and blended. The blend mode changes and the panel shows the different blend result.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_porter_duff_cm4.uvprojx"/>
        <environment name="csolution" load="lcdifv2_porter_duff_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_rgb565_cm4" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/rgb565/cm4" doc="readme.md">
      <description>The RGB565 project shows how to drive the RGB interface LCD using LCDIF driver.If this example runs correctly, a rectangle is moving in the screen, and the colorchanges every time it reaches the edges of the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_rgb565_cm4.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_rgb565_cm4.ewp"/>
        <environment name="csolution" load="lcdifv2_rgb565_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_high_sample_rate_sample_signal_cm4" folder="boards/evkmimxrt1160/demo_apps/lpadc_high_sample_rate/sample_signal/cm4" doc="readme.md">
      <description>This demo application demonstrates the use of the LPADC to sample the analog signal. In this demo, the ADC clock is set as the maximum frequency, users should input analog signals to the ADC channel, press any keys...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_high_sample_rate_sample_signal_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpadc_high_sample_rate_sample_signal_cm4.ewp"/>
        <environment name="csolution" load="lpadc_high_sample_rate_sample_signal_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/lpadc/interrupt/cm4" doc="readme.md">
      <description>The lpadc_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt_cm4.ewp"/>
        <environment name="csolution" load="lpadc_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling_cm4" folder="boards/evkmimxrt1160/driver_examples/lpadc/polling/cm4" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling_cm4.ewp"/>
        <environment name="csolution" load="lpadc_polling_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_sample_rate_count_cm4" folder="boards/evkmimxrt1160/demo_apps/lpadc_high_sample_rate/sample_rate_count/cm4" doc="readme.md">
      <description>The lpadc sample rate count demo application can be used to measure ADC's sample rate roughly. The sample rate for an ADC is defined as the number of output samples available per unit time, and is specified as...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_sample_rate_count_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpadc_sample_rate_count_cm4.ewp"/>
        <environment name="csolution" load="lpadc_sample_rate_count_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/edma_b2b_transfer/master/cm4" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/edma_b2b_transfer/slave/cm4" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/interrupt/cm4" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/interrupt_b2b_transfer/master/cm4" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/interrupt_b2b_transfer/slave/cm4" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/polling_b2b/master/cm4" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/lpi2c/polling_b2b/slave/cm4" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave_cm4.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/lpspi/edma_b2b_transfer/master/cm4" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/lpspi/edma_b2b_transfer/slave/cm4" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/lpspi/interrupt_b2b/master/cm4" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/lpspi/interrupt_b2b/slave/cm4" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master_cm4" folder="boards/evkmimxrt1160/driver_examples/lpspi/polling_b2b_transfer/master/cm4" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master_cm4.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave_cm4" folder="boards/evkmimxrt1160/driver_examples/lpspi/polling_b2b_transfer/slave/cm4" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave_cm4.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/edma_transfer/cm4" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer_cm4.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt/cm4" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_cm4.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt_rb_transfer/cm4" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer_cm4.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt_transfer/cm4" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_cm4.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt_transfer_seven_bits/cm4" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits_cm4.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/polling/cm4" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_cm4.ewp"/>
        <environment name="csolution" load="lpuart_polling_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits_cm4" folder="boards/evkmimxrt1160/driver_examples/lpuart/polling_seven_bits/cm4" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits_cm4.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits_cm4.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mipi_dsi_compliant_test_cm4" folder="boards/evkmimxrt1160/display_examples/mipi_dsi_compliant_test/cm4" doc="readme.md">
      <description>This project is used for MIPI DSI compliant test.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mipi_dsi_compliant_test_cm4.uvprojx"/>
        <environment name="csolution" load="mipi_dsi_compliant_test_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_interrupt_core1" folder="boards/evkmimxrt1160/driver_examples/mu/interrupt/core1" doc="readme.md">
      <description>The mu_interrupt example shows how to use MU driver in interrupt way:In this example:1. Core 0 send message to Core 1 in interrupt mode via MU module.2. Core 1 send message back to Core 0 in interrupt mode.3. Core 0...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_interrupt_core1.uvprojx"/>
        <environment name="iar" load="iar/mu_interrupt_core1.ewp"/>
        <environment name="csolution" load="../mu_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_polling_core1" folder="boards/evkmimxrt1160/driver_examples/mu/polling/core1" doc="readme.md">
      <description>The mu_polling example shows how to use MU driver in polling way:In this example:1. Core 0 send message to Core 1 in polling mode via MU module.2. Core 1 send message back to Core 0 in polling mode.3. Core 0 receive...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_polling_core1.uvprojx"/>
        <environment name="iar" load="iar/mu_polling_core1.ewp"/>
        <environment name="csolution" load="../mu_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project_cm4" folder="boards/evkmimxrt1160/demo_apps/new_project/cm4" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project_cm4.uvprojx"/>
        <environment name="csolution" load="new_project_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ocotp_example_cm4" folder="boards/evkmimxrt1160/driver_examples/ocotp/cm4" doc="readme.md">
      <description>The ocotp driver example shows how to operation the OCOTP register with driver API.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ocotp_example_cm4.uvprojx"/>
        <environment name="iar" load="iar/ocotp_example_cm4.ewp"/>
        <environment name="csolution" load="ocotp_example_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_edma_transfer/cm4" doc="readme.md">
      <description>pdm_edma_transfer</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_edma_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/pdm_edma_transfer_cm4.ewp"/>
        <environment name="csolution" load="pdm_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_interrupt/cm4" doc="readme.md">
      <description>The pdm_interrupt example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/pdm_interrupt_cm4.ewp"/>
        <environment name="csolution" load="pdm_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_edma_cm4" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_edma/cm4" doc="readme.md">
      <description>The pdm_sai_sdma example shows how to use pdm edma driver together with sai edma driver</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_edma_cm4.uvprojx"/>
        <environment name="csolution" load="pdm_sai_edma_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_interrupt/cm4" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_cm4.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_interrupt_transfer/cm4" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt transaction api:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_multi_channel_edma_cm4" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_multi_channel_edma/cm4" doc="readme.md">
      <description>The pdm_sai_multi_channel_edma example shows how to use pdm edma driver to record multi channel data together with sai edma driver</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_multi_channel_edma_cm4.uvprojx"/>
        <environment name="csolution" load="pdm_sai_multi_channel_edma_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pit_cm4" folder="boards/evkmimxrt1160/driver_examples/pit/cm4" doc="readme.md">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit_cm4.uvprojx"/>
        <environment name="iar" load="iar/pit_cm4.ewp"/>
        <environment name="csolution" load="pit_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_bm_dcdc_core1" folder="boards/evkmimxrt1160/demo_apps/power_mode_switch/bm_dcdc/core1" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_bm_dcdc_core1.uvprojx"/>
        <environment name="csolution" load="../power_mode_switch_bm_dcdc_rt1xxx.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="puf_cm4" folder="boards/evkmimxrt1160/driver_examples/puf/cm4" doc="readme.md">
      <description>This driver example project demonstrates how to use driver for the PUF controller which provides a secure key storage.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/puf_cm4.uvprojx"/>
        <environment name="iar" load="iar/puf_cm4.ewp"/>
        <environment name="csolution" load="puf_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm_cm4" folder="boards/evkmimxrt1160/driver_examples/pwm/cm4" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm_cm4.uvprojx"/>
        <environment name="iar" load="iar/pwm_cm4.ewp"/>
        <environment name="csolution" load="pwm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_blend_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/blend/cm4" doc="readme.md">
      <description>The PXP blend project shows how to blend process surface and alpha surface usingPXP. If this example runs correctly, you will see two rectangles moving in thescreen. One rectangle is process surface output, and the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_blend_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_blend_cm4.ewp"/>
        <environment name="csolution" load="pxp_blend_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_color_key_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/color_key/cm4" doc="readme.md">
      <description>The PXP color key project shows how to use the AS color key together with the alpha blend. In this example, the AS pixel format is RGB565, the global alpha is used for alpha blend.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_color_key_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_color_key_cm4.ewp"/>
        <environment name="csolution" load="pxp_color_key_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_copy_pic_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/copy_pic/cm4" doc="readme.md">
      <description>The PXP copy_pic project shows how to use the PXP to copy image from one buffer to another buffer.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_copy_pic_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_copy_pic_cm4.ewp"/>
        <environment name="csolution" load="pxp_copy_pic_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_fill_rectangle_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/fill_rectangle/cm4" doc="readme.md">
      <description>The PXP fill rectangle project shows how to use the PXP to draw a solid rectangle with configured color. If this example runs correctly, you will see the panel filled with red, green and blue in a row.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_fill_rectangle_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_fill_rectangle_cm4.ewp"/>
        <environment name="csolution" load="pxp_fill_rectangle_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_flip_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/flip/cm4" doc="readme.md">
      <description>The PXP flip project shows how to use the PXP flip function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The flip mode is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_flip_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_flip_cm4.ewp"/>
        <environment name="csolution" load="pxp_flip_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_lcdif_handshake_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/lcdif_handshake/cm4" doc="readme.md">
      <description>The PXP LCDIF hand shake project shows how to enable the hand shake betweenPXP and LCDIF. If this example runs correctly, you will see two rectangles moving in thescreen. One rectangle is process surface output, and...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_lcdif_handshake_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_lcdif_handshake_cm4.ewp"/>
        <environment name="csolution" load="pxp_lcdif_handshake_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_porter_duff_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/porter_duff/cm4" doc="readme.md">
      <description>This example shows how to use the PXP Porter Duff compositing. In this example, A blue rectangle is in the left up corner of the destination surface (also named PS surface, or s0 in reference mannal). A red rectangle...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_porter_duff_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_porter_duff_cm4.ewp"/>
        <environment name="csolution" load="pxp_porter_duff_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_queue_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/queue/cm4" doc="readme.md">
      <description>The PXP queue project shows how to use the PXP command queue. This exampleuse the command queue to rotate the process surface. If this exampleruns correctly, you will see a square with three color(red, green and blue).The rotate degree is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_queue_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_queue_cm4.ewp"/>
        <environment name="csolution" load="pxp_queue_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_rotate_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/rotate/cm4" doc="readme.md">
      <description>The PXP rotate project shows how to use the PXP rotate function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The rotate degree is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_rotate_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_rotate_cm4.ewp"/>
        <environment name="csolution" load="pxp_rotate_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_scale_cm4" folder="boards/evkmimxrt1160/driver_examples/pxp/scale/cm4" doc="readme.md">
      <description>The PXP scale project shows how to use the PXP scale function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The square size is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_scale_cm4.uvprojx"/>
        <environment name="iar" load="iar/pxp_scale_cm4.ewp"/>
        <environment name="csolution" load="pxp_scale_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_cm4" folder="boards/evkmimxrt1160/driver_examples/qtmr/inputcapture_outputpwm/cm4" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature.The example sets up a QTMR channel for input capture. Once the input signal is received,this example will...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_cm4.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_cm4.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_dma_cm4" folder="boards/evkmimxrt1160/driver_examples/qtmr/inputcapture_outputpwm_dma/cm4" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature by DMA.The example sets up a QTMR channel for input capture. Once the input signal is received,this example...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_dma_cm4.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_dma_cm4.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_dma_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_timer_cm4" folder="boards/evkmimxrt1160/driver_examples/qtmr/timer/cm4" doc="readme.md">
      <description>The QTMR project is a simple demonstration program of the SDK QTMR driver to use QTMR as a timer.The quad-timer module provides four timer channels with a variety of controls affecting both individualand...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_timer_cm4.uvprojx"/>
        <environment name="iar" load="iar/qtmr_timer_cm4.ewp"/>
        <environment name="csolution" load="qtmr_timer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rdc_cm4" folder="boards/evkmimxrt1160/driver_examples/rdc/cm4" doc="readme.md">
      <description>The RDC example shows how to control the peripheral and memory regionasscess policy using RDC and RDC_SEMA42</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rdc_cm4.uvprojx"/>
        <environment name="iar" load="iar/rdc_cm4.ewp"/>
        <environment name="csolution" load="rdc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_half_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/sai/edma_ping_pong_buffer_half_interrupt/cm4" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer_half_interrupt example shows how to use sai driver with EDMA half interrupt feature: In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_half_interrupt_cm4.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_half_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback_cm4" folder="boards/evkmimxrt1160/driver_examples/sai/edma_record_playback/cm4" doc="readme.md">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback_cm4.uvprojx"/>
        <environment name="csolution" load="sai_edma_record_playback_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/sai/edma_transfer/cm4" doc="readme.md">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="sai_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_cm4" folder="boards/evkmimxrt1160/driver_examples/sai/interrupt/cm4" doc="readme.md">
      <description>The sai_interrupt example shows how to use sai functional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_cm4.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback_cm4" folder="boards/evkmimxrt1160/driver_examples/sai/interrupt_record_playback/cm4" doc="readme.md">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback_cm4.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_record_playback_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/sai/interrupt_transfer/cm4" doc="readme.md">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer_cm4.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema4_basic_cm4" folder="boards/evkmimxrt1160/driver_examples/sema4/basic/cm4" doc="readme.md">
      <description>The sema4 basic example shows how to use SEMA4 driver to do basic gate operaions, including lock, unlock, and reset.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema4_basic_cm4.uvprojx"/>
        <environment name="iar" load="iar/sema4_basic_cm4.ewp"/>
        <environment name="csolution" load="sema4_basic_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema4_dualcore_core1" folder="boards/evkmimxrt1160/driver_examples/sema4/dualcore/core1" doc="readme.md">
      <description>This example shows how to use the SEMA4 gate lock and unlock, and the gate notify feature. In this example, core 0 sends command to core 1 through MU. According to the commands, core 1 locks and unlocks the specific...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema4_dualcore_core1.uvprojx"/>
        <environment name="iar" load="iar/sema4_dualcore_core1.ewp"/>
        <environment name="csolution" load="../sema4_dualcore.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_cm4" folder="boards/evkmimxrt1160/driver_examples/semc/sdram/cm4" doc="readme.md">
      <description>The sdramc example shows how to use SEMC controller driver to initialize the external SDRAM chip.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_cm4.uvprojx"/>
        <environment name="iar" load="iar/semc_cm4.ewp"/>
        <environment name="csolution" load="semc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_xecc_multi_error_cm4" folder="boards/evkmimxrt1160/driver_examples/xecc/semc/xecc_multi_error/cm4" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_xecc_multi_error_cm4.uvprojx"/>
        <environment name="csolution" load="semc_xecc_multi_error_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_xecc_single_error_cm4" folder="boards/evkmimxrt1160/driver_examples/xecc/semc/xecc_single_error/cm4" doc="readme.md">
      <description>The XECC Single Error project is a simple demonstration program of the SDK XECC driver. It supports Single Error Correction ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_xecc_single_error_cm4.uvprojx"/>
        <environment name="csolution" load="semc_xecc_single_error_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell_cm4" folder="boards/evkmimxrt1160/demo_apps/shell/cm4" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell_cm4.uvprojx"/>
        <environment name="csolution" load="shell_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="smartcard_cm4" folder="boards/evkmimxrt1160/driver_examples/smartcard/cm4" doc="readme.md">
      <description>This example demonstrates the SDK Peripheral drivers working with different methods.This example demonstrates use of smartcard driver API to read GSM sim card ICC-ID (Integrated circuit card identifier,which should...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/smartcard_cm4.uvprojx"/>
        <environment name="csolution" load="smartcard_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_hp_hac_cm4" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_hp_hac/cm4" doc="readme.md">
      <description>The SNVS HP HAC project shows how to use the High Assurance Counter (HAC) based on SDK SNVS HP driver. In this example, The HAC is enabled and set a initial value. Software fatal security violation is triggered, and...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_hp_hac_cm4.uvprojx"/>
        <environment name="iar" load="iar/snvs_hp_hac_cm4.ewp"/>
        <environment name="csolution" load="snvs_hp_hac_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_hp_rtc_cm4" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_hp_rtc/cm4" doc="readme.md">
      <description>The SNVS HP RTC project is a simple demonstration program of the SDK SNVS HP driver. The test will set up RTC date and time to a predefined value and starts the counter. RTC then triggers an alarm after a user...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_hp_rtc_cm4.uvprojx"/>
        <environment name="iar" load="iar/snvs_hp_rtc_cm4.ewp"/>
        <environment name="csolution" load="snvs_hp_rtc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_mc_cm4" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_lp_mc/cm4" doc="readme.md">
      <description>The SNVS LP MC project shows how to use the Monotonic Counter (MC) based on SDK SNVS LP driver. In this example, the MC value is increased and checked several times.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_mc_cm4.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_mc_cm4.ewp"/>
        <environment name="csolution" load="snvs_lp_mc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_srtc_cm4" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_lp_srtc/cm4" doc="readme.md">
      <description>The SNVS LP SRTC project is a simple demonstration program of the SDK SNVS LP driver. The test will set up secure RTC (SRTC) date and time to a predefined value and starts the counter, then the SRTC counter value is...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_srtc_cm4.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_srtc_cm4.ewp"/>
        <environment name="csolution" load="snvs_lp_srtc_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_sw_zmk_cm4" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_sw_zmk/cm4" doc="readme.md">
      <description>The SNVS SW ZMK project shows how to provision the zeroizable master key (ZMK) by software based on SDK SNVS driver. In this example, ZMK key value is set and ECC is enabled. When change the ZMK key value, violation detected and ZMK key is zeroized.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_sw_zmk_cm4.uvprojx"/>
        <environment name="iar" load="iar/snvs_sw_zmk_cm4.ewp"/>
        <environment name="csolution" load="snvs_sw_zmk_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_edma_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/spdif/edma_transfer/cm4" doc="readme.md">
      <description>The spdif_edma_transfer example shows how to use spdif driver with edma:In this example, one spdif instance playbacks the audio data recorded by same spdif instance using edma.Notice: Please use 48KHz sample rate for...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_edma_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/spdif_edma_transfer_cm4.ewp"/>
        <environment name="csolution" load="spdif_edma_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_interrupt_transfer_cm4" folder="boards/evkmimxrt1160/driver_examples/spdif/interrupt_transfer/cm4" doc="readme.md">
      <description>The spdif_interrupt_transfer example shows how to use spdif driver with interrupt:In this example, one spdif instance playbacks the audio data recorded by the same spdif instance using interrupt.Notice: Please use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_interrupt_transfer_cm4.uvprojx"/>
        <environment name="iar" load="iar/spdif_interrupt_transfer_cm4.ewp"/>
        <environment name="csolution" load="spdif_interrupt_transfer_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ssarc_software_trigger_cm4" folder="boards/evkmimxrt1160/driver_examples/ssarc/software_trigger/cm4" doc="readme.md">
      <description>The ssarc_software_trigger project is a simple demonstration program of the SDK SSARC driver. It save the memory contains and restore the memory contains based on software method.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ssarc_software_trigger_cm4.uvprojx"/>
        <environment name="iar" load="iar/ssarc_software_trigger_cm4.ewp"/>
        <environment name="csolution" load="ssarc_software_trigger_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tee_fault_core1" folder="boards/evkmimxrt1160/demo_apps/tee_fault/core1" doc="readme.md">
      <description>This example shows how to use the MCUXpresso Config Tools TEE tool.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tee_fault_core1.uvprojx"/>
        <environment name="iar" load="iar/tee_fault_core1.ewp"/>
        <environment name="csolution" load="../tee_fault.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tempsensor_cm4" folder="boards/evkmimxrt1160/driver_examples/tempsensor/cm4" doc="readme.md">
      <description>The TEMPSENSOR project is a simple demonstration program of the SDK TEMPSENSOR driver.The temperatue sensor (TEMPSENSOR) module features alarm functions that can raise independent interrupt signals if the temperature...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tempsensor_cm4.uvprojx"/>
        <environment name="iar" load="iar/tempsensor_cm4.ewp"/>
        <environment name="csolution" load="tempsensor_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar_aoi_cm4" folder="boards/evkmimxrt1160/demo_apps/xbar_aoi/cm4" doc="readme.md">
      <description>The XBAR AOI demo application demonstrates the usage of the XBAR, AOI peripheral to combine interruptfrom CMP and PIT. If the button is pressed(CMP interrupt) and the PIT periodic interrupt occurs a message is printed.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar_aoi_cm4.uvprojx"/>
        <environment name="iar" load="iar/xbar_aoi_cm4.ewp"/>
        <environment name="csolution" load="xbar_aoi_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbara_cm4" folder="boards/evkmimxrt1160/driver_examples/xbara/cm4" doc="readme.md">
      <description>The Xbara project is a simple demonstration program of the SDK Xbara driver.The intended applicationof this module is to provide a flexible crossbar switch function that allows any input to beconnected to any output...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbara_cm4.uvprojx"/>
        <environment name="iar" load="iar/xbara_cm4.ewp"/>
        <environment name="csolution" load="xbara_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xrdc2_cm4" folder="boards/evkmimxrt1160/driver_examples/xrdc2/cm4" doc="readme.md">
      <description>The xrdc2 example shows how to control the memory and peripheral accesspolicy using XRDC.In this example, one peripheral and a memory region are set to unaccessible, thenthe hardfault occurs.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xrdc2_cm4.uvprojx"/>
        <environment name="iar" load="iar/xrdc2_cm4.ewp"/>
        <environment name="csolution" load="xrdc2_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/acmp/interrupt/cm7" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/acmp_interrupt_cm7.ewp"/>
        <environment name="csolution" load="acmp_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling_cm7" folder="boards/evkmimxrt1160/driver_examples/acmp/polling/cm7" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling_cm7.uvprojx"/>
        <environment name="iar" load="iar/acmp_polling_cm7.ewp"/>
        <environment name="csolution" load="acmp_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_etc_hardware_trigger_conv_cm7" folder="boards/evkmimxrt1160/driver_examples/adc_etc/adc_etc_hardware_trigger_conv/cm7" doc="readme.md">
      <description>The adc_etc_software_trigger_conv example shows how to use the ADC_ETC to generate a ADC trigger by PIT channel0 trigger.Every 1 second, PIT channel0 would send a trigger signal to ADC_ETC, which can arbitrate and...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_etc_hardware_trigger_conv_cm7.uvprojx"/>
        <environment name="iar" load="iar/adc_etc_hardware_trigger_conv_cm7.ewp"/>
        <environment name="csolution" load="adc_etc_hardware_trigger_conv_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_etc_software_trigger_conv_cm7" folder="boards/evkmimxrt1160/driver_examples/adc_etc/adc_etc_software_trigger_conv/cm7" doc="readme.md">
      <description>The adc_etc_software_trigger_conv example shows how to use the ADC_ETC to generate a ADC trigger by software trigger.In this example, the ADC is configured with hardware trigger. Once ADC gets the trigger from the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_etc_software_trigger_conv_cm7.uvprojx"/>
        <environment name="iar" load="iar/adc_etc_software_trigger_conv_cm7.ewp"/>
        <environment name="csolution" load="adc_etc_software_trigger_conv_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_edma_cm7" folder="boards/evkmimxrt1160/driver_examples/asrc/asrc_m2m_edma/cm7" doc="readme.md">
      <description>The asrc_m2m_edma example shows how to use asrc driver with edma:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_edma_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/asrc/asrc_m2m_interrupt/cm7" doc="readme.md">
      <description>The asrc_m2m_interrupt example shows how to use asrc driver with interrupt:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_polling_cm7" folder="boards/evkmimxrt1160/driver_examples/asrc/asrc_m2m_polling/cm7" doc="readme.md">
      <description>The asrc_m2m_polling example shows how to use asrc driver with polling:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_polling_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_p2p_out_edma_cm7" folder="boards/evkmimxrt1160/driver_examples/asrc/asrc_p2p_out_edma/cm7" doc="readme.md">
      <description>The asrc_p2p_out_edma example shows how to use asrc driver with edma:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_p2p_out_edma_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_p2p_out_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral_cm7" folder="boards/evkmimxrt1160/demo_apps/bubble_peripheral/cm7" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis....See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral_cm7.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral_cm7.ewp"/>
        <environment name="csolution" load="bubble_peripheral_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="caam_cm7" folder="boards/evkmimxrt1160/driver_examples/caam/cm7" doc="readme.md">
      <description>The CAAM Example project is a demonstration program that uses the KSDK software to encrypt plain textand decrypt it back using AES algorithm. CBC and GCM modes are demonstrated.The symmetric key is generated at random, using CAAM's random number generator.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/caam_cm7.uvprojx"/>
        <environment name="csolution" load="caam_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cache_cm7" folder="boards/evkmimxrt1160/driver_examples/cache/cm7" doc="readme.md">
      <description>The cache example shows how to use memory cache driver.In this example, many memory (such as SDRAM, etc) and DMA will be used to show the example.Those memory is both accessible for cpu and DMA. For the memory data...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cache_cm7.uvprojx"/>
        <environment name="iar" load="iar/cache_cm7.ewp"/>
        <environment name="csolution" load="cache_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/canfd/interrupt_transfer/cm7" doc="readme.md">
      <description>The canfd_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="canfd_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_cm7" folder="boards/evkmimxrt1160/driver_examples/canfd/loopback/cm7" doc="readme.md">
      <description>The canfd_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_cm7.ewp"/>
        <environment name="csolution" load="canfd_loopback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/canfd/loopback_transfer/cm7" doc="readme.md">
      <description>The canfd_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_transfer_cm7.ewp"/>
        <environment name="csolution" load="canfd_loopback_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_ping_pong_buffer_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/canfd/ping_pong_buffer_transfer/cm7" doc="readme.md">
      <description>The canfd_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CANFD frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_ping_pong_buffer_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_ping_pong_buffer_transfer_cm7.ewp"/>
        <environment name="csolution" load="canfd_ping_pong_buffer_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cdog_cm7" folder="boards/evkmimxrt1160/driver_examples/cdog/cm7" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog_cm7.uvprojx"/>
        <environment name="iar" load="iar/cdog_cm7.ewp"/>
        <environment name="csolution" load="cdog_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_enet_txrx_transfer_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/enet/txrx_transfer/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_enet_txrx_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_enet_txrx_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/int_b2b_transfer/master/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/int_b2b_transfer/master/cm7" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpspi/int_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpuart/edma_transfer/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/cmsis_driver_examples/lpuart/interrupt_transfer/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_mipi_rgb_cm7" folder="boards/evkmimxrt1160/driver_examples/csi/mipi_rgb/cm7" doc="readme.md">
      <description>This project shows how to use the CSI with MIPI_CSI. In this example, the camera device output pixel format is RGB565, the MIPI_CSI converts it to RGB888 internally and sends to CSI. In other words, the CSI input...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_mipi_rgb_cm7.uvprojx"/>
        <environment name="csolution" load="csi_mipi_rgb_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_mipi_yuv_cm7" folder="boards/evkmimxrt1160/driver_examples/csi/mipi_yuv/cm7" doc="readme.md">
      <description>This project shows how to use the CSI with MIPI_CSI. In this example, the camera device output pixel format is YUYV, the MIPI_CSI converts it to YUV internally and sends to CSI. In other words, the CSI input data bus...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_mipi_yuv_cm7.uvprojx"/>
        <environment name="csolution" load="csi_mipi_yuv_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_basic_cm7" folder="boards/evkmimxrt1160/driver_examples/dac12/basic/cm7" doc="readme.md">
      <description>The dac12_basic example shows how to use DAC12 module simply as the general DAC12 converter.When the DAC12's fifo feature is not enabled, Any write to the DATA register will replace thedata in the buffer and push...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_basic_cm7.uvprojx"/>
        <environment name="iar" load="iar/dac12_basic_cm7.ewp"/>
        <environment name="csolution" load="dac12_basic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_fifo_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/dac12/fifo_interrupt/cm7" doc="readme.md">
      <description>The dac12_fifo_interrupt example shows how to use DAC12 FIFO interrupt.When the DAC12 FIFO watermark interrupt is enabled firstly, the application would enter the DAC12 ISR immediately, since remaining FIFO data is...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_fifo_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/dac12_fifo_interrupt_cm7.ewp"/>
        <environment name="csolution" load="dac12_fifo_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dcic_cm7" folder="boards/evkmimxrt1160/driver_examples/dcic/cm7" doc="readme.md">
      <description>This example shows how to use DCIC to monitor the display content integrity.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dcic_cm7.uvprojx"/>
        <environment name="iar" load="iar/dcic_cm7.ewp"/>
        <environment name="csolution" load="dcic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ecompass_cm7" folder="boards/evkmimxrt1160/demo_apps/ecompass/cm7" doc="readme.md">
      <description>The E-Compass demo application demonstrates the use of the FXOS8700 sensor. The tilt-compensated algorithm calculatesall three angles (pitch, roll, and yaw or compass heading).</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecompass_cm7.uvprojx"/>
        <environment name="iar" load="iar/ecompass_cm7.ewp"/>
        <environment name="csolution" load="ecompass_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_channel_link_cm7" folder="boards/evkmimxrt1160/driver_examples/edma/channel_link/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_channel_link_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma_channel_link_cm7.ewp"/>
        <environment name="csolution" load="edma_channel_link_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_interleave_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/edma/interleave_transfer/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_interleave_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma_interleave_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma_interleave_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory_cm7" folder="boards/evkmimxrt1160/driver_examples/edma/memory_to_memory/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma_memory_to_memory_cm7.ewp"/>
        <environment name="csolution" load="edma_memory_to_memory_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_ping_pong_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/edma/ping_pong_transfer/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_ping_pong_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma_ping_pong_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma_ping_pong_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather_cm7" folder="boards/evkmimxrt1160/driver_examples/edma/scatter_gather/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma_scatter_gather_cm7.ewp"/>
        <environment name="csolution" load="edma_scatter_gather_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_wrap_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/edma/wrap_transfer/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_wrap_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma_wrap_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma_wrap_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="elcdif_lut_cm7" folder="boards/evkmimxrt1160/driver_examples/elcdif/lut/cm7" doc="readme.md">
      <description>The ELCDIF LUT project shows how to use the ELCDIF LUT to convert 8-bit input pixelto 24-bit output pixel. There are two LUT memories, this project uses one inputframe buffer, and swithes between the two memories, so...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/elcdif_lut_cm7.uvprojx"/>
        <environment name="iar" load="iar/elcdif_lut_cm7.ewp"/>
        <environment name="csolution" load="elcdif_lut_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="elcdif_rgb_cm7" folder="boards/evkmimxrt1160/driver_examples/elcdif/rgb/cm7" doc="readme.md">
      <description>The ELCDIF RGB project shows how to drive the RGB interface LCD using eLCDIF driver.If this example runs correctly, a rectangle is moving in the screen, and the colorchanges every time it reaches the edges of the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/elcdif_rgb_cm7.uvprojx"/>
        <environment name="iar" load="iar/elcdif_rgb_cm7.ewp"/>
        <environment name="csolution" load="elcdif_rgb_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_basic_cm7" folder="boards/evkmimxrt1160/driver_examples/enc/basic/cm7" doc="readme.md">
      <description>The enc_basic example shows how to quickly start using ENC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the basic application. When...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_basic_cm7.uvprojx"/>
        <environment name="iar" load="iar/enc_basic_cm7.ewp"/>
        <environment name="csolution" load="enc_basic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_index_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/enc/index_interrupt/cm7" doc="readme.md">
      <description>The enc_index_interrupt example shows how to use the interrupt of ENC module with ENC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_index_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/enc_index_interrupt_cm7.ewp"/>
        <environment name="csolution" load="enc_index_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_1g_txrx_multiring_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/enet_1g/txrx_multiring_transfer/cm7" doc="readme.md">
      <description>The enet_1g_txrx_multiring_transfer example shows the way to use ENET_1G driver to receive and transmit avb frame in the avb feature supported multi-ring platforms. this example is only supported in multi-ring...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_1g_txrx_multiring_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/enet_1g_txrx_multiring_transfer_cm7.ewp"/>
        <environment name="csolution" load="enet_1g_txrx_multiring_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_ptp1588_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/enet/txrx_ptp1588_transfer/cm7" doc="readme.md">
      <description>The enet_rxtx_ptp1588 example shows the way to use ENET driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_ptp1588_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_ptp1588_transfer_cm7.ewp"/>
        <environment name="csolution" load="enet_txrx_ptp1588_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/enet/txrx_transfer/cm7" doc="readme.md">
      <description>The enet_rxtx example shows the simplest way to use ENET driver for simple frame receive and transmit.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive and transmit frame.The...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_transfer_cm7.ewp"/>
        <environment name="csolution" load="enet_txrx_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm_cm7" folder="boards/evkmimxrt1160/driver_examples/ewm/cm7" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm_cm7.uvprojx"/>
        <environment name="iar" load="iar/ewm_cm7.ewp"/>
        <environment name="csolution" load="ewm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexcan/interrupt_transfer/cm7" doc="readme.md">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_cm7" folder="boards/evkmimxrt1160/driver_examples/flexcan/loopback/cm7" doc="readme.md">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_cm7.ewp"/>
        <environment name="csolution" load="flexcan_loopback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexcan/loopback_transfer/cm7" doc="readme.md">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_loopback_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_ping_pong_buffer_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexcan/ping_pong_buffer_transfer/cm7" doc="readme.md">
      <description>The flexcan_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CAN frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_ping_pong_buffer_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_ping_pong_buffer_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_ping_pong_buffer_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/i2c/interrupt_lpi2c_transfer/cm7" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/i2c/read_accel_value_transfer/cm7" doc="readme.md">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_read_accel_value_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_i2c_read_accel_value_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/i2s/edma_transfer/cm7" doc="readme.md">
      <description>The flexio_i2s_EDMA example shows how to use flexio_i2s driver with EDMA:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using EDMA.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/i2s/interrupt_transfer/cm7" doc="readme.md">
      <description>The flexio_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/pwm/cm7" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm_cm7.ewp"/>
        <environment name="csolution" load="flexio_pwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/edma_lpspi_transfer/master/cm7" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/edma_lpspi_transfer/slave/cm7" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/edma_lpspi_transfer/slave_dynamic/cm7" doc="readme.md">
      <description>The flexio_spi_slave_dynamic example shows how to use flexio spi slave driver in edma way, In this example, a flexio simulated slave connect to a lpspi master. The CS signal remains low during transfer, after master...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/int_lpspi_transfer/master/cm7" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/int_lpspi_transfer/slave/cm7" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_polling_lpspi_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/spi/polling_lpspi_transfer/master/cm7" doc="readme.md">
      <description>The flexio_spi_master_pooling_lpspi_slave example shows how to use flexio spi master driver in polling way. In this example, a flexio simulated master connect to a lpspi slave.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_polling_lpspi_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_polling_lpspi_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_polling_lpspi_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/edma_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/int_rb_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/interrupt_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexio/uart/polling_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexram_magic_address_cm7" folder="boards/evkmimxrt1160/driver_examples/flexram/magic_address/cm7" doc="readme.md">
      <description>The FLEXRAM project is a simple demonstration program of the SDK FLEXRAM driver. It allocate the on-chip ram and then access the OCRAM to demo magic address and access error interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexram_magic_address_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexram_magic_address_cm7.ewp"/>
        <environment name="csolution" load="flexram_magic_address_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexram_ram_access_cm7" folder="boards/evkmimxrt1160/driver_examples/flexram/ram_access/cm7" doc="readme.md">
      <description>The FLEXRAM project is a simple demonstration program of the SDK FLEXRAM driver. It allocate the on-chip ram and then access the OCRAM to demo magic address and access error interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexram_ram_access_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexram_ram_access_cm7.ewp"/>
        <environment name="csolution" load="flexram_ram_access_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexram_ram_allocate_cm7" folder="boards/evkmimxrt1160/driver_examples/flexram/ram_allocate/cm7" doc="readme.md">
      <description>The FLEXRAM project is a simple demonstration program of the SDK FLEXRAM driver. It allocate the on-chip ram and then access the OCRAM to demo magic address and access error interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexram_ram_allocate_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexram_ram_allocate_cm7.ewp"/>
        <environment name="csolution" load="flexram_ram_allocate_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexspi/nor/edma_transfer/cm7" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/flexspi/nor/polling_transfer/cm7" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor_polling_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_xecc_multi_error_cm7" folder="boards/evkmimxrt1160/driver_examples/xecc/flexspi/xecc_multi_error/cm7" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_xecc_multi_error_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexspi_xecc_multi_error_cm7.ewp"/>
        <environment name="csolution" load="flexspi_xecc_multi_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_xecc_single_error_cm7" folder="boards/evkmimxrt1160/driver_examples/xecc/flexspi/xecc_single_error/cm7" doc="readme.md">
      <description>The XECC Single Error project is a simple demonstration program of the SDK XECC driver. It supports Single Error Correction ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_xecc_single_error_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexspi_xecc_single_error_cm7.ewp"/>
        <environment name="csolution" load="flexspi_xecc_single_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fsl_romapi_cm7" folder="boards/evkmimxrt1160/driver_examples/fsl_romapi/cm7" doc="readme.md">
      <description>The fsl_romapi example shows how to use flexspi rom api In this example, rom api will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command willbe executed, such as Write...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fsl_romapi_cm7.uvprojx"/>
        <environment name="iar" load="iar/fsl_romapi_cm7.ewp"/>
        <environment name="csolution" load="fsl_romapi_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_compare_cm7" folder="boards/evkmimxrt1160/driver_examples/gpt/compare/cm7" doc="readme.md">
      <description>The gpt_compare project is a simple demonstration program of the SDK GPT driver's output compare feature. Once content of an OCRx matches the value in GPT_CNT, output compare timer pin is toggled.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_compare_cm7.uvprojx"/>
        <environment name="iar" load="iar/gpt_compare_cm7.ewp"/>
        <environment name="csolution" load="gpt_compare_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_timer_cm7" folder="boards/evkmimxrt1160/driver_examples/gpt/timer/cm7" doc="readme.md">
      <description>The gpt_timer project is a simple demonstration program of the SDK GPT driver. It sets up the GPThardware block to trigger a periodic interrupt after every 1 second. When the GPT interrupt is triggereda message a printed on the UART terminal.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_timer_cm7.uvprojx"/>
        <environment name="iar" load="iar/gpt_timer_cm7.ewp"/>
        <environment name="csolution" load="gpt_timer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_demo_cm7" folder="boards/evkmimxrt1160/demo_apps/hello_world/cm7" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_demo_cm7.uvprojx"/>
        <environment name="iar" load="iar/hello_world_demo_cm7.ewp"/>
        <environment name="csolution" load="hello_world_demo_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_secondary_core_boot_cm7" folder="boards/evkmimxrt1160/demo_apps/hello_world_secondary_core_boot/cm7" doc="readme.md">
      <description>The hello_world_secondary_core_boot demo use cm7 core to wake up cm4 core to prints the "Hello World" string to the terminal on dual XIP way(CM7 core boot from flash, then wake up cm4 core, boot cm4 image from flash...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_secondary_core_boot_cm7.uvprojx"/>
        <environment name="iar" load="iar/hello_world_secondary_core_boot_cm7.ewp"/>
        <environment name="csolution" load="hello_world_secondary_core_boot_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iee_apc_cm7" folder="boards/evkmimxrt1160/demo_apps/iee_apc/cm7" doc="readme.md">
      <description>The IEE APC demo application demonstrates usage of the IEE and IEE APC driver. The Inline Encryption Engine (IEE) together with IEE APC provides a means to perform inline encryption and decryption of information...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iee_apc_cm7.uvprojx"/>
        <environment name="csolution" load="iee_apc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_fast_gpio_cm7" folder="boards/evkmimxrt1160/driver_examples/gpio/fast_gpio/cm7" doc="readme.md">
      <description>The Fast GPIO demos the CM7 fast GPIO usage and performance comparsion with normal GPIO operation.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_fast_gpio_cm7.uvprojx"/>
        <environment name="iar" load="iar/igpio_fast_gpio_cm7.ewp"/>
        <environment name="csolution" load="igpio_fast_gpio_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_input_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/gpio/input_interrupt/cm7" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear registers for each GPIO pin output register. </description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_input_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/igpio_input_interrupt_cm7.ewp"/>
        <environment name="csolution" load="igpio_input_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_led_output_cm7" folder="boards/evkmimxrt1160/driver_examples/gpio/led_output/cm7" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_led_output_cm7.uvprojx"/>
        <environment name="iar" load="iar/igpio_led_output_cm7.ewp"/>
        <environment name="csolution" load="igpio_led_output_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iled_blinky_cm7" folder="boards/evkmimxrt1160/demo_apps/led_blinky/cm7" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iled_blinky_cm7.uvprojx"/>
        <environment name="csolution" load="iled_blinky_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="key_manager_cm7" folder="boards/evkmimxrt1160/driver_examples/key_manager/cm7" doc="readme.md">
      <description>The Key Manager example shows how to configure Key Manager to select PUF key as Master Key and locks this setting</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/key_manager_cm7.uvprojx"/>
        <environment name="iar" load="iar/key_manager_cm7.ewp"/>
        <environment name="csolution" load="key_manager_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="kpp_cm7" folder="boards/evkmimxrt1160/driver_examples/kpp/cm7" doc="readme.md">
      <description>The KPP Example project is a demonstration program that uses the KSDK software to manipulate the Keypad MATRIX.The example is use the continuous column and rows as 4*4 or 8*8 matrix to show the example.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kpp_cm7.uvprojx"/>
        <environment name="iar" load="iar/kpp_cm7.ewp"/>
        <environment name="csolution" load="kpp_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_alpha_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/alpha/cm7" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 layer blend feature. In this example, two layers are enabled. For each layer, a global alpha value 50% (128) is used. When the example runs, two rectangles are shown,...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_alpha_cm7.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_alpha_cm7.ewp"/>
        <environment name="csolution" load="lcdifv2_alpha_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_csc_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/csc/cm7" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 driver to do the YUV to RGB conversion. In this example, the frame buffer pixel format is YUV, the byte order in frame buffer is Y U Y V Y U Y V ... The LCDIV v2...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_csc_cm7.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_csc_cm7.ewp"/>
        <environment name="csolution" load="lcdifv2_csc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_embedded_alpha_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/embedded_alpha/cm7" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 multiple layer blend using the alpha embedded in pixel.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_embedded_alpha_cm7.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_embedded_alpha_cm7.ewp"/>
        <environment name="csolution" load="lcdifv2_embedded_alpha_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_lut_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/lut/cm7" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 color palette (LUT). In this example the pixel format is 8-bit LUT index. Although the LUT supports 256 items in this example only items 0-7 are used, because the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_lut_cm7.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_lut_cm7.ewp"/>
        <environment name="csolution" load="lcdifv2_lut_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_multi_layer_alpha_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/multi_layer_alpha/cm7" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 API to get the global alpha based on desired blended alpha, for the multi-layer blend case.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_multi_layer_alpha_cm7.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_multi_layer_alpha_cm7.ewp"/>
        <environment name="csolution" load="lcdifv2_multi_layer_alpha_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_porter_duff_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/porter_duff/cm7" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF v2 porter duff feature. In this example, two layers are enabled and blended. The blend mode changes and the panel shows the different blend result.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_porter_duff_cm7.uvprojx"/>
        <environment name="csolution" load="lcdifv2_porter_duff_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdifv2_rgb565_cm7" folder="boards/evkmimxrt1160/driver_examples/lcdifv2/rgb565/cm7" doc="readme.md">
      <description>The RGB565 project shows how to drive the RGB interface LCD using LCDIF driver.If this example runs correctly, a rectangle is moving in the screen, and the colorchanges every time it reaches the edges of the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdifv2_rgb565_cm7.uvprojx"/>
        <environment name="iar" load="iar/lcdifv2_rgb565_cm7.ewp"/>
        <environment name="csolution" load="lcdifv2_rgb565_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_high_sample_rate_sample_signal_cm7" folder="boards/evkmimxrt1160/demo_apps/lpadc_high_sample_rate/sample_signal/cm7" doc="readme.md">
      <description>This demo application demonstrates the use of the LPADC to sample the analog signal. In this demo, the ADC clock is set as the maximum frequency, users should input analog signals to the ADC channel, press any keys...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_high_sample_rate_sample_signal_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_high_sample_rate_sample_signal_cm7.ewp"/>
        <environment name="csolution" load="lpadc_high_sample_rate_sample_signal_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/lpadc/interrupt/cm7" doc="readme.md">
      <description>The lpadc_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt_cm7.ewp"/>
        <environment name="csolution" load="lpadc_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling_cm7" folder="boards/evkmimxrt1160/driver_examples/lpadc/polling/cm7" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling_cm7.ewp"/>
        <environment name="csolution" load="lpadc_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_sample_rate_count_cm7" folder="boards/evkmimxrt1160/demo_apps/lpadc_high_sample_rate/sample_rate_count/cm7" doc="readme.md">
      <description>The lpadc sample rate count demo application can be used to measure ADC's sample rate roughly. The sample rate for an ADC is defined as the number of output samples available per unit time, and is specified as...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_sample_rate_count_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_sample_rate_count_cm7.ewp"/>
        <environment name="csolution" load="lpadc_sample_rate_count_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/interrupt/cm7" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/interrupt_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/interrupt_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/polling_b2b/master/cm7" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/lpi2c/polling_b2b/slave/cm7" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/lpspi/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/lpspi/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/lpspi/interrupt_b2b/master/cm7" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/lpspi/interrupt_b2b/slave/cm7" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master_cm7" folder="boards/evkmimxrt1160/driver_examples/lpspi/polling_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1160/driver_examples/lpspi/polling_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/edma_transfer/cm7" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt/cm7" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt_rb_transfer/cm7" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt_transfer/cm7" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/interrupt_transfer_seven_bits/cm7" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/polling/cm7" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_cm7.ewp"/>
        <environment name="csolution" load="lpuart_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits_cm7" folder="boards/evkmimxrt1160/driver_examples/lpuart/polling_seven_bits/cm7" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits_cm7.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mecc_multie_error_cm7" folder="boards/evkmimxrt1160/driver_examples/mecc/mecc_multi_error/cm7" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for 4 banks On-Chip RAM(OCRAM) access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mecc_multie_error_cm7.uvprojx"/>
        <environment name="iar" load="iar/mecc_multie_error_cm7.ewp"/>
        <environment name="csolution" load="mecc_multie_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mecc_single_error_cm7" folder="boards/evkmimxrt1160/driver_examples/mecc/mecc_single_error/cm7" doc="readme.md">
      <description>The MECC Single Error project is a simple demonstration program of the SDK MECC driver. It supports Single Error Correction ECC function to provide reliability for 4 banks On-Chip RAM(OCRAM) access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mecc_single_error_cm7.uvprojx"/>
        <environment name="iar" load="iar/mecc_single_error_cm7.ewp"/>
        <environment name="csolution" load="mecc_single_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mipi_dsi_compliant_test_cm7" folder="boards/evkmimxrt1160/display_examples/mipi_dsi_compliant_test/cm7" doc="readme.md">
      <description>This project is used for MIPI DSI compliant test.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mipi_dsi_compliant_test_cm7.uvprojx"/>
        <environment name="csolution" load="mipi_dsi_compliant_test_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_interrupt_core0" folder="boards/evkmimxrt1160/driver_examples/mu/interrupt/core0" doc="readme.md">
      <description>The mu_interrupt example shows how to use MU driver in interrupt way:In this example:1. Core 0 send message to Core 1 in interrupt mode via MU module.2. Core 1 send message back to Core 0 in interrupt mode.3. Core 0...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_interrupt_core0.uvprojx"/>
        <environment name="iar" load="iar/mu_interrupt_core0.ewp"/>
        <environment name="csolution" load="../mu_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_polling_core0" folder="boards/evkmimxrt1160/driver_examples/mu/polling/core0" doc="readme.md">
      <description>The mu_polling example shows how to use MU driver in polling way:In this example:1. Core 0 send message to Core 1 in polling mode via MU module.2. Core 1 send message back to Core 0 in polling mode.3. Core 0 receive...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_polling_core0.uvprojx"/>
        <environment name="iar" load="iar/mu_polling_core0.ewp"/>
        <environment name="csolution" load="../mu_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project_cm7" folder="boards/evkmimxrt1160/demo_apps/new_project/cm7" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project_cm7.uvprojx"/>
        <environment name="csolution" load="new_project_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ocotp_example_cm7" folder="boards/evkmimxrt1160/driver_examples/ocotp/cm7" doc="readme.md">
      <description>The ocotp driver example shows how to operation the OCOTP register with driver API.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ocotp_example_cm7.uvprojx"/>
        <environment name="iar" load="iar/ocotp_example_cm7.ewp"/>
        <environment name="csolution" load="ocotp_example_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_edma_transfer/cm7" doc="readme.md">
      <description>pdm_edma_transfer</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/pdm_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="pdm_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_interrupt/cm7" doc="readme.md">
      <description>The pdm_interrupt example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/pdm_interrupt_cm7.ewp"/>
        <environment name="csolution" load="pdm_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_edma_cm7" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_edma/cm7" doc="readme.md">
      <description>The pdm_sai_sdma example shows how to use pdm edma driver together with sai edma driver</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_edma_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_interrupt/cm7" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_interrupt_transfer/cm7" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt transaction api:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_multi_channel_edma_cm7" folder="boards/evkmimxrt1160/driver_examples/pdm/pdm_sai_multi_channel_edma/cm7" doc="readme.md">
      <description>The pdm_sai_multi_channel_edma example shows how to use pdm edma driver to record multi channel data together with sai edma driver</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_multi_channel_edma_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_multi_channel_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pit_cm7" folder="boards/evkmimxrt1160/driver_examples/pit/cm7" doc="readme.md">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit_cm7.uvprojx"/>
        <environment name="iar" load="iar/pit_cm7.ewp"/>
        <environment name="csolution" load="pit_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_bm_dcdc_core0" folder="boards/evkmimxrt1160/demo_apps/power_mode_switch/bm_dcdc/core0" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_bm_dcdc_core0.uvprojx"/>
        <environment name="csolution" load="../power_mode_switch_bm_dcdc_rt1xxx.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="puf_cm7" folder="boards/evkmimxrt1160/driver_examples/puf/cm7" doc="readme.md">
      <description>This driver example project demonstrates how to use driver for the PUF controller which provides a secure key storage.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/puf_cm7.uvprojx"/>
        <environment name="iar" load="iar/puf_cm7.ewp"/>
        <environment name="csolution" load="puf_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm_cm7" folder="boards/evkmimxrt1160/driver_examples/pwm/cm7" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/pwm_cm7.ewp"/>
        <environment name="csolution" load="pwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_blend_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/blend/cm7" doc="readme.md">
      <description>The PXP blend project shows how to blend process surface and alpha surface usingPXP. If this example runs correctly, you will see two rectangles moving in thescreen. One rectangle is process surface output, and the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_blend_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_blend_cm7.ewp"/>
        <environment name="csolution" load="pxp_blend_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_color_key_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/color_key/cm7" doc="readme.md">
      <description>The PXP color key project shows how to use the AS color key together with the alpha blend. In this example, the AS pixel format is RGB565, the global alpha is used for alpha blend.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_color_key_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_color_key_cm7.ewp"/>
        <environment name="csolution" load="pxp_color_key_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_copy_pic_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/copy_pic/cm7" doc="readme.md">
      <description>The PXP copy_pic project shows how to use the PXP to copy image from one buffer to another buffer.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_copy_pic_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_copy_pic_cm7.ewp"/>
        <environment name="csolution" load="pxp_copy_pic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_fill_rectangle_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/fill_rectangle/cm7" doc="readme.md">
      <description>The PXP fill rectangle project shows how to use the PXP to draw a solid rectangle with configured color. If this example runs correctly, you will see the panel filled with red, green and blue in a row.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_fill_rectangle_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_fill_rectangle_cm7.ewp"/>
        <environment name="csolution" load="pxp_fill_rectangle_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_flip_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/flip/cm7" doc="readme.md">
      <description>The PXP flip project shows how to use the PXP flip function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The flip mode is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_flip_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_flip_cm7.ewp"/>
        <environment name="csolution" load="pxp_flip_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_lcdif_handshake_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/lcdif_handshake/cm7" doc="readme.md">
      <description>The PXP LCDIF hand shake project shows how to enable the hand shake betweenPXP and LCDIF. If this example runs correctly, you will see two rectangles moving in thescreen. One rectangle is process surface output, and...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_lcdif_handshake_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_lcdif_handshake_cm7.ewp"/>
        <environment name="csolution" load="pxp_lcdif_handshake_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_porter_duff_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/porter_duff/cm7" doc="readme.md">
      <description>This example shows how to use the PXP Porter Duff compositing. In this example, A blue rectangle is in the left up corner of the destination surface (also named PS surface, or s0 in reference mannal). A red rectangle...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_porter_duff_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_porter_duff_cm7.ewp"/>
        <environment name="csolution" load="pxp_porter_duff_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_queue_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/queue/cm7" doc="readme.md">
      <description>The PXP queue project shows how to use the PXP command queue. This exampleuse the command queue to rotate the process surface. If this exampleruns correctly, you will see a square with three color(red, green and blue).The rotate degree is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_queue_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_queue_cm7.ewp"/>
        <environment name="csolution" load="pxp_queue_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_rotate_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/rotate/cm7" doc="readme.md">
      <description>The PXP rotate project shows how to use the PXP rotate function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The rotate degree is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_rotate_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_rotate_cm7.ewp"/>
        <environment name="csolution" load="pxp_rotate_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_scale_cm7" folder="boards/evkmimxrt1160/driver_examples/pxp/scale/cm7" doc="readme.md">
      <description>The PXP scale project shows how to use the PXP scale function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The square size is changing.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_scale_cm7.uvprojx"/>
        <environment name="iar" load="iar/pxp_scale_cm7.ewp"/>
        <environment name="csolution" load="pxp_scale_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_cm7" folder="boards/evkmimxrt1160/driver_examples/qtmr/inputcapture_outputpwm/cm7" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature.The example sets up a QTMR channel for input capture. Once the input signal is received,this example will...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_cm7.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_dma_cm7" folder="boards/evkmimxrt1160/driver_examples/qtmr/inputcapture_outputpwm_dma/cm7" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature by DMA.The example sets up a QTMR channel for input capture. Once the input signal is received,this example...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_dma_cm7.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_dma_cm7.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_dma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_timer_cm7" folder="boards/evkmimxrt1160/driver_examples/qtmr/timer/cm7" doc="readme.md">
      <description>The QTMR project is a simple demonstration program of the SDK QTMR driver to use QTMR as a timer.The quad-timer module provides four timer channels with a variety of controls affecting both individualand...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_timer_cm7.uvprojx"/>
        <environment name="iar" load="iar/qtmr_timer_cm7.ewp"/>
        <environment name="csolution" load="qtmr_timer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rdc_cm7" folder="boards/evkmimxrt1160/driver_examples/rdc/cm7" doc="readme.md">
      <description>The RDC example shows how to control the peripheral and memory regionasscess policy using RDC and RDC_SEMA42</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rdc_cm7.uvprojx"/>
        <environment name="iar" load="iar/rdc_cm7.ewp"/>
        <environment name="csolution" load="rdc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtwdog_cm7" folder="boards/evkmimxrt1160/driver_examples/rtwdog/cm7" doc="readme.md">
      <description>The RTWDOG Example project is to demonstrate usage of the KSDK rtwdog driver.In this example, fast testing is first implemented to test the rtwdog.After this, refreshing the watchdog in None-window mode and window...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtwdog_cm7.uvprojx"/>
        <environment name="iar" load="iar/rtwdog_cm7.ewp"/>
        <environment name="csolution" load="rtwdog_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_half_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/sai/edma_ping_pong_buffer_half_interrupt/cm7" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer_half_interrupt example shows how to use sai driver with EDMA half interrupt feature: In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_half_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_half_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback_cm7" folder="boards/evkmimxrt1160/driver_examples/sai/edma_record_playback/cm7" doc="readme.md">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_record_playback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/sai/edma_transfer/cm7" doc="readme.md">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_cm7" folder="boards/evkmimxrt1160/driver_examples/sai/interrupt/cm7" doc="readme.md">
      <description>The sai_interrupt example shows how to use sai functional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback_cm7" folder="boards/evkmimxrt1160/driver_examples/sai/interrupt_record_playback/cm7" doc="readme.md">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback_cm7.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_record_playback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/sai/interrupt_transfer/cm7" doc="readme.md">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema4_basic_cm7" folder="boards/evkmimxrt1160/driver_examples/sema4/basic/cm7" doc="readme.md">
      <description>The sema4 basic example shows how to use SEMA4 driver to do basic gate operaions, including lock, unlock, and reset.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema4_basic_cm7.uvprojx"/>
        <environment name="iar" load="iar/sema4_basic_cm7.ewp"/>
        <environment name="csolution" load="sema4_basic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema4_dualcore_core0" folder="boards/evkmimxrt1160/driver_examples/sema4/dualcore/core0" doc="readme.md">
      <description>This example shows how to use the SEMA4 gate lock and unlock, and the gate notify feature. In this example, core 0 sends command to core 1 through MU. According to the commands, core 1 locks and unlocks the specific...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema4_dualcore_core0.uvprojx"/>
        <environment name="iar" load="iar/sema4_dualcore_core0.ewp"/>
        <environment name="csolution" load="../sema4_dualcore.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_cm7" folder="boards/evkmimxrt1160/driver_examples/semc/sdram/cm7" doc="readme.md">
      <description>The sdramc example shows how to use SEMC controller driver to initialize the external SDRAM chip.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_cm7.uvprojx"/>
        <environment name="iar" load="iar/semc_cm7.ewp"/>
        <environment name="csolution" load="semc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_xecc_multi_error_cm7" folder="boards/evkmimxrt1160/driver_examples/xecc/semc/xecc_multi_error/cm7" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_xecc_multi_error_cm7.uvprojx"/>
        <environment name="csolution" load="semc_xecc_multi_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_xecc_single_error_cm7" folder="boards/evkmimxrt1160/driver_examples/xecc/semc/xecc_single_error/cm7" doc="readme.md">
      <description>The XECC Single Error project is a simple demonstration program of the SDK XECC driver. It supports Single Error Correction ECC function to provide reliability for external memory ECC region access.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_xecc_single_error_cm7.uvprojx"/>
        <environment name="csolution" load="semc_xecc_single_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell_cm7" folder="boards/evkmimxrt1160/demo_apps/shell/cm7" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell_cm7.uvprojx"/>
        <environment name="csolution" load="shell_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="smartcard_cm7" folder="boards/evkmimxrt1160/driver_examples/smartcard/cm7" doc="readme.md">
      <description>This example demonstrates the SDK Peripheral drivers working with different methods.This example demonstrates use of smartcard driver API to read GSM sim card ICC-ID (Integrated circuit card identifier,which should...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/smartcard_cm7.uvprojx"/>
        <environment name="csolution" load="smartcard_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_hp_hac_cm7" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_hp_hac/cm7" doc="readme.md">
      <description>The SNVS HP HAC project shows how to use the High Assurance Counter (HAC) based on SDK SNVS HP driver. In this example, The HAC is enabled and set a initial value. Software fatal security violation is triggered, and...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_hp_hac_cm7.uvprojx"/>
        <environment name="iar" load="iar/snvs_hp_hac_cm7.ewp"/>
        <environment name="csolution" load="snvs_hp_hac_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_hp_rtc_cm7" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_hp_rtc/cm7" doc="readme.md">
      <description>The SNVS HP RTC project is a simple demonstration program of the SDK SNVS HP driver. The test will set up RTC date and time to a predefined value and starts the counter. RTC then triggers an alarm after a user...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_hp_rtc_cm7.uvprojx"/>
        <environment name="iar" load="iar/snvs_hp_rtc_cm7.ewp"/>
        <environment name="csolution" load="snvs_hp_rtc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_mc_cm7" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_lp_mc/cm7" doc="readme.md">
      <description>The SNVS LP MC project shows how to use the Monotonic Counter (MC) based on SDK SNVS LP driver. In this example, the MC value is increased and checked several times.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_mc_cm7.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_mc_cm7.ewp"/>
        <environment name="csolution" load="snvs_lp_mc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_srtc_cm7" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_lp_srtc/cm7" doc="readme.md">
      <description>The SNVS LP SRTC project is a simple demonstration program of the SDK SNVS LP driver. The test will set up secure RTC (SRTC) date and time to a predefined value and starts the counter, then the SRTC counter value is...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_srtc_cm7.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_srtc_cm7.ewp"/>
        <environment name="csolution" load="snvs_lp_srtc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_tamper_cm7" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_lp_tamper/cm7" doc="readme.md">
      <description>The SNVS LP TAMPER project is a simple demonstration program of the SDK SNVS LP driver. The test will set up pasive and active tamper pins and also enables and test voltage, temperature and clock tampers.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_tamper_cm7.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_tamper_cm7.ewp"/>
        <environment name="csolution" load="snvs_lp_tamper_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_sw_zmk_cm7" folder="boards/evkmimxrt1160/driver_examples/snvs/snvs_sw_zmk/cm7" doc="readme.md">
      <description>The SNVS SW ZMK project shows how to provision the zeroizable master key (ZMK) by software based on SDK SNVS driver. In this example, ZMK key value is set and ECC is enabled. When change the ZMK key value, violation detected and ZMK key is zeroized.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_sw_zmk_cm7.uvprojx"/>
        <environment name="iar" load="iar/snvs_sw_zmk_cm7.ewp"/>
        <environment name="csolution" load="snvs_sw_zmk_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_edma_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/spdif/edma_transfer/cm7" doc="readme.md">
      <description>The spdif_edma_transfer example shows how to use spdif driver with edma:In this example, one spdif instance playbacks the audio data recorded by same spdif instance using edma.Notice: Please use 48KHz sample rate for...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/spdif_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="spdif_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_interrupt_transfer_cm7" folder="boards/evkmimxrt1160/driver_examples/spdif/interrupt_transfer/cm7" doc="readme.md">
      <description>The spdif_interrupt_transfer example shows how to use spdif driver with interrupt:In this example, one spdif instance playbacks the audio data recorded by the same spdif instance using interrupt.Notice: Please use...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/spdif_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="spdif_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="src_global_reset_cm7" folder="boards/evkmimxrt1160/driver_examples/src/src_global_reset/cm7" doc="readme.md">
      <description>The src_global_reset example shows how to reset the selected slice via software.s</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/src_global_reset_cm7.uvprojx"/>
        <environment name="iar" load="iar/src_global_reset_cm7.ewp"/>
        <environment name="csolution" load="src_global_reset_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ssarc_software_trigger_cm7" folder="boards/evkmimxrt1160/driver_examples/ssarc/software_trigger/cm7" doc="readme.md">
      <description>The ssarc_software_trigger project is a simple demonstration program of the SDK SSARC driver. It save the memory contains and restore the memory contains based on software method.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ssarc_software_trigger_cm7.uvprojx"/>
        <environment name="iar" load="iar/ssarc_software_trigger_cm7.ewp"/>
        <environment name="csolution" load="ssarc_software_trigger_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tee_fault_core0" folder="boards/evkmimxrt1160/demo_apps/tee_fault/core0" doc="readme.md">
      <description>This example shows how to use the MCUXpresso Config Tools TEE tool.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tee_fault_core0.uvprojx"/>
        <environment name="iar" load="iar/tee_fault_core0.ewp"/>
        <environment name="csolution" load="../tee_fault.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tempsensor_cm7" folder="boards/evkmimxrt1160/driver_examples/tempsensor/cm7" doc="readme.md">
      <description>The TEMPSENSOR project is a simple demonstration program of the SDK TEMPSENSOR driver.The temperatue sensor (TEMPSENSOR) module features alarm functions that can raise independent interrupt signals if the temperature...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tempsensor_cm7.uvprojx"/>
        <environment name="iar" load="iar/tempsensor_cm7.ewp"/>
        <environment name="csolution" load="tempsensor_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog01_cm7" folder="boards/evkmimxrt1160/driver_examples/wdog/cm7" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,implemented to test the wdog.And then after 10 times of refreshing the watchdog, a timeout reset is generated.We also try to...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog01_cm7.uvprojx"/>
        <environment name="iar" load="iar/wdog01_cm7.ewp"/>
        <environment name="csolution" load="wdog01_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar_aoi_cm7" folder="boards/evkmimxrt1160/demo_apps/xbar_aoi/cm7" doc="readme.md">
      <description>The XBAR AOI demo application demonstrates the usage of the XBAR, AOI peripheral to combine interruptfrom CMP and PIT. If the button is pressed(CMP interrupt) and the PIT periodic interrupt occurs a message is printed.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar_aoi_cm7.uvprojx"/>
        <environment name="iar" load="iar/xbar_aoi_cm7.ewp"/>
        <environment name="csolution" load="xbar_aoi_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbara_cm7" folder="boards/evkmimxrt1160/driver_examples/xbara/cm7" doc="readme.md">
      <description>The Xbara project is a simple demonstration program of the SDK Xbara driver.The intended applicationof this module is to provide a flexible crossbar switch function that allows any input to beconnected to any output...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbara_cm7.uvprojx"/>
        <environment name="iar" load="iar/xbara_cm7.ewp"/>
        <environment name="csolution" load="xbara_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xrdc2_cm7" folder="boards/evkmimxrt1160/driver_examples/xrdc2/cm7" doc="readme.md">
      <description>The xrdc2 example shows how to control the memory and peripheral accesspolicy using XRDC.In this example, one peripheral and a memory region are set to unaccessible, thenthe hardfault occurs.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xrdc2_cm7.uvprojx"/>
        <environment name="iar" load="iar/xrdc2_cm7.ewp"/>
        <environment name="csolution" load="xrdc2_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="evkmimxrt1160" Cversion="1.0.0" condition="BOARD_Project_Template.evkmimxrt1160.condition_id">
      <description>Board_project_template evkmimxrt1160</description>
      <RTE_Components_h>
#ifndef XIP_EXTERNAL_FLASH
#define XIP_EXTERNAL_FLASH 1
#endif
#ifndef XIP_BOOT_HEADER_ENABLE
#define XIP_BOOT_HEADER_ENABLE 1
#endif
#ifndef XIP_BOOT_HEADER_DCD_ENABLE
#define XIP_BOOT_HEADER_DCD_ENABLE 0
#endif
#ifndef XIP_BOOT_HEADER_XMCD_ENABLE
#define XIP_BOOT_HEADER_XMCD_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" name="boards/evkmimxrt1160/project_template/board.h" projectpath="board"/>
        <file category="sourceC" name="boards/evkmimxrt1160/project_template/board.c" projectpath="board"/>
        <file category="header" name="boards/evkmimxrt1160/project_template/clock_config.h" projectpath="board"/>
        <file category="sourceC" name="boards/evkmimxrt1160/project_template/clock_config.c" projectpath="board"/>
        <file condition="allOf.cores=cm7f.condition_id" category="header" name="boards/evkmimxrt1160/project_template/cm7/pin_mux.h" projectpath="board"/>
        <file condition="allOf.cores=cm7f.condition_id" category="sourceC" name="boards/evkmimxrt1160/project_template/cm7/pin_mux.c" projectpath="board"/>
        <file condition="allOf.cores=cm4f.condition_id" category="header" name="boards/evkmimxrt1160/project_template/cm4/pin_mux.h" projectpath="board"/>
        <file condition="allOf.cores=cm4f.condition_id" category="sourceC" name="boards/evkmimxrt1160/project_template/cm4/pin_mux.c" projectpath="board"/>
        <file category="header" name="boards/evkmimxrt1160/project_template/project_template/peripherals.h" projectpath="board"/>
        <file category="sourceC" name="boards/evkmimxrt1160/project_template/project_template/peripherals.c" projectpath="board"/>
        <file category="sourceC" name="boards/evkmimxrt1160/project_template/project_template/dcd.c" projectpath="board"/>
        <file category="header" name="boards/evkmimxrt1160/project_template/project_template/dcd.h" projectpath="board"/>
        <file category="include" name="boards/evkmimxrt1160/project_template/"/>
        <file category="include" name="boards/evkmimxrt1160/project_template/project_template/"/>
        <file condition="allOf.cores=cm7f.condition_id" category="include" name="boards/evkmimxrt1160/project_template/cm7/"/>
        <file condition="allOf.cores=cm4f.condition_id" category="include" name="boards/evkmimxrt1160/project_template/cm4/"/>
      </files>
    </component>
    <component Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkmimxrt1160 xip" Cversion="2.0.1" condition="driver.xip_board.evkmimxrt1160.condition_id">
      <description>XIP Board Driver</description>
      <files>
        <file category="sourceC" name="boards/evkmimxrt1160/xip/evkmimxrt1160_flexspi_nor_config.c" projectpath="xip"/>
        <file category="header" name="boards/evkmimxrt1160/xip/evkmimxrt1160_flexspi_nor_config.h" projectpath="xip"/>
        <file category="include" name="boards/evkmimxrt1160/xip/"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="evkmimxrt1160 xmcd" Cversion="2.0.0" condition="driver.xmcd.evkmimxrt1160.condition_id">
      <description>XMCD Driver</description>
      <files>
        <file category="sourceC" name="boards/evkmimxrt1160/xmcd/xmcd.c" projectpath="board"/>
        <file category="header" name="boards/evkmimxrt1160/xmcd/xmcd.h" projectpath="board"/>
        <file category="include" name="boards/evkmimxrt1160/xmcd/"/>
      </files>
    </component>
  </components>
</package>
