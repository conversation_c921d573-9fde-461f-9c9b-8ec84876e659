<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXA156-A8974_ISSDK_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware issdk Examples Pack for FRDM-MCXA156-A8974</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="ISSDK" vendor="NXP" version="3.0.0"/>
      <package name="MCXA156_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-MCXA156-A8974">
      <description>MCXA156 MCU evaluation kit with FRDM-STBI-A8974 sensor shield board having FXLS8974CF Ultra-low power motion wakeup sensor for Industrial, Medical IoT Applications.</description>
      <image small="boards/frdmmcxa156_a8974/frdmmcxa156_a8974.png"/>
      <mountedDevice Dname="MCXA156VLL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA146VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA146VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA146VLL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA146VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA146VPJ" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA156VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA156VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA156VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA156VPJ" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA144VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA144VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA144VLL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA144VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA144VPJ" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA145VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA145VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA145VLL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA145VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA145VPJ" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA154VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA154VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA154VLL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA154VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA154VPJ" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA155VFT" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA155VLH" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA155VLL" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA155VMP" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MCXA155VPJ" Dvendor="NXP:11"/>
    </board>
  </boards>
  <examples>
    <example name="fxls8974cf_interrupt" folder="boards/frdmmcxa156_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_interrupt" doc="readme.txt">
      <description>The FXLS8974CF Interrupt example application demonstrates the use of the FXLS8974CF sensor in interrupt Mode using I2C interface.The example demonstrates configuration sensor registers reguired to put the sensor in...See more details in readme document.</description>
      <board name="FRDM-MCXA156-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_interrupt.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxls8974cf_motion_wakeup" folder="boards/frdmmcxa156_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_motion_wakeup" doc="readme.txt">
      <description>The FXLS8974CF motion wakeup example application demonstrates motion detection and Auto-Wake/Sleep features of FXLS8974CF sensor.</description>
      <board name="FRDM-MCXA156-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_motion_wakeup.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_motion_wakeup.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxls8974cf_poll" folder="boards/frdmmcxa156_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_poll" doc="readme.txt">
      <description>The FXLS8974CF POLL example application demonstrates the use of the FXLS8974CF sensor in polling Mode using I2C interface.The example demonstrates configuration sensor registers reguired to put the sensor in Poll...See more details in readme document.</description>
      <board name="FRDM-MCXA156-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_poll.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_poll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
