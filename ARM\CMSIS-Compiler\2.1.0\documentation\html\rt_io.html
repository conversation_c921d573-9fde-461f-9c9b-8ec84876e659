<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: Low-Level I/O Retarget</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('rt_io.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Low-Level I/O Retarget </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="md_src_rt_io"></a> CMSIS-Compiler splits low-level I/O retargeting into <b>standard I/O stream</b> related components and <b>file stream</b> related components.</p>
<p>The low-level I/O retarget decision tree is shown below:</p>
<div class="image">
<img src="retarget_low_level_io.png" alt=""/>
<div class="caption">
Low-Level I/O Retarget Decision Tree</div></div>
    <p>Low-Level I/O retargeting implementation checks whether the incoming stream is a standard stream (i.e. stderr, stdin, stdout) or a file system related stream and redirects incoming request to the appropriate software component. Chapters <a class="el" href="rt_io.html#rt_io_components">Standard Stream Components</a> and <a class="el" href="rt_io.html#rt_file_interface">File Interface Components</a> explain these software components in detail.</p>
<h1><a class="anchor" id="rt_io_components"></a>
Standard Stream Components</h1>
<p>Software components that retarget the standard C library input/output streams are as follows:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadLeft">Component   </th><th class="markdownTableHeadLeft">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft"><b>STDERR</b>   </td><td class="markdownTableBodyLeft">Standard error stream of the application to output diagnostic messages.    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft"><b>STDIN</b>   </td><td class="markdownTableBodyLeft">Standard input stream going into the application (<code>scanf</code> etc.).    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft"><b>STDOUT</b>   </td><td class="markdownTableBodyLeft">Standard output stream of the application (<code>printf</code> etc.).    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft"><b>TTY</b>   </td><td class="markdownTableBodyLeft">Teletypewriter, which is the last resort for error output.   </td></tr>
</table>
<blockquote class="doxtable">
<p>&zwj;<b>Note</b></p><ul>
<li><b>TTY</b> is only available for Arm Compiler toolchain. </li>
</ul>
</blockquote>
<p>Each component can have various <a class="el" href="rt_io.html#rt_io_subcomponents">Standard Stream Subcomponents</a>.</p>
<h2><a class="anchor" id="rt_io_subcomponents"></a>
Standard Stream Subcomponents</h2>
<p>The subcomponent selection allows you to change the target hardware interface of the I/O stream.</p>
<h3><a class="anchor" id="rt_io_std_subcomponents"></a>
STDERR/STDIN/STDOUT Subcomponents</h3>
<div class="image">
<img src="retarget_io_std.png" alt=""/>
<div class="caption">
STDERR/STDIN/STDOUT subcomponents</div></div>
    <p>The following subcomponents are available:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadLeft">Variant   </th><th class="markdownTableHeadLeft">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft"><b>Breakpoint</b>   </td><td class="markdownTableBodyLeft">When the I/O stream is used, the application stops with <a href="https://developer.arm.com/documentation/100073/latest/The-Arm-C-and-C---Libraries/Support-for-building-an-application-with-the-C-library/Using-the-C-and-C---libraries-with-an-application-in-a-semihosting-environment?lang=en">BKPT</a> instruction. No additional code is required.    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft"><b>Event Recorder</b>   </td><td class="markdownTableBodyLeft">STDOUT and STDERR can be redirected using the <a href="https://arm-software.github.io/CMSIS-View/main/evr.html">Event Recorder</a> (especially interesting for targets without ITM (such as Cortex-M0/M0+/M23)).    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft"><b>ITM</b>   </td><td class="markdownTableBodyLeft">Use <a href="https://developer.arm.com/documentation/ddi0314/h/Instrumentation-Trace-Macrocell?lang=en">Instrumentation Trace Macrocell (ITM)</a> for I/O communication via the debugger (only available for Cortex-M3/M4/M7/M33/M55/M85 processors).<br  />
 Usually, data is shown in a dedicated window.<br  />
 No additional code is required to output or input data through the ITM channel. However, you have to configure the ITM channel for tracing.    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft"><b>Custom</b>   </td><td class="markdownTableBodyLeft">Retarget I/O stream to a user defined interface (such as UART or other application specific interface).<br  />
See <a class="el" href="rt_io.html#custom_subcomponent">"Custom" Subcomponent</a> for further details.   </td></tr>
</table>
<blockquote class="doxtable">
<p>&zwj;<b>Note</b></p><ul>
<li>Depending on the selected subcomponent, certain <code>#define</code> are set in the header file <b>RTE_Components.h</b> that enable the appropriate code sections in the retarget core implementation.</li>
<li>Retargeting <b>STDOUT</b> and <b>STDERR</b> using the <em>Event Recorder</em> variant is available for all Cortex-M based devices.</li>
<li>The <a href="https://developer.arm.com/documentation/100073/latest/The-Arm-C-Micro-library?lang=en">microlib</a> of Arm Compiler C run-time library interfaces to the hardware via low-level functions. It implements a reduced set of high-level functions and therefore does not implement system I/O functions. Thus, in case of using the microlib, you cannot redefine the system I/O functions. Using any of the features of the Arm Compiler component provides the <a href="https://developer.arm.com/documentation/101754/latest/armasm-Legacy-Assembler-Reference/armasm-Directives-Reference/ASSERT-directive?lang=en">assert</a> facility for microlib. </li>
</ul>
</blockquote>
<h2><a class="anchor" id="custom_subcomponent"></a>
"Custom" Subcomponent</h2>
<p>The <b>Custom</b> component provides code template that helps you to implement the retarget interface functionality for custom interfaces that are not mentioned above.</p>
<p>The following user code templates are available:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadLeft">Component   </th><th class="markdownTableHeadLeft">Name   </th><th class="markdownTableHeadLeft">File Name    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft">STDOUT:Custom   </td><td class="markdownTableBodyLeft">STDOUT User template   </td><td class="markdownTableBodyLeft"><a class="el" href="rt_template_stdout.html#stdout_user_c">stdout_user.c</a>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft">STDIN:Custom   </td><td class="markdownTableBodyLeft">STDIN User template   </td><td class="markdownTableBodyLeft"><a class="el" href="rt_template_stdin.html#stdin_user_c">stdin_user.c</a>    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft">STDERR:Custom   </td><td class="markdownTableBodyLeft">STDERR User template   </td><td class="markdownTableBodyLeft"><a class="el" href="rt_template_stderr.html#stderr_user_c">stderr_user.c</a>    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft">TTY:Custom   </td><td class="markdownTableBodyLeft">TTY User template   </td><td class="markdownTableBodyLeft"><a class="el" href="rt_template_tty.html#tty_user_c">tty_user.c</a>   </td></tr>
</table>
<h1><a class="anchor" id="rt_file_interface"></a>
File Interface Components</h1>
<p><b>CMSIS-Compiler:File Interface</b> software component provides generic shim layer interface between the C library and an arbitrary file system implementation.</p>
<p>Standard C library functions interact with files in a same manner as with standard I/O streams and offer the same retargeting interface. The existing interface is split to enable component interchangeability and <a class="el" href="group__fs__interface__api.html">File Interface</a> API is used to enable quick file system retargeting.</p>
<p>Default components are as follows:</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadLeft">Component   </th><th class="markdownTableHeadLeft">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyLeft"><b>Breakpoint</b>   </td><td class="markdownTableBodyLeft">When the file stream is used, the application stops with <a href="https://developer.arm.com/documentation/100073/latest/The-Arm-C-and-C---Libraries/Support-for-building-an-application-with-the-C-library/Using-the-C-and-C---libraries-with-an-application-in-a-semihosting-environment?lang=en">BKPT</a> instruction. No additional code is required.    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyLeft"><b>Custom</b>   </td><td class="markdownTableBodyLeft">Placeholder for custom <a class="el" href="group__fs__interface__api.html">File Interface</a> implementation   </td></tr>
</table>
<p><b>Custom</b> component is available to enable access to <a class="el" href="group__fs__interface__api.html">File Interface</a> API header file when application provides an custom implementation. Custom implementation is typically needed when file system components do not provide its own implementation. To help you implement the functionality the File Interface:Custom component also provides the <a class="el" href="rt_template_file_interface.html#retarget_fs_c">code template</a>. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
