<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss/shared/docs/product/micro/device-family-Pack/</url>
  <name>TXZ4-M4K1_DFP</name>
  <description>Toshiba TXZ4 Series TMPM4K Group (1) Device Support</description>

  <releases>
    <release version="1.0.0" date="2018-07-13">
      First Release version of TXZ4 Series TMPM4K Group (1) Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4</keyword>
    <keyword>TXZ4</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4 Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
The TXZ4 microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>

      <!-- ************************  Subfamily 'TMPM4K4Ax'  **************************** -->
      <subFamily DsubFamily="M4K(1)">

        <!-- ***********************  Device 'TMPM4K4Ax'  ************************* -->
        <device Dname="TMPM4K4FYAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K4A.h" define="TMPM4K4A"/>
          <debug svd="SVD/M4K4A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="32"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 10x10x0.5"/>
        </device>
        <device Dname="TMPM4K4FWAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K4A.h" define="TMPM4K4A"/>
          <debug svd="SVD/M4K4A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 10x10x0.5"/>
        </device>
        <device Dname="TMPM4K4FUAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K2A.h" define="TMPM4K2A"/>
          <debug svd="SVD/M4K2A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00018000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_96.FLM" start="0x00000000" size="0x00018000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 10x10x0.5"/>
        </device>
        <device Dname="TMPM4K4FSAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K4A.h" define="TMPM4K4A"/>
          <debug svd="SVD/M4K4A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_64.FLM" start="0x00000000" size="0x00010000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="32"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 10x10x0.5"/>
        </device>
        <device Dname="TMPM4K4FYAFG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K4A.h" define="TMPM4K4A"/>
          <debug svd="SVD/M4K4A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="32"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 14x14x0.8"/>
        </device>
        <device Dname="TMPM4K4FWAFG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K4A.h" define="TMPM4K4A"/>
          <debug svd="SVD/M4K4A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 14x14x0.8"/>
        </device>
        <device Dname="TMPM4K4FUAFG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K2A.h" define="TMPM4K2A"/>
          <debug svd="SVD/M4K2A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00018000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_96.FLM" start="0x00000000" size="0x00018000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 14x14x0.8"/>
        </device>
        <device Dname="TMPM4K4FSAFG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K4A.h" define="TMPM4K4A"/>
          <debug svd="SVD/M4K4A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_64.FLM" start="0x00000000" size="0x00010000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="32"                           name="DMA"/>
          <feature type="UART"          n="4"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="13"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="11"/>
          <feature type="IOs"           n="52"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="64" name="LQFP 64 14x14x0.8"/>
        </device>

      <!-- ************************  Subfamily 'TMPM4K2Ax'  **************************** -->

        <!-- ***********************  Device 'TMPM4K2Ax'  ************************* -->
        <device Dname="TMPM4K2FYADUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K2A.h" define="TMPM4K2A"/>
          <debug svd="SVD/M4K2A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="3"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="11"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="10"/>
          <feature type="IOs"           n="40"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="48" name="LQFP 48 7x7x0.5"/>
        </device>
        <device Dname="TMPM4K2FWADUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K2A.h" define="TMPM4K2A"/>
          <debug svd="SVD/M4K2A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="3"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="11"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="10"/>
          <feature type="IOs"           n="40"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="48" name="LQFP 48 7x7x0.5"/>
        </device>
        <device Dname="TMPM4K2FUADUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K2A.h" define="TMPM4K2A"/>
          <debug svd="SVD/M4K2A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00018000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_96.FLM" start="0x00000000" size="0x00018000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="3"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="11"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="10"/>
          <feature type="IOs"           n="40"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="48" name="LQFP 48 7x7x0.5"/>
        </device>
        <device Dname="TMPM4K2FSADUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K2A.h" define="TMPM4K2A"/>
          <debug svd="SVD/M4K2A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_64.FLM" start="0x00000000" size="0x00010000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="26"                           name="DMA"/>
          <feature type="UART"          n="3"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="11"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="10"/>
          <feature type="IOs"           n="40"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="48" name="LQFP 48 7x7x0.5"/>
        </device>

      <!-- ************************  Subfamily 'TMPM4K1Ax'  **************************** -->

        <!-- ***********************  Device 'TMPM4K1Ax'  ************************* -->
        <device Dname="TMPM4K1FYAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K1A.h" define="TMPM4K1A"/>
          <debug svd="SVD/M4K1A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="24"                           name="DMA"/>
          <feature type="UART"          n="2"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="10"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="9"/>
          <feature type="IOs"           n="36"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="44" name="LQFP 44 10x10x0.8"/>
        </device>
        <device Dname="TMPM4K1FWAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K1A.h" define="TMPM4K1A"/>
          <debug svd="SVD/M4K1A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="24"                           name="DMA"/>
          <feature type="UART"          n="2"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="10"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="9"/>
          <feature type="IOs"           n="36"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="44" name="LQFP 44 10x10x0.8"/>
        </device>
        <device Dname="TMPM4K1FUAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K1A.h" define="TMPM4K1A"/>
          <debug svd="SVD/M4K1A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00018000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00018000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="24"                           name="DMA"/>
          <feature type="UART"          n="2"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="10"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="9"/>
          <feature type="IOs"           n="36"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="44" name="LQFP 44 10x10x0.8"/>
        </device>
        <device Dname="TMPM4K1FSAUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K1A.h" define="TMPM4K1A"/>
          <debug svd="SVD/M4K1A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_256.FLM" start="0x00000000" size="0x00010000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="24"                           name="DMA"/>
          <feature type="UART"          n="2"/>
          <feature type="I2C"           n="1"                           name="I2C"/>
          <feature type="ADC"           n="10"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="9"/>
          <feature type="IOs"           n="36"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="44" name="LQFP 44 10x10x0.8"/>
        </device>

      <!-- ************************  Subfamily 'TMPM4K0Ax'  **************************** -->

        <!-- ***********************  Device 'TMPM4K0Ax'  ************************* -->
        <device Dname="TMPM4K0FSADUG">
          <processor Dclock="80000000"/>
          <compile header="Device/Include/TMPM4K0A.h" define="TMPM4K0A"/>
          <debug svd="SVD/M4K0A.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00004800" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4KxA_code_64.FLM" start="0x00000000" size="0x00010000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="6"      m="32"/>
          <feature type="DMA"           n="20"                           name="DMA"/>
          <feature type="UART"          n="2"/>
          <feature type="I2C"           n="0"                           name="I2C"/>
          <feature type="ADC"           n="8"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="6"/>
          <feature type="IOs"           n="24"/>
          <feature type="VCC"           n="2.70"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="105"/>
          <feature type="QFP" n="32" name="LQFP 32 7x7x0.8"/>
        </device>
      </subFamily>
    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <!-- Device + CMSIS Conditions -->

    <condition id="TMPM4K4Ax CMSIS">
      <description>Toshiba TMPM4K4Ax Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K4*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4K2Ax CMSIS">
      <description>Toshiba TMPM4K2Ax Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K2*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4K1Ax CMSIS">
      <description>Toshiba TMPM4K1Ax Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K1*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4K0Ax CMSIS">
      <description>Toshiba TMPM4K0Ax Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4K0*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>

  </conditions>

  <components>
    <!-- Startup TMPM4K4Ax -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4K4Ax CMSIS">
      <description>System Startup for Toshiba TMPM4K4Ax Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4K4Ax      /* Device Startup for TMPM4K4Ax */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4K4A.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/system_TMPM4KxA.c"      attr="config" version="0.0.1"/>
      </files>
    </component>

    <!-- Startup TMPM4K2Ax -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4K2Ax CMSIS">
      <description>System Startup for Toshiba TMPM4K2Ax Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4K2Ax      /* Device Startup for TMPM4K2Ax */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4K2A.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/system_TMPM4KxA.c"      attr="config" version="0.0.1"/>
      </files>
    </component>

    <!-- Startup TMPM4K1Ax -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4K1Ax CMSIS">
      <description>System Startup for Toshiba TMPM4K1Ax Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4K1Ax      /* Device Startup for TMPM4K1Ax */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4K1A.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/system_TMPM4KxA.c"      attr="config" version="0.0.1"/>
      </files>
    </component>

    <!-- Startup TMPM4K0Ax -->
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TMPM4K0Ax CMSIS">
      <description>System Startup for Toshiba TMPM4K0Ax Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4K0Ax      /* Device Startup for TMPM4K0Ax */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4K0A.s" attr="config" version="0.0.1" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/system_TMPM4KxA.c"      attr="config" version="0.0.1"/>
      </files>
    </component>


  </components>

</package>
