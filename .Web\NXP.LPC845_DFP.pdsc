<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>LPC845_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for LPC845</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version='18.0.0' date='2024-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version='17.0.0' date='2023-07-27'>NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version='16.0.0' date='2023-01-16'>NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version='15.0.0' date='2022-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version='14.0.0' date='2022-01-05'>NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version='13.1.0' date='2021-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version='13.0.0' date='2021-02-05'>
      NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files
    </release>
    <release version='12.3.0' date='2021-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version='12.2.0' date='2020-07-20'>NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version='12.1.0' date='2019-12-19'>NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version='12.0.0' date='2019-06-10'>NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version='11.0.1' date='2019-04-26'>Removed invalid entries from Software Content Register</release>
    <release version='11.0.0' date='2018-12-19'>NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version='10.0.3' date='2018-06-21'>
      NXP CMSIS packs based on MCUXpresso SDK 2.4.2. Fixed missing flash algorithms for LPC8xx CMSIS packs.
    </release>
    <release version='10.0.2' date='2018-05-25'>NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='LPC845' Dvendor='NXP:11'>
      <debugvars configfile='devices/LPC845/arm/LPC84x.dbgconf'>
        // Debug Access Variables, can be modified by user via copies of DBGCONF files as created by uVision. Also see sub-family level.
        __var Dbg_CR                = 0x00000000;                            // DBG_CR
      </debugvars>
      <sequences>
        <sequence name='DebugDeviceUnlock'>
          <block>
            __var value = 0;
            
            value  = Read32(0x40048080);                                     // Read SYSAHBCLKCTRL0
            value |= (1 &lt;&lt; 26);                                        // Enable MTB Control Register Clock
            Write32(0x40048080, value);                                      // Write modified value to SYSAHBCLKCTRL0
          </block>
        </sequence>
        <sequence name='ResetCatchSet'>
          <block>
            __var SCS_Addr   = 0xE000E000;
            __var DHCSR_Addr = SCS_Addr + 0xDF0;
            __var DEMCR_Addr = SCS_Addr + 0xDFC;
            __var value      = 0;
            __var FPB_BKPT_H = 0x80000000;
            __var FPB_BKPT_L = 0x40000000;
            __var FPB_COMP_M = 0x1FFFFFFC;
            __var FPB_KEY    = 0x00000002;
            __var FPB_ENABLE = 0x00000001;
          </block>
          <control if='Dbg_CR == 0x00000000' info='Stop after bootloader disabled'>
            <block>
              value = Read32(DEMCR_Addr);
              Write32(DEMCR_Addr, (value | 0x00000001));                     // Enable Reset Vector Catch in DEMCR
            </block>
          </control>
          <control if='Dbg_CR == 0x00000001' info='Stop after bootloader enabled'>
            <block>
              value = Read32(DEMCR_Addr);
              Write32(DEMCR_Addr, (value &amp; (~0x00000001)));              // Disable Reset Vector Catch in DEMCR
            </block>
            <control if='value &lt; 0x20000000' info='Set and enable breakpoint'>
              <block>
                Write32(0x40048000, 0x00000002);                             // Map Flash to Vectors
                value = Read32 (0x00000004);                                 // Read Reset Vector
                
                value = ((value &amp; 0x02) ? FPB_BKPT_H : FPB_BKPT_L) | (value &amp; FPB_COMP_M) | FPB_ENABLE ;
                Write32(0xE0002008, value);                                  // Set BP0 to Reset Vector
                Write32(0xE0002000, (FPB_KEY | FPB_ENABLE));                 // Enable FPB
              </block>
            </control>
            <control if='value &gt;= 0x20000000' info='Enable reset vector catch'>
              <block>
                value = Read32(DEMCR_Addr);
                Write32(DEMCR_Addr, (value | 0x00000001));                   // Enable Reset Vector Catch in DEMCR
              </block>
            </control>
          </control>
          <block>
            Read32(DHCSR_Addr);                                              // Read DHCSR to clear potentially set DHCSR.S_RESET_ST bit
          </block>
        </sequence>
        <sequence name='ResetCatchClear'>
          <block>
            // System Control Space (SCS) offset as defined in ARMv6-M/ARMv7-M.
            // Reimplement this sequence if the SCS is located at a different offset.
            __var SCS_Addr   = 0xE000E000;
            __var DEMCR_Addr = SCS_Addr + 0xDFC;
            __var value      = 0;
            
            value = Read32(DEMCR_Addr);
            Write32(DEMCR_Addr, (value &amp; (~0x00000001)));                // Disable Reset Vector Catch in DEMCR
            
            Write32(0xE0002008, 0);                                          // Clear BP0
            Write32(0xE0002000, 0x00000002);                                 // Disable FPB
          </block>
        </sequence>
      </sequences>
      <description>Low-Cost Microcontrollers (MCUs) based on Arm Cortex-M0+ Core</description>
      <device Dname='LPC845'>
        <processor Dcore='Cortex-M0+' Dfpu='NO_FPU' Dmpu='NO_MPU' Dendian='Little-endian' Dclock='30000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/LPC845/iar/LPC845_flash.icf'/>
        </environment>
        <memory name='IAP_SRAM' start='0x10003fe0' size='0x20' access='rw' default='1'/>
        <memory name='PROGRAM_FLASH' start='0x00000000' size='0x010000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x10000000' size='0x3fe0' access='rw' default='1'/>
        <algorithm name='devices/LPC845/arm/LPC84x_64.FLM' start='0x00000000' size='0x00010000' RAMstart='0x10000000' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/LPC845/LPC845.xml'/>
        <variant Dvariant='LPC845M301JBD64'>
          <compile header='devices/LPC845/fsl_device_registers.h' define='CPU_LPC845M301JBD64'/>
        </variant>
        <variant Dvariant='LPC845M301JBD48'>
          <compile header='devices/LPC845/fsl_device_registers.h' define='CPU_LPC845M301JBD48'/>
        </variant>
        <variant Dvariant='LPC845M301JHI48'>
          <compile header='devices/LPC845/fsl_device_registers.h' define='CPU_LPC845M301JHI48'/>
        </variant>
        <variant Dvariant='LPC845M301JHI33'>
          <compile header='devices/LPC845/fsl_device_registers.h' define='CPU_LPC845M301JHI33'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.LPC845.internal_condition'>
      <accept Dname='LPC845M301JBD48' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JBD64' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JHI33' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JHI48' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.device=LPC845.internal_condition'>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.button.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter, component.timer_manager, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter, component.timer_manager, device=LPC845.internal_condition'>
      <require condition='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio, allOf=anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'>
      <accept condition='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
      <accept condition='allOf.anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_gpio_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpc_gpio, component.rt_gpio_adapter.internal_condition'>
      <require condition='anyOf.driver.lpc_gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rt_gpio_adapter'/>
    </condition>
    <condition id='anyOf.driver.lpc_gpio.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='component.ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ctimer, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpc_i2c_adapter, driver.lpc_i2c, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpc_i2c_adapter, driver.lpc_i2c, device=LPC845.internal_condition'>
      <require condition='anyOf.allOf=component.lpc_i2c_adapter, driver.lpc_i2c.internal_condition'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpc_i2c_adapter, driver.lpc_i2c.internal_condition'>
      <accept condition='allOf.component.lpc_i2c_adapter, driver.lpc_i2c.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpc_i2c_adapter, driver.lpc_i2c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_i2c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.led.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, component.timer_manager, driver.common, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpc_gpio_adapter, driver.lpc_gpio, component.timer_manager, driver.common, device=LPC845.internal_condition'>
      <require condition='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'>
      <accept condition='allOf.component.lpc_gpio_adapter, driver.lpc_gpio.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.lpc_crc_adapter.condition_id'>
      <require condition='allOf.driver.lpc_crc, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_crc, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.lpc_gpio_adapter.condition_id'>
      <require condition='allOf.driver.inputmux, driver.lpc_gpio, driver.pint, driver.syscon, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.inputmux, driver.lpc_gpio, driver.pint, driver.syscon, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pint'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='syscon'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.lpc_i2c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.lpc_i2c, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.lpc_i2c, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=LPC845.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.miniusart_adapter.condition_id'>
      <require condition='allOf.driver.lpc_miniusart, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_miniusart, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.mrt_adapter.condition_id'>
      <require condition='allOf.driver.mrt, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.mrt, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.pwm_ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=component.software_rng_adapter, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.software_rng_adapter, device=LPC845.internal_condition'>
      <require condition='anyOf.component.software_rng_adapter.internal_condition'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.component.software_rng_adapter.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.rt_gpio_adapter.condition_id'>
      <require condition='allOf.driver.lpc_gpio, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpc_gpio, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, component.lists, driver.common, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt, component.lists, driver.common, device=LPC845.internal_condition'>
      <require condition='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.mrt_adapter, driver.mrt.internal_condition'>
      <accept condition='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'/>
      <accept condition='allOf.component.mrt_adapter, driver.mrt.internal_condition'/>
    </condition>
    <condition id='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
    </condition>
    <condition id='allOf.component.mrt_adapter, driver.mrt.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mrt'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=LPC845.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='device_id.LPC845.internal_condition'>
      <accept Dname='LPC845M301JBD48' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JBD64' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JHI33' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JHI48' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=LPC845.internal_condition'>
      <require condition='device_id.LPC845.internal_condition'/>
    </condition>
    <condition id='device_ids.LPC845.internal_condition'>
      <accept Dname='LPC845M301JBD48' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JBD64' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JHI33' Dvendor='NXP:11'/>
      <accept Dname='LPC845M301JHI48' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=LPC845.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.LPC845.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=LPC845.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.LPC845.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=LPC845.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.LPC845.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.component.miniusart_adapter, device_id=LPC845, driver.common, device.startup, driver.clock, driver.power, driver.reset, driver.swm, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.syscon_connections, driver.swm_connections, utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.miniusart_adapter, device_id=LPC845, driver.common, device.startup, driver.clock, driver.power, driver.reset, driver.swm, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.syscon_connections, driver.swm_connections, utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='miniusart_adapter'/>
      <require condition='device_id.LPC845.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='power'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='reset'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='swm'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='iocon'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='syscon_connections'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='swm_connections'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='LPC845_system'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='LPC845_header'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='driver.capt.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.LPC845.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm0p.condition_id'>
      <require condition='cores.cm0p.internal_condition'/>
    </condition>
    <condition id='cores.cm0p.internal_condition'>
      <accept Dcore='Cortex-M0+'/>
    </condition>
    <condition id='driver.ctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.iap.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.inputmux.condition_id'>
      <require condition='allOf.driver.common, driver.inputmux_connections, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.inputmux_connections, device_id=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections'/>
      <require condition='device_id.LPC845.internal_condition'/>
    </condition>
    <condition id='driver.inputmux_connections.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_acomp.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_adc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_crc.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_dac.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_dma.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_gpio.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_i2c.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_iocon_lite.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_minispi.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.lpc_miniusart.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.mrt.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.pint.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.power.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.reset.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.sctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.swm.condition_id'>
      <require condition='allOf.driver.common, driver.swm_connections, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.swm_connections, device_id=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='swm_connections'/>
      <require condition='device_id.LPC845.internal_condition'/>
    </condition>
    <condition id='driver.swm_connections.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.syscon.condition_id'>
      <require condition='allOf.driver.common, driver.syscon_connections, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.syscon_connections, device_id=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='syscon_connections'/>
      <require condition='device_id.LPC845.internal_condition'/>
    </condition>
    <condition id='driver.syscon_connections.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.wkt.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='driver.wwdt.condition_id'>
      <require condition='allOf.driver.common, device_id=LPC845.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console_lite, component.miniusart_adapter, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console_lite, component.miniusart_adapter, device=LPC845.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='miniusart_adapter'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.lpc_miniusart, driver.common, utility.str, component.miniusart_adapter, device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpc_miniusart, driver.common, utility.str, component.miniusart_adapter, device=LPC845.internal_condition'>
      <require condition='anyOf.driver.lpc_miniusart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='miniusart_adapter'/>
      <require condition='device.LPC845.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.lpc_miniusart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='usart'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=LPC845.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=LPC845.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='button' Cversion='1.0.0' condition='component.button.condition_id'>
      <description>Component button</description>
      <files>
        <file category='header' name='components/button/fsl_component_button.h' projectpath='component/button'/>
        <file category='sourceC' name='components/button/fsl_component_button.c' projectpath='component/button'/>
        <file category='include' name='components/button/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter' Cversion='1.0.0' condition='component.ctimer_adapter.condition_id'>
      <description>Component ctimer_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_CTIMER
#define TIMER_PORT_TYPE_CTIMER 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ctimer.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='led' Cversion='1.0.0' condition='component.led.condition_id'>
      <description>Component led</description>
      <files>
        <file category='header' name='components/led/fsl_component_led.h' projectpath='component/led'/>
        <file category='sourceC' name='components/led/fsl_component_led.c' projectpath='component/led'/>
        <file category='include' name='components/led/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc_adapter' Cversion='1.0.0' condition='component.lpc_crc_adapter.condition_id'>
      <description>Component lpc_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_lpc_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_gpio_adapter' Cversion='1.0.1' condition='component.lpc_gpio_adapter.condition_id'>
      <description>Component lpc_gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_lpc_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_i2c_adapter' Cversion='1.0.0' condition='component.lpc_i2c_adapter.condition_id'>
      <description>Component lpc_i2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_lpc_i2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='miniusart_adapter' Cversion='1.0.0' condition='component.miniusart_adapter.condition_id'>
      <description>Component miniusart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_miniusart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mrt_adapter' Cversion='1.0.0' condition='component.mrt_adapter.condition_id'>
      <description>Component mrt_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_MRT
#define TIMER_PORT_TYPE_MRT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_mrt.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm_ctimer_adapter' Cversion='1.0.0' condition='component.pwm_ctimer_adapter.condition_id'>
      <description>Component pwm_ctimer_adapter</description>
      <files>
        <file category='header' name='components/pwm/fsl_adapter_pwm.h' projectpath='component/pwm'/>
        <file category='sourceC' name='components/pwm/fsl_adapter_pwm_ctimer.c' projectpath='component/pwm'/>
        <file category='include' name='components/pwm/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rt_gpio_adapter' Cversion='1.0.1' condition='component.rt_gpio_adapter.condition_id'>
      <description>Component rt_gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_rt_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='LPC845_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device LPC845_cmsis</description>
      <files>
        <file category='header' name='devices/LPC845/fsl_device_registers.h' projectpath='device'/>
        <file category='header' name='devices/LPC845/LPC845.h' projectpath='device'/>
        <file category='header' name='devices/LPC845/LPC845_features.h' projectpath='device'/>
        <file category='header' name='devices/LPC845/LPC845_COMMON.h' projectpath='device'/>
        <file category='header' name='devices/LPC845/periph2/PERI_ACOMP.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_ADC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_CAPT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_CRC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_CTIMER.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_DAC.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_DMA.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_FLASH_CTRL.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_GPIO.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_I2C.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_INPUTMUX.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_IOCON.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_MRT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_MTB.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_PINT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_PMU.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_SCT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_SPI.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_SWM.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_SYSCON.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_USART.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_WKT.h' projectpath='device/periph2'/>
        <file category='header' name='devices/LPC845/periph2/PERI_WWDT.h' projectpath='device/periph2'/>
        <file category='include' name='devices/LPC845/'/>
        <file category='include' name='devices/LPC845/periph2/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='doc' name='devices/LPC845/template/device.RTE_dummy.txt'/>
        <file category='include' name='devices/LPC845/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='LPC845_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device LPC845_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, device_ids=LPC845.condition_id' category='linkerScript' attr='config' name='devices/LPC845/arm/LPC845_flash.scf' version='1.0.0' projectpath='LPC845/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=LPC845.condition_id' category='linkerScript' attr='config' name='devices/LPC845/arm/LPC845_ram.scf' version='1.0.0' projectpath='LPC845/arm'/>
        <file condition='allOf.toolchains=armgcc, device_ids=LPC845.condition_id' category='linkerScript' attr='config' name='devices/LPC845/gcc/LPC845_flash.ld' version='1.0.0' projectpath='LPC845/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=LPC845.condition_id' category='linkerScript' attr='config' name='devices/LPC845/gcc/LPC845_ram.ld' version='1.0.0' projectpath='LPC845/gcc'/>
        <file condition='allOf.toolchains=iar, device_ids=LPC845.condition_id' category='linkerScript' attr='config' name='devices/LPC845/iar/LPC845_flash.icf' version='1.0.0' projectpath='LPC845/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=LPC845.condition_id' category='linkerScript' attr='config' name='devices/LPC845/iar/LPC845_ram.icf' version='1.0.0' projectpath='LPC845/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='LPC845' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template LPC845</description>
      <files>
        <file category='header' attr='config' name='devices/LPC845/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC845/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/LPC845/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC845/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/LPC845/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/LPC845/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/LPC845/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device LPC845_startup</description>
      <files>
        <file condition='allOf.toolchains=iar.condition_id' category='sourceAsm' attr='config' name='devices/LPC845/iar/startup_LPC845.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceAsm' attr='config' name='devices/LPC845/gcc/startup_LPC845.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk.condition_id' category='sourceAsm' attr='config' name='devices/LPC845/arm/startup_LPC845.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='LPC845_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device LPC845_system</description>
      <files>
        <file category='sourceC' name='devices/LPC845/system_LPC845.c' projectpath='device'/>
        <file category='header' name='devices/LPC845/system_LPC845.h' projectpath='device'/>
        <file category='include' name='devices/LPC845/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='capt' Cversion='2.1.0' condition='driver.capt.condition_id'>
      <description>CAPT Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_capt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_capt.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.3.4' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC845/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm0p.condition_id' category='sourceC' name='devices/LPC845/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm0p.condition_id' category='header' name='devices/LPC845/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer' Cversion='2.3.3' condition='driver.ctimer.condition_id'>
      <description>CTimer Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_ctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_ctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iap' Cversion='2.0.7' condition='driver.iap.condition_id'>
      <description>IAP Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_iap.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_iap.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux' Cversion='2.0.9' condition='driver.inputmux.condition_id'>
      <description>INPUTMUX Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_inputmux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_inputmux.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections' Cversion='2.0.0' condition='driver.inputmux_connections.condition_id'>
      <description>Inputmux_connections Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_inputmux_connections.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_acomp' Cversion='2.1.0' condition='driver.lpc_acomp.condition_id'>
      <description>LPC_ACOMP Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_acomp.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_acomp.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adc' Cversion='2.6.0' condition='driver.lpc_adc.condition_id'>
      <description>ADC Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_adc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_adc.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_crc' Cversion='2.1.1' condition='driver.lpc_crc.condition_id'>
      <description>CRC Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_crc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_crc.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dac' Cversion='2.0.2' condition='driver.lpc_dac.condition_id'>
      <description>DAC Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_dac.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_dac.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dma' Cversion='2.5.3' condition='driver.lpc_dma.condition_id'>
      <description>DMA Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_dma.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC845/drivers/fsl_dma.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.1.7' condition='driver.lpc_gpio.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.2.1' condition='driver.lpc_i2c.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_i2c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_i2c.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='iocon' Cversion='2.0.2' condition='driver.lpc_iocon_lite.condition_id'>
      <description>IOCON Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_iocon.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi' Cversion='2.0.8' condition='driver.lpc_minispi.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_spi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_spi.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='usart' Cversion='2.5.2' condition='driver.lpc_miniusart.condition_id'>
      <description>USART Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_usart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_usart.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mrt' Cversion='2.0.5' condition='driver.mrt.condition_id'>
      <description>MRT Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_mrt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_mrt.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pint' Cversion='2.2.0' condition='driver.pint.condition_id'>
      <description>PINT Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_pint.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_pint.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='power' Cversion='2.1.0' condition='driver.power.condition_id'>
      <description>Power driver</description>
      <files>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_power.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC845/drivers/fsl_power.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset' Cversion='2.4.0' condition='driver.reset.condition_id'>
      <description>Reset Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_reset.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC845/drivers/fsl_reset.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='sctimer' Cversion='2.5.1' condition='driver.sctimer.condition_id'>
      <description>SCT Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_sctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_sctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='swm' Cversion='2.1.2' condition='driver.swm.condition_id'>
      <description>SWM Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_swm.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_swm.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='swm_connections' Cversion='2.0.0' condition='driver.swm_connections.condition_id'>
      <description>swm_connections Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_swm_connections.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='syscon' Cversion='2.0.1' condition='driver.syscon.condition_id'>
      <description>SYSCON Driver</description>
      <files>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_syscon.c' projectpath='drivers'/>
        <file category='header' name='devices/LPC845/drivers/fsl_syscon.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='syscon_connections' Cversion='2.0.0' condition='driver.syscon_connections.condition_id'>
      <description>syscon_connections Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_syscon_connections.h' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wkt' Cversion='2.0.2' condition='driver.wkt.condition_id'>
      <description>WKT Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_wkt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_wkt.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wwdt' Cversion='2.1.9' condition='driver.wwdt.condition_id'>
      <description>WWDT Driver</description>
      <files>
        <file category='header' name='devices/LPC845/drivers/fsl_wwdt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/LPC845/drivers/fsl_wwdt.c' projectpath='drivers'/>
        <file category='include' name='devices/LPC845/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/LPC845/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/LPC845/utilities/fsl_sbrk.c' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/LPC845/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/LPC845/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/LPC845/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/LPC845/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/LPC845/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/LPC845/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/LPC845/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/LPC845/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/LPC845/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/LPC845/utilities/str/'/>
      </files>
    </component>
  </components>
</package>