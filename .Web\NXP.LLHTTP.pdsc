<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LLHTTP</name>
  <vendor>NXP</vendor>
  <description>Software Pack for llhttp</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="AWS IoT" Cgroup="llhttp">AWS IoT NXP</description>
  </taxonomy>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.llhttp.condition_id">
      <require condition="allOf.utility.debug_console.internal_condition"/>
    </condition>
    <condition id="allOf.utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
  </conditions>
  <components>
    <bundle Cbundle="AWS IoT NXP" Cclass="AWS IoT" Cversion="1.0.0">
      <description>AWS IoT NXP</description>
      <doc>middleware/llhttp/AWS IoT NXP_dummy.txt</doc>
      <component Cgroup="llhttp" Csub="llhttp" Cversion="6.0.5" condition="middleware.llhttp.condition_id">
        <description>HTTP parser in LLVM IR</description>
        <files>
          <file category="header" name="middleware/llhttp/include/llhttp.h" projectpath="llhttp/include"/>
          <file category="sourceC" name="middleware/llhttp/src/api.c" projectpath="llhttp/src"/>
          <file category="sourceC" name="middleware/llhttp/src/http.c" projectpath="llhttp/src"/>
          <file category="sourceC" name="middleware/llhttp/src/llhttp.c" projectpath="llhttp/src"/>
          <file category="doc" name="middleware/llhttp/AWS IoT NXP_dummy.txt"/>
          <file category="include" name="middleware/llhttp/include/"/>
        </files>
      </component>
    </bundle>
  </components>
</package>
