<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT700-EVK_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for MIMXRT700-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="25.06.00" date="2025-06-25">NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM" version="6.1.0"/>
      <package name="MIMXRT798S_DFP" vendor="NXP" version="25.06.00"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT700-EVK">
      <description>i.MX RT700 Evaluation Kit</description>
      <image small="boards/mimxrt700evk/mimxrt700evk.png"/>
      <mountedDevice Dname="MIMXRT798SGFOA" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT798SGAWAR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT798SGAWBR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT798SGFOB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT735SGAWAR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT735SGAWBR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT735SGFOA" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT735SGFOB" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT758SGAWAR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT758SGAWBR" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT758SGFOA" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT758SGFOB" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MIMXRT798S.internal_condition">
      <accept Dname="MIMXRT798SGAWAR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT798SGAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT798SGFOA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT798SGFOB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT798S.internal_condition">
      <accept Dname="MIMXRT798SGAWAR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT798SGAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT798SGFOA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT798SGFOB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT735S.internal_condition">
      <accept Dname="MIMXRT735SGAWAR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT735SGAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT735SGFOA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT735SGFOB" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT758S.internal_condition">
      <accept Dname="MIMXRT758SGAWAR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT758SGAWBR" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT758SGFOA" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT758SGFOB" Dvendor="NXP:11"/>
    </condition>
    <condition id="board.project_template.condition_id">
      <require condition="allOf.driver.lpflexcomm, driver.lpflexcomm_lpuart, driver.iopctl_soc, driver.gpio, driver.reset, device_id=MIMXRT798S, device.startup, driver.common, driver.clock, driver.glikey, board=mimxrt700evk, anyOf=allOf=utility.debug_console, utility.debug_console_template_config, utility.assert, component.serial_manager, component.serial_manager_uart, allOf=utility.debug_console_lite, utility.assert_lite, anyOf=allOf=driver.xspi, driver.cache_xcache, driver.flash_config, driver.power, core_id=cm33_core0, allOf=driver.power, core_id=cm33_core1.internal_condition"/>
    </condition>
    <condition id="allOf.driver.lpflexcomm, driver.lpflexcomm_lpuart, driver.iopctl_soc, driver.gpio, driver.reset, device_id=MIMXRT798S, device.startup, driver.common, driver.clock, driver.glikey, board=mimxrt700evk, anyOf=allOf=utility.debug_console, utility.debug_console_template_config, utility.assert, component.serial_manager, component.serial_manager_uart, allOf=utility.debug_console_lite, utility.assert_lite, anyOf=allOf=driver.xspi, driver.cache_xcache, driver.flash_config, driver.power, core_id=cm33_core0, allOf=driver.power, core_id=cm33_core1.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpflexcomm"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iopctl"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
      <require condition="device_id.MIMXRT798S.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="glikey"/>
      <require condition="board.mimxrt700evk.internal_condition"/>
      <require condition="anyOf.allOf=utility.debug_console, utility.debug_console_template_config, utility.assert, component.serial_manager, component.serial_manager_uart, allOf=utility.debug_console_lite, utility.assert_lite.internal_condition"/>
      <require condition="anyOf.allOf=driver.xspi, driver.cache_xcache, driver.flash_config, driver.power, core_id=cm33_core0, allOf=driver.power, core_id=cm33_core1.internal_condition"/>
    </condition>
    <condition id="board.mimxrt700evk.internal_condition">
      <accept condition="device.MIMXRT798S.internal_condition"/>
      <accept condition="device.MIMXRT735S.internal_condition"/>
      <accept condition="device.MIMXRT758S.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=utility.debug_console, utility.debug_console_template_config, utility.assert, component.serial_manager, component.serial_manager_uart, allOf=utility.debug_console_lite, utility.assert_lite.internal_condition">
      <accept condition="allOf.utility.debug_console, utility.debug_console_template_config, utility.assert, component.serial_manager, component.serial_manager_uart.internal_condition"/>
      <accept condition="allOf.utility.debug_console_lite, utility.assert_lite.internal_condition"/>
    </condition>
    <condition id="allOf.utility.debug_console, utility.debug_console_template_config, utility.assert, component.serial_manager, component.serial_manager_uart.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug console template config"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
    </condition>
    <condition id="allOf.utility.debug_console_lite, utility.assert_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
    </condition>
    <condition id="anyOf.allOf=driver.xspi, driver.cache_xcache, driver.flash_config, driver.power, core_id=cm33_core0, allOf=driver.power, core_id=cm33_core1.internal_condition">
      <accept condition="allOf.driver.xspi, driver.cache_xcache, driver.flash_config, driver.power, core_id=cm33_core0.internal_condition"/>
      <accept condition="allOf.driver.power, core_id=cm33_core1.internal_condition"/>
    </condition>
    <condition id="allOf.driver.xspi, driver.cache_xcache, driver.flash_config, driver.power, core_id=cm33_core0.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="cache_xcache"/>
      <require Cclass="Device" Cgroup="SDK drivers" Csub="mimxrt700evk_flash_config"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require condition="core_id.cm33_core0.internal_condition"/>
    </condition>
    <condition id="core_id.cm33_core0.internal_condition">
      <accept Pname="cm33_core0" Dname="*"/>
    </condition>
    <condition id="allOf.driver.power, core_id=cm33_core1.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require condition="core_id.cm33_core1.internal_condition"/>
    </condition>
    <condition id="core_id.cm33_core1.internal_condition">
      <accept Pname="cm33_core1" Dname="*"/>
    </condition>
    <condition id="allOf.core_ids=cm33_core0.condition_id">
      <require condition="core_ids.cm33_core0.internal_condition"/>
    </condition>
    <condition id="core_ids.cm33_core0.internal_condition">
      <accept Pname="cm33_core0" Dname="*"/>
    </condition>
    <condition id="allOf.core_ids=cm33_core1.condition_id">
      <require condition="core_ids.cm33_core1.internal_condition"/>
    </condition>
    <condition id="core_ids.cm33_core1.internal_condition">
      <accept Pname="cm33_core1" Dname="*"/>
    </condition>
    <condition id="driver.flash_config.condition_id">
      <require condition="allOf.device=MIMXRT798S.internal_condition"/>
    </condition>
    <condition id="allOf.device=MIMXRT798S.internal_condition">
      <require condition="device.MIMXRT798S.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_interrupt_print_cm33_core0" folder="boards/mimxrt700evk/driver_examples/acmp/interrupt/cm33_core0" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt_print_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling_print_cm33_core0" folder="boards/mimxrt700evk/driver_examples/acmp/polling/cm33_core0" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling_print_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cdog_cm33_core0" folder="boards/mimxrt700evk/driver_examples/cdog/cm33_core0" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpi2c/int_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpspi/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpspi/int_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpspi/int_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpuart/edma_transfer/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer_cm33_core0" folder="boards/mimxrt700evk/cmsis_driver_examples/lpuart/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="crc_cm33_core0" folder="boards/mimxrt700evk/driver_examples/crc/cm33_core0" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_match/cm33_core0" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_match_interrupt/cm33_core0" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_pwm/cm33_core0" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_pwm_interrupt/cm33_core0" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="decnano_decompress_cm33_core0" folder="boards/mimxrt700evk/display_examples/decnano_decompress/cm33_core0" doc="readme.md">
      <description>The DECNano decompress Demo reads the RGB888 or ARGB8888 picture that is compressed in DECNano format, decompress them, and shows them in the LCD panel.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/decnano_decompress_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_channel_link_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/channel_link/cm33_core0" doc="readme.md">
      <description>The EDMA4 channel link example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_channel_link_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_interleave_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/interleave_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA4 interleave example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_interleave_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/memory_to_memory/cm33_core0" doc="readme.md">
      <description>The EDMA4 memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/memory_to_memory_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA4 memory to memory transfer example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memset_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/memset/cm33_core0" doc="readme.md">
      <description>The EDMA4 memset example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memset_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_ping_pong_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/ping_pong_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA4 ping pong transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_ping_pong_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_scatter_gather_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/scatter_gather/cm33_core0" doc="readme.md">
      <description>The EDMA4 scatter gather example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_scatter_gather_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_wrap_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/edma4/wrap_transfer/cm33_core0" doc="readme.md">
      <description>The EDMA4 wrap transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_wrap_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="els_hash_cm33_core0" folder="boards/mimxrt700evk/els_pkc_examples/els_hash/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of hash algorithms and a crypto library lightweight testing.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_hash_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="els_pkc_asymmetric_cm33_core0" folder="boards/mimxrt700evk/els_pkc_examples/els_pkc_asymmetric/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of asymmetric algorithms and a crypto library lightweight testing.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_pkc_asymmetric_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="els_pkc_common_cm33_core0" folder="boards/mimxrt700evk/els_pkc_examples/els_pkc_common/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of common features including PRNG and a crypto library lightweight testing.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_pkc_common_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="els_symmetric_cm33_core0" folder="boards/mimxrt700evk/els_pkc_examples/els_symmetric/cm33_core0" doc="readme.md">
      <description>The ELS PKC application provides examples which demonstrate usage of symmetric algorithms and a crypto library lightweight testing.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/els_symmetric_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_octal_xspi_cm33_core0" folder="boards/mimxrt700evk/component_examples/flash_component/xspi_octal/cm33_core0" doc="readme.md">
      <description>octal flash demo shows the use of nor flash component to erase, program, and read an external serial nor flash device.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_octal_xspi_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/i2c/interrupt_lpi2c_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/pwm/cm33_core0" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_edma example shows how to use flexio spi master  driver in edma way:In this example, a flexio simulated master connect to a flexio simulated spi slave .</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_edma example shows how to use flexio spi slave  driver in dma way:In this example, a flexio simulated slave connect to a flexio simulated spi master.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/edma_lpspi_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/edma_lpspi_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/int_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_interrupt example shows how to use flexio spi master  driver in interrupt way:In this example, a flexio simulated master connect to a flexio simulated spi slave .</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/int_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_interrupt example shows how to use flexio spi slave  driver in interrupt way:In this example, a flexio simulated slave connect to a flexio simulated spi master.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/int_lpspi_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/int_lpspi_transfer/slave/cm33_core0" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_polling_lpspi_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/spi/polling_lpspi_transfer/master/cm33_core0" doc="readme.md">
      <description>The flexio_spi_master_pooling_lpspi_slave example shows how to use flexio spi master driver in polling way. In this example, a flexio simulated master connect to a lpspi slave.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_polling_lpspi_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/uart/edma_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/uart/int_rb_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/uart/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/flexio/uart/polling_transfer/cm33_core0" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="freqme_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/freqme/cm33_core0" doc="readme.md">
      <description>The freqme_interrupt is a demonstration program of the SDK LPC_FREQME driver's features. The example demostrate the usage of frequency measurement operate mode and pulse width measurement operate mode.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freqme_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="gdet_cm33_core0" folder="boards/mimxrt700evk/driver_examples/gdet/cm33_core0" doc="readme.md">
      <description>The GDET Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Glitch Detect (GDET) module.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdet_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="glikey_cm33_core0" folder="boards/mimxrt700evk/driver_examples/glikey/cm33_core0" doc="readme.md">
      <description>The GLIKEY Example project is a demonstration program that uses the MCUX SDK software to show funcionality of GLIKEY IP.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/glikey_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/gpio/input_interrupt/cm33_core0" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output_cm33_core0" folder="boards/mimxrt700evk/driver_examples/gpio/led_output/cm33_core0" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_cm33_core0" folder="boards/mimxrt700evk/demo_apps/hello_world/cm33_core0" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="irtc_cm33_core0" folder="boards/mimxrt700evk/driver_examples/irtc/cm33_core0" doc="readme.md">
      <description>The IRTC project is a simple demonstration program of the SDK IRTC driver.This example is a low power module that provides time keeping and calendaring functions and additionally providesprotection against tampering,...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irtc_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="itrc_cm33_core0" folder="boards/mimxrt700evk/driver_examples/itrc/cm33_core0" doc="readme.md">
      <description>The ITRC Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Intrusion and Tamper Response Controller.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/itrc_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="jpegdec_single_stream_single_frame_config_cm33_core0" folder="boards/mimxrt700evk/driver_examples/jpegdec/single_stream_single_frame/config/cm33_core0" doc="readme.md">
      <description>The JPEG decoder example decodes one JPEG picture using slot 0 then shows it on panel. First we configure the decoder configuration then starts the decoder. After decoding complete the picture will be shown on panel.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/jpegdec_single_stream_single_frame_config_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="jpegdec_single_stream_single_frame_descriptor_cm33_core0" folder="boards/mimxrt700evk/driver_examples/jpegdec/single_stream_single_frame/descriptor/cm33_core0" doc="readme.md">
      <description>The JPEG decoder example decodes one JPEG picture using slot 0 then shows it on panel. First we configure the descriptor, then enable the descriptor to let it update the decoder configuration and start decode. After...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/jpegdec_single_stream_single_frame_descriptor_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_color_key_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/color_key/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF color key feature in ARGB8888 mode. In this exapmle, background layer is use as destination layer and video layer is used as source layer. The gray rectangles in source...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_color_key_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_cursor_argb_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/cursor_argb/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF cursor in ARGB8888 mode. In this exapmle, the screen is devided into two parts: red and blue. A cursor is moving in the screen, the cursor alpha value changes during moving.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_cursor_argb_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_cursor_masked_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/cursor_masked/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF cursor in masked mode.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_cursor_masked_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_dbi_rgb565_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/dbi_rgb565/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF driver to show the dbi_rgb565 format frame buffer. When the example runs, a rectangle is moving in the screen, and its color changes when touch border.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_dbi_rgb565_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_gamma_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/gamma/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF gamma correction feature. In this example, the gamma corretion table is set to invert the original picture. The original picture is gradual changed gray bars, the gamma...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_gamma_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_porter_duff_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/porter_duff/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF alpha blend function to perform the Porter Duff compositing. In this example, a blue rectangle is in the top left corner of the video/graphic layer as destination...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_porter_duff_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_rgb565_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/rgb565/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF driver to show the RGB565 format frame buffer. When the example runs, a rectangle is moving in the screen, and its color changes when touch border.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_rgb565_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lcdif_rotate_flip_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lcdif/rotate_flip/cm33_core0" doc="readme.md">
      <description>This example demonstrates how to use the LCDIF rotate/flip feature. In this example, you will see a square with three color(red, green and blue). The rotate/flip mode is changing.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lcdif_rotate_flip_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_dual_single_ended_conversion_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpadc/dual_single_ended_conversion/cm33_core0" doc="readme.md">
      <description>The lpadc_dual_single_ended_conversion example shows how to use two channel with LPADC driver. In this example, user should indicate two channel to provide a voltage signal (can be controlled by user) as the LPADC's...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_dual_single_ended_conversion_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpadc/interrupt/cm33_core0" doc="readme.md">
      <description>The lpdc_single_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpadc/polling/cm33_core0" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpi2c/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpi2c/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpi2c/interrupt_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpi2c/interrupt_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpi2c/polling_b2b/master/cm33_core0" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpi2c/polling_b2b/slave/cm33_core0" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpspi/edma_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpspi/edma_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpspi/interrupt_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpspi/interrupt_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpspi/polling_b2b_transfer/master/cm33_core0" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpspi/polling_b2b_transfer/slave/cm33_core0" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_rb_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/edma_rb_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_edma Ring Buffer Example project is to demonstrate usage of the KSDK lpuart driver.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_rb_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/edma_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_hardware_flow_control_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/hardware_flow_control" doc="readme.md">
      <description>The lpuart_hardware_flow_control Example project is to demonstrate usage of the hardware flow control function.This example will send data to itself(loopback), and hardware flow control will be enabled in the...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_hardware_flow_control_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt/cm33_core0" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt_rb_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt_transfer_seven_bits/cm33_core0" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/polling/cm33_core0" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits_cm33_core0" folder="boards/mimxrt700evk/driver_examples/lpuart/polling_seven_bits/cm33_core0" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="mmu_xspi" folder="boards/mimxrt700evk/driver_examples/mmu/xspi/cm33_core0" doc="readme.md">
      <description>The mmu xspi example shows how to use MMU driver to configure MMU module.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mmu_xspi.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/mrt/cm33_core0" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project_cm33_core0" folder="boards/mimxrt700evk/demo_apps/new_project/cm33_core0" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/ostimer/cm33_core0" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_edma_transfer/cm33_core0" doc="readme.md">
      <description>pdm_edma_transfer</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_hwvad_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_hwvad/cm33_core0" doc="readme.md">
      <description>The pdm_hwvad example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_hwvad_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_interrupt/cm33_core0" doc="readme.md">
      <description>The pdm_interrupt example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_edma_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_sai_edma/cm33_core0" doc="readme.md">
      <description>The pdm_sai_sdma example shows how to use pdm edma driver together with sai edma driver</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_edma_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_sai_interrupt/cm33_core0" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_sai_interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt transaction api:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_multi_channel_edma_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_sai_multi_channel_edma/cm33_core0" doc="readme.md">
      <description>The pdm_sai_multi_channel_edma example shows how to use pdm edma driver to record multi channel data together with sai edma driver</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_multi_channel_edma_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_multi_channel_tdm_edma_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pdm/pdm_sai_multi_channel_tdm_edma/cm33_core0" doc="readme.md">
      <description>The pdm_sai_multi_channel_tdm_edma example shows how to use pdm edma driver to record multi channel data together with sai edma driver</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_multi_channel_tdm_edma_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pint/pattern_match/cm33_core0" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pint/pin_interrupt/cm33_core0" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pmc_temperature_sensor_cm33_core0" folder="boards/mimxrt700evk/demo_apps/pmc_temperature_sensor/cm33_core0" doc="readme.md">
      <description>The demo shows how to measure the temperature within the PMC module.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pmc_temperature_sensor_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="pngdec_cm33_core0" folder="boards/mimxrt700evk/driver_examples/pngdec/cm33_core0" doc="readme.md">
      <description>The PNG decoder demo uses the PNG decoder driver to decode an PNG picture of NXP logo and shows it in the LCD panel.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pngdec_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="puf_v3_cm33_core0" folder="boards/mimxrt700evk/driver_examples/puf_v3/cm33_core0" doc="readme.md">
      <description>This driver example project demonstrates how to use driver for the PUFv3 controller which provides a secure key storage.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/puf_v3_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_bootloader_cm33_core0" folder="boards/mimxrt700evk/driver_examples/romapi/bootloader/cm33_core0" doc="readme.md">
      <description>Rom API driver example for bootloader functions.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/romapi_bootloader_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="romapi_otp_cm33_core0" folder="boards/mimxrt700evk/driver_examples/romapi/otp/cm33_core0" doc="readme.md">
      <description>Rom API driver example for OCOTP interface.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/romapi_otp_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback_cm33_core0" folder="boards/mimxrt700evk/driver_examples/sai/edma_record_playback/cm33_core0" doc="readme.md">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/sai/edma_transfer/cm33_core0" doc="readme.md">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback_cm33_core0" folder="boards/mimxrt700evk/driver_examples/sai/interrupt_record_playback/cm33_core0" doc="readme.md">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/sai/interrupt_transfer/cm33_core0" doc="readme.md">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter_cm33_core0" folder="boards/mimxrt700evk/driver_examples/sctimer/16bit_counter/cm33_core0" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="sdadc_sai_interrupt_cm33_core0" folder="boards/mimxrt700evk/driver_examples/sdadc/sdadc_sai_interrupt/cm33_core0" doc="readme.md">
      <description>The sdadc sai interrupt example shows how to use sdadc driver with interrupt. In this example, sdadc gathers analog data from the microphone, and uses the sai to send the digital data to the codec. The user can hear...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdadc_sai_interrupt_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="shell_cm33_core0" folder="boards/mimxrt700evk/demo_apps/shell/cm33_core0" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="syscon_clockout_cm33_core0" folder="boards/mimxrt700evk/driver_examples/clockout/cm33_core0" doc="readme.md">
      <description>The syscon_clockout driver example shows how to output the internal clock signal. In this driver example, users can choose the clock signal to be outputted, and the divider of the output clock signal. By probe the...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/syscon_clockout_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="syspm_cm33_core0" folder="boards/mimxrt700evk/driver_examples/syspm/cm33_core0" doc="readme.md">
      <description>The System Performance Monitor (SYSPM) is a memory mapped peripheral that enables the user to monitor system and/or CPU events. This demo project obtains the event count value through the system performance monitor, and prints three values.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/syspm_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="trng_random_cm33_core0" folder="boards/mimxrt700evk/driver_examples/trng/random/cm33_core0" doc="readme.md">
      <description>The True Random Number Generator (TRNG) is a hardware accelerator module that generates a 512-bitentropy as needed by an entropy consuming module or by other post processing functions. The TRNGExample project is a...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/trng_random_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/utick/cm33_core0" doc="readme.md">
      <description>The utick project is a simple demonstration program of the SDK utick driver. It sets up the utick hardware block to trigger a periodic interrupt after every 1 second. When the utick interrupt is triggered a message is printed on the UART terminal.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example_cm33_core0" folder="boards/mimxrt700evk/driver_examples/wwdt/cm33_core0" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="xspi_octal_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/xspi/octal/edma_transfer/cm33_core0" doc="readme.md">
      <description>The xspi_octal_edma_transfer example shows how to use xspi driver with dma. In this example, xspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xspi_octal_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="xspi_octal_polling_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/xspi/octal/polling_transfer/cm33_core0" doc="readme.md">
      <description>The xspi_octal_polling_transfer example shows how to use xspi driver with polling. In this example, xspi will send data and operate the external Nor flash connected with XSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xspi_octal_polling_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="xspi_psram_edma_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/xspi/psram/edma_transfer/cm33_core0" doc="readme.md">
      <description>The xspi_psram_edma_transfer example shows how to use xspi driver with edma. In this example, xspi will send data and operate the external PSRAM connected with XSPI. Some simple flash command will be executed, such...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xspi_psram_edma_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="xspi_psram_polling_transfer_cm33_core0" folder="boards/mimxrt700evk/driver_examples/xspi/psram/polling_transfer/cm33_core0" doc="readme.md">
      <description>The xspi_psram_polling_transfer example shows how to use xspi driver with polling. In this example, xspi will send data and operate the external PSRAM connected with XSPI. Some simple flash command will be executed,...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xspi_psram_polling_transfer_cm33_core0.uvprojx"/>
      </project>
      <attributes/>
    </example>
    <example name="cdog_cm33_core1" folder="boards/mimxrt700evk/driver_examples/cdog/cm33_core1" doc="readme.md">
      <description>The CDOG Example project is a demonstration program that uses the KSDK software to show funcionality of Code Watchdog Timer.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cdog_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/cdog_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer_cm33_core1" folder="boards/mimxrt700evk/cmsis_driver_examples/lpuart/edma_transfer/cm33_core1" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpuart_edma_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer_cm33_core1" folder="boards/mimxrt700evk/cmsis_driver_examples/lpuart/interrupt_transfer/cm33_core1" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpuart_interrupt_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example_cm33_core1" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_match/cm33_core1" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example_cm33_core1" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_pwm/cm33_core1" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example_cm33_core1" folder="boards/mimxrt700evk/driver_examples/ctimer/simple_pwm_interrupt/cm33_core1" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_channel_link_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/channel_link/cm33_core1" doc="readme.md">
      <description>The EDMA4 channel link example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_channel_link_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_channel_link_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_interleave_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/interleave_transfer/cm33_core1" doc="readme.md">
      <description>The EDMA4 interleave example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_interleave_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_interleave_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/memory_to_memory/cm33_core1" doc="readme.md">
      <description>The EDMA4 memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_memory_to_memory_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/memory_to_memory_transfer/cm33_core1" doc="readme.md">
      <description>The EDMA4 memory to memory transfer example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_memory_to_memory_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memset_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/memset/cm33_core1" doc="readme.md">
      <description>The EDMA4 memset example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memset_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_memset_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_ping_pong_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/ping_pong_transfer/cm33_core1" doc="readme.md">
      <description>The EDMA4 ping pong transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_ping_pong_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_ping_pong_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_scatter_gather_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/scatter_gather/cm33_core1" doc="readme.md">
      <description>The EDMA4 scatter gather example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_scatter_gather_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_scatter_gather_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_wrap_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/edma4/wrap_transfer/cm33_core1" doc="readme.md">
      <description>The EDMA4 wrap transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_wrap_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/edma4_wrap_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gdet_cm33_core1" folder="boards/mimxrt700evk/driver_examples/gdet/cm33_core1" doc="readme.md">
      <description>The GDET Example project is a demonstration program that uses the MCUX SDK software to show funcionality of Glitch Detect (GDET) module.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gdet_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/gdet_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="glikey_cm33_core1" folder="boards/mimxrt700evk/driver_examples/glikey/cm33_core1" doc="readme.md">
      <description>The GLIKEY Example project is a demonstration program that uses the MCUX SDK software to show funcionality of GLIKEY IP.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/glikey_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/glikey_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt_cm33_core1" folder="boards/mimxrt700evk/driver_examples/gpio/input_interrupt/cm33_core1" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_cm33_core1" folder="boards/mimxrt700evk/demo_apps/hello_world/cm33_core1" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/hello_world_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_master_cm33_core1" folder="boards/mimxrt700evk/component_examples/i3c_bus/master/cm33_core1" doc="readme.md">
      <description>The i3c_bus_master example shows how to use i3c_bus component to create I3C bus and i3c master on bus.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_master_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_master_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_bus_slave_cm33_core1" folder="boards/mimxrt700evk/component_examples/i3c_bus/slave/cm33_core1" doc="readme.md">
      <description>The i3c_bus_slave example shows how to use i3c_bus component to work as i3c bus slave.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_bus_slave_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_bus_slave_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_master_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/edma_b2b_transfer/master/cm33_core1" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_master example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as master and another i3c instance on the other board as slave....See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_master_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_master_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_slave_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/edma_b2b_transfer/slave/cm33_core1" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as slave and another i3c instance on the other board as master....See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_slave_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_slave_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/interrupt_b2b_transfer/master/cm33_core1" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/interrupt_b2b_transfer/slave/cm33_core1" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_p3t1755_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/master_read_sensor_p3t1755/cm33_core1" doc="readme.md">
      <description>The i3c_master_read_sensor_p3t1755 example shows how to use i3c driver as master to communicate with sensor P3T1755.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_p3t1755_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_p3t1755_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/polling_b2b_transfer/master/cm33_core1" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave_cm33_core1" folder="boards/mimxrt700evk/driver_examples/i3c/polling_b2b_transfer/slave/cm33_core1" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="irtc_cm33_core1" folder="boards/mimxrt700evk/driver_examples/irtc/cm33_core1" doc="readme.md">
      <description>The IRTC project is a simple demonstration program of the SDK IRTC driver.This example is a low power module that provides time keeping and calendaring functions and additionally providesprotection against tampering,...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irtc_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/irtc_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_rb_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/lpuart/edma_rb_transfer/cm33_core1" doc="readme.md">
      <description>The lpuart_edma Ring Buffer Example project is to demonstrate usage of the KSDK lpuart driver.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_rb_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_rb_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/lpuart/edma_transfer/cm33_core1" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_cm33_core1" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt/cm33_core1" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt_rb_transfer/cm33_core1" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_cm33_core1" folder="boards/mimxrt700evk/driver_examples/lpuart/interrupt_transfer/cm33_core1" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_cm33_core1" folder="boards/mimxrt700evk/driver_examples/lpuart/polling/cm33_core1" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_interrupt_secondary_core" folder="boards/mimxrt700evk/driver_examples/mu/interrupt/cm33_core1" doc="readme.md">
      <description>The mu_interrupt example shows how to use MU driver in interrupt way:In this example:1. Core 0 send message to Core 1 in interrupt mode via MU module.2. Core 1 send message back to Core 0 in interrupt mode.3. Core 0...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_interrupt_secondary_core.uvprojx"/>
        <environment name="iar" load="iar/mu_interrupt_secondary_core.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_polling_secondary_core" folder="boards/mimxrt700evk/driver_examples/mu/polling/cm33_core1" doc="readme.md">
      <description>The mu_polling example shows how to use MU driver in polling way:In this example:1. Core 0 send message to Core 1 in polling mode via MU module.2. Core 1 send message back to Core 0 in polling mode.3. Core 0 receive...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_polling_secondary_core.uvprojx"/>
        <environment name="iar" load="iar/mu_polling_secondary_core.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project_cm33_core1" folder="boards/mimxrt700evk/demo_apps/new_project/cm33_core1" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/new_project_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ostimer_example_cm33_core1" folder="boards/mimxrt700evk/driver_examples/ostimer/cm33_core1" doc="readme.md">
      <description>The OSTIMER project is a simple demonstration program of the SDK OSTIMER driver. It sets the OSTIMER as the wakeup source from deep-sleep mode. After wakeup from deep-sleep mode, OS timer will set match value to...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ostimer_example_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/ostimer_example_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt_cm33_core1" folder="boards/mimxrt700evk/driver_examples/pint/pin_interrupt/cm33_core1" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt_cm33_core1.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt_cm33_core1.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sema42_dualcore_secondary_core" folder="boards/mimxrt700evk/driver_examples/sema42/dual_core/cm33_core1" doc="readme.md">
      <description>The sema42 example shows how to use SEMA42 driver to lock and unlock a sema gate:In this example:1. Firstly, Core 0 turn on LED and lock a sema gate then boot up Core 1 wake up.2. Core 1 must be wait until Core 0...See more details in readme document.</description>
      <board name="MIMXRT700-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema42_dualcore_secondary_core.uvprojx"/>
        <environment name="iar" load="iar/sema42_dualcore_secondary_core.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="mimxrt700evk" Cversion="1.0.0" condition="board.project_template.condition_id">
      <description>Board_project_template mimxrt700evk</description>
      <RTE_Components_h>
#ifndef FSL_SDK_DRIVER_QUICK_ACCESS_ENABLE
#define FSL_SDK_DRIVER_QUICK_ACCESS_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" name="boards/mimxrt700evk/project_template/board.h" projectpath="board"/>
        <file category="sourceC" name="boards/mimxrt700evk/project_template/board.c" projectpath="board"/>
        <file condition="allOf.core_ids=cm33_core0.condition_id" category="header" attr="config" name="boards/mimxrt700evk/project_template/cm33_core0/clock_config.h" version="1.0.0" projectpath="board" path="boards/mimxrt700evk/project_template"/>
        <file condition="allOf.core_ids=cm33_core0.condition_id" category="sourceC" attr="config" name="boards/mimxrt700evk/project_template/cm33_core0/clock_config.c" version="1.0.0" projectpath="board"/>
        <file condition="allOf.core_ids=cm33_core0.condition_id" category="header" attr="config" name="boards/mimxrt700evk/project_template/cm33_core0/pin_mux.h" version="1.0.0" projectpath="board" path="boards/mimxrt700evk/project_template"/>
        <file condition="allOf.core_ids=cm33_core0.condition_id" category="sourceC" attr="config" name="boards/mimxrt700evk/project_template/cm33_core0/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file condition="allOf.core_ids=cm33_core0.condition_id" category="header" attr="config" name="boards/mimxrt700evk/project_template/cm33_core0/peripherals.h" version="1.0.0" projectpath="board" path="boards/mimxrt700evk/project_template"/>
        <file condition="allOf.core_ids=cm33_core0.condition_id" category="sourceC" attr="config" name="boards/mimxrt700evk/project_template/cm33_core0/peripherals.c" version="1.0.0" projectpath="board"/>
        <file condition="allOf.core_ids=cm33_core1.condition_id" category="header" attr="config" name="boards/mimxrt700evk/project_template/cm33_core1/clock_config.h" version="1.0.0" projectpath="board" path="boards/mimxrt700evk/project_template"/>
        <file condition="allOf.core_ids=cm33_core1.condition_id" category="sourceC" attr="config" name="boards/mimxrt700evk/project_template/cm33_core1/clock_config.c" version="1.0.0" projectpath="board"/>
        <file condition="allOf.core_ids=cm33_core1.condition_id" category="header" attr="config" name="boards/mimxrt700evk/project_template/cm33_core1/pin_mux.h" version="1.0.0" projectpath="board" path="boards/mimxrt700evk/project_template"/>
        <file condition="allOf.core_ids=cm33_core1.condition_id" category="sourceC" attr="config" name="boards/mimxrt700evk/project_template/cm33_core1/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file condition="allOf.core_ids=cm33_core1.condition_id" category="header" attr="config" name="boards/mimxrt700evk/project_template/cm33_core1/peripherals.h" version="1.0.0" projectpath="board" path="boards/mimxrt700evk/project_template"/>
        <file condition="allOf.core_ids=cm33_core1.condition_id" category="sourceC" attr="config" name="boards/mimxrt700evk/project_template/cm33_core1/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/mimxrt700evk/project_template/"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK drivers" Csub="mimxrt700evk_flash_config" Cversion="2.0.0" condition="driver.flash_config.condition_id">
      <description>flash config block</description>
      <RTE_Components_h>
#ifndef BOOT_HEADER_ENABLE
#define BOOT_HEADER_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="sourceC" name="boards/mimxrt700evk/flash_config/flash_config.c" projectpath="flash_config"/>
        <file category="header" name="boards/mimxrt700evk/flash_config/flash_config.h" projectpath="flash_config"/>
        <file category="include" name="boards/mimxrt700evk/flash_config/"/>
      </files>
    </component>
  </components>
</package>
