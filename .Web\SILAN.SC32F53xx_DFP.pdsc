<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
	<vendor>SILAN</vendor>
	<name>SC32F53xx_DFP</name>
	<description>Silan SC32F53xx Device Family Pack</description>
	<url>https://github.com/Silanpack/Pack/raw/main/</url>
	
	<releases>
		<release version="1.1.5" date="2024-04-25">
			Update SC32F53xxH Flash algorithm
		</release>
	
		<release version="1.1.4" date="2023-11-12">
			update startup_SC32F53xxH.s
		</release>
	
		<release version="1.1.3" date="2023-07-27">
			Add SC32F53xxH
		</release>
	
		<release version="1.1.2" date="2023-07-13">
			Update Flash algorithm
		</release>
	
		<release version="1.1.1" date="2021-10-14">
			Update Drivers
		</release>
	
		<release version="1.1.0" date="2020-12-11">
			Initial Version
		</release>	
	</releases>
  
	<keywords>
		<!-- keywords for indexing -->
		<keyword>SC32F53xx</keyword>
		<keyword>SC32F5364</keyword>
		<keyword>SC32F53128</keyword>
		<keyword>SC32F53xxH</keyword>
		<keyword>SC32F5332H</keyword>
		<keyword>SC32F5364H</keyword>
	</keywords>

	<!-- devices section (mandatory for Device Family Packs) -->
	<devices>
		<family Dfamily="SC32F53 Series" Dvendor="SILAN:164">
			<!-- ************************  Subfamily 'SC32F53xx'  **************************** -->
			<subFamily DsubFamily="SC32F53xx">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="48000000"/>
				<feature type="VCC"				n="2.00"	m="5.50"/>
				<feature type="Temp"			n="-40"		m="105"/>
				<feature type="UART"			n="6"/>
				<feature type="SPI"				n="1"/>
				<feature type="Timer"			n="6"       m="16"/>
				<feature type="WDT"    			n="2"/>
				<feature type="ADC"    			n="16"      m="12"/>
			  
				<!-- *************************  Device 'SC32F53128'  ***************************** -->
				<device Dname="SC32F53128">
					<compile header="Device/Include/SC32F53128.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x4000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F53xx.FLM"        start="0x00000000"  size="0x20000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F53128.svd"/>
					<description>
SC32F53128 is a series of 32bit MCU based on Cortex-M0 with maximum operating frequency 48MHz. 
SC32F53128 supports up to 128KB FLASH and 16KB SRAM, and integrates abundant timers, communication interfaces (6-ch UARTS, 1-ch SPI, 1-ch I2C) and high-performance analog front-end processing modules, such as 12bit 500ksps ADC, multistage gain adjustable OPA, etc. This IC is especially suitable for the new generation of white electricity products, office automation, IOT and other applications.
					</description>
				</device>
			
				<!-- *************************  Device 'SC32F53128_BOOT'  ***************************** -->
				<device Dname="SC32F53128_BOOT">
					<compile header="Device/Include/SC32F53128.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x4000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F53xx_BOOT.FLM"        start="0x00000000"  size="0x20000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F53128.svd"/>
					<description>
SC32F53128 is a series of 32bit MCU based on Cortex-M0 with maximum operating frequency 48MHz. 
SC32F53128 supports up to 128KB FLASH and 16KB SRAM, and integrates abundant timers, communication interfaces (6-ch UARTS, 1-ch SPI, 1-ch I2C) and high-performance analog front-end processing modules, such as 12bit 500ksps ADC, multistage gain adjustable OPA, etc. This IC is especially suitable for the new generation of white electricity products, office automation, IOT and other applications.
					</description>
				</device>
				
				<!-- *************************  Device 'SC32F5364'  ***************************** -->
				<device Dname="SC32F5364">
					<compile header="Device/Include/SC32F5364.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F53xx.FLM"        start="0x00000000"  size="0x10000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F5364.svd"/>
					<description>
SC32F5364 is a series of 32bit MCU based on Cortex-M0 with maximum operating frequency 48MHz. 
SC32F5364 supports up to 64KB FLASH and 8KB SRAM, and integrates abundant timers, communication interfaces (6-ch UARTS, 1-ch SPI, 1-ch I2C) and high-performance analog front-end processing modules, such as 12bit 500ksps ADC, multistage gain adjustable OPA, etc. This IC is especially suitable for the new generation of white electricity products, office automation, IOT and other applications.
					</description>
				</device>
			
				<!-- *************************  Device 'SC32F5364_BOOT'  ***************************** -->
				<device Dname="SC32F5364_BOOT">
					<compile header="Device/Include/SC32F5364.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F53xx_BOOT.FLM"        start="0x00000000"  size="0x10000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F5364.svd"/>
					<description>
SC32F5364 is a series of 32bit MCU based on Cortex-M0 with maximum operating frequency 48MHz. 
SC32F5364 supports up to 64KB FLASH and 8KB SRAM, and integrates abundant timers, communication interfaces (6-ch UARTS, 1-ch SPI, 1-ch I2C) and high-performance analog front-end processing modules, such as 12bit 500ksps ADC, multistage gain adjustable OPA, etc. This IC is especially suitable for the new generation of white electricity products, office automation, IOT and other applications.
					</description>
				</device>
			</subFamily>
			
			<!-- ************************  Subfamily 'SC32F53xxH'  **************************** -->
			<subFamily DsubFamily="SC32F53xxH">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="48000000"/>
				<debug svd="SVD/SC32F53xxH.svd"/>
			  
				<!-- *************************  Device 'SC32F5332H'  ***************************** -->
				<device Dname="SC32F5332H">
					<compile header="Device/Include/SC32F53xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x8000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2800"     init="0"   default="1"/>
					<algorithm name="Flash/SC32F5332H.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F5332H_BOOT'  ***************************** -->
				<device Dname="SC32F5332H_BOOT">
					<compile header="Device/Include/SC32F53xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x8000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2800"     init="0"   default="1"/>
					<algorithm name="Flash/SC32F5332H_BOOT.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F5364H'  ***************************** -->
				<device Dname="SC32F5364H">
					<compile header="Device/Include/SC32F53xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2800"     init="0"   default="1"/>
					<algorithm name="Flash/SC32F5364H.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F5364H_BOOT'  ***************************** -->
				<device Dname="SC32F5364H_BOOT">
					<compile header="Device/Include/SC32F53xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2800"     init="0"   default="1"/>
					<algorithm name="Flash/SC32F5364H_BOOT.FLM" start="0x00000000" size="0x10000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
				</device>		
			</subFamily>
		</family>
	</devices>

	<!-- examples section (optional for all Software Packs)-->
	<!--
	<examples>
	</examples>
	-->
	
	<!-- conditions section (optional for all Software Packs)-->
	<conditions>
		<!-- Compiler Conditions -->
		<condition id="Compiler ARMCC">
			<require Tcompiler="ARMCC"/>
		</condition>
    
		<condition id="SC32F53128">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F53128"/>
		</condition>
	
		<condition id="SC32F53128_BOOT">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F53128_BOOT"/>
		</condition>
		
		<condition id="SC32F5364">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5364"/>
		</condition>
	
		<condition id="SC32F5364_BOOT">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5364_BOOT"/>
		</condition>
		
		<condition id="SC32F5332H">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5332H"/>
		</condition>
		
		<condition id="SC32F5332H_BOOT">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5332H_BOOT"/>
		</condition>
		
		<condition id="SC32F5364H">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5364H"/>
		</condition>
		
		<condition id="SC32F5364H_BOOT">
			<description>Silan SC32F53 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5364H_BOOT"/>
		</condition>
    
		<condition id="Startup ARM">
			<description>Startupassembler file for ARMCC</description>
			<require Tcompiler="ARMCC"/>
		</condition>

		<condition id="Startup GCC">
			<description>Startupassembler file for GCC</description>
			<require Tcompiler="GCC"/>
		</condition>

		<condition id="Startup IAR">
			<description>Startupassembler file for IAR</description>
			<require Tcompiler="IAR"/>
		</condition>
		
		<condition id="SC32F53xx">
			<description>Silan Microelectronics SC32F53128xx Device</description>
			<accept Dvendor="SILAN:164" Dname="SC32F5364"/>
			<accept Dvendor="SILAN:164" Dname="SC32F53128"/>
			<accept Dvendor="SILAN:164" Dname="SC32F5364_BOOT"/>
			<accept Dvendor="SILAN:164" Dname="SC32F53128_BOOT"/>
		</condition>
		
		<condition id="SC32F53xx STDPERIPH">
			<description>Silan Microelectronics SC32F53xx with Standard Peripherals Drivers Framework</description>
			<require condition="SC32F53xx"/>
			<require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework"/>
		</condition>
	</conditions>
  
	<!-- component section (optional for all Software Packs)-->
	<components>
		<!-- Startup SC32F53128 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F53128">
			<description>System Startup for Slian SC32F53128</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F53128.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F53128.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F53128.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F53128.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F53128_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F53128_BOOT">
			<description>System Startup for Slian SC32F53128_BOOT</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F53128_boot.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F53128_boot.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F53128_boot.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F53128.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5364 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5364">
			<description>System Startup for Slian SC32F5364</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5364.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5364.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5364.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5364.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5364_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5364_BOOT">
			<description>System Startup for Slian SC32F5364_BOOT</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5364_boot.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5364_boot.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5364_boot.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F5364.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5332H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5332H">
			<description>System Startup for Slian SC32F5332H Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F53xxH.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F53xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5332H_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5332H_BOOT">
			<description>System Startup for Slian SC32F5332H_BOOT</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F53xxH.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F53xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
	
		<!-- Startup SC32F5364H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5364H">
			<description>System Startup for Slian SC32F5364H</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F53xxH.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F53xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5364H_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5364H_BOOT">
			<description>System Startup for Slian SC32F5364H_BOOT</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F53xxH.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F53xxH.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F53xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>

		<!-- START: Silan Microelectronics Standard Peripherals Drivers -->
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework" Cversion="1.0.0" condition="SC32F53xx STDPERIPH">
			<description>Standard Peripherals Drivers Framework</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_FRAMEWORK
      		</RTE_Components_h>
			<files>
				<!-- 
				<file category="doc" name="Device/StdPeriph_Driver/sc32f53xx_stdperiph_lib_um.pdf"/>
				-->
				<file category="include" name="Device/StdPeriph_Driver/inc/"/>
				<file category="header"  name="Device/StdPeriph_Driver/inc/misc.h"/>
				<file category="source"  name="Device/StdPeriph_Driver/src/misc.c"/>
				<file category="header" name="Device/StdPeriph_Driver/templates/sc32f53xx_conf.h" attr="config" version="2.0.1"/>
				<file category="header" name="Device/StdPeriph_Driver/templates/sc32f53xx_it.h" attr="template" select="Interrupt Service Routines"/>
				<file category="source" name="Device/StdPeriph_Driver/templates/sc32f53xx_it.c" attr="template" select="Interrupt Service Routines"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="ADC" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>ADC driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_ADC
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_adc.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_adc.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CLOCK" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>CLOCK driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_CLOCK
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_clock.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_clock.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CMP" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>CMP driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_CMP
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_cmp.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_cmp.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CRC" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>CRC driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_CRC
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_crc.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_crc.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="DMA" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>DMA driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_DMA
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_dma.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_dma.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="ERU" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>ERU driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_ERU
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_eru.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_eru.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="FLASH" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>FLASH driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_FLASH
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_flash.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_flash.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="GPIO" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>GPIO driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_GPIO
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_gpio.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_gpio.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="I2C" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>I2C driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_I2C
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_i2c.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_i2c.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="LED" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>LED driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_LED
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_led.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_led.c"/>
			</files>
		</component>		
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="OPA" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>OPA driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_OPA
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_opa.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_opa.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="PPU" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>PPU driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_PPU
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_ppu.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_ppu.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SSP" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>SSP driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_SSP
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_ssp.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_ssp.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SYSCFG" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>SYSCFG driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_SYSCFG
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_syscfg.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_syscfg.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SYSTEM" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>SYSTEM driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_SYSTEM
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_system.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_system.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="TIM" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>TIM driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_TIM
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_tim.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_tim.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="UART" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>UART driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_UART
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_uart.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_uart.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="WDT" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>WDT driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_WDT
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_wdt.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_wdt.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="IWDT" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>IWDT driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_IWDT
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_iwdt.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_iwdt.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="WT" Cversion="2.0.0" condition="SC32F53xx STDPERIPH">
			<description>WT driver for SC32F53xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_WT
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f53xx_wt.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f53xx_wt.c"/>
			</files>
		</component>
		<!-- END: Slian Microelectronics Standard Peripherals Drivers -->
	</components>

</package>
