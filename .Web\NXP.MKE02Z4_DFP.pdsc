<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>MKE02Z4_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MKE02Z4</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version='18.0.0' date='2024-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version='17.0.0' date='2023-07-27'>NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version='16.0.0' date='2023-01-16'>NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version='15.0.0' date='2022-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version='14.0.0' date='2022-01-05'>NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version='13.1.0' date='2021-07-13'>NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version='13.0.0' date='2021-02-05'>
      NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files
    </release>
    <release version='12.3.0' date='2021-01-14'>NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version='12.2.0' date='2020-07-20'>NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version='12.1.0' date='2019-12-19'>NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version='12.0.0' date='2019-06-10'>NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version='11.0.1' date='2019-04-26'>Removed invalid entries from Software Content Register</release>
    <release version='11.0.0' date='2018-12-19'>NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version='10.0.3' date='2018-07-16'>
      A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).
    </release>
    <release version='10.0.2' date='2018-05-25'>NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version='10.0.1' date='2018-04-04'>NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version='10.0.0' date='2018-01-19'>
      NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0
    </release>
    <release version='2.3.0' date='2017-11-17'>NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='MKE02Z4' Dvendor='NXP:11'>
      <description>
        Kinetis KE02-40 MHz, robust Microcontrollers (MCUs) based on ARM Cortex-M0+ Core
      </description>
      <device Dname='MKE02Z16xxx4'>
        <processor Dcore='Cortex-M0+' Dfpu='NO_FPU' Dmpu='NO_MPU' Dendian='Little-endian' Dclock='40000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/MKE02Z4/iar/MKE02Z16xxx4_flash.icf'/>
        </environment>
        <memory name='PROGRAM_FLASH' start='0x00000000' size='0x4000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x1ffffe00' size='0x0800' access='rw' default='1'/>
        <algorithm name='devices/MKE02Z4/arm/MKE02Zxxx_P16KB.FLM' start='0x00000000' size='0x00004000' RAMstart='0x1ffffe00' RAMsize='0x00000800' default='1'/>
        <algorithm name='devices/MKE02Z4/arm/MKE02Zxxx_EE256B.FLM' start='0x10000000' size='0x00000100' RAMstart='0x1ffffe00' RAMsize='0x00000800' default='1'/>
        <debug svd='devices/MKE02Z4/MKE02Z4.xml'/>
        <variant Dvariant='MKE02Z16VFM4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z16VFM4'/>
        </variant>
        <variant Dvariant='MKE02Z16VLC4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z16VLC4'/>
        </variant>
        <variant Dvariant='MKE02Z16VLD4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z16VLD4'/>
        </variant>
      </device>
      <device Dname='MKE02Z32xxx4'>
        <processor Dcore='Cortex-M0+' Dfpu='NO_FPU' Dmpu='NO_MPU' Dendian='Little-endian' Dclock='40000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/MKE02Z4/iar/MKE02Z32xxx4_flash.icf'/>
        </environment>
        <memory name='PROGRAM_FLASH' start='0x00000000' size='0x8000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x1ffffc00' size='0x1000' access='rw' default='1'/>
        <algorithm name='devices/MKE02Z4/arm/MKE02Zxxx_P32KB.FLM' start='0x00000000' size='0x00008000' RAMstart='0x1ffffc00' RAMsize='0x00001000' default='1'/>
        <algorithm name='devices/MKE02Z4/arm/MKE02Zxxx_EE256B.FLM' start='0x10000000' size='0x00000100' RAMstart='0x1ffffc00' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/MKE02Z4/MKE02Z4.xml'/>
        <variant Dvariant='MKE02Z32VFM4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z32VFM4'/>
        </variant>
        <variant Dvariant='MKE02Z32VLC4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z32VLC4'/>
        </variant>
        <variant Dvariant='MKE02Z32VLD4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z32VLD4'/>
        </variant>
        <variant Dvariant='MKE02Z32VLH4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z32VLH4'/>
        </variant>
        <variant Dvariant='MKE02Z32VQH4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z32VQH4'/>
        </variant>
      </device>
      <device Dname='MKE02Z64xxx4'>
        <processor Dcore='Cortex-M0+' Dfpu='NO_FPU' Dmpu='NO_MPU' Dendian='Little-endian' Dclock='40000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/MKE02Z4/iar/MKE02Z64xxx4_flash.icf'/>
        </environment>
        <memory name='PROGRAM_FLASH' start='0x00000000' size='0x010000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x1ffffc00' size='0x1000' access='rw' default='1'/>
        <algorithm name='devices/MKE02Z4/arm/MKE02Zxxx_P64KB.FLM' start='0x00000000' size='0x00010000' RAMstart='0x1ffffc00' RAMsize='0x00001000' default='1'/>
        <algorithm name='devices/MKE02Z4/arm/MKE02Zxxx_EE256B.FLM' start='0x10000000' size='0x00000100' RAMstart='0x1ffffc00' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/MKE02Z4/MKE02Z4.xml'/>
        <variant Dvariant='MKE02Z64VFM4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z64VFM4'/>
        </variant>
        <variant Dvariant='MKE02Z64VLC4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z64VLC4'/>
        </variant>
        <variant Dvariant='MKE02Z64VLD4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z64VLD4'/>
        </variant>
        <variant Dvariant='MKE02Z64VLH4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z64VLH4'/>
        </variant>
        <variant Dvariant='MKE02Z64VQH4'>
          <compile header='devices/MKE02Z4/fsl_device_registers.h' define='CPU_MKE02Z64VQH4'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.MKE02Z4.internal_condition'>
      <accept Dname='MKE02Z16VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z16VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z16VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VQH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VQH4' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.device=MKE02Z4.internal_condition'>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT25Fxxxx.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.eeprom_AT45DBxxxx.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.eeprom_InternalFlash.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.ftm_adapter.condition_id'>
      <require condition='allOf.driver.ftm, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ftm, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ftm'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.i2c, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.i2c, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=component.i2c_adapter, driver.i2c, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.i2c_adapter, driver.i2c, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.allOf=component.i2c_adapter, driver.i2c.internal_condition'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.i2c_adapter, driver.i2c.internal_condition'>
      <accept condition='allOf.component.i2c_adapter, driver.i2c.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter, driver.i2c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.log.condition_id'>
      <require condition='allOf.driver.common, utility.str, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.str, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.log.backend.debug_console_lite.condition_id'>
      <require condition='allOf.component.log, driver.common, utility.debug_console_lite, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, utility.debug_console_lite, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.log.backend.ringbuffer.condition_id'>
      <require condition='allOf.component.log, driver.common, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.log, driver.common, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='log'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.pit_adapter.condition_id'>
      <require condition='allOf.driver.pit, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.pit, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.pwm_ftm_adapter.condition_id'>
      <require condition='allOf.driver.ftm, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.pwm_tpm_adapter.condition_id'>
      <require condition='allOf.driver.tpm, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.tpm, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tpm'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.rng_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=component.software_rng_adapter, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.software_rng_adapter, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.component.software_rng_adapter.internal_condition'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.component.software_rng_adapter.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter'/>
    </condition>
    <condition id='component.rnga_adapter.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.spi_adapter, driver.spi, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.spi_adapter, driver.spi, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.allOf=component.spi_adapter, driver.spi.internal_condition'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.spi_adapter, driver.spi.internal_condition'>
      <accept condition='allOf.component.spi_adapter, driver.spi.internal_condition'/>
    </condition>
    <condition id='allOf.component.spi_adapter, driver.spi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.anyOf=driver.uart, component.uart_adapter, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.uart, component.uart_adapter, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.driver.uart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='uart_adapter'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.uart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='uart'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.software_crc_adapter.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.software_rng_adapter.condition_id'>
      <require condition='allOf.component.rng_adapter_interface, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.rng_adapter_interface, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.spi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.spi, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.spi, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.ftm_adapter, driver.ftm, allOf=component.pit_adapter, driver.pit, allOf=component.tpm_adapter, driver.tpm, component.lists, driver.common, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.ftm_adapter, driver.ftm, allOf=component.pit_adapter, driver.pit, allOf=component.tpm_adapter, driver.tpm, component.lists, driver.common, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.allOf=component.ftm_adapter, driver.ftm, allOf=component.pit_adapter, driver.pit, allOf=component.tpm_adapter, driver.tpm.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.ftm_adapter, driver.ftm, allOf=component.pit_adapter, driver.pit, allOf=component.tpm_adapter, driver.tpm.internal_condition'>
      <accept condition='allOf.component.ftm_adapter, driver.ftm.internal_condition'/>
      <accept condition='allOf.component.pit_adapter, driver.pit.internal_condition'/>
      <accept condition='allOf.component.tpm_adapter, driver.tpm.internal_condition'/>
    </condition>
    <condition id='allOf.component.ftm_adapter, driver.ftm.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ftm_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ftm'/>
    </condition>
    <condition id='allOf.component.pit_adapter, driver.pit.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='pit'/>
    </condition>
    <condition id='allOf.component.tpm_adapter, driver.tpm.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tpm_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='tpm'/>
    </condition>
    <condition id='component.tpm_adapter.condition_id'>
      <require condition='allOf.driver.tpm, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='component.uart_adapter.condition_id'>
      <require condition='allOf.driver.uart, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.uart, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='uart'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=MKE02Z4.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'>
      <accept Dname='MKE02Z16VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z16VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z16VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VQH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VQH4' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'>
      <require condition='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='device_ids.MKE02Z16xxx4.internal_condition'>
      <accept Dname='MKE02Z16VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z16VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z16VLD4' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=MKE02Z16xxx4.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.MKE02Z16xxx4.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='device_ids.MKE02Z32xxx4.internal_condition'>
      <accept Dname='MKE02Z32VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VLH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z32VQH4' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=MKE02Z32xxx4.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.MKE02Z32xxx4.internal_condition'/>
    </condition>
    <condition id='device_ids.MKE02Z64xxx4.internal_condition'>
      <accept Dname='MKE02Z64VFM4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLC4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLD4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VLH4' Dvendor='NXP:11'/>
      <accept Dname='MKE02Z64VQH4' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=MKE02Z64xxx4.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=MKE02Z16xxx4.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.MKE02Z16xxx4.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=MKE02Z32xxx4.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.MKE02Z32xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=MKE02Z64xxx4.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=MKE02Z16xxx4.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.MKE02Z16xxx4.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=MKE02Z32xxx4.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.MKE02Z32xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=MKE02Z64xxx4.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.component.uart_adapter, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4, device.startup, driver.clock, driver.common, driver.gpio_1, driver.port_ke02, driver.uart, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.uart_adapter, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4, device.startup, driver.clock, driver.common, driver.gpio_1, driver.port_ke02, driver.uart, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='uart_adapter'/>
      <require condition='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='port'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='uart'/>
      <require condition='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <accept condition='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'/>
      <accept condition='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config'/>
    </condition>
    <condition id='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MKE02Z4_system'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MKE02Z4_header'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='driver.acmp_1.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.adc_5v12b_ll18_015.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_i2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.i2c, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device.RTE, driver.i2c, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_spi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.spi, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, device.RTE, driver.spi, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='spi'/>
      <require condition='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_uart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, device.RTE, driver.uart, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, device.RTE, driver.uart, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='uart'/>
      <require condition='device_id.MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm0p.condition_id'>
      <require condition='cores.cm0p.internal_condition'/>
    </condition>
    <condition id='cores.cm0p.internal_condition'>
      <accept Dcore='Cortex-M0+'/>
    </condition>
    <condition id='driver.flash_ftmr.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.ftm.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.gpio_1.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.i2c.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.i2c_edma.condition_id'>
      <require condition='allOf.driver.i2c, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.i2c, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='driver.irq.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.kbi.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.pit.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.port_ke02.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='driver.rtc_1.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.spi.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.tpm.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.uart.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='driver.wdog8.condition_id'>
      <require condition='allOf.driver.common, device_id=MKE02Z16xxx4, MKE02Z32xxx4, MKE02Z64xxx4.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.uart, driver.common, not=utility.debug_console, utility.str, component.uart_adapter, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.uart, driver.common, not=utility.debug_console, utility.str, component.uart_adapter, device=MKE02Z4.internal_condition'>
      <require condition='anyOf.driver.uart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.debug_console.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='uart_adapter'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='not.utility.debug_console.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.notifier.condition_id'>
      <require condition='allOf.device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=MKE02Z4.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=MKE02Z4.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MKE02Z4.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=MKE02Z4.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT25Fxxxx' Cversion='1.0.0' condition='component.eeprom_AT25Fxxxx.condition_id'>
      <description>Component eeprom_at25fxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT25Fxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_AT45DBxxxx' Cversion='1.0.0' condition='component.eeprom_AT45DBxxxx.condition_id'>
      <description>Component eeprom_at45dbxxxx</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_AT45DBxxxx_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eeprom_InternalFlash' Cversion='1.0.0' condition='component.eeprom_InternalFlash.condition_id'>
      <description>Component eeprom_internalflash</description>
      <files>
        <file category='doc' name='components/extern_flash/component.eeprom_InternalFlash_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ftm_adapter' Cversion='1.0.0' condition='component.ftm_adapter.condition_id'>
      <description>Component ftm_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_FTM
#define TIMER_PORT_TYPE_FTM 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ftm.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter' Cversion='1.0.0' condition='component.i2c_adapter.condition_id'>
      <description>Component i2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_i2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='log' Cversion='1.0.0' condition='component.log.condition_id'>
      <description>Component log</description>
      <files>
        <file category='header' name='components/log/fsl_component_log.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log.c' projectpath='component/log'/>
        <file category='header' name='components/log/fsl_component_log_config.h' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole' Cversion='1.0.0' condition='component.log.backend.debug_console.condition_id'>
      <description>Component log backend debug console</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='debugconsole_lite' Cversion='1.0.0' condition='component.log.backend.debug_console_lite.condition_id'>
      <description>Component log backend debug console lite</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_debugconsole.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_debugconsole.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ringbuffer' Cversion='1.0.0' condition='component.log.backend.ringbuffer.condition_id'>
      <description>Component log backend ring buffer</description>
      <files>
        <file category='header' name='components/log/fsl_component_log_backend_ringbuffer.h' projectpath='component/log'/>
        <file category='sourceC' name='components/log/fsl_component_log_backend_ringbuffer.c' projectpath='component/log'/>
        <file category='include' name='components/log/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pit_adapter' Cversion='1.0.0' condition='component.pit_adapter.condition_id'>
      <description>Component pit_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_PIT
#define TIMER_PORT_TYPE_PIT 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_pit.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm_ftm_adapter' Cversion='1.0.0' condition='component.pwm_ftm_adapter.condition_id'>
      <description>Component pwm_ftm_adapter</description>
      <files>
        <file category='header' name='components/pwm/fsl_adapter_pwm.h' projectpath='component/pwm'/>
        <file category='sourceC' name='components/pwm/fsl_adapter_pwm_ftm.c' projectpath='component/pwm'/>
        <file category='include' name='components/pwm/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm_tpm_adapter' Cversion='1.0.0' condition='component.pwm_tpm_adapter.condition_id'>
      <description>Component pwm_tpm_adapter</description>
      <files>
        <file category='header' name='components/pwm/fsl_adapter_pwm.h' projectpath='component/pwm'/>
        <file category='sourceC' name='components/pwm/fsl_adapter_pwm_tpm.c' projectpath='component/pwm'/>
        <file category='include' name='components/pwm/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rng_adapter_interface' Cversion='1.0.0' condition='component.rng_adapter_interface.condition_id'>
      <description>Component rng_adapter_interface</description>
      <files>
        <file category='header' name='components/rng/fsl_adapter_rng.h' projectpath='component/rng'/>
        <file category='include' name='components/rng/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rnga_adapter' Cversion='1.0.0' condition='component.rnga_adapter.condition_id'>
      <description>Component rnga_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_rnga.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_crc_adapter' Cversion='1.0.0' condition='component.software_crc_adapter.condition_id'>
      <description>Component software_crc_adapter</description>
      <files>
        <file category='header' name='components/crc/fsl_adapter_crc.h' projectpath='component/crc'/>
        <file category='sourceC' name='components/crc/fsl_adapter_software_crc.c' projectpath='component/crc'/>
        <file category='include' name='components/crc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='software_rng_adapter' Cversion='1.0.0' condition='component.software_rng_adapter.condition_id'>
      <description>Component software_rng_adapter</description>
      <files>
        <file category='sourceC' name='components/rng/fsl_adapter_software_rng.c' projectpath='component/rng'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi_adapter' Cversion='1.0.0' condition='component.spi_adapter.condition_id'>
      <description>Component spi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_spi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tpm_adapter' Cversion='1.0.0' condition='component.tpm_adapter.condition_id'>
      <description>Component tpm_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_TPM
#define TIMER_PORT_TYPE_TPM 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_tpm.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='uart_adapter' Cversion='1.0.0' condition='component.uart_adapter.condition_id'>
      <description>Component uart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_uart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MKE02Z4_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device MKE02Z4_cmsis</description>
      <files>
        <file category='header' name='devices/MKE02Z4/fsl_device_registers.h' projectpath='device'/>
        <file category='header' name='devices/MKE02Z4/MKE02Z4.h' projectpath='device'/>
        <file category='header' name='devices/MKE02Z4/MKE02Z4_features.h' projectpath='device'/>
        <file category='header' name='devices/MKE02Z4/MKE02Z4_COMMON.h' projectpath='device'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_ACMP.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_ADC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_CRC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_FGPIO.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_FTM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_FTMRH.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_GPIO.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_I2C.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_ICS.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_IRQ.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_KBI.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_MCM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_OSC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_PIT.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_PMC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_PORT.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_ROM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_RTC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_SIM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_SPI.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_UART.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MKE02Z4/periph1/PERI_WDOG.h' projectpath='device/periph1'/>
        <file category='include' name='devices/MKE02Z4/'/>
        <file category='include' name='devices/MKE02Z4/periph1/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='header' attr='config' name='devices/MKE02Z4/template/RTE_Device.h' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MKE02Z4/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='MKE02Z4_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device MKE02Z4_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, device_ids=MKE02Z16xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/arm/MKE02Z16xxx4_flash.scf' version='1.0.0' projectpath='MKE02Z4/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MKE02Z16xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/arm/MKE02Z16xxx4_ram.scf' version='1.0.0' projectpath='MKE02Z4/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MKE02Z32xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/arm/MKE02Z32xxx4_flash.scf' version='1.0.0' projectpath='MKE02Z4/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MKE02Z32xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/arm/MKE02Z32xxx4_ram.scf' version='1.0.0' projectpath='MKE02Z4/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MKE02Z64xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/arm/MKE02Z64xxx4_flash.scf' version='1.0.0' projectpath='MKE02Z4/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MKE02Z64xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/arm/MKE02Z64xxx4_ram.scf' version='1.0.0' projectpath='MKE02Z4/arm'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MKE02Z16xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/gcc/MKE02Z16xxx4_flash.ld' version='1.0.0' projectpath='MKE02Z4/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MKE02Z16xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/gcc/MKE02Z16xxx4_ram.ld' version='1.0.0' projectpath='MKE02Z4/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MKE02Z32xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/gcc/MKE02Z32xxx4_flash.ld' version='1.0.0' projectpath='MKE02Z4/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MKE02Z32xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/gcc/MKE02Z32xxx4_ram.ld' version='1.0.0' projectpath='MKE02Z4/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MKE02Z64xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/gcc/MKE02Z64xxx4_flash.ld' version='1.0.0' projectpath='MKE02Z4/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MKE02Z64xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/gcc/MKE02Z64xxx4_ram.ld' version='1.0.0' projectpath='MKE02Z4/gcc'/>
        <file condition='allOf.toolchains=iar, device_ids=MKE02Z16xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/iar/MKE02Z16xxx4_flash.icf' version='1.0.0' projectpath='MKE02Z4/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MKE02Z16xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/iar/MKE02Z16xxx4_ram.icf' version='1.0.0' projectpath='MKE02Z4/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MKE02Z32xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/iar/MKE02Z32xxx4_flash.icf' version='1.0.0' projectpath='MKE02Z4/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MKE02Z32xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/iar/MKE02Z32xxx4_ram.icf' version='1.0.0' projectpath='MKE02Z4/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MKE02Z64xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/iar/MKE02Z64xxx4_flash.icf' version='1.0.0' projectpath='MKE02Z4/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MKE02Z64xxx4.condition_id' category='linkerScript' attr='config' name='devices/MKE02Z4/iar/MKE02Z64xxx4_ram.icf' version='1.0.0' projectpath='MKE02Z4/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='MKE02Z4' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template MKE02Z4</description>
      <files>
        <file category='header' attr='config' name='devices/MKE02Z4/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MKE02Z4/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MKE02Z4/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MKE02Z4/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MKE02Z4/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MKE02Z4/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MKE02Z4/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MKE02Z4/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MKE02Z4/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device MKE02Z4_startup</description>
      <files>
        <file condition='allOf.toolchains=iar.condition_id' category='sourceAsm' attr='config' name='devices/MKE02Z4/iar/startup_MKE02Z4.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceAsm' attr='config' name='devices/MKE02Z4/gcc/startup_MKE02Z4.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk.condition_id' category='sourceAsm' attr='config' name='devices/MKE02Z4/arm/startup_MKE02Z4.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MKE02Z4_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device MKE02Z4_system</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/system_MKE02Z4.c' projectpath='device'/>
        <file category='header' name='devices/MKE02Z4/system_MKE02Z4.h' projectpath='device'/>
        <file category='include' name='devices/MKE02Z4/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='acmp' Cversion='2.0.2' condition='driver.acmp_1.condition_id'>
      <description>ACMP Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_acmp.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_acmp.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='adc' Cversion='2.1.0' condition='driver.adc_5v12b_ll18_015.condition_id'>
      <description>ADC12 Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_adc.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_adc.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.2.3' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='i2c_cmsis' Cversion='2.2.0' Capiversion='2.3.0' condition='driver.cmsis_i2c.condition_id'>
      <description>I2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/cmsis_drivers/fsl_i2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/cmsis_drivers/fsl_i2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='spi_cmsis' Cversion='2.3.0' Capiversion='2.2.0' condition='driver.cmsis_spi.condition_id'>
      <description>SPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/cmsis_drivers/fsl_spi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/cmsis_drivers/fsl_spi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='uart_cmsis' Cversion='2.2.0' Capiversion='2.3.0' condition='driver.cmsis_uart.condition_id'>
      <description>UART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/cmsis_drivers/fsl_uart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/cmsis_drivers/fsl_uart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm0p.condition_id' category='sourceC' name='devices/MKE02Z4/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm0p.condition_id' category='header' name='devices/MKE02Z4/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash' Cversion='2.1.2' condition='driver.flash_ftmr.condition_id'>
      <description>Flash Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_flash.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_flash.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ftm' Cversion='2.7.1' condition='driver.ftm.condition_id'>
      <description>FTM Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_ftm.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_ftm.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.1.1' condition='driver.gpio_1.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.0.9' condition='driver.i2c.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_i2c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_i2c.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma' Cversion='2.0.9' condition='driver.i2c_edma.condition_id'>
      <description>I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_i2c_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_i2c_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='irq' Cversion='2.0.2' condition='driver.irq.condition_id'>
      <description>IRQ Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_irq.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_irq.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='kbi' Cversion='2.0.3' condition='driver.kbi.condition_id'>
      <description>KBI Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_kbi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_kbi.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pit' Cversion='2.2.0' condition='driver.pit.condition_id'>
      <description>PIT Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_pit.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_pit.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='port' Cversion='2.0.2' condition='driver.port_ke02.condition_id'>
      <description>KE02 Port Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_port.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_port.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtc' Cversion='2.0.6' condition='driver.rtc_1.condition_id'>
      <description>RTC Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_rtc.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_rtc.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='spi' Cversion='2.1.4' condition='driver.spi.condition_id'>
      <description>SPI Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_spi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_spi.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tpm' Cversion='2.3.5' condition='driver.tpm.condition_id'>
      <description>TPM Driver</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_tpm.c' projectpath='drivers'/>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_tpm.h' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='uart' Cversion='2.5.1' condition='driver.uart.condition_id'>
      <description>UART Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_uart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_uart.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wdog' Cversion='2.0.1' condition='driver.wdog8.condition_id'>
      <description>WDOG Driver</description>
      <files>
        <file category='header' name='devices/MKE02Z4/drivers/fsl_wdog8.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MKE02Z4/drivers/fsl_wdog8.c' projectpath='drivers'/>
        <file category='include' name='devices/MKE02Z4/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MKE02Z4/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MKE02Z4/utilities/fsl_sbrk.c' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/MKE02Z4/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MKE02Z4/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MKE02Z4/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/MKE02Z4/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MKE02Z4/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MKE02Z4/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/MKE02Z4/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/MKE02Z4/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/MKE02Z4/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/MKE02Z4/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/MKE02Z4/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/MKE02Z4/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/MKE02Z4/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/MKE02Z4/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='notifier' Cversion='1.0.0' condition='utility.notifier.condition_id'>
      <description>Utility notifier</description>
      <files>
        <file category='sourceC' name='devices/MKE02Z4/utilities/fsl_notifier.c' projectpath='utilities/utilities'/>
        <file category='header' name='devices/MKE02Z4/utilities/fsl_notifier.h' projectpath='utilities/utilities'/>
        <file category='include' name='devices/MKE02Z4/utilities/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/MKE02Z4/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MKE02Z4/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/MKE02Z4/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/MKE02Z4/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/MKE02Z4/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/MKE02Z4/utilities/str/'/>
      </files>
    </component>
  </components>
</package>