{"default-registry": {"kind": "git", "baseline": "032d9d0820db290ce9ff644dabfdf564343013ae", "repository": "https://github.com/microsoft/vcpkg"}, "registries": [{"kind": "artifact", "location": "https://aka.ms/vcpkg-ce-default", "name": "microsoft"}, {"kind": "artifact", "location": "https://aka.ms/vcpkg-artifacts-arm", "name": "arm"}], "requires": {"microsoft:cmake": "^3.25.2", "microsoft:ninja": "^1.10.2", "arm:compilers/arm/armclang": "^6.20.0", "arm:compilers/arm/arm-none-eabi-gcc": "^13.2.1", "arm:compilers/arm/llvm-embedded": "^17.0.1", "arm:tools/open-cmsis-pack/cmsis-toolbox": "^2.1.0", "arm:models/arm/avh-fvp": "^11.22.39"}}