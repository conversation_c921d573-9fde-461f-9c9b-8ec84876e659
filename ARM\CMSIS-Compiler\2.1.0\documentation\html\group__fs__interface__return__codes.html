<!-- HTML header for doxygen 1.9.6-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Compiler Support: Return Codes</title>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="tabs.js"></script>
<script type="text/javascript" src="footer.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<script type="text/javascript" src="darkmode_toggle.js"></script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="extra_navtree.css" rel="stylesheet" type="text/css"/>
<link href="extra_search.css" rel="stylesheet" type="text/css"/>
<link href="extra_tabs.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><a href="https://www.keil.arm.com/cmsis" target="_blank"><img alt="Logo" src="cmsis_logo_white_small.png"/</a></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Compiler Support
   &#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown.call(this, "CMSIS-Compiler Support");
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">Standard C Library File, I/O and OS Retargeting</div>
  </td>
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
  <!--END !PROJECT_NAME-->
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<div id="CMSISnav" class="tabs1">
  <ul class="tablist">
    <script type="text/javascript">
      writeComponentTabs.call(this);
    </script>
  </ul>
</div>
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('group__fs__interface__return__codes.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">Return Codes<div class="ingroups"><a class="el" href="group__fs__interface__api.html">File Interface</a> &raquo; <a class="el" href="group__fs__interface__definitions.html">Definitions</a></div></div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:gad40d818ba12b5b0d7789afdb993c8d64"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#gad40d818ba12b5b0d7789afdb993c8d64">RT_ERR</a>&#160;&#160;&#160;(-1)</td></tr>
<tr class="memdesc:gad40d818ba12b5b0d7789afdb993c8d64"><td class="mdescLeft">&#160;</td><td class="mdescRight">File Operation Return Codes.  <br /></td></tr>
<tr class="separator:gad40d818ba12b5b0d7789afdb993c8d64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0358b3062b226f4000c15fc67a67582f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga0358b3062b226f4000c15fc67a67582f">RT_ERR_NOTSUP</a>&#160;&#160;&#160;(-2)</td></tr>
<tr class="memdesc:ga0358b3062b226f4000c15fc67a67582f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not supported.  <br /></td></tr>
<tr class="separator:ga0358b3062b226f4000c15fc67a67582f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga435c0c940b9bc1e18ef2eb3567d02b5a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga435c0c940b9bc1e18ef2eb3567d02b5a">RT_ERR_INVAL</a>&#160;&#160;&#160;(-3)</td></tr>
<tr class="memdesc:ga435c0c940b9bc1e18ef2eb3567d02b5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Invalid argument.  <br /></td></tr>
<tr class="separator:ga435c0c940b9bc1e18ef2eb3567d02b5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga403b16115044c491e9d3dba8d7f28d92"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga403b16115044c491e9d3dba8d7f28d92">RT_ERR_IO</a>&#160;&#160;&#160;(-4)</td></tr>
<tr class="memdesc:ga403b16115044c491e9d3dba8d7f28d92"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low level I/O error.  <br /></td></tr>
<tr class="separator:ga403b16115044c491e9d3dba8d7f28d92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa815195b3d8535d50d4010a6165aafc8"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#gaa815195b3d8535d50d4010a6165aafc8">RT_ERR_NOTFOUND</a>&#160;&#160;&#160;(-5)</td></tr>
<tr class="memdesc:gaa815195b3d8535d50d4010a6165aafc8"><td class="mdescLeft">&#160;</td><td class="mdescRight">File not found.  <br /></td></tr>
<tr class="separator:gaa815195b3d8535d50d4010a6165aafc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f9d31c8f178cedbf850540846c76d2e"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga4f9d31c8f178cedbf850540846c76d2e">RT_ERR_EXIST</a>&#160;&#160;&#160;(-6)</td></tr>
<tr class="memdesc:ga4f9d31c8f178cedbf850540846c76d2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">File already exists.  <br /></td></tr>
<tr class="separator:ga4f9d31c8f178cedbf850540846c76d2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga074ddde1d636f5e0464c607fad71be8c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga074ddde1d636f5e0464c607fad71be8c">RT_ERR_ISDIR</a>&#160;&#160;&#160;(-7)</td></tr>
<tr class="memdesc:ga074ddde1d636f5e0464c607fad71be8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">File is a directory.  <br /></td></tr>
<tr class="separator:ga074ddde1d636f5e0464c607fad71be8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga76388ade0a9bdee95a1585840643e9f6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga76388ade0a9bdee95a1585840643e9f6">RT_ERR_NOSPACE</a>&#160;&#160;&#160;(-8)</td></tr>
<tr class="memdesc:ga76388ade0a9bdee95a1585840643e9f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not enough space or out of memory.  <br /></td></tr>
<tr class="separator:ga76388ade0a9bdee95a1585840643e9f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a4f8287feb2e4378846646721008d97"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga5a4f8287feb2e4378846646721008d97">RT_ERR_READONLY</a>&#160;&#160;&#160;(-9)</td></tr>
<tr class="memdesc:ga5a4f8287feb2e4378846646721008d97"><td class="mdescLeft">&#160;</td><td class="mdescRight">File is read only.  <br /></td></tr>
<tr class="separator:ga5a4f8287feb2e4378846646721008d97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf20769999452b2d869616dbc9a826e2"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#gabf20769999452b2d869616dbc9a826e2">RT_ERR_FILEDES</a>&#160;&#160;&#160;(-10)</td></tr>
<tr class="memdesc:gabf20769999452b2d869616dbc9a826e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">File descriptor error (out of range, file not open, w/r to file opened in r/w mode)  <br /></td></tr>
<tr class="separator:gabf20769999452b2d869616dbc9a826e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac917005ac422004821b26afd71c0e1c7"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#gac917005ac422004821b26afd71c0e1c7">RT_ERR_MAXPATH</a>&#160;&#160;&#160;(-11)</td></tr>
<tr class="memdesc:gac917005ac422004821b26afd71c0e1c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">File or path name is too long.  <br /></td></tr>
<tr class="separator:gac917005ac422004821b26afd71c0e1c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga878bdbbde2d5f17cb23a3530c02e0dc0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga878bdbbde2d5f17cb23a3530c02e0dc0">RT_ERR_BUSY</a>&#160;&#160;&#160;(-12)</td></tr>
<tr class="memdesc:ga878bdbbde2d5f17cb23a3530c02e0dc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">File is busy, access is denied.  <br /></td></tr>
<tr class="separator:ga878bdbbde2d5f17cb23a3530c02e0dc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga979101007b0b1c45cf17e24cb015c8cb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#ga979101007b0b1c45cf17e24cb015c8cb">RT_ERR_NOTEMPTY</a>&#160;&#160;&#160;(-13)</td></tr>
<tr class="memdesc:ga979101007b0b1c45cf17e24cb015c8cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Directory is not empty.  <br /></td></tr>
<tr class="separator:ga979101007b0b1c45cf17e24cb015c8cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7a2f75dac0a4cfece51540a2e842a45"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#gac7a2f75dac0a4cfece51540a2e842a45">RT_ERR_MAXFILES</a>&#160;&#160;&#160;(-14)</td></tr>
<tr class="memdesc:gac7a2f75dac0a4cfece51540a2e842a45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Too many files open.  <br /></td></tr>
<tr class="separator:gac7a2f75dac0a4cfece51540a2e842a45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7233cd3c7d051d22bba9fef66a3fe73"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__fs__interface__return__codes.html#gaa7233cd3c7d051d22bba9fef66a3fe73">RT_ERR_OVERFLOW</a>&#160;&#160;&#160;(-15)</td></tr>
<tr class="memdesc:gaa7233cd3c7d051d22bba9fef66a3fe73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Value overflows the specified range.  <br /></td></tr>
<tr class="separator:gaa7233cd3c7d051d22bba9fef66a3fe73"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Description</h2>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a id="gad40d818ba12b5b0d7789afdb993c8d64" name="gad40d818ba12b5b0d7789afdb993c8d64"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gad40d818ba12b5b0d7789afdb993c8d64">&#9670;&#160;</a></span>RT_ERR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR&#160;&#160;&#160;(-1)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File Operation Return Codes. </p>
<p>Unspecified error </p>

</div>
</div>
<a id="ga0358b3062b226f4000c15fc67a67582f" name="ga0358b3062b226f4000c15fc67a67582f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga0358b3062b226f4000c15fc67a67582f">&#9670;&#160;</a></span>RT_ERR_NOTSUP</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_NOTSUP&#160;&#160;&#160;(-2)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not supported. </p>

</div>
</div>
<a id="ga435c0c940b9bc1e18ef2eb3567d02b5a" name="ga435c0c940b9bc1e18ef2eb3567d02b5a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga435c0c940b9bc1e18ef2eb3567d02b5a">&#9670;&#160;</a></span>RT_ERR_INVAL</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_INVAL&#160;&#160;&#160;(-3)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Invalid argument. </p>

</div>
</div>
<a id="ga403b16115044c491e9d3dba8d7f28d92" name="ga403b16115044c491e9d3dba8d7f28d92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga403b16115044c491e9d3dba8d7f28d92">&#9670;&#160;</a></span>RT_ERR_IO</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_IO&#160;&#160;&#160;(-4)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low level I/O error. </p>

</div>
</div>
<a id="gaa815195b3d8535d50d4010a6165aafc8" name="gaa815195b3d8535d50d4010a6165aafc8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa815195b3d8535d50d4010a6165aafc8">&#9670;&#160;</a></span>RT_ERR_NOTFOUND</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_NOTFOUND&#160;&#160;&#160;(-5)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File not found. </p>

</div>
</div>
<a id="ga4f9d31c8f178cedbf850540846c76d2e" name="ga4f9d31c8f178cedbf850540846c76d2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga4f9d31c8f178cedbf850540846c76d2e">&#9670;&#160;</a></span>RT_ERR_EXIST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_EXIST&#160;&#160;&#160;(-6)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File already exists. </p>

</div>
</div>
<a id="ga074ddde1d636f5e0464c607fad71be8c" name="ga074ddde1d636f5e0464c607fad71be8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga074ddde1d636f5e0464c607fad71be8c">&#9670;&#160;</a></span>RT_ERR_ISDIR</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_ISDIR&#160;&#160;&#160;(-7)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File is a directory. </p>

</div>
</div>
<a id="ga76388ade0a9bdee95a1585840643e9f6" name="ga76388ade0a9bdee95a1585840643e9f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga76388ade0a9bdee95a1585840643e9f6">&#9670;&#160;</a></span>RT_ERR_NOSPACE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_NOSPACE&#160;&#160;&#160;(-8)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not enough space or out of memory. </p>

</div>
</div>
<a id="ga5a4f8287feb2e4378846646721008d97" name="ga5a4f8287feb2e4378846646721008d97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga5a4f8287feb2e4378846646721008d97">&#9670;&#160;</a></span>RT_ERR_READONLY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_READONLY&#160;&#160;&#160;(-9)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File is read only. </p>

</div>
</div>
<a id="gabf20769999452b2d869616dbc9a826e2" name="gabf20769999452b2d869616dbc9a826e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gabf20769999452b2d869616dbc9a826e2">&#9670;&#160;</a></span>RT_ERR_FILEDES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_FILEDES&#160;&#160;&#160;(-10)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File descriptor error (out of range, file not open, w/r to file opened in r/w mode) </p>

</div>
</div>
<a id="gac917005ac422004821b26afd71c0e1c7" name="gac917005ac422004821b26afd71c0e1c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac917005ac422004821b26afd71c0e1c7">&#9670;&#160;</a></span>RT_ERR_MAXPATH</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_MAXPATH&#160;&#160;&#160;(-11)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File or path name is too long. </p>

</div>
</div>
<a id="ga878bdbbde2d5f17cb23a3530c02e0dc0" name="ga878bdbbde2d5f17cb23a3530c02e0dc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga878bdbbde2d5f17cb23a3530c02e0dc0">&#9670;&#160;</a></span>RT_ERR_BUSY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_BUSY&#160;&#160;&#160;(-12)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>File is busy, access is denied. </p>

</div>
</div>
<a id="ga979101007b0b1c45cf17e24cb015c8cb" name="ga979101007b0b1c45cf17e24cb015c8cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga979101007b0b1c45cf17e24cb015c8cb">&#9670;&#160;</a></span>RT_ERR_NOTEMPTY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_NOTEMPTY&#160;&#160;&#160;(-13)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Directory is not empty. </p>

</div>
</div>
<a id="gac7a2f75dac0a4cfece51540a2e842a45" name="gac7a2f75dac0a4cfece51540a2e842a45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gac7a2f75dac0a4cfece51540a2e842a45">&#9670;&#160;</a></span>RT_ERR_MAXFILES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_MAXFILES&#160;&#160;&#160;(-14)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Too many files open. </p>

</div>
</div>
<a id="gaa7233cd3c7d051d22bba9fef66a3fe73" name="gaa7233cd3c7d051d22bba9fef66a3fe73"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gaa7233cd3c7d051d22bba9fef66a3fe73">&#9670;&#160;</a></span>RT_ERR_OVERFLOW</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define RT_ERR_OVERFLOW&#160;&#160;&#160;(-15)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Value overflows the specified range. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script> 
    </li>
  </ul>
</div>
</body>
</html>
