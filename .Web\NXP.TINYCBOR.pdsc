<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>TINYCBOR</name>
  <vendor>NXP</vendor>
  <description>Software Pack for tinycbor</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="TinyCBOR" Cgroup="TinyCBOR">TinyCBOR NXP</description>
  </taxonomy>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <components>
    <bundle Cbundle="TinyCBOR NXP" Cclass="TinyCBOR" Cversion="0.6.0">
      <description>TinyCBOR NXP</description>
      <doc>middleware/tinycbor/TinyCBOR NXP_dummy.txt</doc>
      <component Cgroup="TinyCBOR" Csub="tinycbor" Cversion="0.6.0">
        <description>Concise Binary Object Representation (CBOR) Library</description>
        <files>
          <file category="header" name="middleware/tinycbor/src/cbor.h" projectpath="tinycbor/src"/>
          <file category="sourceC" name="middleware/tinycbor/src/cborencoder.c" projectpath="tinycbor/src"/>
          <file category="sourceC" name="middleware/tinycbor/src/cborencoder_close_container_checked.c" projectpath="tinycbor/src"/>
          <file category="sourceC" name="middleware/tinycbor/src/cborerrorstrings.c" projectpath="tinycbor/src"/>
          <file category="header" name="middleware/tinycbor/src/cborinternal_p.h" projectpath="tinycbor/src"/>
          <file category="sourceC" name="middleware/tinycbor/src/cborparser.c" projectpath="tinycbor/src"/>
          <file category="header" name="middleware/tinycbor/src/compilersupport_p.h" projectpath="tinycbor/src"/>
          <file category="header" name="middleware/tinycbor/src/tinycbor-version.h" projectpath="tinycbor/src"/>
          <file category="header" name="middleware/tinycbor/src/utf8_p.h" projectpath="tinycbor/src"/>
          <file category="doc" name="middleware/tinycbor/TinyCBOR NXP_dummy.txt"/>
          <file category="include" name="middleware/tinycbor/src/"/>
        </files>
      </component>
    </bundle>
  </components>
</package>
