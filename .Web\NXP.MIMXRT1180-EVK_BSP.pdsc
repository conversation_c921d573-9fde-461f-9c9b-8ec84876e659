<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1180-EVK_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for MIMXRT1180-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-02-07">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1189_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT1180-EVK">
      <description>EVK Board for MIMXRT1180</description>
      <image small="boards/evkmimxrt1180/evkmimxrt1180.png"/>
      <mountedDevice Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MIMXRT1189xxxxx.internal_condition">
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1181.internal_condition">
      <accept Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1182.internal_condition">
      <accept Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1187.internal_condition">
      <accept Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1189.internal_condition">
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.evkmimxrt1180.condition_id">
      <require condition="allOf.device_id=MIMXRT1189xxxxx, device.MIMXRT1189_startup, device.MIMXRT1189_mcux_scripts, driver.common, driver.clock, driver.rgpio, driver.iomuxc, driver.misc, driver.lpuart, component.lpuart_adapter, driver.dcdc_soc, driver.pmu_1, driver.xip_board.evkmimxrt1180, driver.xip_device, anyOf=driver.cache_armv7_m7, driver.cache_xcache, anyOf=allOf=utility.debug_console, utility.assert, component.serial_manager, allOf=utility.debug_console_lite, utility.assert_lite, board=evkmimxrt1180.internal_condition"/>
    </condition>
    <condition id="allOf.device_id=MIMXRT1189xxxxx, device.MIMXRT1189_startup, device.MIMXRT1189_mcux_scripts, driver.common, driver.clock, driver.rgpio, driver.iomuxc, driver.misc, driver.lpuart, component.lpuart_adapter, driver.dcdc_soc, driver.pmu_1, driver.xip_board.evkmimxrt1180, driver.xip_device, anyOf=driver.cache_armv7_m7, driver.cache_xcache, anyOf=allOf=utility.debug_console, utility.assert, component.serial_manager, allOf=utility.debug_console_lite, utility.assert_lite, board=evkmimxrt1180.internal_condition">
      <require condition="device_id.MIMXRT1189xxxxx.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Board Support" Cgroup="SDK Project Template" Csub="MIMXRT1189_mcux_scripts"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iomuxc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="misc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dcdc_soc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="pmu_1"/>
      <require Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkmimxrt1180 xip"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xip_device"/>
      <require condition="anyOf.driver.cache_armv7_m7, driver.cache_xcache.internal_condition"/>
      <require condition="anyOf.allOf=utility.debug_console, utility.assert, component.serial_manager, allOf=utility.debug_console_lite, utility.assert_lite.internal_condition"/>
      <require condition="board.evkmimxrt1180.internal_condition"/>
    </condition>
    <condition id="anyOf.driver.cache_armv7_m7, driver.cache_xcache.internal_condition">
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="cache"/>
      <accept Cclass="Device" Cgroup="SDK Drivers" Csub="cache_xcache"/>
    </condition>
    <condition id="anyOf.allOf=utility.debug_console, utility.assert, component.serial_manager, allOf=utility.debug_console_lite, utility.assert_lite.internal_condition">
      <accept condition="allOf.utility.debug_console, utility.assert, component.serial_manager.internal_condition"/>
      <accept condition="allOf.utility.debug_console_lite, utility.assert_lite.internal_condition"/>
    </condition>
    <condition id="allOf.utility.debug_console, utility.assert, component.serial_manager.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
    </condition>
    <condition id="allOf.utility.debug_console_lite, utility.assert_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
    </condition>
    <condition id="board.evkmimxrt1180.internal_condition">
      <accept condition="device.MIMXRT1181.internal_condition"/>
      <accept condition="device.MIMXRT1182.internal_condition"/>
      <accept condition="device.MIMXRT1187.internal_condition"/>
      <accept condition="device.MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="allOf.cores=cm7f.condition_id">
      <require condition="cores.cm7f.internal_condition"/>
    </condition>
    <condition id="cores.cm7f.internal_condition">
      <accept Dcore="Cortex-M7"/>
    </condition>
    <condition id="allOf.cores=cm33.condition_id">
      <require condition="cores.cm33.internal_condition"/>
    </condition>
    <condition id="cores.cm33.internal_condition">
      <accept Dcore="Cortex-M33"/>
    </condition>
    <condition id="driver.xip_board.evkmimxrt1180.condition_id">
      <require condition="allOf.driver.common, device=MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="allOf.driver.common, device=MIMXRT1189.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require condition="device.MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="utility.jlinkscript.evkmimxrt1180.condition_id">
      <require condition="allOf.device=MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="allOf.device=MIMXRT1189.internal_condition">
      <require condition="device.MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="utility.xmcd.evkmimxrt1180.condition_id">
      <require condition="allOf.device=MIMXRT1189.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/acmp/interrupt/cm33" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/acmp_interrupt_cm33.ewp"/>
        <environment name="csolution" load="acmp_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling_cm33" folder="boards/evkmimxrt1180/driver_examples/acmp/polling/cm33" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling_cm33.uvprojx"/>
        <environment name="iar" load="iar/acmp_polling_cm33.ewp"/>
        <environment name="csolution" load="acmp_polling_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_edma_cm33" folder="boards/evkmimxrt1180/driver_examples/asrc/asrc_m2m_edma/cm33" doc="readme.md">
      <description>The asrc_m2m_edma example shows how to use asrc driver with edma:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_edma_cm33.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_edma_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_p2p_out_edma_cm33" folder="boards/evkmimxrt1180/driver_examples/asrc/asrc_p2p_out_edma/cm33" doc="readme.md">
      <description>The asrc_p2p_out_edma example shows how to use asrc driver with edma:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_p2p_out_edma_cm33.uvprojx"/>
        <environment name="csolution" load="asrc_p2p_out_edma_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral_cm33" folder="boards/evkmimxrt1180/demo_apps/bubble_peripheral/cm33" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral_cm33.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral_cm33.ewp"/>
        <environment name="csolution" load="bubble_peripheral_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cache_cm33" folder="boards/evkmimxrt1180/driver_examples/cache/cm33" doc="readme.md">
      <description>The cache example shows how to use memory cache driver.In this example, many memory (such as SDRAM, etc) and DMA will be used to show the example.Those memory is both accessible for cpu and DMA. For the memory data...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cache_cm33.uvprojx"/>
        <environment name="iar" load="iar/cache_cm33.ewp"/>
        <environment name="csolution" load="cache_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/canfd/interrupt_transfer/cm33" doc="readme.md">
      <description>The canfd_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/canfd_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="canfd_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_cm33" folder="boards/evkmimxrt1180/driver_examples/canfd/loopback/cm33" doc="readme.md">
      <description>The canfd_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_cm33.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_cm33.ewp"/>
        <environment name="csolution" load="canfd_loopback_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/canfd/loopback_transfer/cm33" doc="readme.md">
      <description>The canfd_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_transfer_cm33.ewp"/>
        <environment name="csolution" load="canfd_loopback_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_ping_pong_buffer_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/canfd/ping_pong_buffer_transfer/cm33" doc="readme.md">
      <description>The canfd_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CANFD frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_ping_pong_buffer_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/canfd_ping_pong_buffer_transfer_cm33.ewp"/>
        <environment name="csolution" load="canfd_ping_pong_buffer_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master/cm33" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave/cm33" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/int_b2b_transfer/master/cm33" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave/cm33" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/edma_b2b_transfer/master/cm33" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/int_b2b_transfer/master/cm33" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/int_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpuart/edma_transfer/cm33" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpuart/interrupt_transfer/cm33" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_basic_cm33" folder="boards/evkmimxrt1180/driver_examples/dac12/basic/cm33" doc="readme.md">
      <description>The dac12_basic example shows how to use DAC12 module simply as the general DAC12 converter.When the DAC12's fifo feature is not enabled, Any write to the DATA register will replace thedata in the buffer and push...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_basic_cm33.uvprojx"/>
        <environment name="iar" load="iar/dac12_basic_cm33.ewp"/>
        <environment name="csolution" load="dac12_basic_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_fifo_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/dac12/fifo_interrupt/cm33" doc="readme.md">
      <description>The dac12_fifo_interrupt example shows how to use DAC12 FIFO interrupt.When the DAC12 FIFO watermark interrupt is enabled firstly, the application would enter the DAC12 ISR immediately, since remaining FIFO data is...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_fifo_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/dac12_fifo_interrupt_cm33.ewp"/>
        <environment name="csolution" load="dac12_fifo_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ecat_examples_digital_io_cm33" folder="boards/evkmimxrt1180/ecat_examples/digital_io/cm33" doc="readme.md">
      <description>An EtherCAT device example with the SSC tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecat_examples_digital_io_cm33.uvprojx"/>
        <environment name="csolution" load="ecat_examples_digital_io_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ecat_examples_foe_cm33" folder="boards/evkmimxrt1180/ecat_examples/foe/cm33" doc="readme.md">
      <description>An EtherCAT FoE example with the SSC tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecat_examples_foe_cm33.uvprojx"/>
        <environment name="csolution" load="ecat_examples_foe_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memory_to_memory_cm33" folder="boards/evkmimxrt1180/driver_examples/edma3/memory_to_memory/cm33" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memory_to_memory_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma3_memory_to_memory_cm33.ewp"/>
        <environment name="csolution" load="edma3_memory_to_memory_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_channel_link_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/channel_link/cm33" doc="readme.md">
      <description>The EDMA4 channel link example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_channel_link_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_channel_link_cm33.ewp"/>
        <environment name="csolution" load="edma4_channel_link_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_interleave_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/interleave_transfer/cm33" doc="readme.md">
      <description>The EDMA4 interleave example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_interleave_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_interleave_transfer_cm33.ewp"/>
        <environment name="csolution" load="edma4_interleave_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/memory_to_memory/cm33" doc="readme.md">
      <description>The EDMA4 memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_memory_to_memory_cm33.ewp"/>
        <environment name="csolution" load="edma4_memory_to_memory_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/memory_to_memory_transfer/cm33" doc="readme.md">
      <description>The EDMA4 memory to memory transfer example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_memory_to_memory_transfer_cm33.ewp"/>
        <environment name="csolution" load="edma4_memory_to_memory_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memset_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/memset/cm33" doc="readme.md">
      <description>The EDMA4 memset example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memset_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_memset_cm33.ewp"/>
        <environment name="csolution" load="edma4_memset_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_ping_pong_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/ping_pong_transfer/cm33" doc="readme.md">
      <description>The EDMA4 ping pong transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_ping_pong_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_ping_pong_transfer_cm33.ewp"/>
        <environment name="csolution" load="edma4_ping_pong_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_scatter_gather_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/scatter_gather/cm33" doc="readme.md">
      <description>The EDMA4 scatter gather example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_scatter_gather_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_scatter_gather_cm33.ewp"/>
        <environment name="csolution" load="edma4_scatter_gather_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_wrap_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/edma4/wrap_transfer/cm33" doc="readme.md">
      <description>The EDMA4 wrap transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_wrap_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/edma4_wrap_transfer_cm33.ewp"/>
        <environment name="csolution" load="edma4_wrap_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eqdc_basic_cm33" folder="boards/evkmimxrt1180/driver_examples/eqdc/basic/cm33" doc="readme.md">
      <description>The EQDC project shows how to sample the shaft position based on quadrature signal (PHASEA and PHASEB).</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eqdc_basic_cm33.uvprojx"/>
        <environment name="iar" load="iar/eqdc_basic_cm33.ewp"/>
        <environment name="csolution" load="eqdc_basic_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eqdc_index_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/eqdc/index_interrupt/cm33" doc="readme.md">
      <description>The EQDC project shows how to sample the shaft position based on quadrature signal (PHASEA and PHASEB).</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eqdc_index_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/eqdc_index_interrupt_cm33.ewp"/>
        <environment name="csolution" load="eqdc_index_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm_cm33" folder="boards/evkmimxrt1180/driver_examples/ewm/cm33" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm_cm33.uvprojx"/>
        <environment name="iar" load="iar/ewm_cm33.ewp"/>
        <environment name="csolution" load="ewm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_nor_flexspi_cm33" folder="boards/evkmimxrt1180/component_examples/flash_component/flexspi_nor/cm33" doc="readme.md">
      <description>nor flash demo shows the use of nor flash component to erase, program, and read an external nor flash device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_nor_flexspi_cm33.uvprojx"/>
        <environment name="iar" load="iar/flash_component_nor_flexspi_cm33.ewp"/>
        <environment name="csolution" load="flash_component_nor_flexspi_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_octal_flexspi_cm33" folder="boards/evkmimxrt1180/component_examples/flash_component/flexspi_octal/cm33" doc="readme.md">
      <description>octal flash demo shows the use of nor flash component to erase, program, and read an external serial nor flash device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_octal_flexspi_cm33.uvprojx"/>
        <environment name="iar" load="iar/flash_component_octal_flexspi_cm33.ewp"/>
        <environment name="csolution" load="flash_component_octal_flexspi_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_efifo_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/efifo_edma_transfer/cm33" doc="readme.md">
      <description>The flexcan_efifo_edma_transfer example shows how to use the EDMA version transactional driver to receive CAN FD Message from Enhanced Rx FIFO:In this example, when set ENABLE_LOOPBACK macro, only one board is...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_efifo_edma_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_efifo_edma_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexcan_efifo_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_efifo_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/efifo_interrupt_transfer/cm33" doc="readme.md">
      <description>flexcan_efifo_interrupt_transfer example shows how to use FlexCAN Enhanced Rx FIFO in none-blocking interrupt way:In this example, when set ENABLE_LOOPBACK macro, only one board is needed. The example will config one...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_efifo_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_efifo_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexcan_efifo_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/interrupt_transfer/cm33" doc="readme.md">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexcan_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/loopback/cm33" doc="readme.md">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_cm33.ewp"/>
        <environment name="csolution" load="flexcan_loopback_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/loopback_edma_transfer/cm33" doc="readme.md">
      <description>The flexcan_loopback_edma example shows how to use the EDMA version transactional driver to receiveCAN Message from Rx FIFO:To demonstrates this example, only one board is needed. The example will config one FlexCAN...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_edma_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_edma_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexcan_loopback_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/loopback_transfer/cm33" doc="readme.md">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexcan_loopback_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_ping_pong_buffer_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexcan/ping_pong_buffer_transfer/cm33" doc="readme.md">
      <description>The flexcan_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CAN frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_ping_pong_buffer_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexcan_ping_pong_buffer_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexcan_ping_pong_buffer_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/i2c/interrupt_lpi2c_transfer/cm33" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/i2c/read_accel_value_transfer/cm33" doc="readme.md">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_read_accel_value_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexio_i2c_read_accel_value_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/i2s/edma_transfer/cm33" doc="readme.md">
      <description>The flexio_i2s_EDMA example shows how to use flexio_i2s driver with EDMA:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using EDMA.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_edma_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/i2s/interrupt_transfer/cm33" doc="readme.md">
      <description>The flexio_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_interrupt_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pin_input_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/pin/input_interrupt/cm33" doc="readme.md">
      <description>The FLEXIO PIN Example project is a demonstration program that uses the FLEXIO software to manipulate the flexio-pin as input function.The example uses FLEXIO-PIN input to capture the edge of other gpio pin.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pin_input_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_pin_input_interrupt_cm33.ewp"/>
        <environment name="csolution" load="flexio_pin_input_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pin_led_output_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/pin/led_output/cm33" doc="readme.md">
      <description>The FLEXIO led project is a demonstration program that uses the FELXIO software to manipulate the felxio-pin.The example is supported by the set, clear, and toggle write-only registers for each flexio output data...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pin_led_output_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_pin_led_output_cm33.ewp"/>
        <environment name="csolution" load="flexio_pin_led_output_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/pwm/cm33" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm_cm33.ewp"/>
        <environment name="csolution" load="flexio_pwm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/edma_lpspi_transfer/master/cm33" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/edma_lpspi_transfer/slave/cm33" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/edma_lpspi_transfer/slave_dynamic/cm33" doc="readme.md">
      <description>The flexio_spi_slave_dynamic example shows how to use flexio spi slave driver in edma way, In this example, a flexio simulated slave connect to a lpspi master. The CS signal remains low during transfer, after master...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm33.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/int_lpspi_transfer/master/cm33" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/int_lpspi_transfer/slave/cm33" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_polling_lpspi_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/polling_lpspi_transfer/master/cm33" doc="readme.md">
      <description>The flexio_spi_master_pooling_lpspi_slave example shows how to use flexio spi master driver in polling way. In this example, a flexio simulated master connect to a lpspi slave.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_polling_lpspi_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_polling_lpspi_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="flexio_spi_polling_lpspi_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/edma_transfer/cm33" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/int_rb_transfer/cm33" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/interrupt_transfer/cm33" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/polling_transfer/cm33" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_hyper_ram_polling_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexspi/hyper_ram/polling_transfer/cm33" doc="readme.md">
      <description>The flexspi_hyper_ram_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Hyper RAM connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_hyper_ram_polling_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexspi_hyper_ram_polling_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexspi_hyper_ram_polling_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexspi/nor/edma_transfer/cm33" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_edma_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexspi/nor/polling_transfer/cm33" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor_polling_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexspi/octal/edma_transfer/cm33" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_edma_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="flexspi_octal_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_polling_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/flexspi/octal/polling_transfer/cm33" doc="readme.md">
      <description>The flexspi_octal_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_polling_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_polling_transfer_cm33.ewp"/>
        <environment name="csolution" load="flexspi_octal_polling_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fsl_romapi_cm33" folder="boards/evkmimxrt1180/driver_examples/fsl_romapi/cm33" doc="readme.md">
      <description>The fsl_romapi example shows how to use flexspi rom api In this example, rom api will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command willbe executed, such as Write...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fsl_romapi_cm33.uvprojx"/>
        <environment name="iar" load="iar/fsl_romapi_cm33.ewp"/>
        <environment name="csolution" load="fsl_romapi_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_compare_cm33" folder="boards/evkmimxrt1180/driver_examples/gpt/compare/cm33" doc="readme.md">
      <description>The gpt_compare project is a simple demonstration program of the SDK GPT driver's output compare feature. Once content of an OCRx matches the value in GPT_CNT, output compare timer pin is toggled.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_compare_cm33.uvprojx"/>
        <environment name="iar" load="iar/gpt_compare_cm33.ewp"/>
        <environment name="csolution" load="gpt_compare_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_timer_cm33" folder="boards/evkmimxrt1180/driver_examples/gpt/timer/cm33" doc="readme.md">
      <description>The gpt_timer project is a simple demonstration program of the SDK GPT driver. It sets up the GPThardware block to trigger a periodic interrupt after every 1 second. When the GPT interrupt is triggereda message a printed on the UART terminal.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_timer_cm33.uvprojx"/>
        <environment name="iar" load="iar/gpt_timer_cm33.ewp"/>
        <environment name="csolution" load="gpt_timer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_demo_cm33" folder="boards/evkmimxrt1180/demo_apps/hello_world/cm33" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_demo_cm33.uvprojx"/>
        <environment name="iar" load="iar/hello_world_demo_cm33.ewp"/>
        <environment name="csolution" load="hello_world_demo_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/edma_b2b_transfer/master/cm33" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_master example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as master and another i3c instance on the other board as slave....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/edma_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as slave and another i3c instance on the other board as master....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/interrupt_b2b_transfer/master/cm33" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/interrupt_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_lsm6dso_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/master_read_sensor_lsm6dso/cm33" doc="readme.md">
      <description>The i3c_master_read_sensor_lsm6dso example shows how to use i3c driver as master to communicate with sensor LSM6DSO.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_lsm6dso_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_lsm6dso_cm33.ewp"/>
        <environment name="csolution" load="i3c_master_read_sensor_lsm6dso_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/polling_b2b_transfer/master/cm33" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/i3c/polling_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iee_apc_cm33" folder="boards/evkmimxrt1180/demo_apps/iee_apc/cm33" doc="readme.md">
      <description>The IEE APC demo application demonstrates usage of the IEE and IEE APC driver. The Inline Encryption Engine (IEE) together with IEE APC provides a means to perform inline encryption and decryption of information...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iee_apc_cm33.uvprojx"/>
        <environment name="csolution" load="iee_apc_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="kpp_cm33" folder="boards/evkmimxrt1180/driver_examples/kpp/cm33" doc="readme.md">
      <description>The KPP Example project is a demonstration program that uses the KSDK software to manipulate the Keypad MATRIX.The example is use the continuous column and rows as 4*4 or 8*8 matrix to show the example.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kpp_cm33.uvprojx"/>
        <environment name="iar" load="iar/kpp_cm33.ewp"/>
        <environment name="csolution" load="kpp_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_dual_single_ended_conversion_cm33" folder="boards/evkmimxrt1180/driver_examples/lpadc/dual_single_ended_conversion/cm33" doc="readme.md">
      <description>The lpadc_dual_single_ended_conversion example shows how to use two channel with LPADC driver. In this example, user should indicate two channel to provide a voltage signal (can be controlled by user) as the LPADC's...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_dual_single_ended_conversion_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpadc_dual_single_ended_conversion_cm33.ewp"/>
        <environment name="csolution" load="lpadc_dual_single_ended_conversion_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_high_sample_rate_sample_signal_cm33" folder="boards/evkmimxrt1180/demo_apps/lpadc_high_sample_rate/sample_signal/cm33" doc="readme.md">
      <description>This demo application demonstrates the use of the LPADC to sample the analog signal. In this demo, the ADC clock is set as the maximum frequency, users should input analog signals to the ADC channel, press any keys...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_high_sample_rate_sample_signal_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpadc_high_sample_rate_sample_signal_cm33.ewp"/>
        <environment name="csolution" load="lpadc_high_sample_rate_sample_signal_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/lpadc/interrupt/cm33" doc="readme.md">
      <description>The lpadc_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt_cm33.ewp"/>
        <environment name="csolution" load="lpadc_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling_cm33" folder="boards/evkmimxrt1180/driver_examples/lpadc/polling/cm33" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling_cm33.ewp"/>
        <environment name="csolution" load="lpadc_polling_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_sample_rate_count_cm33" folder="boards/evkmimxrt1180/demo_apps/lpadc_high_sample_rate/sample_rate_count/cm33" doc="readme.md">
      <description>The lpadc sample rate count demo application can be used to measure ADC's sample rate roughly. The sample rate for an ADC is defined as the number of output samples available per unit time, and is specified as...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_sample_rate_count_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpadc_sample_rate_count_cm33.ewp"/>
        <environment name="csolution" load="lpadc_sample_rate_count_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/edma_b2b_transfer/master/cm33" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/edma_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/interrupt/cm33" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/interrupt_b2b_transfer/master/cm33" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/interrupt_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/polling_b2b/master/cm33" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/lpi2c/polling_b2b/slave/cm33" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave_cm33.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_adc_cm33" folder="boards/evkmimxrt1180/driver_examples/lpadc/lpit_adc/cm33" doc="readme.md">
      <description>The lpit_adc example shows how to use LPIT to generate ADC triggers. On top of the basic counter, to use the ADC trigger, simply enable the "milestone" of the ADC trigger and set it with a user-defined value. When...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_adc_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpit_adc_cm33.ewp"/>
        <environment name="csolution" load="lpit_adc_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_pwm_cm33" folder="boards/evkmimxrt1180/driver_examples/lpit/lpit_pwm/cm33" doc="readme.md">
      <description>This example show how to use SDK drivers to implement the PWM feature by LPIT IP module in dualperiodcounter mode.You can set up PWM singal frequency and duty in this example.Connect PWM singal output pin to oscilloscope, you will see PWM wave.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_pwm_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpit_pwm_cm33.ewp"/>
        <environment name="csolution" load="lpit_pwm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_single_channel_cm33" folder="boards/evkmimxrt1180/driver_examples/lpit/single_channel/cm33" doc="readme.md">
      <description>The LPIT single channel project is a simple example of the SDK LPIT driver. It sets up the LPIThardware block to trigger a periodic interrupt after every 1 second. When the LPIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_single_channel_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpit_single_channel_cm33.ewp"/>
        <environment name="csolution" load="lpit_single_channel_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/lpspi/edma_b2b_transfer/master/cm33" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/lpspi/edma_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master_cm33" folder="boards/evkmimxrt1180/driver_examples/lpspi/interrupt_b2b/master/cm33" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master_cm33.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/lpspi/interrupt_b2b/slave/cm33" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave_cm33.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master_cm33" folder="boards/evkmimxrt1180/driver_examples/lpspi/polling_b2b_transfer/master/cm33" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master_cm33.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave_cm33" folder="boards/evkmimxrt1180/driver_examples/lpspi/polling_b2b_transfer/slave/cm33" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave_cm33.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr_cm33" folder="boards/evkmimxrt1180/driver_examples/lptmr/cm33" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr_cm33.uvprojx"/>
        <environment name="iar" load="iar/lptmr_cm33.ewp"/>
        <environment name="csolution" load="lptmr_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/9bit_interrupt_transfer/cm33" doc="readme.md">
      <description>The lpuart_9bit_interrupt_transfer example shows how to use lpuart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="lpuart_9bit_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/edma_transfer/cm33" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer_cm33.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt/cm33" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_cm33.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt_rb_transfer/cm33" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer_cm33.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt_transfer/cm33" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt_transfer_seven_bits/cm33" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits_cm33.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/polling/cm33" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_cm33.ewp"/>
        <environment name="csolution" load="lpuart_polling_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits_cm33" folder="boards/evkmimxrt1180/driver_examples/lpuart/polling_seven_bits/cm33" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits_cm33.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits_cm33.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mecc_multie_error_cm33" folder="boards/evkmimxrt1180/driver_examples/mecc/mecc_multi_error/cm33" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for 4 banks On-Chip RAM(OCRAM) access.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mecc_multie_error_cm33.uvprojx"/>
        <environment name="iar" load="iar/mecc_multie_error_cm33.ewp"/>
        <environment name="csolution" load="mecc_multie_error_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mecc_single_error_cm33" folder="boards/evkmimxrt1180/driver_examples/mecc/mecc_single_error/cm33" doc="readme.md">
      <description>The MECC Single Error project is a simple demonstration program of the SDK MECC driver. It supports Single Error Correction ECC function to provide reliability for 4 banks On-Chip RAM(OCRAM) access.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mecc_single_error_cm33.uvprojx"/>
        <environment name="iar" load="iar/mecc_single_error_cm33.ewp"/>
        <environment name="csolution" load="mecc_single_error_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_interrupt_core0" folder="boards/evkmimxrt1180/driver_examples/mu/interrupt/core0" doc="readme.md">
      <description>The mu_interrupt example shows how to use MU driver in interrupt way:In this example:1. Core 0 send message to Core 1 in interrupt mode via MU module.2. Core 1 send message back to Core 0 in interrupt mode.3. Core 0...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_interrupt_core0.uvprojx"/>
        <environment name="iar" load="iar/mu_interrupt_core0.ewp"/>
        <environment name="csolution" load="../mu_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_polling_core0" folder="boards/evkmimxrt1180/driver_examples/mu/polling/core0" doc="readme.md">
      <description>The mu_polling example shows how to use MU driver in polling way:In this example:1. Core 0 send message to Core 1 in polling mode via MU module.2. Core 1 send message back to Core 0 in polling mode.3. Core 0 receive...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_polling_core0.uvprojx"/>
        <environment name="iar" load="iar/mu_polling_core0.ewp"/>
        <environment name="csolution" load="../mu_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="netc_switch_cm33" folder="boards/evkmimxrt1180/driver_examples/netc/switch/cm33" doc="readme.md">
      <description>The example shows the way to use NETC driver to act as a switch. </description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/netc_switch_cm33.uvprojx"/>
        <environment name="iar" load="iar/netc_switch_cm33.ewp"/>
        <environment name="csolution" load="netc_switch_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="netc_txrx_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/netc/txrx_transfer/cm33" doc="readme.md">
      <description>The example shows the way to use NETC driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the NETC. 2. How to get the time stamp of the PTP 1588 timer. 3....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/netc_txrx_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/netc_txrx_transfer_cm33.ewp"/>
        <environment name="csolution" load="netc_txrx_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_edma_transfer/cm33" doc="readme.md">
      <description>pdm_edma_transfer</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_edma_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/pdm_edma_transfer_cm33.ewp"/>
        <environment name="csolution" load="pdm_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_interrupt/cm33" doc="readme.md">
      <description>The pdm_interrupt example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/pdm_interrupt_cm33.ewp"/>
        <environment name="csolution" load="pdm_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_edma_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_edma/cm33" doc="readme.md">
      <description>The pdm_sai_sdma example shows how to use pdm edma driver together with sai edma driver</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_edma_cm33.uvprojx"/>
        <environment name="csolution" load="pdm_sai_edma_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_interrupt/cm33" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_cm33.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_interrupt_transfer/cm33" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt transaction api:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_multi_channel_edma_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_multi_channel_edma/cm33" doc="readme.md">
      <description>The pdm_sai_multi_channel_edma example shows how to use pdm edma driver to record multi channel data together with sai edma driver</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_multi_channel_edma_cm33.uvprojx"/>
        <environment name="csolution" load="pdm_sai_multi_channel_edma_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_standalone_transfer_edma_cm33" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_standalone_transfer_edma/cm33" doc="readme.md">
      <description>pdm_standalone_transfer_edma</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_standalone_transfer_edma_cm33.uvprojx"/>
        <environment name="iar" load="iar/pdm_standalone_transfer_edma_cm33.ewp"/>
        <environment name="csolution" load="pdm_standalone_transfer_edma_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm_cm33" folder="boards/evkmimxrt1180/driver_examples/pwm/cm33" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm_cm33.uvprojx"/>
        <environment name="iar" load="iar/pwm_cm33.ewp"/>
        <environment name="csolution" load="pwm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_cm33" folder="boards/evkmimxrt1180/driver_examples/qtmr/inputcapture_outputpwm/cm33" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature.The example sets up a QTMR channel for input capture. Once the input signal is received,this example will...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_cm33.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_cm33.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_dma_cm33" folder="boards/evkmimxrt1180/driver_examples/qtmr/inputcapture_outputpwm_dma/cm33" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature by DMA.The example sets up a QTMR channel for input capture. Once the input signal is received,this example...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_dma_cm33.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_dma_cm33.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_dma_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_timer_cm33" folder="boards/evkmimxrt1180/driver_examples/qtmr/timer/cm33" doc="readme.md">
      <description>The QTMR project is a simple demonstration program of the SDK QTMR driver to use QTMR as a timer.The quad-timer module provides four timer channels with a variety of controls affecting both individualand...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_timer_cm33.uvprojx"/>
        <environment name="iar" load="iar/qtmr_timer_cm33.ewp"/>
        <environment name="csolution" load="qtmr_timer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rgpio_input_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/rgpio/input_interrupt/cm33" doc="readme.md">
      <description>The RGPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rgpio_input_interrupt_cm33.uvprojx"/>
        <environment name="iar" load="iar/rgpio_input_interrupt_cm33.ewp"/>
        <environment name="csolution" load="rgpio_input_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rgpio_led_output_cm33" folder="boards/evkmimxrt1180/driver_examples/rgpio/led_output/cm33" doc="readme.md">
      <description>The RGPIO Example project is a demonstration program that uses the SDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rgpio_led_output_cm33.uvprojx"/>
        <environment name="iar" load="iar/rgpio_led_output_cm33.ewp"/>
        <environment name="csolution" load="rgpio_led_output_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rled_blinky_demo_cm33" folder="boards/evkmimxrt1180/demo_apps/led_blinky/cm33" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rled_blinky_demo_cm33.uvprojx"/>
        <environment name="iar" load="iar/rled_blinky_demo_cm33.ewp"/>
        <environment name="csolution" load="rled_blinky_demo_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtwdog_cm33" folder="boards/evkmimxrt1180/driver_examples/rtwdog/cm33" doc="readme.md">
      <description>The RTWDOG Example project is to demonstrate usage of the KSDK rtwdog driver.In this example, fast testing is first implemented to test the rtwdog.After this, refreshing the watchdog in None-window mode and window...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtwdog_cm33.uvprojx"/>
        <environment name="iar" load="iar/rtwdog_cm33.ewp"/>
        <environment name="csolution" load="rtwdog_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="s3mu_cm33" folder="boards/evkmimxrt1180/driver_examples/s3mu/cm33" doc="readme.md">
      <description>The S3MU Example project is a demonstration program that uses the MCUX SDK software to show basic communication with EdgeLock Enclave (ELE) and usage of its services with direct use of S3MU driver.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/s3mu_cm33.uvprojx"/>
        <environment name="iar" load="iar/s3mu_cm33.ewp"/>
        <environment name="csolution" load="s3mu_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/edma_ping_pong_buffer/cm33" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_cm33.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_half_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/edma_ping_pong_buffer_half_interrupt/cm33" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer_half_interrupt example shows how to use sai driver with EDMA half interrupt feature: In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_half_interrupt_cm33.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_half_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/edma_record_playback/cm33" doc="readme.md">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback_cm33.uvprojx"/>
        <environment name="csolution" load="sai_edma_record_playback_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_tdm_record_playback_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/edma_tdm_record_playback/cm33" doc="readme.md">
      <description>The sai_edma_tdm_record_plyback example shows how to use sai driver to generate TDM format with EDMA:In this example, one sai instance record and playbacks the audio data in TDM format.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_tdm_record_playback_cm33.uvprojx"/>
        <environment name="csolution" load="sai_edma_tdm_record_playback_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/edma_transfer/cm33" doc="readme.md">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="sai_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/interrupt/cm33" doc="readme.md">
      <description>The sai_interrupt example shows how to use sai functional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_cm33.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/interrupt_record_playback/cm33" doc="readme.md">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback_cm33.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_record_playback_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/sai/interrupt_transfer/cm33" doc="readme.md">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer_cm33.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema42_dual_core_core0" folder="boards/evkmimxrt1180/driver_examples/sema42/dual_core/core0" doc="readme.md">
      <description>The sema42 example shows how to use SEMA42 driver to lock and unlock a sema gate:In this example:1. Firstly, Core 0 turn on LED and lock a sema gate then boot up Core 1 wake up.2. Core 1 must be wait until Core 0...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema42_dual_core_core0.uvprojx"/>
        <environment name="iar" load="iar/sema42_dual_core_core0.ewp"/>
        <environment name="csolution" load="../sema42_dual_core.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_cm33" folder="boards/evkmimxrt1180/driver_examples/semc/sdram/cm33" doc="readme.md">
      <description>The sdramc example shows how to use SEMC controller driver to initialize the external SDRAM chip.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_cm33.uvprojx"/>
        <environment name="iar" load="iar/semc_cm33.ewp"/>
        <environment name="csolution" load="semc_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell_cm33" folder="boards/evkmimxrt1180/demo_apps/shell/cm33" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell_cm33.uvprojx"/>
        <environment name="csolution" load="shell_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sinc_adc_cm33" folder="boards/evkmimxrt1180/driver_examples/sinc/adc/cm33" doc="readme.md">
      <description>The sinc adc example shows how to use SINC driver to configure SINC module as single conversion mode.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sinc_adc_cm33.uvprojx"/>
        <environment name="iar" load="iar/sinc_adc_cm33.ewp"/>
        <environment name="csolution" load="sinc_adc_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_edma_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/spdif/edma_transfer/cm33" doc="readme.md">
      <description>The spdif_edma_transfer example shows how to use spdif driver with edma:In this example, one spdif instance playbacks the audio data recorded by same spdif instance using edma.Notice: Please use 48KHz sample rate for...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_edma_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/spdif_edma_transfer_cm33.ewp"/>
        <environment name="csolution" load="spdif_edma_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_interrupt_transfer_cm33" folder="boards/evkmimxrt1180/driver_examples/spdif/interrupt_transfer/cm33" doc="readme.md">
      <description>The spdif_interrupt_transfer example shows how to use spdif driver with interrupt:In this example, one spdif instance playbacks the audio data recorded by the same spdif instance using interrupt.Notice: Please use...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_interrupt_transfer_cm33.uvprojx"/>
        <environment name="iar" load="iar/spdif_interrupt_transfer_cm33.ewp"/>
        <environment name="csolution" load="spdif_interrupt_transfer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="src_global_reset_cm33" folder="boards/evkmimxrt1180/driver_examples/src/src_global_reset/cm33" doc="readme.md">
      <description>The src_global_reset example shows how to reset the selected slice via software.s</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/src_global_reset_cm33.uvprojx"/>
        <environment name="iar" load="iar/src_global_reset_cm33.ewp"/>
        <environment name="csolution" load="src_global_reset_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sysctr_cm33" folder="boards/evkmimxrt1180/driver_examples/sysctr/cm33" doc="readme.md">
      <description>The sysctr example shows the usage of System Counter driver in application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sysctr_cm33.uvprojx"/>
        <environment name="iar" load="iar/sysctr_cm33.ewp"/>
        <environment name="csolution" load="sysctr_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tee_fault_core0" folder="boards/evkmimxrt1180/demo_apps/tee_fault/core0" doc="readme.md">
      <description>This example shows how to use the MCUXpresso Config Tools TEE tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tee_fault_core0.uvprojx"/>
        <environment name="iar" load="iar/tee_fault_core0.ewp"/>
        <environment name="csolution" load="../tee_fault.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tempsensor_cm33" folder="boards/evkmimxrt1180/driver_examples/tempsensor/cm33" doc="readme.md">
      <description>The TEMPSENSOR project is a simple demonstration program of the SDK TEMPSENSOR driver.The temperatue sensor (TEMPSENSOR) module features alarm functions that can raise independent interrupt signals if the temperature...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tempsensor_cm33.uvprojx"/>
        <environment name="iar" load="iar/tempsensor_cm33.ewp"/>
        <environment name="csolution" load="tempsensor_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer_cm33" folder="boards/evkmimxrt1180/driver_examples/tpm/timer/cm33" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer_cm33.uvprojx"/>
        <environment name="iar" load="iar/tpm_timer_cm33.ewp"/>
        <environment name="csolution" load="tpm_timer_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tstmr_cm33" folder="boards/evkmimxrt1180/driver_examples/tstmr/cm33" doc="readme.md">
      <description>The tstmr example shows the usage of TSTMR driver in application. The TSTMR module is a free running incrementing counter that starts running after system reset de-assertion and can be read at any time by the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tstmr_cm33.uvprojx"/>
        <environment name="iar" load="iar/tstmr_cm33.ewp"/>
        <environment name="csolution" load="tstmr_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar_aoi_cm33" folder="boards/evkmimxrt1180/demo_apps/xbar_aoi/cm33" doc="readme.md">
      <description>The XBAR AOI demo application demonstrates the usage of the XBAR, AOI peripheral to combine interruptfrom CMP and PIT. If the button is pressed(CMP interrupt) and the PIT periodic interrupt occurs a message is printed.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar_aoi_cm33.uvprojx"/>
        <environment name="iar" load="iar/xbar_aoi_cm33.ewp"/>
        <environment name="csolution" load="xbar_aoi_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/acmp/interrupt/cm7" doc="readme.md">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/acmp_interrupt_cm7.ewp"/>
        <environment name="csolution" load="acmp_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling_cm7" folder="boards/evkmimxrt1180/driver_examples/acmp/polling/cm7" doc="readme.md">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling_cm7.uvprojx"/>
        <environment name="iar" load="iar/acmp_polling_cm7.ewp"/>
        <environment name="csolution" load="acmp_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_edma_cm7" folder="boards/evkmimxrt1180/driver_examples/asrc/asrc_m2m_edma/cm7" doc="readme.md">
      <description>The asrc_m2m_edma example shows how to use asrc driver with edma:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_edma_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/asrc/asrc_m2m_interrupt/cm7" doc="readme.md">
      <description>The asrc_m2m_interrupt example shows how to use asrc driver with interrupt:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_m2m_polling_cm7" folder="boards/evkmimxrt1180/driver_examples/asrc/asrc_m2m_polling/cm7" doc="readme.md">
      <description>The asrc_m2m_polling example shows how to use asrc driver with polling:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_m2m_polling_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_m2m_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="asrc_p2p_out_edma_cm7" folder="boards/evkmimxrt1180/driver_examples/asrc/asrc_p2p_out_edma/cm7" doc="readme.md">
      <description>The asrc_p2p_out_edma example shows how to use asrc driver with edma:In this example, one sai instance playbacks the audio data conveted by asrc.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/asrc_p2p_out_edma_cm7.uvprojx"/>
        <environment name="csolution" load="asrc_p2p_out_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral_cm7" folder="boards/evkmimxrt1180/demo_apps/bubble_peripheral/cm7" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral_cm7.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral_cm7.ewp"/>
        <environment name="csolution" load="bubble_peripheral_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cache_cm7" folder="boards/evkmimxrt1180/driver_examples/cache/cm7" doc="readme.md">
      <description>The cache example shows how to use memory cache driver.In this example, many memory (such as SDRAM, etc) and DMA will be used to show the example.Those memory is both accessible for cpu and DMA. For the memory data...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cache_cm7.uvprojx"/>
        <environment name="iar" load="iar/cache_cm7.ewp"/>
        <environment name="csolution" load="cache_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/canfd/interrupt_transfer/cm7" doc="readme.md">
      <description>The canfd_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="canfd_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_cm7" folder="boards/evkmimxrt1180/driver_examples/canfd/loopback/cm7" doc="readme.md">
      <description>The canfd_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_cm7.ewp"/>
        <environment name="csolution" load="canfd_loopback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/canfd/loopback_transfer/cm7" doc="readme.md">
      <description>The canfd_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_transfer_cm7.ewp"/>
        <environment name="csolution" load="canfd_loopback_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_ping_pong_buffer_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/canfd/ping_pong_buffer_transfer/cm7" doc="readme.md">
      <description>The canfd_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CANFD frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_ping_pong_buffer_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/canfd_ping_pong_buffer_transfer_cm7.ewp"/>
        <environment name="csolution" load="canfd_ping_pong_buffer_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/int_b2b_transfer/master/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/int_b2b_transfer/master/cm7" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpspi/int_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpuart/edma_transfer/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/cmsis_driver_examples/lpuart/interrupt_transfer/cm7" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_basic_cm7" folder="boards/evkmimxrt1180/driver_examples/dac12/basic/cm7" doc="readme.md">
      <description>The dac12_basic example shows how to use DAC12 module simply as the general DAC12 converter.When the DAC12's fifo feature is not enabled, Any write to the DATA register will replace thedata in the buffer and push...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_basic_cm7.uvprojx"/>
        <environment name="iar" load="iar/dac12_basic_cm7.ewp"/>
        <environment name="csolution" load="dac12_basic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dac12_fifo_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/dac12/fifo_interrupt/cm7" doc="readme.md">
      <description>The dac12_fifo_interrupt example shows how to use DAC12 FIFO interrupt.When the DAC12 FIFO watermark interrupt is enabled firstly, the application would enter the DAC12 ISR immediately, since remaining FIFO data is...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac12_fifo_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/dac12_fifo_interrupt_cm7.ewp"/>
        <environment name="csolution" load="dac12_fifo_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ecat_examples_digital_io_cm7" folder="boards/evkmimxrt1180/ecat_examples/digital_io/cm7" doc="readme.md">
      <description>An EtherCAT device example with the SSC tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecat_examples_digital_io_cm7.uvprojx"/>
        <environment name="csolution" load="ecat_examples_digital_io_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma3_memory_to_memory_cm7" folder="boards/evkmimxrt1180/driver_examples/edma3/memory_to_memory/cm7" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma3_memory_to_memory_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma3_memory_to_memory_cm7.ewp"/>
        <environment name="csolution" load="edma3_memory_to_memory_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_channel_link_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/channel_link/cm7" doc="readme.md">
      <description>The EDMA4 channel link example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_channel_link_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_channel_link_cm7.ewp"/>
        <environment name="csolution" load="edma4_channel_link_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_interleave_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/interleave_transfer/cm7" doc="readme.md">
      <description>The EDMA4 interleave example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_interleave_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_interleave_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma4_interleave_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/memory_to_memory/cm7" doc="readme.md">
      <description>The EDMA4 memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_memory_to_memory_cm7.ewp"/>
        <environment name="csolution" load="edma4_memory_to_memory_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memory_to_memory_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/memory_to_memory_transfer/cm7" doc="readme.md">
      <description>The EDMA4 memory to memory transfer example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memory_to_memory_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_memory_to_memory_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma4_memory_to_memory_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_memset_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/memset/cm7" doc="readme.md">
      <description>The EDMA4 memset example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_memset_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_memset_cm7.ewp"/>
        <environment name="csolution" load="edma4_memset_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_ping_pong_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/ping_pong_transfer/cm7" doc="readme.md">
      <description>The EDMA4 ping pong transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_ping_pong_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_ping_pong_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma4_ping_pong_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_scatter_gather_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/scatter_gather/cm7" doc="readme.md">
      <description>The EDMA4 scatter gather example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK EDMA4 drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_scatter_gather_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_scatter_gather_cm7.ewp"/>
        <environment name="csolution" load="edma4_scatter_gather_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma4_wrap_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/edma4/wrap_transfer/cm7" doc="readme.md">
      <description>The EDMA4 wrap transfer example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the EDMA4 and to provide a simple example for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma4_wrap_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/edma4_wrap_transfer_cm7.ewp"/>
        <environment name="csolution" load="edma4_wrap_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eqdc_basic_cm7" folder="boards/evkmimxrt1180/driver_examples/eqdc/basic/cm7" doc="readme.md">
      <description>The EQDC project shows how to sample the shaft position based on quadrature signal (PHASEA and PHASEB).</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eqdc_basic_cm7.uvprojx"/>
        <environment name="iar" load="iar/eqdc_basic_cm7.ewp"/>
        <environment name="csolution" load="eqdc_basic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="eqdc_index_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/eqdc/index_interrupt/cm7" doc="readme.md">
      <description>The EQDC project shows how to sample the shaft position based on quadrature signal (PHASEA and PHASEB).</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/eqdc_index_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/eqdc_index_interrupt_cm7.ewp"/>
        <environment name="csolution" load="eqdc_index_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm_cm7" folder="boards/evkmimxrt1180/driver_examples/ewm/cm7" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm_cm7.uvprojx"/>
        <environment name="iar" load="iar/ewm_cm7.ewp"/>
        <environment name="csolution" load="ewm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_nor_flexspi_cm7" folder="boards/evkmimxrt1180/component_examples/flash_component/flexspi_nor/cm7" doc="readme.md">
      <description>nor flash demo shows the use of nor flash component to erase, program, and read an external nor flash device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_nor_flexspi_cm7.uvprojx"/>
        <environment name="iar" load="iar/flash_component_nor_flexspi_cm7.ewp"/>
        <environment name="csolution" load="flash_component_nor_flexspi_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_octal_flexspi_cm7" folder="boards/evkmimxrt1180/component_examples/flash_component/flexspi_octal/cm7" doc="readme.md">
      <description>octal flash demo shows the use of nor flash component to erase, program, and read an external serial nor flash device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_octal_flexspi_cm7.uvprojx"/>
        <environment name="iar" load="iar/flash_component_octal_flexspi_cm7.ewp"/>
        <environment name="csolution" load="flash_component_octal_flexspi_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_efifo_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/efifo_edma_transfer/cm7" doc="readme.md">
      <description>The flexcan_efifo_edma_transfer example shows how to use the EDMA version transactional driver to receive CAN FD Message from Enhanced Rx FIFO:In this example, when set ENABLE_LOOPBACK macro, only one board is...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_efifo_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_efifo_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_efifo_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_efifo_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/efifo_interrupt_transfer/cm7" doc="readme.md">
      <description>flexcan_efifo_interrupt_transfer example shows how to use FlexCAN Enhanced Rx FIFO in none-blocking interrupt way:In this example, when set ENABLE_LOOPBACK macro, only one board is needed. The example will config one...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_efifo_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_efifo_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_efifo_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/interrupt_transfer/cm7" doc="readme.md">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/loopback/cm7" doc="readme.md">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_cm7.ewp"/>
        <environment name="csolution" load="flexcan_loopback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/loopback_edma_transfer/cm7" doc="readme.md">
      <description>The flexcan_loopback_edma example shows how to use the EDMA version transactional driver to receiveCAN Message from Rx FIFO:To demonstrates this example, only one board is needed. The example will config one FlexCAN...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_loopback_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/loopback_transfer/cm7" doc="readme.md">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_loopback_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_ping_pong_buffer_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexcan/ping_pong_buffer_transfer/cm7" doc="readme.md">
      <description>The flexcan_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CAN frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_ping_pong_buffer_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexcan_ping_pong_buffer_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexcan_ping_pong_buffer_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/i2c/interrupt_lpi2c_transfer/cm7" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/i2c/read_accel_value_transfer/cm7" doc="readme.md">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_read_accel_value_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_i2c_read_accel_value_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/i2s/edma_transfer/cm7" doc="readme.md">
      <description>The flexio_i2s_EDMA example shows how to use flexio_i2s driver with EDMA:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using EDMA.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/i2s/interrupt_transfer/cm7" doc="readme.md">
      <description>The flexio_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pin_input_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/pin/input_interrupt/cm7" doc="readme.md">
      <description>The FLEXIO PIN Example project is a demonstration program that uses the FLEXIO software to manipulate the flexio-pin as input function.The example uses FLEXIO-PIN input to capture the edge of other gpio pin.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pin_input_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_pin_input_interrupt_cm7.ewp"/>
        <environment name="csolution" load="flexio_pin_input_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pin_led_output_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/pin/led_output/cm7" doc="readme.md">
      <description>The FLEXIO led project is a demonstration program that uses the FELXIO software to manipulate the felxio-pin.The example is supported by the set, clear, and toggle write-only registers for each flexio output data...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pin_led_output_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_pin_led_output_cm7.ewp"/>
        <environment name="csolution" load="flexio_pin_led_output_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/pwm/cm7" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm_cm7.ewp"/>
        <environment name="csolution" load="flexio_pwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/edma_lpspi_transfer/master/cm7" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/edma_lpspi_transfer/slave/cm7" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/edma_lpspi_transfer/slave_dynamic/cm7" doc="readme.md">
      <description>The flexio_spi_slave_dynamic example shows how to use flexio spi slave driver in edma way, In this example, a flexio simulated slave connect to a lpspi master. The CS signal remains low during transfer, after master...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_dynamic_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/int_lpspi_transfer/master/cm7" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/int_lpspi_transfer/slave/cm7" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_polling_lpspi_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/spi/polling_lpspi_transfer/master/cm7" doc="readme.md">
      <description>The flexio_spi_master_pooling_lpspi_slave example shows how to use flexio spi master driver in polling way. In this example, a flexio simulated master connect to a lpspi slave.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_polling_lpspi_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_polling_lpspi_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="flexio_spi_polling_lpspi_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/edma_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/int_rb_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/interrupt_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexio/uart/polling_transfer/cm7" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_hyper_ram_polling_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexspi/hyper_ram/polling_transfer/cm7" doc="readme.md">
      <description>The flexspi_hyper_ram_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Hyper RAM connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_hyper_ram_polling_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexspi_hyper_ram_polling_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexspi_hyper_ram_polling_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexspi/nor/edma_transfer/cm7" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexspi/nor/polling_transfer/cm7" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor_polling_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexspi/octal/edma_transfer/cm7" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="flexspi_octal_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_octal_polling_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/flexspi/octal/polling_transfer/cm7" doc="readme.md">
      <description>The flexspi_octal_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external octal flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_octal_polling_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/flexspi_octal_polling_transfer_cm7.ewp"/>
        <environment name="csolution" load="flexspi_octal_polling_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fsl_romapi_cm7" folder="boards/evkmimxrt1180/driver_examples/fsl_romapi/cm7" doc="readme.md">
      <description>The fsl_romapi example shows how to use flexspi rom api In this example, rom api will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command willbe executed, such as Write...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fsl_romapi_cm7.uvprojx"/>
        <environment name="iar" load="iar/fsl_romapi_cm7.ewp"/>
        <environment name="csolution" load="fsl_romapi_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_compare_cm7" folder="boards/evkmimxrt1180/driver_examples/gpt/compare/cm7" doc="readme.md">
      <description>The gpt_compare project is a simple demonstration program of the SDK GPT driver's output compare feature. Once content of an OCRx matches the value in GPT_CNT, output compare timer pin is toggled.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_compare_cm7.uvprojx"/>
        <environment name="iar" load="iar/gpt_compare_cm7.ewp"/>
        <environment name="csolution" load="gpt_compare_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_timer_cm7" folder="boards/evkmimxrt1180/driver_examples/gpt/timer/cm7" doc="readme.md">
      <description>The gpt_timer project is a simple demonstration program of the SDK GPT driver. It sets up the GPThardware block to trigger a periodic interrupt after every 1 second. When the GPT interrupt is triggereda message a printed on the UART terminal.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_timer_cm7.uvprojx"/>
        <environment name="iar" load="iar/gpt_timer_cm7.ewp"/>
        <environment name="csolution" load="gpt_timer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_demo_cm7" folder="boards/evkmimxrt1180/demo_apps/hello_world/cm7" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_demo_cm7.uvprojx"/>
        <environment name="iar" load="iar/hello_world_demo_cm7.ewp"/>
        <environment name="csolution" load="hello_world_demo_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_master example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as master and another i3c instance on the other board as slave....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The i3c_edma_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with EDMA. In this example, one i3c instance as slave and another i3c instance on the other board as master....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_edma_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="i3c_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/interrupt_b2b_transfer/master/cm7" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using interrupt method. In this example, one i3c instance as master and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_interrupt_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/interrupt_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The i3c_interrupt_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a interrupt master. In this example, one i3c instance as slave and another i3c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_interrupt_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_interrupt_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="i3c_interrupt_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_master_read_sensor_lsm6dso_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/master_read_sensor_lsm6dso/cm7" doc="readme.md">
      <description>The i3c_master_read_sensor_lsm6dso example shows how to use i3c driver as master to communicate with sensor LSM6DSO.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_master_read_sensor_lsm6dso_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_master_read_sensor_lsm6dso_cm7.ewp"/>
        <environment name="csolution" load="i3c_master_read_sensor_lsm6dso_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/polling_b2b_transfer/master/cm7" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_master example shows how to use i3c driver as master to do board to board transfer using polling method. In this example, one i3c instance as master and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i3c_polling_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/i3c/polling_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The i3c_polling_b2b_transfer_slave example shows how to use i3c driver as slave to do board to board transfer with a polling master. In this example, one i3c instance as slave and another i3c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i3c_polling_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/i3c_polling_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="i3c_polling_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="kpp_cm7" folder="boards/evkmimxrt1180/driver_examples/kpp/cm7" doc="readme.md">
      <description>The KPP Example project is a demonstration program that uses the KSDK software to manipulate the Keypad MATRIX.The example is use the continuous column and rows as 4*4 or 8*8 matrix to show the example.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kpp_cm7.uvprojx"/>
        <environment name="iar" load="iar/kpp_cm7.ewp"/>
        <environment name="csolution" load="kpp_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_dual_single_ended_conversion_cm7" folder="boards/evkmimxrt1180/driver_examples/lpadc/dual_single_ended_conversion/cm7" doc="readme.md">
      <description>The lpadc_dual_single_ended_conversion example shows how to use two channel with LPADC driver. In this example, user should indicate two channel to provide a voltage signal (can be controlled by user) as the LPADC's...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_dual_single_ended_conversion_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_dual_single_ended_conversion_cm7.ewp"/>
        <environment name="csolution" load="lpadc_dual_single_ended_conversion_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_high_sample_rate_sample_signal_cm7" folder="boards/evkmimxrt1180/demo_apps/lpadc_high_sample_rate/sample_signal/cm7" doc="readme.md">
      <description>This demo application demonstrates the use of the LPADC to sample the analog signal. In this demo, the ADC clock is set as the maximum frequency, users should input analog signals to the ADC channel, press any keys...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_high_sample_rate_sample_signal_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_high_sample_rate_sample_signal_cm7.ewp"/>
        <environment name="csolution" load="lpadc_high_sample_rate_sample_signal_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/lpadc/interrupt/cm7" doc="readme.md">
      <description>The lpadc_interrupt example shows how to use interrupt with LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_interrupt_cm7.ewp"/>
        <environment name="csolution" load="lpadc_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_polling_cm7" folder="boards/evkmimxrt1180/driver_examples/lpadc/polling/cm7" doc="readme.md">
      <description>The lpadc_single_polling example shows the simplest way to use LPADC driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the LPADC'ssample input. When...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_polling_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_polling_cm7.ewp"/>
        <environment name="csolution" load="lpadc_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpadc_sample_rate_count_cm7" folder="boards/evkmimxrt1180/demo_apps/lpadc_high_sample_rate/sample_rate_count/cm7" doc="readme.md">
      <description>The lpadc sample rate count demo application can be used to measure ADC's sample rate roughly. The sample rate for an ADC is defined as the number of output samples available per unit time, and is specified as...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpadc_sample_rate_count_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpadc_sample_rate_count_cm7.ewp"/>
        <environment name="csolution" load="lpadc_sample_rate_count_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/interrupt/cm7" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/interrupt_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/interrupt_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/polling_b2b/master/cm7" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/lpi2c/polling_b2b/slave/cm7" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave_cm7.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_adc_cm7" folder="boards/evkmimxrt1180/driver_examples/lpadc/lpit_adc/cm7" doc="readme.md">
      <description>The lpit_adc example shows how to use LPIT to generate ADC triggers. On top of the basic counter, to use the ADC trigger, simply enable the "milestone" of the ADC trigger and set it with a user-defined value. When...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_adc_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpit_adc_cm7.ewp"/>
        <environment name="csolution" load="lpit_adc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_pwm_cm7" folder="boards/evkmimxrt1180/driver_examples/lpit/lpit_pwm/cm7" doc="readme.md">
      <description>This example show how to use SDK drivers to implement the PWM feature by LPIT IP module in dualperiodcounter mode.You can set up PWM singal frequency and duty in this example.Connect PWM singal output pin to oscilloscope, you will see PWM wave.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_pwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpit_pwm_cm7.ewp"/>
        <environment name="csolution" load="lpit_pwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_single_channel_cm7" folder="boards/evkmimxrt1180/driver_examples/lpit/single_channel/cm7" doc="readme.md">
      <description>The LPIT single channel project is a simple example of the SDK LPIT driver. It sets up the LPIThardware block to trigger a periodic interrupt after every 1 second. When the LPIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_single_channel_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpit_single_channel_cm7.ewp"/>
        <environment name="csolution" load="lpit_single_channel_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/lpspi/edma_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/lpspi/edma_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master_cm7" folder="boards/evkmimxrt1180/driver_examples/lpspi/interrupt_b2b/master/cm7" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master_cm7.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/lpspi/interrupt_b2b/slave/cm7" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave_cm7.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master_cm7" folder="boards/evkmimxrt1180/driver_examples/lpspi/polling_b2b_transfer/master/cm7" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master_cm7.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave_cm7" folder="boards/evkmimxrt1180/driver_examples/lpspi/polling_b2b_transfer/slave/cm7" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave_cm7.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr_cm7" folder="boards/evkmimxrt1180/driver_examples/lptmr/cm7" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr_cm7.uvprojx"/>
        <environment name="iar" load="iar/lptmr_cm7.ewp"/>
        <environment name="csolution" load="lptmr_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/9bit_interrupt_transfer/cm7" doc="readme.md">
      <description>The lpuart_9bit_interrupt_transfer example shows how to use lpuart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_9bit_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/edma_transfer/cm7" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt/cm7" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt_rb_transfer/cm7" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt_transfer/cm7" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/interrupt_transfer_seven_bits/cm7" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits_cm7.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/polling/cm7" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_cm7.ewp"/>
        <environment name="csolution" load="lpuart_polling_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits_cm7" folder="boards/evkmimxrt1180/driver_examples/lpuart/polling_seven_bits/cm7" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits_cm7.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits_cm7.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mecc_multie_error_cm7" folder="boards/evkmimxrt1180/driver_examples/mecc/mecc_multi_error/cm7" doc="readme.md">
      <description>The MECC Multi Error project is a simple demonstration program of the SDK MECC driver. It supports Double Error Detection(SECDED) ECC function to provide reliability for 4 banks On-Chip RAM(OCRAM) access.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mecc_multie_error_cm7.uvprojx"/>
        <environment name="iar" load="iar/mecc_multie_error_cm7.ewp"/>
        <environment name="csolution" load="mecc_multie_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mecc_single_error_cm7" folder="boards/evkmimxrt1180/driver_examples/mecc/mecc_single_error/cm7" doc="readme.md">
      <description>The MECC Single Error project is a simple demonstration program of the SDK MECC driver. It supports Single Error Correction ECC function to provide reliability for 4 banks On-Chip RAM(OCRAM) access.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mecc_single_error_cm7.uvprojx"/>
        <environment name="iar" load="iar/mecc_single_error_cm7.ewp"/>
        <environment name="csolution" load="mecc_single_error_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_interrupt_core1" folder="boards/evkmimxrt1180/driver_examples/mu/interrupt/core1" doc="readme.md">
      <description>The mu_interrupt example shows how to use MU driver in interrupt way:In this example:1. Core 0 send message to Core 1 in interrupt mode via MU module.2. Core 1 send message back to Core 0 in interrupt mode.3. Core 0...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_interrupt_core1.uvprojx"/>
        <environment name="iar" load="iar/mu_interrupt_core1.ewp"/>
        <environment name="csolution" load="../mu_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mu_polling_core1" folder="boards/evkmimxrt1180/driver_examples/mu/polling/core1" doc="readme.md">
      <description>The mu_polling example shows how to use MU driver in polling way:In this example:1. Core 0 send message to Core 1 in polling mode via MU module.2. Core 1 send message back to Core 0 in polling mode.3. Core 0 receive...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mu_polling_core1.uvprojx"/>
        <environment name="iar" load="iar/mu_polling_core1.ewp"/>
        <environment name="csolution" load="../mu_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="netc_switch_cm7" folder="boards/evkmimxrt1180/driver_examples/netc/switch/cm7" doc="readme.md">
      <description>The example shows the way to use NETC driver to act as a switch. </description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/netc_switch_cm7.uvprojx"/>
        <environment name="iar" load="iar/netc_switch_cm7.ewp"/>
        <environment name="csolution" load="netc_switch_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="netc_txrx_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/netc/txrx_transfer/cm7" doc="readme.md">
      <description>The example shows the way to use NETC driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the NETC. 2. How to get the time stamp of the PTP 1588 timer. 3....See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/netc_txrx_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/netc_txrx_transfer_cm7.ewp"/>
        <environment name="csolution" load="netc_txrx_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_edma_transfer/cm7" doc="readme.md">
      <description>pdm_edma_transfer</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/pdm_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="pdm_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_interrupt/cm7" doc="readme.md">
      <description>The pdm_interrupt example shows how to use pdm driver with interrupt:In this example, one pdm instance playbacks the audio data stored in flash using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/pdm_interrupt_cm7.ewp"/>
        <environment name="csolution" load="pdm_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_edma_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_edma/cm7" doc="readme.md">
      <description>The pdm_sai_sdma example shows how to use pdm edma driver together with sai edma driver</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_edma_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_interrupt/cm7" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_interrupt_transfer/cm7" doc="readme.md">
      <description>The pdm_sdma_transfer example shows how to use pdm driver with interrupt transaction api:In this example, one sai instance loopback the audio data that recieve from PDM.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_sai_multi_channel_edma_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_sai_multi_channel_edma/cm7" doc="readme.md">
      <description>The pdm_sai_multi_channel_edma example shows how to use pdm edma driver to record multi channel data together with sai edma driver</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_sai_multi_channel_edma_cm7.uvprojx"/>
        <environment name="csolution" load="pdm_sai_multi_channel_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdm_standalone_transfer_edma_cm7" folder="boards/evkmimxrt1180/driver_examples/pdm/pdm_standalone_transfer_edma/cm7" doc="readme.md">
      <description>pdm_standalone_transfer_edma</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdm_standalone_transfer_edma_cm7.uvprojx"/>
        <environment name="iar" load="iar/pdm_standalone_transfer_edma_cm7.ewp"/>
        <environment name="csolution" load="pdm_standalone_transfer_edma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm_cm7" folder="boards/evkmimxrt1180/driver_examples/pwm/cm7" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/pwm_cm7.ewp"/>
        <environment name="csolution" load="pwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_cm7" folder="boards/evkmimxrt1180/driver_examples/qtmr/inputcapture_outputpwm/cm7" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature.The example sets up a QTMR channel for input capture. Once the input signal is received,this example will...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_cm7.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_cm7.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_dma_cm7" folder="boards/evkmimxrt1180/driver_examples/qtmr/inputcapture_outputpwm_dma/cm7" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature by DMA.The example sets up a QTMR channel for input capture. Once the input signal is received,this example...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_dma_cm7.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_dma_cm7.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_dma_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_timer_cm7" folder="boards/evkmimxrt1180/driver_examples/qtmr/timer/cm7" doc="readme.md">
      <description>The QTMR project is a simple demonstration program of the SDK QTMR driver to use QTMR as a timer.The quad-timer module provides four timer channels with a variety of controls affecting both individualand...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_timer_cm7.uvprojx"/>
        <environment name="iar" load="iar/qtmr_timer_cm7.ewp"/>
        <environment name="csolution" load="qtmr_timer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rgpio_input_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/rgpio/input_interrupt/cm7" doc="readme.md">
      <description>The RGPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rgpio_input_interrupt_cm7.uvprojx"/>
        <environment name="iar" load="iar/rgpio_input_interrupt_cm7.ewp"/>
        <environment name="csolution" load="rgpio_input_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rgpio_led_output_cm7" folder="boards/evkmimxrt1180/driver_examples/rgpio/led_output/cm7" doc="readme.md">
      <description>The RGPIO Example project is a demonstration program that uses the SDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rgpio_led_output_cm7.uvprojx"/>
        <environment name="iar" load="iar/rgpio_led_output_cm7.ewp"/>
        <environment name="csolution" load="rgpio_led_output_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rled_blinky_demo_cm7" folder="boards/evkmimxrt1180/demo_apps/led_blinky/cm7" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rled_blinky_demo_cm7.uvprojx"/>
        <environment name="iar" load="iar/rled_blinky_demo_cm7.ewp"/>
        <environment name="csolution" load="rled_blinky_demo_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/edma_ping_pong_buffer/cm7" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_half_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/edma_ping_pong_buffer_half_interrupt/cm7" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer_half_interrupt example shows how to use sai driver with EDMA half interrupt feature: In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_half_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_half_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/edma_record_playback/cm7" doc="readme.md">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_record_playback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_tdm_record_playback_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/edma_tdm_record_playback/cm7" doc="readme.md">
      <description>The sai_edma_tdm_record_plyback example shows how to use sai driver to generate TDM format with EDMA:In this example, one sai instance record and playbacks the audio data in TDM format.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_tdm_record_playback_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_tdm_record_playback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/edma_transfer/cm7" doc="readme.md">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="sai_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/interrupt/cm7" doc="readme.md">
      <description>The sai_interrupt example shows how to use sai functional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_cm7.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/interrupt_record_playback/cm7" doc="readme.md">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback_cm7.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_record_playback_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/sai/interrupt_transfer/cm7" doc="readme.md">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer_cm7.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sema42_dual_core_core1" folder="boards/evkmimxrt1180/driver_examples/sema42/dual_core/core1" doc="readme.md">
      <description>The sema42 example shows how to use SEMA42 driver to lock and unlock a sema gate:In this example:1. Firstly, Core 0 turn on LED and lock a sema gate then boot up Core 1 wake up.2. Core 1 must be wait until Core 0...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sema42_dual_core_core1.uvprojx"/>
        <environment name="iar" load="iar/sema42_dual_core_core1.ewp"/>
        <environment name="csolution" load="../sema42_dual_core.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc_cm7" folder="boards/evkmimxrt1180/driver_examples/semc/sdram/cm7" doc="readme.md">
      <description>The sdramc example shows how to use SEMC controller driver to initialize the external SDRAM chip.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc_cm7.uvprojx"/>
        <environment name="iar" load="iar/semc_cm7.ewp"/>
        <environment name="csolution" load="semc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell_cm7" folder="boards/evkmimxrt1180/demo_apps/shell/cm7" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell_cm7.uvprojx"/>
        <environment name="csolution" load="shell_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sinc_adc_cm7" folder="boards/evkmimxrt1180/driver_examples/sinc/adc/cm7" doc="readme.md">
      <description>The sinc adc example shows how to use SINC driver to configure SINC module as single conversion mode.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sinc_adc_cm7.uvprojx"/>
        <environment name="iar" load="iar/sinc_adc_cm7.ewp"/>
        <environment name="csolution" load="sinc_adc_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_edma_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/spdif/edma_transfer/cm7" doc="readme.md">
      <description>The spdif_edma_transfer example shows how to use spdif driver with edma:In this example, one spdif instance playbacks the audio data recorded by same spdif instance using edma.Notice: Please use 48KHz sample rate for...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_edma_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/spdif_edma_transfer_cm7.ewp"/>
        <environment name="csolution" load="spdif_edma_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spdif_interrupt_transfer_cm7" folder="boards/evkmimxrt1180/driver_examples/spdif/interrupt_transfer/cm7" doc="readme.md">
      <description>The spdif_interrupt_transfer example shows how to use spdif driver with interrupt:In this example, one spdif instance playbacks the audio data recorded by the same spdif instance using interrupt.Notice: Please use...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spdif_interrupt_transfer_cm7.uvprojx"/>
        <environment name="iar" load="iar/spdif_interrupt_transfer_cm7.ewp"/>
        <environment name="csolution" load="spdif_interrupt_transfer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sysctr_cm7" folder="boards/evkmimxrt1180/driver_examples/sysctr/cm7" doc="readme.md">
      <description>The sysctr example shows the usage of System Counter driver in application.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sysctr_cm7.uvprojx"/>
        <environment name="iar" load="iar/sysctr_cm7.ewp"/>
        <environment name="csolution" load="sysctr_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tee_fault_core1" folder="boards/evkmimxrt1180/demo_apps/tee_fault/core1" doc="readme.md">
      <description>This example shows how to use the MCUXpresso Config Tools TEE tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tee_fault_core1.uvprojx"/>
        <environment name="iar" load="iar/tee_fault_core1.ewp"/>
        <environment name="csolution" load="../tee_fault.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tempsensor_cm7" folder="boards/evkmimxrt1180/driver_examples/tempsensor/cm7" doc="readme.md">
      <description>The TEMPSENSOR project is a simple demonstration program of the SDK TEMPSENSOR driver.The temperatue sensor (TEMPSENSOR) module features alarm functions that can raise independent interrupt signals if the temperature...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tempsensor_cm7.uvprojx"/>
        <environment name="iar" load="iar/tempsensor_cm7.ewp"/>
        <environment name="csolution" load="tempsensor_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer_cm7" folder="boards/evkmimxrt1180/driver_examples/tpm/timer/cm7" doc="readme.md">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer_cm7.uvprojx"/>
        <environment name="iar" load="iar/tpm_timer_cm7.ewp"/>
        <environment name="csolution" load="tpm_timer_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="tstmr_cm7" folder="boards/evkmimxrt1180/driver_examples/tstmr/cm7" doc="readme.md">
      <description>The tstmr example shows the usage of TSTMR driver in application. The TSTMR module is a free running incrementing counter that starts running after system reset de-assertion and can be read at any time by the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tstmr_cm7.uvprojx"/>
        <environment name="iar" load="iar/tstmr_cm7.ewp"/>
        <environment name="csolution" load="tstmr_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar_aoi_cm7" folder="boards/evkmimxrt1180/demo_apps/xbar_aoi/cm7" doc="readme.md">
      <description>The XBAR AOI demo application demonstrates the usage of the XBAR, AOI peripheral to combine interruptfrom CMP and PIT. If the button is pressed(CMP interrupt) and the PIT periodic interrupt occurs a message is printed.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar_aoi_cm7.uvprojx"/>
        <environment name="iar" load="iar/xbar_aoi_cm7.ewp"/>
        <environment name="csolution" load="xbar_aoi_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="evkmimxrt1180" Cversion="1.0.0" condition="BOARD_Project_Template.evkmimxrt1180.condition_id">
      <description>Board_project_template evkmimxrt1180</description>
      <files>
        <file category="header" name="boards/evkmimxrt1180/project_template/board.h" projectpath="board"/>
        <file category="sourceC" name="boards/evkmimxrt1180/project_template/board.c" projectpath="board"/>
        <file category="header" name="boards/evkmimxrt1180/project_template/clock_config.h" projectpath="board"/>
        <file category="sourceC" name="boards/evkmimxrt1180/project_template/clock_config.c" projectpath="board"/>
        <file condition="allOf.cores=cm7f.condition_id" category="header" name="boards/evkmimxrt1180/project_template/cm7/pin_mux.h" projectpath="board"/>
        <file condition="allOf.cores=cm7f.condition_id" category="sourceC" name="boards/evkmimxrt1180/project_template/cm7/pin_mux.c" projectpath="board"/>
        <file condition="allOf.cores=cm33.condition_id" category="header" name="boards/evkmimxrt1180/project_template/cm33/pin_mux.h" projectpath="board"/>
        <file condition="allOf.cores=cm33.condition_id" category="sourceC" name="boards/evkmimxrt1180/project_template/cm33/pin_mux.c" projectpath="board"/>
        <file category="header" name="boards/evkmimxrt1180/project_template/project_template/peripherals.h" projectpath="board" path="boards/evkmimxrt1180/project_template"/>
        <file category="sourceC" name="boards/evkmimxrt1180/project_template/project_template/peripherals.c" projectpath="board"/>
        <file category="include" name="boards/evkmimxrt1180/project_template/"/>
        <file condition="allOf.cores=cm7f.condition_id" category="include" name="boards/evkmimxrt1180/project_template/cm7/"/>
        <file condition="allOf.cores=cm33.condition_id" category="include" name="boards/evkmimxrt1180/project_template/cm33/"/>
      </files>
    </component>
    <component Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkmimxrt1180 xip" Cversion="2.0.1" condition="driver.xip_board.evkmimxrt1180.condition_id">
      <description>XIP Board Driver</description>
      <files>
        <file category="sourceC" name="boards/evkmimxrt1180/xip/evkmimxrt1180_flexspi_nor_config.c" projectpath="xip"/>
        <file category="header" name="boards/evkmimxrt1180/xip/evkmimxrt1180_flexspi_nor_config.h" projectpath="xip"/>
        <file category="include" name="boards/evkmimxrt1180/xip/"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Misc" Csub="jlinkscript" Cversion="1.0.0" condition="utility.jlinkscript.evkmimxrt1180.condition_id">
      <description>Utility evkmimxrt1180_jlinkscript</description>
      <files>
        <file category="other" name="boards/evkmimxrt1180/jlinkscript/evkmimxrt1180_cm33.jlinkscript" projectpath="board"/>
        <file category="other" name="boards/evkmimxrt1180/jlinkscript/evkmimxrt1180_cm7.jlinkscript" projectpath="board"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Misc" Csub="xmcd" Cversion="1.0.0" condition="utility.xmcd.evkmimxrt1180.condition_id">
      <description>Utility evkmimxrt1180_xmcd</description>
      <files>
        <file category="other" name="boards/evkmimxrt1180/xmcd/hyperram_xmc_auto_detect.bin" projectpath="board"/>
        <file category="other" name="boards/evkmimxrt1180/xmcd/semc_sdram_xmc_166MHz_32MB_16bits.bin" projectpath="board"/>
      </files>
    </component>
  </components>
</package>
