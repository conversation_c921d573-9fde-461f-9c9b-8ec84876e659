<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss-v3/master/en/semiconductor/product/dl/device-family-pack/</url>
  <name>TXZ4Aplus-M4K2_DFP</name>
  <description>Toshiba TXZ4A+ Series TMPM4K(2) Group Device Support</description>
  <releases>
    <release version="1.0.1" date="2024-03-25">
      Release version of TXZ4A+ Series Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4K</keyword>
    <keyword>TXZ4A+</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4A+ Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
The TXZ4A+ microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>
      <!-- ************************  Subfamily 'TMPM4Kx'  **************************** -->
      <subFamily DsubFamily="M4K(2)">
        <!-- *************************1************ NFYAD*N=100P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNFYAFG'  ************************* -->
        <device Dname="TMPM4KNFYAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-LQFP144-2020-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************2*************NFWAD*N=100P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNFWAFG'  ************************* -->
        <device Dname="TMPM4KNFWAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-LQFP144-2020-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************3*************NFYA*N=100P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNFYADFG'  ************************* -->
        <device Dname="TMPM4KNFYADFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-QFP100-1420-0.65-001"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************4*************NFWA*N=100P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNFWADFG'  ************************* -->
        <device Dname="TMPM4KNFWADFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-QFP100-1420-0.65-001"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************5*************MFYA*M=80P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4KMFYAFG'  ************************* -->
        <device Dname="TMPM4KMFYAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KMA.h" define="TMPM4KMA"/>
          <debug svd="SVD/M4KMA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="67"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="18"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="18"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="4"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="80"                         name="P-LQFP80-1212-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************6*************MFWA*M=80P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4KMFWAFG'  ************************* -->
        <device Dname="TMPM4KMFWAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KMA.h" define="TMPM4KMA"/>
          <debug svd="SVD/M4KMA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="67"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="18"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="18"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="4"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="80"                         name="P-LQFP80-1212-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************7*************LFYA*L=64P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLFYAUG'  ************************* -->
        <device Dname="TMPM4KLFYAUG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1010-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************8*************LFWA*L=64P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLFWAUG'  ************************* -->
        <device Dname="TMPM4KLFWAUG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1010-0.50-003"/>
        </device>

        <!-- *************************9*************LFYAF*L=64P 256****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLFYAFG'  ************************* -->
        <device Dname="TMPM4KLFYAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_256.FLM" start="0x00000000" size="0x00040000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1414-0.8-002"/>
          <!--book name=""/-->

        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************10*************LFWAF*L=64P 128****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLFWAFG'  ************************* -->
        <device Dname="TMPM4KLFWAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_128.FLM" start="0x00000000" size="0x00020000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="15"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="15"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"      m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver Plus (PMD+)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (A-ENC)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power-on Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1414-0.8-002"/>
          <!--book name=""/-->

        </device>

        <!-- *************************13************NF10AD*N=100P 1024****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNF10ADFG'  ************************* -->
        <device Dname="TMPM4KNF10ADFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <!--book name=""/-->
          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="32"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="32"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>
          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>
          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>
          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>
          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-QFP100-1420-0.65-001"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************14************NFDAD*N=100P 512****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNFDADFG'  ************************* -->
        <device Dname="TMPM4KNFDADFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="32"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="32"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-QFP100-1420-0.65-001"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************15************NF10A*N=100P 1024****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNF10AFG'  ************************* -->
        <device Dname="TMPM4KNF10AFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="32"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="32"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-LQFP100-1414-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************16************NFDA*N=100P 512****************************************************** -->
        <!-- ***********************  Device 'TMPM4KNFDAFG'  ************************* -->
        <device Dname="TMPM4KNFDAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KNA.h" define="TMPM4KNA"/>
          <debug svd="SVD/M4KNA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="87"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="32"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="32"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="32"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="4"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="5"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="6"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="3"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="100"                         name="P-LQFP100-1414-0.50-002"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************17************LF10AU*N=64P 1024****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLF10AUG'  ************************* -->
        <device Dname="TMPM4KLF10AUG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1010-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************18************LFDAU*N=64P 512****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLFDAUG'  ************************* -->
        <device Dname="TMPM4KLFDAUG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1010-0.50-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************19************LF10AF*N=64P 1024****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLF10AFG'  ************************* -->
        <device Dname="TMPM4KLF10AFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1414-0.80-003"/>
        </device>
        <!-- ********************************************************************************************* -->

        <!-- *************************20************LFDAF*N=64P 512****************************************************** -->
        <!-- ***********************  Device 'TMPM4KLFDAFG'  ************************* -->
        <device Dname="TMPM4KLFDAFG">
          <processor Dclock="160000000"/>
          <compile header="Device/Include/TMPM4KLA.h" define="TMPM4KLA"/>
          <debug svd="SVD/M4KLA.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Kx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Kx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="IOs"           n="51"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="20"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="20"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="6"      m="32"               name="32bit Timer Event counter (T32A)"/>

          <feature type="UART"          n="3"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit (I2C)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="2"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="8"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter B (ADC)"/>
          <feature type="ADC"           n="3"      m="12"               name="Analog to Digital Converter C (ADC)"/>

          <feature type="CoreOther"     n="1"                           name="Advanced Vector Engine Plus (A-VE+)"/>
          <feature type="CoreOther"     n="3"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="1"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Clock Selective Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="2.70"   m="5.50"/>
          <feature type="Temp"          n="-40"    m="105"/>
          <feature type="QFP"           n="64"                         name="P-LQFP64-1414-0.80-003"/>
        </device>
        <!-- ********************************************************************************************* -->
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="TMPM4KYA Compiler">
      <accept condition="Compiler ARMCC"/>
    </condition>

    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="TMPM4KNx CMSIS">
      <description>Toshiba TMPM4KNx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4KN*"/>
      <require Cclass="CMSIS" Cgroup="CORE" Cversion="5.6.0"/>
    </condition>
    <condition id="TMPM4KMx CMSIS">
      <description>Toshiba TMPM4KMx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4KM*"/>
      <require Cclass="CMSIS" Cgroup="CORE" Cversion="5.6.0"/>
    </condition>
    <condition id="TMPM4KLx CMSIS">
      <description>Toshiba TMPM4KLx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4KL*"/>
      <require Cclass="CMSIS" Cgroup="CORE" Cversion="5.6.0"/>
    </condition>
  </conditions>

  <components>

    <!-- Startup TMPM4KNx -->
    <!-- Startup TMPM4KNx N=100P -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="TMPM4KNx CMSIS">
      <description>System Startup for Toshiba TMPM4KNx Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4KYA Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4KNA.s"          attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4KNA.scat"               attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM4KyA.c"               attr="config" version="1.0.1" condition="TMPM4KYA Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4KMx -->
    <!-- Startup TMPM4KMx M=80P -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="TMPM4KMx CMSIS">
      <description>System Startup for Toshiba TMPM4KMx Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4KYA Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4KMA.s"          attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4KMA.scat"               attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM4KyA.c"               attr="config" version="1.0.1" condition="TMPM4KYA Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4KLx -->
    <!-- Startup TMPM4KLx L=64P -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.1" condition="TMPM4KLx CMSIS">
      <description>System Startup for Toshiba TMPM4KLx Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM4KYA Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM4KLA.s"          attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM4KLA.scat"               attr="config" version="1.0.1" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM4KyA.c"               attr="config" version="1.0.1" condition="TMPM4KYA Compiler"/>
      </files>
    </component>

  </components>

</package>
