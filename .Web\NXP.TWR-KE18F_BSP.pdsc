<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>TWR-KE18F_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for TWRKE18F</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKE18F16_DFP" vendor="NXP" version="12.2.0"/>
      <package name="CMSIS" vendor="ARM" version="5.6.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="TWR-KE18F">
      <description>Kinetis KE18 MCU Tower System Module</description>
      <mountedDevice Dname="MKE18F512xxx16" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKE18F16">
      <accept Dname="MKE18F512xxx16" Dvariant="MKE18F512VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F512VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F512xxx16" Dvariant="MKE18F512VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F512VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256xxx16" Dvariant="MKE18F256VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256VLL16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256xxx16" Dvariant="MKE18F256VLH16" Dvendor="NXP:11"/>
      <accept Dname="MKE18F256VLH16" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MKE18F16_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKE18F16_startup_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_driver.trgmux_AND_utility.debug_console">
      <require condition="device.MKE18F16"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKE18F16_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="trgmux"/>
    </condition>
  </conditions>
  <examples>
    <example name="acmp_interrupt" folder="driver_examples/acmp/interrupt" doc="readme.txt">
      <description>The ACMP Interrupt project is a simple demonstration program that uses the SDK software. Itcompares the selected analog input with ACMP internal DAC output continuously and toggle the LEDwhen the final comparison result changed. The purpose of this demo is to show how to use theACMP driver in SDK software by interrupt way. The ACMP can be configured based on defaultconfiguration returned by the API ACMP_GetDefaultConfig(). The default configuration is: highspeed is not enabled, invert output is not enabled, unfiltered output is not enabled, pin outis not enabled, offset level is level 0, hysteresis level is level 0.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acmp_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_polling" folder="driver_examples/acmp/polling" doc="readme.txt">
      <description>The ACMP Polling project is a simple demonstration program that uses the SDK software. It comparesthe selected analog input with ACMP internal DAC output continuously and toggle the LED when thefinal comparison result changed. The purpose of this demo is to show how to use the ACMP driverin SDK software by polling way. The ACMP can be configured based on default configuration returnedby the API ACMP_GetDefaultConfig(). The default configuration is: high speed is not enabled, invertoutput is not enabled, unfiltered output is not enabled, pin out is not enabled, offset level islevel 0, hysteresis level is level 0.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acmp_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="acmp_round_robin" folder="driver_examples/acmp/round_robin" doc="readme.txt">
      <description>The ACMP Round-Robin project is a simple demonstration program that uses the SDK software. Usermust set the round-robin mode trigger in specific board properly according to the board resourcebefore running the example. When the example running, it sets positive port as fixed channel andinternal DAC output as comparison reference in positive port and sets the channels input by useras round-robin checker channel. The example will enter stop mode and wait user to change the voltageof round-robin checker channel. It will exit stop mode after the voltage of round-robin checkerchannel changed by user.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acmp_round_robin.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/acmp_round_robin.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_continuous_edma" folder="driver_examples/adc12/continuous_edma" doc="readme.txt">
      <description>The ADC12 continuous EDMA demo application demonstrates the usage of the ADC and EDMA peripheral while in a continuous mode. TheADC12 is first set to continuous mode. In continuous convert configuration, only the initial rising-edge to launch continuous conversions isobserved, and until conversion is aborted, the ADC12 continues to do conversions on the same SCn register that initiated the conversion. EDMA request will be asserted during an ADC12 conversion complete event noted when any of the SC1n[COCO] flags is asserted. EDMA will transferADC12 results to memory and if users press any key, demo will average ADC12 results stored in memory and print average on the terminal.  </description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_continuous_edma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_continuous_edma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_interrupt" folder="driver_examples/adc12/interrupt" doc="readme.txt">
      <description>The adc12_interrupt example shows how to use interrupt with ADC12 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12's sample input.When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value.Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the ADC12 interrupt configuration is set when configuring the ADC12's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing a conversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_low_power" folder="demo_apps/adc12_low_power" doc="readme.txt">
      <description>The ADC Low Power Demo project is a demonstration program that uses the KSDK software. Themicrocontroller is set to a very low power stop (VLPS) mode, and every 500 ms an interrupt wakes upthe ADC module and takes the current temperature sensor value of the microcontroller. While the temperature remains within boundaries, both LEDs are on. If the temperature is higher or lower thanaverage, a led comes off. This demo provides an example to show how ADC works during a VLPS mode anda simple debugging.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_low_power.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_low_power.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_low_power_peripheral" folder="demo_apps/adc12_low_power_peripheral" doc="readme.txt">
      <description>The ADC Low Power Demo project is a demonstration program that uses the KSDK software. Themicrocontroller is set to a very low power stop (VLPS) mode, and every 500 ms an interrupt wakes upthe ADC module and takes the current temperature sensor value of the microcontroller. While the temperature remains within boundaries, both LEDs are on. If the temperature is higher or lower thanaverage, a led comes off. This demo provides an example to show how ADC works during a VLPS mode anda simple debugging.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_low_power_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_low_power_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_lpit_trgmux" folder="driver_examples/adc12/lpit_trgmux" doc="readme.txt">
      <description>The adc12_lpit_trgmux example shows how to use the LPIT and TRGMUX to generate a ADC trigger.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12'ssample input. When run the example, the ADC is triggered by TRGMUX and gets the ADC conversion result in the ADCConversion Complete (COCO) Interrupt. The LPIT is configured as periodic counter which will output pre-trigger andtigger signal to TRGMUX periodically.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_lpit_trgmux.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_lpit_trgmux.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc12_polling" folder="driver_examples/adc12/polling" doc="readme.txt">
      <description>The adc12_polling example shows the simplest way to use ADC12 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC12's sample input.When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.Note, the default setting of initialization for the ADC converter is just an available configuration. User can change theconfiguration structure's setting in application to fit the special requirement.The auto-calibration is not essential but strongly recommended. It can help to adjust the converter itself and improve theADC12's performance.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc12_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/adc12_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble" folder="demo_apps/bubble" doc="readme.txt">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/bubble.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral" folder="demo_apps/bubble_peripheral" doc="readme.txt">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis. You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/bubble_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master" folder="cmsis_driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_edma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with EDMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpi2c_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave" folder="cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_edma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with EDMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpi2c_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master" folder="cmsis_driver_examples/lpi2c/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The lpi2c_int_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpi2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/lpi2c/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The lpi2c_int_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpi2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master" folder="cmsis_driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.txt">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_edma_b2b_transfer_master.c' includes the LPSPI master code.1. LPSPI master send/received data to/from LPSPI slave in edma . </description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpspi_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave" folder="cmsis_driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_edma_b2b_transfer_slave.c' includes the LPSPI slave code.1. LPSPI master send/received data to/from LPSPI slave in edma . </description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpspi_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master" folder="cmsis_driver_examples/lpspi/int_b2b_transfer/master" doc="readme.txt">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_int_b2b_transfer_master.c' includes the LPSPI master code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . </description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpspi_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave" folder="cmsis_driver_examples/lpspi/int_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_int_b2b_transfer_slave.c' includes the LPSPI slave code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . </description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpspi_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="cmsis_driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/cmsis_lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac32_adc12" folder="demo_apps/dac32_adc12" doc="readme.txt">
      <description>The dac32_adc12 example shows a simple case of using DAC32 and ADC12. The DAC32 module would output analog signal, then ADC12 module would read this value of this signal and output the voltage value.User needs to make sure that DAC's output and ADC's input are stable.Notice:In few cases of this demo,DAC's input and ADC's output might not match,since the voltage signalis unstable.User has to wait the DAC a few while to make the result as expected in application if necessary.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac32_adc12.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dac32_adc12.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac32_basic" folder="driver_examples/dac32/basic" doc="readme.txt">
      <description>The dac32_basic example shows how to use DAC32 module simply as the general DAC converter.When the DAC32's buffer feature is not enabled, the first item of the buffer is used as the DAC output data register.The converter would always output the value of the first item. In this example, it gets the value from terminal,outputs the DAC output voltage through DAC output pin.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac32_basic.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dac32_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac32_buffer_interrupt" folder="driver_examples/dac32/buffer_interrupt" doc="readme.txt">
      <description>The dac32_buffer_interrupt example shows how to use DAC32 buffer with interrupts.When the DAC32's buffer feature is enabled, user can benefit from the automation of updating DAC output by hardware/software trigger.As we know, the DAC converter outputs the value of item pointed by current read pointer. Once the buffer is triggered by softwareor hardware, the buffer's read pointer would move automatically as the work mode is set, like normal (cycle) mode, swing mode,one-time-scan mode or FIFO mode.In this example, it captures the user's type-in operation from terminal and does the software trigger to the buffer.The terminal would also display the log that shows the current buffer pointer's position with buffer events.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac32_buffer_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dac32_buffer_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac32_continuous_pdb_edma" folder="driver_examples/dac32/continuous_pdb_edma" doc="readme.txt">
      <description>The demo shows how to use the PDB to generate a DAC32 trigger and use the DMA to transfer data into DAC32 buffer.In this example, DAC32 is first set to normal buffer mode. PDB is as DAC32 hardware trigger source and DMA would work when DAC32 read pointer is zero. When run the example, the DAC32 is triggered by PDB and the read pointer increases by one,every time the trigger occurs. When the read pointer reaches the upper limit, it goes to zero directly in the next trigger event.While read pointer goes to zero, DMA request will be triggered and transfer data into DAC32 buffer. The user should probethe DAC output with a oscilloscope to see the Half-sine signal.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac32_continuous_pdb_edma.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/dac32_continuous_pdb_edma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ecompass" folder="demo_apps/ecompass" doc="readme.txt">
      <description>The E-Compass demo application demonstrates the use of the FXOS8700 sensor. The tilt-compensated algorithm calculatesall three angles (pitch, roll, and yaw or compass heading).</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecompass.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ecompass.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ecompass_peripheral" folder="demo_apps/ecompass_peripheral" doc="readme.txt">
      <description>The E-Compass demo application demonstrates the use of the FXOS8700 sensor. The tilt-compensated algorithm calculatesall three angles (pitch, roll, and yaw or compass heading). You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecompass_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ecompass_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="driver_examples/edma/memory_to_memory" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="driver_examples/edma/scatter_gather" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/edma_scatter_gather.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm" folder="driver_examples/ewm" doc="readme.txt">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt will be generated.After the first pressing, another interrupt can be triggered by pressing button again.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ewm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer" folder="driver_examples/flexcan/interrupt_transfer" doc="readme.txt">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when user press space key in terminal. Endpoint B receive the message, printthe message content to terminal and echo back the message. Endpoint A will increase the receivedmessage and waiting for the next transmission of the user initiated.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexcan_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback" folder="driver_examples/flexcan/loopback" doc="readme.txt">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message Buffer and the other FlexCAN Message Buffer to Tx Message Buffer with same ID.After that, the example will send a CAN Message from the Tx Message Buffer to the Rx Message Bufferthrouth internal loopback interconnect and print out the Message payload to terminal.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexcan_loopback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_edma_transfer" folder="driver_examples/flexcan/loopback_edma_transfer" doc="readme.txt">
      <description>The flexcan_loopback_edma example shows how to use the EDMA version transactional driver to receiveCAN Message from Rx FIFO:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Tx Message Buffer and also setup Rx FIFO. After that, the example will send 4 CAN Messagesfrom Tx Message Buffer to Rx FIFO through internal loopback interconnect and read them out usingEDMA version FlexCAN transactional driver. The Sent and received message will be print out to terminalat last.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexcan_loopback_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer" folder="driver_examples/flexcan/loopback_transfer" doc="readme.txt">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message Buffer and the other FlexCAN Message Buffer to Tx Message Buffer with same ID.After that, the example will send a CAN Message from the Tx Message Buffer to the Rx Message Bufferthrough internal loopback interconnect and print out the Message payload to terminal.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexcan_loopback_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer" folder="driver_examples/flexio/i2c/interrupt_lpi2c_transfer" doc="readme.txt">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer" folder="driver_examples/flexio/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C  Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use the flexio i2c master driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="driver_examples/flexio/pwm" doc="readme.txt">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master" folder="driver_examples/flexio/spi/edma_lpspi_transfer/master" doc="readme.txt">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_edma_lpspi_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave" folder="driver_examples/flexio/spi/edma_lpspi_transfer/slave" doc="readme.txt">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master" folder="driver_examples/flexio/spi/int_lpspi_transfer/master" doc="readme.txt">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_int_lpspi_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave" folder="driver_examples/flexio/spi/int_lpspi_transfer/slave" doc="readme.txt">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_spi_int_lpspi_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer" folder="driver_examples/flexio/uart/edma_transfer" doc="readme.txt">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="driver_examples/flexio/uart/int_rb_transfer" doc="readme.txt">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="driver_examples/flexio/uart/interrupt_transfer" doc="readme.txt">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board. Note: two queued transfer in this example, so please input even number characters.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="driver_examples/flexio/uart/polling_transfer" doc="readme.txt">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_dflash" folder="driver_examples/flash/flexnvm_dflash" doc="readme.txt">
      <description>The flexnvm_dflash example shows how to use flash driver to operate data flash:</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_dflash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexnvm_dflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_eeprom" folder="driver_examples/flash/flexnvm_eeprom" doc="readme.txt">
      <description>The flexnvm_eeprom example shows how to use flash driver to operate eeprom:</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_eeprom.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexnvm_eeprom.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexnvm_flexram" folder="driver_examples/flash/flexnvm_flexram" doc="readme.txt">
      <description>The flexnvm_flexram example shows how to use flash driver to operate eeprom:</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexnvm_flexram.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/flexnvm_flexram.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_combine_pwm" folder="driver_examples/ftm/combine_pwm" doc="readme.txt">
      <description>The FTM project is a demonstration program of generating a combined PWM signal by the SDK FTM driver. It sets up the FTMhardware block to output PWM signals on two TPM channels. The example also shows the complementary mode of operationand deadtime insertion.On boards that have 2 LEDs connected to the FTM pins, the user will see a change in LED brightness.And if the board do not support LEDs to show, the outputs can be observed by oscilloscope.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_combine_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_combine_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_dual_edge_capture" folder="driver_examples/ftm/dual_edge_capture" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a FTM channel-pair for dual-edge capture. Once the input signal is received,this example will print the capture values and period of the input signal on the terminal window.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_dual_edge_capture.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_dual_edge_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_input_capture" folder="driver_examples/ftm/input_capture" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's input capture feature.The example sets up a FTM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_input_capture.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_input_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_output_compare" folder="driver_examples/ftm/output_compare" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's output compare feature.It sets up one FTM channel to toggle the output when a match occurs with the channel value. The usershould probe the FTM output with a oscilloscope to see the signal toggling.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_output_compare.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_output_compare.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pdb_adc12" folder="demo_apps/ftm_pdb_adc12" doc="readme.txt">
      <description>This application demonstrates how to use the trigger signal generated by FTM to trigger the ADCconversion through PDB module.For instance, the FTM0 is configured to work in PWM mode. And its channel 0 monitors the FTM counterand compares with the channel value. Once the FTM counter matches the channel value, an externaltrigger would be generated. TRGMUX would capture this trigger and route it to PDB module. When thePDB is triggered, the PDB counter increase. Once the PDB matches the ADC pre-trigger value, a triggersignal would generated and sent to ADC module. Finally, the ADC gets the trigger signal and start theconversion.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pdb_adc12.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_pdb_adc12.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pdb_adc12_peripheral" folder="demo_apps/ftm_pdb_adc12_peripheral" doc="readme.txt">
      <description>This application demonstrates how to use the trigger signal generated by FTM to trigger the ADCconversion through PDB module.For instance, the FTM0 is configured to work in PWM mode. And its channel 0 monitors the FTM counterand compares with the channel value. Once the FTM counter matches the channel value, an externaltrigger would be generated. TRGMUX would capture this trigger and route it to PDB module. When thePDB is triggered, the PDB counter increase. Once the PDB matches the ADC pre-trigger value, a triggersignal would generated and sent to ADC module. Finally, the ADC gets the trigger signal and start theconversion.You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pdb_adc12_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_pdb_adc12_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pwm_twochannel" folder="driver_examples/ftm/pwm_twochannel" doc="readme.txt">
      <description>The FTM pwm two channel Example project is a demonstration program that uses the KSDK software to generate a square pulse PWM on 2 channel to control the LED brightness.- FTM generates a PWM with the increasing and decreasing duty cycle.- LED brightness is increasing and then dimming. This is a continuous process.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pwm_twochannel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_pwm_twochannel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_simple_pwm" folder="driver_examples/ftm/simple_pwm" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver. It sets up the FTMhardware block to output a center-aligned PWM signal. The PWM dutycycle is periodically updated.On boards that have an LED connected to the FTM pins, the user will see a change in LED brightness.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_simple_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="driver_examples/ftm/timer" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/ftm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="driver_examples/gpio/input_interrupt" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example uses the software button to control/toggle the LED.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_input_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master" folder="driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.txt">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave" folder="driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt" folder="driver_examples/lpi2c/interrupt" doc="readme.txt">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as lpi2c slave .1. lpi2c master send data using interrupt to lpi2c slave in interrupt .2. lpi2c master read data using interrupt from lpi2c slave in interrupt .3. The example assumes that the connection is OK between master and slave, so there's NO error handling code.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master" folder="driver_examples/lpi2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave" folder="driver_examples/lpi2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master" folder="driver_examples/lpi2c/polling_b2b/master" doc="readme.txt">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_polling_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave" folder="driver_examples/lpi2c/polling_b2b/slave" doc="readme.txt">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_polling_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_read_accel_value_transfer" folder="driver_examples/lpi2c/read_accel_value_transfer" doc="readme.txt">
      <description>The lpi2c_read_accel_value example shows how to use LPI2C driver to communicate with an lpi2c device: 1. How to use the lpi2c driver to read a lpi2c device who_am_I register. 2. How to use the lpi2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_vlps" folder="demo_apps/lpi2c_vlps" doc="readme.txt">
      <description>This example describes how to use kinetis SDK drivers to implement LPI2C transmit and receive in the VLPS (very low power STOP mode) with async DMA.The LPI2C module is designed to have ability to work under low power module like STOP, VLPW and VLPS. It can use DMA to transmit the data from or to application user buffer without CPU interaction.It uses LPI2C to access the on board accelerometer sensor to read the Accelerometer X, Y, Z data every 500ms. CPU would keep in VLPS low power mode, except for some trigger events and data output to LPUART0.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_vlps.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_vlps.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_vlps_peripheral" folder="demo_apps/lpi2c_vlps_peripheral" doc="readme.txt">
      <description>This example describes how to use kinetis SDK drivers to implement LPI2C transmit and receive in the VLPS (very low power STOP mode) with async DMA.The LPI2C module is designed to have ability to work under low power module like STOP, VLPW and VLPS. It can use DMA to transmit the data from or to application user buffer without CPU interaction.It uses LPI2C to access the on board accelerometer sensor to read the Accelerometer X, Y, Z data every 500ms. CPU would keep in VLPS low power mode, except for some trigger events and data output to LPUART0.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_vlps_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpi2c_vlps_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_chained_channel" folder="driver_examples/lpit/chained_channel" doc="readme.txt">
      <description>The LPIT chained channel project is a simple example of the SDK LPIT driver. It sets up the LPIT hardware block to trigger a periodic interrupt after every 1 second in the channel No.0, the channel No.1 chained with channel No.0, if LPIT contain more than two channels, the channel No.2 chained with channel No.1....the channel No.N chained with Channel No.N-1. When the LPIT interrupt is triggered.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_chained_channel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpit_chained_channel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit_single_channel" folder="driver_examples/lpit/single_channel" doc="readme.txt">
      <description>The LPIT single channel project is a simple example of the SDK LPIT driver. It sets up the LPIThardware block to trigger a periodic interrupt after every 1 second. When the LPIT interrupt is triggereda message a printed on the serial terminal and an LED is toggled on the board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit_single_channel.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpit_single_channel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master" folder="driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.txt">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_edma_b2b_transfer_master.c' includes the LPSPI master code.1. LPSPI master send/received data to/from LPSPI slave in edma . (LPSPI Slave using edma to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave" folder="driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_edma_b2b_transfer_slave.c' includes the LPSPI slave code.1. LPSPI master send/received data to/from LPSPI slave in edma . (LPSPI Slave using edma to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt" folder="driver_examples/lpspi/interrupt" doc="readme.txt">
      <description>The lpspi_functional_interrupt example shows how to use LPSPI driver in interrupt way:In this example , one lpspi instance used as LPSPI master and another lpspi instance used as LPSPI slave .1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master" folder="driver_examples/lpspi/interrupt_b2b/master" doc="readme.txt">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_interrupt_b2b_master.c' includes the LPSPI master code.This example does not use the transactional API in LPSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave" folder="driver_examples/lpspi/interrupt_b2b/slave" doc="readme.txt">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file 'dspi_interrupt_b2b_slave.c' includes the LPSPI slave code.This example does not use the transactional API in LPSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master" folder="driver_examples/lpspi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_interrupt_b2b_transfer_master.c' includes the LPSPI master code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave" folder="driver_examples/lpspi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_interrupt_b2b_transfer_slave.c' includes the LPSPI slave code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master" folder="driver_examples/lpspi/polling_b2b_transfer/master" doc="readme.txt">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_polling_b2b_transfer_master.c' includes the LPSPI master code.1. LPSPI master send/received data to/from LPSPI slave in polling . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave" folder="driver_examples/lpspi/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_polling_b2b_transfer_slave.c' includes the LPSPI slave code.1. LPSPI master send/received data to/from LPSPI slave in polling . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpspi_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="driver_examples/lptmr" doc="readme.txt">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lptmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_rb_transfer" folder="driver_examples/lpuart/edma_rb_transfer" doc="readme.txt">
      <description>The lpuart_edma Ring Buffer Example project is to demonstrate usage of the KSDK lpuart driver.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_edma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="driver_examples/lpuart/interrupt" doc="readme.txt">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="driver_examples/lpuart/interrupt_rb_transfer" doc="readme.txt">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits" folder="driver_examples/lpuart/interrupt_transfer_seven_bits" doc="readme.txt">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.NOTE: Please set com port format to "7 data bits without parity bit" in PC's com port tool</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_interrupt_transfer_seven_bits.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="driver_examples/lpuart/polling" doc="readme.txt">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits" folder="driver_examples/lpuart/polling_seven_bits" doc="readme.txt">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be printed out onto console instantly.NOTE: Please set com port format to "7 data bits without parity bit" in PC's com port tool</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/lpuart_polling_seven_bits.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_adc12_trigger" folder="driver_examples/pdb/adc12_trigger" doc="readme.txt">
      <description>The pdb_adc12_trigger example shows how to use the PDB to generate a ADC trigger.Based on the basic counter, to use the ADC trigger, just to enable the ADC trigger's "milestone" and set the user-defined value for it.After the PDB counter is triggered to start, when the counter pass the "milestone", the ADC's Pre-Trigger would be generated and sentto the ADC12 module.In this example, the ADC12 is configured with hardware trigger and conversion complete interrupt enabled.Once it gets the trigger from the PDB, the conversion goes, then the ISR would be executed.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_adc12_trigger.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pdb_adc12_trigger.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_dac32_trigger" folder="driver_examples/pdb/dac32_trigger" doc="readme.txt">
      <description>The pdb_dac32_trigger example shows how to use the PDB to generate a DAC trigger.Based on the basic counter, to use the DAC trigger, just to enable the DAC trigger's "milestone" and set the user-defined value for it.The DAC's "milestone" is called as "interval". Multiple DAC trigger intervals can be included into one PDB counter's cycle.DAC trigger's counter would reset after the trigger is created and start counting again to the interval value.In this example, the DAC32 is configured with hardware buffer enabled in normal work mode. Once it gets the trigger from the PDB,the buffer read pointer increases.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_dac32_trigger.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pdb_dac32_trigger.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_delay_interrupt" folder="driver_examples/pdb/delay_interrupt" doc="readme.txt">
      <description>The pdb_delay_interrupt example show how to use the PDB as a general programmable interrupt timer.The PDB is triggered by software, and other external triggers are generated from PDB in this project,so that user can see just a general counter is working with interrupt.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_delay_interrupt.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pdb_delay_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="demo_apps/power_manager" doc="readme.txt">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a specific power mode. The usercan also set the wakeup source by following the debug console prompts. The purpose of this demo is to demonstrate theimplementation of a power mode manager. The callback can be registered to the framework. If a power mode transition happens,the callback will be called and user can do something. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/power_manager.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="demo_apps/power_mode_switch" doc="readme.txt">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user can also set the wakeupsource by following the debug console prompts. The purpose of this demo is to show how to switch between different power modes, and how to configure a wakeup source and wakeup the MCU from low power modes. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - Debug pins(e.g SWD_DIO) would consume addtional power, had better to disable related pins or disconnect them. </description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/power_mode_switch.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pwt_example" folder="driver_examples/pwt" doc="readme.txt">
      <description>The PWT project is a simple demonstration program of the SDK PWT driver. It sets up the PWThardware block to edge detection, capture control part and detects measurement trigger edges andcontrols when and which pulse width register(s) will be updated. Once the input signal is received,this example will print overflow flag status, positive pulse width and negative pulse width.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwt_example.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/pwt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="driver_examples/rtc" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="demo_apps/rtc_func" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc_func.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func_peripheral" folder="demo_apps/rtc_func_peripheral" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func_peripheral.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/rtc_func_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="scg" folder="driver_examples/scg" doc="readme.txt">
      <description>The SCG example shows how to use SCG driver: 1. How to setup the SCG clock source. 2. How to use SCG clock while power mode switch. 3. How to use SCG APIs to get clock frequency.This example prints the clock frequency through the terminal using the SDK driver.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/scg.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/scg.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="demo_apps/shell" doc="readme.txt">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/shell.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sysmpu" folder="driver_examples/sysmpu" doc="readme.txt">
      <description>The SYSMPU example defines protected/unprotected memory region for the core access.First, the SYSMPU will capture the hardware information and show it on theterminal. Then, a memory region is configured as the non-writable region. Thebus fault interrupt is enabled to report the memory protection interrupt eventfor this non-writable region. If an operation writes to this region, the busfault interrupt happens. Then the bus fault interrupt handler provides aprevention alert by outputting a message on terminal, then the write rightswill be given to this region for core access. After the write access enabled,the writing to the region becomes successful. When the bus fault happen, thedetails of the error information will be captured and printed on the terminal.This example provides the terminal input control to give the example show for several regions access test. Just press any key to the terminal when theterminal show "Press any key to continue".</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sysmpu.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/sysmpu.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="trgmux_clone_pwm" folder="driver_examples/trgmux/clone_pwm" doc="readme.txt">
      <description>The TRGMUX project is a simple demonstration program of the SDK TRGMUX driver. It generatesa 10KHz PWM by LPIT0 CH0, and clone by TRGMUX to output this PWM to eight TRGMUX_OUTx pins.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/trgmux_clone_pwm.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/trgmux_clone_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog32" folder="driver_examples/wdog32" doc="readme.txt">
      <description>The WDOG32 Example project is to demonstrate usage of the KSDK wdog32 driver.In this example, fast testing is first implemented to test the wdog32.After this, refreshing the watchdog in None-window mode and window mode is executed.Note wdog32 is disabled in SystemInit function which means wdog32 is disabledafter chip emerges from reset.</description>
      <board name="TWR-KE18F" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog32.uvprojx"/>
        <environment name="iar" folder="iar" load="iar/wdog32.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="twrke18f" Cversion="1.0.0" condition="device.MKE18F16_AND_component.lpuart_adapter_AND_component.serial_manager_AND_device.MKE18F16_startup_AND_driver.common_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_driver.trgmux_AND_utility.debug_console">
      <description>Board_project_template twrke18f</description>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
