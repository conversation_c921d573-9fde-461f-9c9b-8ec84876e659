<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>TWR-KM35Z75M_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for TWR-KM35Z75M</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.2.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.1.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.0.0" date="2020-03-30">NXP CMSIS Packs based on MCUXpresso SDK 2.7.1</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MKM35Z7_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="TWR-KM35Z75M">
      <description>Modular Development Platform for Kinetis MKM35Z7 MCUs</description>
      <image small="boards/twrkm35z75m/twrkm35z75m.png"/>
      <book category="overview" name="https://www.nxp.com/pip/TWR-KM35Z75M" title="Modular Development Platform for Kinetis MKM35Z7 MCUs" public="true"/>
      <mountedDevice Dname="MKM35Z512VLQ7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z256VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z256VLL7R" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z256VLQ7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z256VLQ7R" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z512VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z512VLL7R" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKM35Z512VLQ7R" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MKM35Z512xxx7.internal_condition">
      <accept Dname="MKM35Z512VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7R" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MKM35Z7.internal_condition">
      <accept Dname="MKM35Z512VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z512VLQ7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLL7R" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLQ7" Dvendor="NXP:11"/>
      <accept Dname="MKM35Z256VLQ7R" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.twrkm35z75m.condition_id">
      <require condition="allOf.component.uart_adapter, device_id=MKM35Z512xxx7, device.MKM35Z7_startup, driver.common, driver.gpio, driver.irtc, driver.port, driver.smc, driver.uart, driver.xbar, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite, board=twrkm35z75m.internal_condition"/>
    </condition>
    <condition id="allOf.component.uart_adapter, device_id=MKM35Z512xxx7, device.MKM35Z7_startup, driver.common, driver.gpio, driver.irtc, driver.port, driver.smc, driver.uart, driver.xbar, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite, board=twrkm35z75m.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require condition="device_id.MKM35Z512xxx7.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rtc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xbar"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
      <require condition="board.twrkm35z75m.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="board.twrkm35z75m.internal_condition">
      <accept condition="device.MKM35Z7.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc16_interrupt" folder="boards/twrkm35z75m/driver_examples/adc16/interrupt" doc="readme.md">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc16_interrupt.ewp"/>
        <environment name="csolution" load="adc16_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power" folder="boards/twrkm35z75m/demo_apps/adc16_low_power" doc="readme.md">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power.ewp"/>
        <environment name="csolution" load="adc16_low_power.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_async_dma" folder="boards/twrkm35z75m/demo_apps/adc16_low_power_async_dma" doc="readme.md">
      <description>The ADC Low Power Async DMA demo application demonstrates the usage of the ADC and DMA peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 100 ms, low power...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_async_dma.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power_async_dma.ewp"/>
        <environment name="csolution" load="adc16_low_power_async_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_peripheral" folder="boards/twrkm35z75m/demo_apps/adc16_low_power_peripheral" doc="readme.md">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_peripheral.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power_peripheral.ewp"/>
        <environment name="csolution" load="adc16_low_power_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="boards/twrkm35z75m/driver_examples/adc16/polling" doc="readme.md">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" load="iar/adc16_polling.ewp"/>
        <environment name="csolution" load="adc16_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_potentiometer" folder="boards/twrkm35z75m/demo_apps/adc16_potentiometer" doc="readme.md">
      <description>The ADC potentiometer demo application demonstrates how to read the analog value from a hardware potentiometer via theADC peripheral.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_potentiometer.uvprojx"/>
        <environment name="iar" load="iar/adc16_potentiometer.ewp"/>
        <environment name="csolution" load="adc16_potentiometer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="afe_interrupt" folder="boards/twrkm35z75m/driver_examples/afe/interrupt" doc="readme.md">
      <description>The AFE Example project is a demonstration program that uses the KSDK software.In this example, the AFE module samples the voltage difference of the EXT_SD_ADP0 and EXT_SD_ADM0 pins.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/afe_interrupt.uvprojx"/>
        <environment name="iar" load="iar/afe_interrupt.ewp"/>
        <environment name="csolution" load="afe_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="afe_polling" folder="boards/twrkm35z75m/driver_examples/afe/polling" doc="readme.md">
      <description>The AFE Example project is a demonstration program that uses the KSDK software.In this example, the AFE module samples the voltage difference of the EXT_SD_ADP0 and EXT_SD_ADM0 pins.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/afe_polling.uvprojx"/>
        <environment name="iar" load="iar/afe_polling.ewp"/>
        <environment name="csolution" load="afe_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="afe_qtimer" folder="boards/twrkm35z75m/demo_apps/afe_qtimer" doc="readme.md">
      <description>The AFE Example project is a demonstration program that uses the KSDK software.In this case the example provide two functions. Measure the voltage difference two pin of thechannel 2 and 3. And monitor AC frequency on two pins of channel 2.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/afe_qtimer.uvprojx"/>
        <environment name="csolution" load="afe_qtimer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="boards/twrkm35z75m/driver_examples/cmp/interrupt" doc="readme.md">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input....See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
        <environment name="csolution" load="cmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="boards/twrkm35z75m/driver_examples/cmp/polling" doc="readme.md">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
        <environment name="csolution" load="cmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_master" folder="boards/twrkm35z75m/cmsis_driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_dma_b2b_transfer_slave" folder="boards/twrkm35z75m/cmsis_driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="boards/twrkm35z75m/cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="boards/twrkm35z75m/cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_read_accel_value_transfer" folder="boards/twrkm35z75m/cmsis_driver_examples/i2c/read_accel_value_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_i2c_read_accel_value_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_master" folder="boards/twrkm35z75m/cmsis_driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_dma_b2b_transfer_slave" folder="boards/twrkm35z75m/cmsis_driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_master" folder="boards/twrkm35z75m/cmsis_driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use spi CMSIS driver in interrupt way:In this example , we need two boards, one board used as spi master and another board used as spi slave.The file...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_spi_interrupt_b2b_transfer_slave" folder="boards/twrkm35z75m/cmsis_driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_dma_transfer" folder="boards/twrkm35z75m/cmsis_driver_examples/uart/dma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_dma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_uart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="boards/twrkm35z75m/cmsis_driver_examples/uart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/twrkm35z75m/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_link" folder="boards/twrkm35z75m/driver_examples/dma/channel_link" doc="readme.md">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_link.uvprojx"/>
        <environment name="iar" load="iar/dma_channel_link.ewp"/>
        <environment name="csolution" load="dma_channel_link.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/twrkm35z75m/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_ring_buffer" folder="boards/twrkm35z75m/driver_examples/dma/ring_buffer" doc="readme.md">
      <description>The DMA ring buffer example is a simple demonstration program that uses the SDK software.It demostrates how to implement ring buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_ring_buffer.uvprojx"/>
        <environment name="iar" load="iar/dma_ring_buffer.ewp"/>
        <environment name="csolution" load="dma_ring_buffer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="boards/twrkm35z75m/driver_examples/dma/wrap_transfer" doc="readme.md">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_wrap_transfer.ewp"/>
        <environment name="csolution" load="dma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm" folder="boards/twrkm35z75m/driver_examples/ewm" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm.uvprojx"/>
        <environment name="iar" load="iar/ewm.ewp"/>
        <environment name="csolution" load="ewm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="boards/twrkm35z75m/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
        <environment name="csolution" load="gpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/twrkm35z75m/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/twrkm35z75m/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_master" folder="boards/twrkm35z75m/driver_examples/i2c/dma_b2b_transfer/master" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave....See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_dma_b2b_transfer_slave" folder="boards/twrkm35z75m/driver_examples/i2c/dma_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="boards/twrkm35z75m/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="boards/twrkm35z75m/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="boards/twrkm35z75m/driver_examples/i2c/polling_b2b_transfer/master" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="boards/twrkm35z75m/driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.md">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="i2c_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_read_accel_value_transfer" folder="boards/twrkm35z75m/driver_examples/i2c/read_accel_value_transfer" doc="readme.md">
      <description>The i2c_read_accel_value example shows how to use I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/i2c_read_accel_value_transfer.ewp"/>
        <environment name="csolution" load="i2c_read_accel_value_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="irtc" folder="boards/twrkm35z75m/driver_examples/irtc" doc="readme.md">
      <description>The IRTC project is a simple demonstration program of the SDK IRTC driver.This example is a low power module that provides time keeping and calendaring functions and additionally providesprotection against tampering,...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/irtc.uvprojx"/>
        <environment name="iar" load="iar/irtc.ewp"/>
        <environment name="csolution" load="irtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/twrkm35z75m/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="log_bm" folder="boards/twrkm35z75m/component_examples/log/bm" doc="readme.md">
      <description>The log demo is used to demonstrate how to use log component. The main function of the demo is to prompt the LOG level string according to the user input log level command.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/log_bm.uvprojx"/>
        <environment name="csolution" load="log_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="boards/twrkm35z75m/driver_examples/lptmr" doc="readme.md">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
        <environment name="csolution" load="lptmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_interrupt_transfer" folder="boards/twrkm35z75m/driver_examples/lpuart/9bit_interrupt_transfer" doc="readme.md">
      <description>The lpuart_9bit_interrupt_transfer example shows how to use lpuart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_dma_rb_transfer" folder="boards/twrkm35z75m/driver_examples/lpuart/dma_rb_transfer" doc="readme.md">
      <description>The lpuart_dma ring buffer example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_dma_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_dma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_dma_transfer" folder="boards/twrkm35z75m/driver_examples/lpuart/dma_transfer" doc="readme.md">
      <description>The lpuart_dma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_dma_transfer.ewp"/>
        <environment name="csolution" load="lpuart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/twrkm35z75m/driver_examples/lpuart/interrupt" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/twrkm35z75m/driver_examples/lpuart/interrupt_rb_transfer" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/twrkm35z75m/driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/twrkm35z75m/driver_examples/lpuart/polling" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fee_blpe" folder="boards/twrkm35z75m/driver_examples/mcg/fee_blpe" doc="readme.md">
      <description>The fee_bple example shows how to use MCG driver to change from FEE mode to BLPE mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fee_blpe.uvprojx"/>
        <environment name="iar" load="iar/mcg_fee_blpe.ewp"/>
        <environment name="csolution" load="mcg_fee_blpe.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fee_blpi" folder="boards/twrkm35z75m/driver_examples/mcg/fee_blpi" doc="readme.md">
      <description>The fee_bpli example shows how to use MCG driver to change from FEE mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fee_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_fee_blpi.ewp"/>
        <environment name="csolution" load="mcg_fee_blpi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fei_blpi" folder="boards/twrkm35z75m/driver_examples/mcg/fei_blpi" doc="readme.md">
      <description>The fei_bpli example shows how to use MCG driver to change from FEI mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fei_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_fei_blpi.ewp"/>
        <environment name="csolution" load="mcg_fei_blpi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_pee_blpe" folder="boards/twrkm35z75m/driver_examples/mcg/pee_blpe" doc="readme.md">
      <description>The pee_bple example shows how to use MCG driver to change from PEE mode to BLPE mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_pee_blpe.uvprojx"/>
        <environment name="iar" load="iar/mcg_pee_blpe.ewp"/>
        <environment name="csolution" load="mcg_pee_blpe.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_pee_blpi" folder="boards/twrkm35z75m/driver_examples/mcg/pee_blpi" doc="readme.md">
      <description>The pee_bpli example shows how to use MCG driver to change from PEE mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_pee_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_pee_blpi.ewp"/>
        <environment name="csolution" load="mcg_pee_blpi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_pei_blpi" folder="boards/twrkm35z75m/driver_examples/mcg/pei_blpi" doc="readme.md">
      <description>The pei_bpli example shows how to use MCG driver to change from PEI mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_pei_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_pei_blpi.ewp"/>
        <environment name="csolution" load="mcg_pei_blpi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mmau" folder="boards/twrkm35z75m/driver_examples/mmau" doc="readme.md">
      <description>The mmau project is a simple demonstration program that use the MMAU driver:How to use calculate functions to create sine function for the arithmetic computation.In this example, use calculate functions to compute sinusoidal function for various angles.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mmau.uvprojx"/>
        <environment name="iar" load="iar/mmau.ewp"/>
        <environment name="csolution" load="mmau.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/twrkm35z75m/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="iar" load="iar/new_project.ewp"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_adc16_trigger" folder="boards/twrkm35z75m/driver_examples/pdb/adc16_trigger" doc="readme.md">
      <description>The pdb_adc16_trigger example shows how to use the PDB to generate a ADC trigger.Based on the basic counter, to use the ADC trigger, just to enable the ADC trigger's "milestone" and set the user-defined value for...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_adc16_trigger.uvprojx"/>
        <environment name="iar" load="iar/pdb_adc16_trigger.ewp"/>
        <environment name="csolution" load="pdb_adc16_trigger.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_delay_interrupt" folder="boards/twrkm35z75m/driver_examples/pdb/delay_interrupt" doc="readme.md">
      <description>The pdb_delay_interrupt example show how to use the PDB as a general programmable interrupt timer.The PDB is triggered by software, and other external triggers are generated from PDB in this project,so that user can...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_delay_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pdb_delay_interrupt.ewp"/>
        <environment name="csolution" load="pdb_delay_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="boards/twrkm35z75m/driver_examples/flash/pflash" doc="readme.md">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" load="iar/pflash.ewp"/>
        <environment name="csolution" load="pflash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="boards/twrkm35z75m/driver_examples/pit" doc="readme.md">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" load="iar/pit.ewp"/>
        <environment name="csolution" load="pit.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="boards/twrkm35z75m/demo_apps/power_manager" doc="readme.md">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" load="iar/power_manager.ewp"/>
        <environment name="csolution" load="power_manager.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="boards/twrkm35z75m/demo_apps/power_mode_switch" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="csolution" load="power_mode_switch.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr" folder="boards/twrkm35z75m/driver_examples/qtmr" doc="readme.md">
      <description>The QTMR project is a simple demonstration program of the SDK QTMR driver.The quad-timer module provides four timer channels with a variety of controls affecting both individualand multi-channel features. Specific...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr.uvprojx"/>
        <environment name="iar" load="iar/qtmr.ewp"/>
        <environment name="csolution" load="qtmr.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rnga_random" folder="boards/twrkm35z75m/driver_examples/rnga/random" doc="readme.md">
      <description>The RNGA is a digital integrated circuit capable of generating the 32-bit random numbers. The RNGAExample project is a demonstration program that uses the KSDK software to generate random numbersand prints them to the terminal.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rnga_random.uvprojx"/>
        <environment name="iar" load="iar/rnga_random.ewp"/>
        <environment name="csolution" load="rnga_random.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/twrkm35z75m/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="slcd" folder="boards/twrkm35z75m/driver_examples/slcd" doc="readme.md">
      <description>The SLCD example shows how to use SLCD driver.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/slcd.uvprojx"/>
        <environment name="csolution" load="slcd.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="slcd_guessnum" folder="boards/twrkm35z75m/demo_apps/slcd_guessnum" doc="readme.md">
      <description>The SLCD number guess demo application demonstrates the use of the SLCD peripheral and driver.The SLCD number guess demo has two demo displays:1. The first demo is a basic SLCD test, where all numbers and icons are...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/slcd_guessnum.uvprojx"/>
        <environment name="csolution" load="slcd_guessnum.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_master" folder="boards/twrkm35z75m/driver_examples/spi/dma_b2b_transfer/master" doc="readme.md">
      <description>The spi_dma_board2board_master example shows how to use spi driver as master to do board to board transfer with DMA:In this example, one spi instance as master and another spi instance on othereboard as slave. Master...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_dma_b2b_transfer_slave" folder="boards/twrkm35z75m/driver_examples/spi/dma_b2b_transfer/slave" doc="readme.md">
      <description>The spi_dma_board2board_slave example shows how to use spi driver as slave to do board to board transfer with DMA:In this example, one spi instance as slave and another spi instance on other board as master. Master...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_dma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_dma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_master" folder="boards/twrkm35z75m/driver_examples/spi/interrupt_b2b/master" doc="readme.md">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_slave" folder="boards/twrkm35z75m/driver_examples/spi/interrupt_b2b/slave" doc="readme.md">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_master" folder="boards/twrkm35z75m/driver_examples/spi/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_b2b_transfer_slave" folder="boards/twrkm35z75m/driver_examples/spi/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_master" folder="boards/twrkm35z75m/driver_examples/spi/polling_b2b_transfer/master" doc="readme.md">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_b2b_transfer_slave" folder="boards/twrkm35z75m/driver_examples/spi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="spi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sysmpu" folder="boards/twrkm35z75m/driver_examples/sysmpu" doc="readme.md">
      <description>The SYSMPU example defines protected/unprotected memory region for the core access.First, the SYSMPU will capture the hardware information and show it on theterminal. Then, a memory region is configured as the...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sysmpu.uvprojx"/>
        <environment name="iar" load="iar/sysmpu.ewp"/>
        <environment name="csolution" load="sysmpu.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_9bit_interrupt_transfer" folder="boards/twrkm35z75m/driver_examples/uart/9bit_interrupt_transfer" doc="readme.md">
      <description>The uart_9bit_interrupt_transfer example shows how to use uart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it is...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_9bit_interrupt_transfer.ewp"/>
        <environment name="csolution" load="uart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_dma_rb_transfer" folder="boards/twrkm35z75m/driver_examples/uart/dma_rb_transfer" doc="readme.md">
      <description>The uart_dma example shows how to use uart driver with DMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_dma_rb_transfer.ewp"/>
        <environment name="csolution" load="uart_dma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_dma_transfer" folder="boards/twrkm35z75m/driver_examples/uart/dma_transfer" doc="readme.md">
      <description>The uart_dma example shows how to use uart driver with DMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_dma_transfer.ewp"/>
        <environment name="csolution" load="uart_dma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="boards/twrkm35z75m/driver_examples/uart/interrupt" doc="readme.md">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt.ewp"/>
        <environment name="csolution" load="uart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="boards/twrkm35z75m/driver_examples/uart/interrupt_rb_transfer" doc="readme.md">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="uart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="boards/twrkm35z75m/driver_examples/uart/interrupt_transfer" doc="readme.md">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="boards/twrkm35z75m/driver_examples/uart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" load="iar/uart_polling.ewp"/>
        <environment name="csolution" load="uart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="vref_example" folder="boards/twrkm35z75m/driver_examples/vref" doc="readme.md">
      <description>In this example, the adc16 module is initiealized and used to measure the VREF output voltage. So, it cannot use interal VREF as the reference voltage. Then, user should configure the VREF output pin as the ADC16's...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/vref_example.uvprojx"/>
        <environment name="iar" load="iar/vref_example.ewp"/>
        <environment name="csolution" load="vref_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog" folder="boards/twrkm35z75m/driver_examples/wdog" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 10 times of refreshing the watchdog in None-window mode, a...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog.uvprojx"/>
        <environment name="iar" load="iar/wdog.ewp"/>
        <environment name="csolution" load="wdog.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar" folder="boards/twrkm35z75m/driver_examples/xbar" doc="readme.md">
      <description>The Xbar project is a simple demonstration program of the SDK Xbar driver.The intended applicationof this module is to provide a flexible crossbar switch function that allows any input to beconnected to any output...See more details in readme document.</description>
      <board name="TWR-KM35Z75M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar.uvprojx"/>
        <environment name="iar" load="iar/xbar.ewp"/>
        <environment name="csolution" load="xbar.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="twrkm35z75m" Cversion="1.0.0" condition="BOARD_Project_Template.twrkm35z75m.condition_id">
      <description>Board_project_template twrkm35z75m</description>
      <files>
        <file category="header" attr="config" name="boards/twrkm35z75m/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/twrkm35z75m/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/twrkm35z75m/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/twrkm35z75m/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/twrkm35z75m/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/twrkm35z75m/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/twrkm35z75m/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/twrkm35z75m/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/twrkm35z75m/project_template/"/>
      </files>
    </component>
  </components>
</package>
