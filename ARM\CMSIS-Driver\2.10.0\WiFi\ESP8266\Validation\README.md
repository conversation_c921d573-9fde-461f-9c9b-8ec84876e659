# Validation of the Espressif ESP8266EX WiFi CMSIS-Driver

## The [Test Report](./TestReport.txt) file was produced with the following configuration:
 - Hardware:  **NXP MIMXRT1064-EVK** board with **ESP8266EX** on **SparkFun ESP8266 WiFi Shield**
 - Firmware:  **AT Command Set 1.6.2.0**
 - Interface: **UART at 921600 bps**

## Packs used for validation:
 - **ARM::CMSIS-Driver v2.6.0**
 - ARM::CMSIS-Driver_Validation v2.0.0
 - ARM::CMSIS v5.7.0
 - Keil::ARM_Compiler v1.6.2
 - NXP MIMXRT1064_DFP v12.1.0
 - NXP EVK-MIMXRT1064_BSP v12.1.0

### AT Version Information (AT+GMR):
 - AT version:1.6.2.0(Apr 13 2018 11:10:59)
 - SDK version:2.2.1(6ab97e9)
 - compile time:Jun  7 2018 19:34:26
 - Bin version(Wroom 02):1.6.2
