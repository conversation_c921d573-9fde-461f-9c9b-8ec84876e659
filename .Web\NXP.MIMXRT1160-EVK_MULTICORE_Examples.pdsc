<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1160-EVK_MULTICORE_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware multicore Examples Pack for MIMXRT1160-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1160-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MULTICORE" vendor="NXP" version="12.0.0"/>
      <package name="MIMXRT1166_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="freertos_message_buffers_cm4" folder="boards/evkmimxrt1160/multicore_examples/freertos_message_buffers/cm4" doc="readme.md">
      <description>The FreeRTOS Message Buffers multicore project is a simple demonstration program that uses the MCUXpresso SDK software and the Message Buffers component of FreeRTOS. It shows how to implement lightweight core to core...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freertos_message_buffers_cm4.uvprojx"/>
        <environment name="csolution" load="../freertos_message_buffers.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_cm4" folder="boards/evkmimxrt1160/multicore_examples/hello_world/cm4" doc="readme.md">
      <description>The Multicore Hello World demo application demonstrates how to set up projects for individualcores on a multicore system. In this demo, the primary core prints the "Hello World from the Primary Core!"string to the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_cm4.uvprojx"/>
        <environment name="csolution" load="../hello_world_multicore.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_rtos_cm4" folder="boards/evkmimxrt1160/multicore_examples/rpmsg_lite_pingpong_rtos/cm4" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong RTOS project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_rtos_cm4.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong_rtos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_cm4" folder="boards/evkmimxrt1160/multicore_examples/rpmsg_lite_pingpong/cm4" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_cm4.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="freertos_message_buffers_cm7" folder="boards/evkmimxrt1160/multicore_examples/freertos_message_buffers/cm7" doc="readme.md">
      <description>The FreeRTOS Message Buffers multicore project is a simple demonstration program that uses the MCUXpresso SDK software and the Message Buffers component of FreeRTOS. It shows how to implement lightweight core to core...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/freertos_message_buffers_cm7.uvprojx"/>
        <environment name="csolution" load="../freertos_message_buffers.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_cm7" folder="boards/evkmimxrt1160/multicore_examples/hello_world/cm7" doc="readme.md">
      <description>The Multicore Hello World demo application demonstrates how to set up projects for individualcores on a multicore system. In this demo, the primary core prints the "Hello World from the Primary Core!"string to the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_cm7.uvprojx"/>
        <environment name="csolution" load="../hello_world_multicore.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_cm7" folder="boards/evkmimxrt1160/multicore_examples/rpmsg_lite_pingpong/cm7" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of the...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_cm7.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rpmsg_lite_pingpong_rtos_cm7" folder="boards/evkmimxrt1160/multicore_examples/rpmsg_lite_pingpong_rtos/cm7" doc="readme.md">
      <description>The Multicore RPMsg-Lite pingpong RTOS project is a simple demonstration program that uses theMCUXpresso SDK software and the RPMsg-Lite library and shows how to implement the inter-corecommunicaton between cores of...See more details in readme document.</description>
      <board name="MIMXRT1160-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rpmsg_lite_pingpong_rtos_cm7.uvprojx"/>
        <environment name="csolution" load="../rpmsg_lite_pingpong_rtos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
