/* -----------------------------------------------------------------------------
 * Copyright (c) 2019 Arm Limited (or its affiliates). All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 *
 * $Date:        16. September 2019
 * $Revision:    V1.0
 *
 * Project:      WiFi Driver Hardware specific header file for
 *               Inventek ISM43362-M3G-L44 WiFi Module (SPI variant)
 * -------------------------------------------------------------------------- */

#include <stdint.h>

extern void    WiFi_ISM43362_Pin_Initialize   (void);
extern void    WiFi_ISM43362_Pin_Uninitialize (void);
extern void    WiFi_ISM43362_Pin_RSTN         (uint8_t rstn);
extern void    WiFi_ISM43362_Pin_SSN          (uint8_t ssn);
extern uint8_t WiFi_ISM43362_Pin_DATARDY      (void);
