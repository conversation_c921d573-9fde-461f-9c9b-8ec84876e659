<?xml version='1.0' encoding='UTF-8'?>
<package xmlns:xs='http://www.w3.org/2001/XMLSchema-instance' xs:noNamespaceSchemaLocation='PACK.xsd' schemaVersion='1.4'>
  <name>MCXA156_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for MCXA156</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version='25.06.00' date='2025-06-24'>NXP CMSIS Packs based on MCUXpresso SDK 25.06.00</release>
    <release version='25.03.00' date='2025-03-28'>NXP CMSIS Packs based on MCUXpresso SDK 25.03.00</release>
    <release version='19.0.0' date='2024-07-17'>NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name='CMSIS' vendor='ARM' version='6.1.0'/>
    </packages>
    <languages>
      <language name='C' version='99'/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily='MCXA156' Dvendor='NXP:11'>
      <debugconfig default='swd' clock='5000000' swj='true'/>
      <sequences>
        <sequence name='EnableTraceClk'>
          <block>
            __var SYSCON_NS_Base_Addr = 0x40091000;
            __var TRACECLKSEL_Addr    = SYSCON_NS_Base_Addr + 0x190;
            __var TRACECLKDIV_Addr    = SYSCON_NS_Base_Addr + 0x194;
            __var AHBRESETSET1_Addr   = SYSCON_NS_Base_Addr + 0x14;
            __var AHBCLKCTRLSET1_Addr = SYSCON_NS_Base_Addr + 0x54;
            __var clksel              = 0;
            __var clkdiv              = 0;
          </block>
          <control if='__traceout &amp; 0x3'>
            <block>clksel = Read32(TRACECLKSEL_Addr);  // Read current TRACECLKSEL value</block>
            <control if='clksel &gt; 2' info='Trace (TPIU) Clock not selected?'>
              <block>Write32(TRACECLKSEL_Addr, 0x0);   // Select Trace divided clock</block>
            </control>
            <block>
              clkdiv = (Read32(TRACECLKDIV_Addr) &amp; 0xFF);  // Read current TRACECLKDIV value, preserve divider but clear rest to enable
              Write32(TRACECLKDIV_Addr, clkdiv);
              
              Write32(AHBRESETSET1_Addr, (1 &lt;&lt; 7));     // Release Port0 from reset
              Write32(AHBCLKCTRLSET1_Addr, (1 &lt;&lt; 7));   // Enable Port0 clock
            </block>
          </control>
        </sequence>
        <sequence name='EnableDebugMailbox'>
          <block/>
          <control if='!(ReadAP(0x0) &amp; 0x40)'>
            <!-- Check AHB-AP CSW DbgStatus to decide if need enable DebugMailbox -->
            <block>
              __var status=0x55aa;
              Message(0, "MCX connect script start");
              
              // Read APIDR
              __dp = 0;
              __ap = 1;
              status = ReadAP(0xFC);
              Message(0, "APIDR: 0x%08X", status);
              
              // Read DPIDR
              __dp = 0;
              __ap = 1;
              status = ReadDP(0x0);
              Message(0, "DPIDR: 0x%08X", status);
              
              // Active DebugMailbox
              __dp = 0;
              __ap = 1;
              WriteAP(0x0, 0x00000021);
              DAP_Delay(30000);
              ReadAP(0x0);
              
              // Enter Debug Session
              __dp = 0;
              __ap = 1;
              WriteAP(0x4, 0x00000007);
              DAP_Delay(30000);
              ReadAP(0x8);
              
              __dp = 0;
              __ap = 0;
              
              Message(0, "MCX connect script end");
            </block>
          </control>
        </sequence>
        <sequence name='DebugPortStart'>
          <block>
            __var SW_DP_ABORT  = 0x0;
            __var DP_CTRL_STAT = 0x4;
            __var DP_SELECT    = 0x8;
            __var powered_down = 0;
            // Switch to DP Register Bank 0
            WriteDP(DP_SELECT, 0x00000000);
            
            // Read DP CTRL/STAT Register and check if CSYSPWRUPACK and CDBGPWRUPACK bits are set
            powered_down = ((ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000);
          </block>
          <control if='powered_down'>
            <block>
              // Request Debug/System Power-Up
              WriteDP(DP_CTRL_STAT, 0x50000000);
            </block>
            <!-- Wait for Power-Up Request to be acknowledged -->
            <control while='(ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000' timeout='1000000'/>
            <!-- JTAG Specific Part of sequence -->
            <control if='(__protocol &amp; 0xFFFF) == 1'>
              <block>
                // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                // Additionally clear STICKYORUN, STICKYCMP, and STICKYERR bits by writing '1'
                WriteDP(DP_CTRL_STAT, 0x50000F32);
                
                Sequence("EnableDebugMailbox");
              </block>
            </control>
            <!-- SWD Specific Part of sequence -->
            <control if='(__protocol &amp; 0xFFFF) == 2'>
              <block>
                // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                WriteDP(DP_CTRL_STAT, 0x50000F00);
                
                // Clear WDATAERR, STICKYORUN, STICKYCMP, and STICKYERR bits of CTRL/STAT Register by write to ABORT register
                WriteDP(SW_DP_ABORT, 0x0000001E);
                
                Sequence("EnableDebugMailbox");
              </block>
            </control>
            <block>
              __var SYSCON_NS_Base_Addr = 0x40091000;
              __var TRACECLKSEL_Addr    = SYSCON_NS_Base_Addr + 0x190;
              __var TRACECLKDIV_Addr    = SYSCON_NS_Base_Addr + 0x194;
              
              // config trace clock source and divide
              Write32(TRACECLKSEL_Addr, 0x0);
              Write32(TRACECLKDIV_Addr, 0x2);
            </block>
          </control>
        </sequence>
        <sequence name='WaitForStopAfterReset'>
          <block>
            __var SCS_Addr    = 0xE000E000;
            __var DHCSR_Addr  = SCS_Addr + 0xDF0;
            __var DFSR_Addr  = SCS_Addr + 0xD30;
            DAP_Delay(100000);  // Give bootloader time to do what it needs to do
            Sequence("EnableDebugMailbox");
            Write32(DHCSR_Addr, 0xA05F0003);  // Halt the core in case it didn't stop at a breakpiont.
            // Clear watch point
            Write32(0xE0001020, 0x0);
            Write32(0xE0001028, 0x0);
            Write32(0xE0001030, 0x0);
            Write32(0xE0001038, 0x0);
            
            // Clear XPSR to avoid undefined instruction fault caused by IT/ICI
            Write32(0xE000EDF8, 0x01000000);
            Write32(0xE000EDF4, 0x00010010);
            // Set MSPLIM to 0
            Write32(0xE000EDF8, 0x00000000);
            Write32(0xE000EDF4, 0x0001001C);
          </block>
          <!-- Reset Recovery: Wait for DHCSR.S_RESET_ST bit to clear on read -->
          <control while='(Read32(DHCSR_Addr) &amp; 0x02000000)' timeout='500000'/>
          <control if='(Read32(DHCSR_Addr) &amp; 0x00020000) == 0'>
            <block>
              Write32(DHCSR_Addr, 0xA05F0003);  // Force halt until finding a proper catch. Probably works better from flash.
            </block>
          </control>
        </sequence>
        <sequence name='ResetHardware'>
          <block>
            __var nReset      = 0x80;
            __var canReadPins = 0;
            __var SCS_Addr    = 0xE000E000;
            __var DHCSR_Addr  = SCS_Addr + 0xDF0;
            __var DEMCR_Addr  = SCS_Addr + 0xDFC;
            __var tmp;
            
            //Halt the core
            Write32(DHCSR_Addr, 0xA05F0003);
            //clear VECTOR CATCH and set TRCENA
            tmp = Read32(DEMCR_Addr);
            Write32(DEMCR_Addr, tmp | 0x1000000);
            __errorcontrol = 1;
            
            //// // Set watch point at SYSCON_BASE + 0x40 access
            //// Write32(0xE0001020, 0x4009123C);
            //// Write32(0xE0001028, 0x00000814);
            
            // Set watch point
            Write32(0xE0001020, 0x00000000);
            Write32(0xE0001028, 0xF0000412);
            Write32(0xE0001030, 0x000FFFFF);
            Write32(0xE0001038, 0xF0000403);
            
            // De-assert nRESET line
            canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
          </block>
          <!-- Keep reset active for 50 ms -->
          <control while='1' timeout='50000'/>
          <control if='canReadPins'>
            <!-- Assert nRESET line and wait max. 1s for recovery -->
            <control while='(DAP_SWJ_Pins(nReset, nReset, 0) &amp; nReset) == 0' timeout='1000000'/>
          </control>
          <control if='!canReadPins'>
            <block>
              // Assert nRESET line
              DAP_SWJ_Pins(nReset, nReset, 0);
            </block>
            <!-- Wait 100ms for recovery if nRESET not readable -->
            <control while='1' timeout='100000'/>
          </control>
          <control if='(__connection &amp; (1 &lt;&lt; 17)) == 0'>
            <block>
              Sequence("WaitForStopAfterReset");
              __errorcontrol = 0;
            </block>
          </control>
        </sequence>
        <sequence name='ResetSystem'>
          <block>
            __dp = 0;
            __ap = 0;
            // System Control Space (SCS) offset as defined in Armv6-M/Armv7-M.
            __var SCS_Addr   = 0xE000E000;
            __var AIRCR_Addr = SCS_Addr + 0xD0C;
            __var DHCSR_Addr = SCS_Addr + 0xDF0;
            __var DEMCR_Addr = SCS_Addr + 0xDFC;
            __var tmp;
            //Halt the core
            Write32(DHCSR_Addr, 0xA05F0003);
            //clear VECTOR CATCH and set TRCENA
            tmp = Read32(DEMCR_Addr);
            Write32(DEMCR_Addr, tmp | 0x1000000);
            
            //// // Set watch point at SYSCON_BASE + 0x40 access
            //// Write32(0xE0001020, 0x4009123C);
            //// Write32(0xE0001028, 0x00000814);
            
            // Set watch point
            Write32(0xE0001020, 0x00000000);
            Write32(0xE0001028, 0xF0000412);
            Write32(0xE0001030, 0x000FFFFF);
            Write32(0xE0001038, 0xF0000403);
            
            __errorcontrol = 1;
            // Execute SYSRESETREQ via AIRCR
            Write32(AIRCR_Addr, 0x05FA0004);
            Sequence("WaitForStopAfterReset");
            __errorcontrol = 0;
          </block>
        </sequence>
        <sequence name='ResetProcessor' disable='1'/>
        <sequence name='TraceStart'>
          <control if='__traceout &amp; 0x1' info='SWO Trace output selected'>
            <block>Sequence("EnableTraceClk");       // Enable and configure trace clock</block>
            <control if='SWO_Pin == 0' info='SWO via PIO0_2'>
              <block>Write32(0x400BC088, 0x00001140); // PIO0_2 PORT0_PCR2 mux to SWO</block>
            </control>
          </control>
        </sequence>
      </sequences>
      <debugvars configfile='devices/MCXA156/arm/MCXA15X.dbgconf'>
        // Debug Access Variables, can be modified by user via copies of DBGCONF files as created by uVision. Also see sub-family level.
        __var SWO_Pin               = 0;                    // Serial Wire Output pin: 0 = P0_2
        __var Dbg_CR                = 0x00000000;           // DBG_CR
        __var BootTime              = 300000;               // 300 milliseconds
      </debugvars>
      <description>
        Arm® Cortex®-M33 microcontroller with multiple high-speed connectivity, operating up to 96 MHz, serial peripherals, timers, analog and low power consumption.
      </description>
      <device Dname='MCXA156'>
        <processor Dcore='Cortex-M33' Dfpu='SP_FPU' Dmpu='NO_MPU' Dtz='NO_TZ' Ddsp='DSP' Dendian='Little-endian' Dclock='96000000'/>
        <environment name='iar'>
          <file category='linkerfile' name='devices/MCXA156/iar/MCXA156_flash.icf'/>
        </environment>
        <memory name='BootROM' start='0x03000000' size='0x2000' access='rx'/>
        <memory name='PROGRAM_FLASH' start='0x00000000' size='0x100000' access='rx' default='1' startup='1'/>
        <memory name='SRAM' start='0x20000000' size='0x01e000' access='rw' default='1'/>
        <memory name='SRAMX' start='0x04000000' size='0x3000' access='rw' default='1'/>
        <algorithm name='devices/MCXA156/arm/MCXA15X_1024.FLM' start='0x00000000' size='0x00100000' RAMstart='0x20000000' RAMsize='0x00001000' default='1'/>
        <debug svd='devices/MCXA156/MCXA156.xml'/>
        <variant Dvariant='MCXA156VPJ'>
          <compile header='devices/MCXA156/fsl_device_registers.h' define='CPU_MCXA156VPJ'/>
        </variant>
        <variant Dvariant='MCXA156VLL'>
          <compile header='devices/MCXA156/fsl_device_registers.h' define='CPU_MCXA156VLL'/>
        </variant>
        <variant Dvariant='MCXA156VMP'>
          <compile header='devices/MCXA156/fsl_device_registers.h' define='CPU_MCXA156VMP'/>
        </variant>
        <variant Dvariant='MCXA156VLH'>
          <compile header='devices/MCXA156/fsl_device_registers.h' define='CPU_MCXA156VLH'/>
        </variant>
        <variant Dvariant='MCXA156VFT'>
          <compile header='devices/MCXA156/fsl_device_registers.h' define='CPU_MCXA156VFT'/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id='device.MCXA156.internal_condition'>
      <accept Dname='MCXA156VFT' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VLH' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VLL' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VMP' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VPJ' Dvendor='NXP:11'/>
    </condition>
    <condition id='cmake_toolchain.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.device=MCXA156.internal_condition'>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux_pca954x, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.button.condition_id'>
      <require condition='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, device=MCXA156.internal_condition'>
      <require condition='anyOf.allOf=component.gpio_adapter, driver.gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.gpio_adapter, driver.gpio.internal_condition'>
      <accept condition='allOf.component.gpio_adapter, driver.gpio.internal_condition'/>
    </condition>
    <condition id='allOf.component.gpio_adapter, driver.gpio.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
    </condition>
    <condition id='component.cmsis_drivers.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.ctimer_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ctimer, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.ctimer_time_stamp_adapter.condition_id'>
      <require condition='allOf.driver.ctimer, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9544.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.enable_pca9548.condition_id'>
      <require condition='allOf.component.i2c_mux_pca954x, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.flash_adapter.condition_id'>
      <require condition='allOf.anyOf=allOf=component.mcxw_flash_adapter, driver.romapi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.mcxw_flash_adapter, driver.romapi, device=MCXA156.internal_condition'>
      <require condition='anyOf.allOf=component.mcxw_flash_adapter, driver.romapi.internal_condition'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.mcxw_flash_adapter, driver.romapi.internal_condition'>
      <accept condition='allOf.component.mcxw_flash_adapter, driver.romapi.internal_condition'/>
    </condition>
    <condition id='allOf.component.mcxw_flash_adapter, driver.romapi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mcxw_flash_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
    </condition>
    <condition id='component.flash_nor_lpspi.condition_id'>
      <require condition='allOf.driver.lpspi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpspi, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.gpio_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.port, driver.gpio, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.port, driver.gpio, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.port.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.port.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='port'/>
    </condition>
    <condition id='component.i2c_adapter_interface.condition_id'>
      <require condition='allOf.anyOf=allOf=anyOf=driver.lpi2c, component.lpi2c_adapter, allOf=component.i3c_adapter, driver.i3c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=anyOf=driver.lpi2c, component.lpi2c_adapter, allOf=component.i3c_adapter, driver.i3c, device=MCXA156.internal_condition'>
      <require condition='anyOf.allOf=anyOf=driver.lpi2c, component.lpi2c_adapter, allOf=component.i3c_adapter, driver.i3c.internal_condition'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=anyOf=driver.lpi2c, component.lpi2c_adapter, allOf=component.i3c_adapter, driver.i3c.internal_condition'>
      <accept condition='allOf.anyOf=driver.lpi2c, component.lpi2c_adapter.internal_condition'/>
      <accept condition='allOf.component.i3c_adapter, driver.i3c.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, component.lpi2c_adapter.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpi2c_adapter'/>
    </condition>
    <condition id='anyOf.driver.lpi2c.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
    </condition>
    <condition id='allOf.component.i3c_adapter, driver.i3c.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
    </condition>
    <condition id='component.i2c_mux.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.i2c_mux_pca954x.condition_id'>
      <require condition='allOf.component.i2c_mux, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_mux, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.i3c_adapter.condition_id'>
      <require condition='allOf.component.i2c_adapter_interface, driver.i3c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.i2c_adapter_interface, driver.i3c, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.i3c_bus.condition_id'>
      <require condition='allOf.component.lists, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.i3c_bus_adapter.condition_id'>
      <require condition='allOf.component.i3c_bus, driver.i3c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.i3c_bus, driver.i3c, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_bus'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.led.condition_id'>
      <require condition='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.gpio_adapter, driver.gpio, component.timer_manager, driver.common, device=MCXA156.internal_condition'>
      <require condition='anyOf.allOf=component.gpio_adapter, driver.gpio.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.lists.condition_id'>
      <require condition='allOf.driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.lpi2c_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, component.i2c_adapter_interface, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, component.i2c_adapter_interface, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.lpspi_adapter.condition_id'>
      <require condition='allOf.driver.common, driver.lpspi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.lpspi, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.lptmr_adapter.condition_id'>
      <require condition='allOf.driver.lptmr, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lptmr, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.lptmr_time_stamp_adapter.condition_id'>
      <require condition='allOf.driver.lptmr, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.lpuart_adapter.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.lpuart.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
    </condition>
    <condition id='component.mcxw_flash_adapter.condition_id'>
      <require condition='allOf.driver.romapi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.romapi, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.mem_manager.condition_id'>
      <require condition='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.mem_manager_legacy, component.mem_manager_light, component.lists, device=MCXA156.internal_condition'>
      <require condition='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.component.mem_manager_legacy, component.mem_manager_light.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy'/>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light'/>
    </condition>
    <condition id='component.mem_manager_legacy.condition_id'>
      <require condition='allOf.component.mem_manager, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.mem_manager, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.mem_manager_light.condition_id'>
      <require condition='allOf.component.mem_manager, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.messaging.condition_id'>
      <require condition='allOf.component.lists, component.mem_manager, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, component.mem_manager, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.mflash_offchip.condition_id'>
      <require condition='allOf.anyOf=driver.romapi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.romapi, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.romapi.internal_condition'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.driver.romapi.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Drivers' Csub='romapi'/>
    </condition>
    <condition id='component.mflash_onchip.condition_id'>
      <require condition='allOf.anyOf=driver.romapi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.mflash_onchip_fmu.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.ostimer_adapter.condition_id'>
      <require condition='allOf.driver.ostimer, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.ostimer, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.ostimer_time_stamp_adapter.condition_id'>
      <require condition='allOf.driver.ostimer, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.panic.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.reset_adapter.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.serial_manager.condition_id'>
      <require condition='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual, component.lists, driver.common, device=MCXA156.internal_condition'>
      <require condition='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.component.serial_manager_spi, component.serial_manager_swo, component.serial_manager_uart, component.serial_manager_virtual.internal_condition'>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <accept Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual'/>
    </condition>
    <condition id='component.serial_manager_spi.condition_id'>
      <require condition='allOf.anyOf=allOf=component.lpspi_adapter, driver.lpspi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.lpspi_adapter, driver.lpspi, device=MCXA156.internal_condition'>
      <require condition='anyOf.allOf=component.lpspi_adapter, driver.lpspi.internal_condition'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.lpspi_adapter, driver.lpspi.internal_condition'>
      <accept condition='allOf.component.lpspi_adapter, driver.lpspi.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpspi_adapter, driver.lpspi.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
    </condition>
    <condition id='component.serial_manager_swo.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_uart.condition_id'>
      <require condition='allOf.component.lpuart_adapter, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpuart_adapter, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='component.serial_manager_virtual.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='component.timer_manager.condition_id'>
      <require condition='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.lptmr_adapter, driver.lptmr, allOf=component.ostimer_adapter, driver.ostimer, component.lists, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=allOf=component.ctimer_adapter, driver.ctimer, allOf=component.lptmr_adapter, driver.lptmr, allOf=component.ostimer_adapter, driver.ostimer, component.lists, driver.common, device=MCXA156.internal_condition'>
      <require condition='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.lptmr_adapter, driver.lptmr, allOf=component.ostimer_adapter, driver.ostimer.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.ctimer_adapter, driver.ctimer, allOf=component.lptmr_adapter, driver.lptmr, allOf=component.ostimer_adapter, driver.ostimer.internal_condition'>
      <accept condition='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'/>
      <accept condition='allOf.component.lptmr_adapter, driver.lptmr.internal_condition'/>
      <accept condition='allOf.component.ostimer_adapter, driver.ostimer.internal_condition'/>
    </condition>
    <condition id='allOf.component.ctimer_adapter, driver.ctimer.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer'/>
    </condition>
    <condition id='allOf.component.lptmr_adapter, driver.lptmr.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr'/>
    </condition>
    <condition id='allOf.component.ostimer_adapter, driver.ostimer.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer_adapter'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer'/>
    </condition>
    <condition id='device.CMSIS.condition_id'>
      <require condition='allOf.CMSIS_Include_core_cm, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Include_core_cm, device=MCXA156.internal_condition'>
      <require Cclass='CMSIS' Cgroup='CORE'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='device_id.MCXA156.internal_condition'>
      <accept Dname='MCXA156VFT' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VLH' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VLL' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VMP' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VPJ' Dvendor='NXP:11'/>
    </condition>
    <condition id='device.RTE.condition_id'>
      <require condition='allOf.device_id=MCXA156, driver.edma4.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MCXA156, driver.edma4.internal_condition'>
      <require condition='device_id.MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='edma'/>
    </condition>
    <condition id='device.linker.condition_id'>
      <require condition='allOf.device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.device_id=MCXA156.internal_condition'>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='device_ids.MCXA156.internal_condition'>
      <accept Dname='MCXA156VFT' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VLH' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VLL' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VMP' Dvendor='NXP:11'/>
      <accept Dname='MCXA156VPJ' Dvendor='NXP:11'/>
    </condition>
    <condition id='allOf.toolchains=mdk, device_ids=MCXA156.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
      <require condition='device_ids.MCXA156.internal_condition'/>
    </condition>
    <condition id='toolchains.mdk.internal_condition'>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, device_ids=MCXA156.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
      <require condition='device_ids.MCXA156.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc.internal_condition'>
      <accept Tcompiler='GCC'/>
    </condition>
    <condition id='allOf.toolchains=iar, device_ids=MCXA156.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
      <require condition='device_ids.MCXA156.internal_condition'/>
    </condition>
    <condition id='toolchains.iar.internal_condition'>
      <accept Tcompiler='IAR'/>
    </condition>
    <condition id='device.project_template.condition_id'>
      <require condition='allOf.component.lpuart_adapter, device_id=MCXA156, device.startup, driver.clock, driver.common, driver.gpio, driver.lpuart, driver.port, driver.mcx_spc, driver.reset, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.lpuart_adapter, device_id=MCXA156, device.startup, driver.clock, driver.common, driver.gpio, driver.lpuart, driver.port, driver.mcx_spc, driver.reset, anyOf=allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter'/>
      <require condition='device_id.MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='Startup' Csub=''/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='clock'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='port'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mcx_spc'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='reset'/>
      <require condition='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='anyOf.allOf=component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <accept condition='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'/>
      <accept condition='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, component.serial_manager_uart, utility.assert, utility.debug_console, utility.debug_console_template_config.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config'/>
    </condition>
    <condition id='allOf.utility.assert_lite, utility.debug_console_lite.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
    </condition>
    <condition id='device.startup.condition_id'>
      <require condition='allOf.device.system, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.device.system, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXA156_system'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=iar.condition_id'>
      <require condition='toolchains.iar.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc.condition_id'>
      <require condition='toolchains.armgcc.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=mdk.condition_id'>
      <require condition='toolchains.mdk.internal_condition'/>
    </condition>
    <condition id='device.system.condition_id'>
      <require condition='allOf.device.CMSIS, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.device.CMSIS, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='CMSIS' Csub='MCXA156_header'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.aoi.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, device_id=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.cache_lpcac_n4a_mcxn.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.video-common, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ap1302.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.video-i2c, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-common, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-max9286.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-mt9m114.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov5640.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7670.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-ov7725.condition_id'>
      <require condition='allOf.driver.camera-device-common, driver.camera-device-sccb, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-device-sccb.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.camera-receiver-common.condition_id'>
      <require condition='allOf.driver.camera-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.cdog.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.clock.condition_id'>
      <require condition='allOf.driver.mcx_spc, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.mcx_spc, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='mcx_spc'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_gpio.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.GPIO, device.RTE, driver.gpio, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.GPIO, device.RTE, driver.gpio, device_id=MCXA156.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='GPIO' Capiversion='1.0.0'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='gpio'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpi2c.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, anyOf=allOf=driver.lpi2c, device_id=MCXA156, device.RTE, device_id=MCXA156, driver.lpi2c_edma.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, anyOf=allOf=driver.lpi2c, device_id=MCXA156, device.RTE, device_id=MCXA156, driver.lpi2c_edma.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpi2c, device_id=MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpi2c, device_id=MCXA156.internal_condition'>
      <accept condition='allOf.driver.lpi2c, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpi2c, device_id=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i2c'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpspi.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.SPI, anyOf=allOf=driver.lpspi, device_id=MCXA156, device.RTE, device_id=MCXA156, driver.lpspi_edma.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.SPI, anyOf=allOf=driver.lpspi, device_id=MCXA156, device.RTE, device_id=MCXA156, driver.lpspi_edma.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='SPI' Capiversion='2.3.0'/>
      <require condition='anyOf.allOf=driver.lpspi, device_id=MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_edma'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpspi, device_id=MCXA156.internal_condition'>
      <accept condition='allOf.driver.lpspi, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpspi, device_id=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.cmsis_lpuart.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MCXA156, device.RTE, device_id=MCXA156, driver.lpuart_edma.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.USART, anyOf=allOf=driver.lpuart, device_id=MCXA156, device.RTE, device_id=MCXA156, driver.lpuart_edma.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='USART' Capiversion='2.4.0'/>
      <require condition='anyOf.allOf=driver.lpuart, device_id=MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device'/>
      <require condition='device_id.MCXA156.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_edma'/>
    </condition>
    <condition id='anyOf.allOf=driver.lpuart, device_id=MCXA156.internal_condition'>
      <accept condition='allOf.driver.lpuart, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.lpuart, device_id=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.common.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.cores=cm33.condition_id'>
      <require condition='cores.cm33.internal_condition'/>
    </condition>
    <condition id='cores.cm33.internal_condition'>
      <accept Dcore='Cortex-M33'/>
    </condition>
    <condition id='driver.crc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ctimer.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.dac_1.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.dbi.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.dbi_flexio_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.edma4, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='edma'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-dbi.condition_id'>
      <require condition='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dbi, driver.dc-fb-common, driver.display-common, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.dc-fb-ssd1963.condition_id'>
      <require condition='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.dc-fb-common, driver.display-common, driver.ssd1963, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.display-adv7535.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.display-common, driver.video-i2c, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='display-common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.display-common.condition_id'>
      <require condition='allOf.driver.video-common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.display-it6161.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.display-it6263.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.display-sn65dsi83.condition_id'>
      <require condition='allOf.driver.display-common, driver.video-i2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.edma4.condition_id'>
      <require condition='allOf.driver.edma_soc, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.edma_soc, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='edma_soc'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.edma_soc.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.eim.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ektf2k.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.eqdc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.erm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexcan.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexcan_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_i2c_master.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.flexio, device_id=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='flexio'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_mculcd.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_mculcd_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_spi_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart.condition_id'>
      <require condition='allOf.driver.flexio, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.flexio_uart_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ft3267.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ft5406.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ft5406_rt.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpi2c, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.lpi2c.internal_condition'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ft6x06.condition_id'>
      <require condition='allOf.CMSIS_Driver_Include.I2C, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.CMSIS_Driver_Include.I2C, device=MCXA156.internal_condition'>
      <require Cclass='CMSIS Driver' Cgroup='I2C' Capiversion='2.4.0'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.fxas21002cq.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.fxls8974cf.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.fxos8700cq.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.glikey.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.gpio.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.gt911.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.htu21d.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.i3c.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.i3c_edma.condition_id'>
      <require condition='allOf.driver.edma4, driver.i3c, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.edma4, driver.i3c, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='edma'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='i3c'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.icm42688p.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ili9341.condition_id'>
      <require condition='allOf.driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.inputmux.condition_id'>
      <require condition='allOf.driver.common, driver.inputmux_connections, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, driver.inputmux_connections, device_id=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections'/>
      <require condition='device_id.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.inputmux_connections.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpadc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpc_freqme.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpcmp.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpi2c.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpi2c_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpspi.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpspi_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lptmr.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpuart.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lpuart_edma.condition_id'>
      <require condition='allOf.driver.edma4, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.lsm6dso.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.max30101.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.mcx_cmc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.mcx_spc.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.mcx_vbat.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.mma8451q.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.mma8652fc.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.nmh1000.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.opamp.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ostimer.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.p3t1755.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.port.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.psp27801.condition_id'>
      <require condition='allOf.driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.pwm.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.reset.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.romapi.condition_id'>
      <require condition='allOf.driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.rtt.condition_id'>
      <require condition='allOf.driver.rtt.template, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.rtt.template, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='rtt template'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.rtt.template.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.ssd1963.condition_id'>
      <require condition='allOf.driver.dbi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.dbi, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='dbi'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.st7796s.condition_id'>
      <require condition='allOf.driver.dbi, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.tma525b.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.trdc_1.condition_id'>
      <require condition='allOf.driver.trdc_soc, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.trdc_soc, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='trdc_soc'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.trdc_soc.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.tsl2561.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.utick.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.video-common.condition_id'>
      <require condition='allOf.driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.video-i2c.condition_id'>
      <require condition='allOf.anyOf=driver.lpi2c, driver.common, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.waketimer.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.wuu.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='driver.wwdt.condition_id'>
      <require condition='allOf.driver.common, device_id=MCXA156.internal_condition'/>
    </condition>
    <condition id='utilities.misc_utilities.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk, cores=cm33.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
      <require condition='cores.cm33.internal_condition'/>
    </condition>
    <condition id='toolchains.armgcc, mdk.internal_condition'>
      <accept Tcompiler='GCC'/>
      <accept Tcompiler='ARMCC'/>
    </condition>
    <condition id='utility.assert.condition_id'>
      <require condition='allOf.driver.common, utility.debug_console, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, utility.debug_console, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='utility.assert_lite.condition_id'>
      <require condition='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.driver.common, not=utility.assert, utility.debug_console_lite, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.assert.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='not.utility.assert.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='assert'/>
    </condition>
    <condition id='utility.debug_console.condition_id'>
      <require condition='allOf.component.serial_manager, driver.common, utility.str, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.serial_manager, driver.common, utility.str, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='utility.debug_console_lite.condition_id'>
      <require condition='allOf.anyOf=driver.lpuart, driver.common, not=utility.debug_console, utility.str, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.anyOf=driver.lpuart, driver.common, not=utility.debug_console, utility.str, device=MCXA156.internal_condition'>
      <require condition='anyOf.driver.lpuart.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require condition='not.utility.debug_console.internal_condition'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='not.utility.debug_console.internal_condition'>
      <deny Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
    </condition>
    <condition id='utility.debug_console_template_config.condition_id'>
      <require condition='allOf.utility.debug_console, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.utility.debug_console, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='utility.format.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='utility.incbin.condition_id'>
      <require condition='allOf.device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.toolchains=armgcc, mdk.condition_id'>
      <require condition='toolchains.armgcc, mdk.internal_condition'/>
    </condition>
    <condition id='utility.shell.condition_id'>
      <require condition='allOf.component.lists, driver.common, utility.str, device=MCXA156.internal_condition'/>
    </condition>
    <condition id='allOf.component.lists, driver.common, utility.str, device=MCXA156.internal_condition'>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='lists'/>
      <require Cclass='Device' Cgroup='SDK Drivers' Csub='common'/>
      <require Cclass='Device' Cgroup='SDK Utilities' Csub='str'/>
      <require condition='device.MCXA156.internal_condition'/>
    </condition>
    <condition id='utility.str.condition_id'>
      <require condition='allOf.driver.common, device=MCXA156.internal_condition'/>
    </condition>
  </conditions>
  <components>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='cmake_toolchain' Cversion='1.0.0' condition='cmake_toolchain.condition_id'>
      <description>Cmake_toolchain</description>
      <files>
        <file category='other' name='tools/cmake_toolchain_files/armgcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xcc.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/xclang.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/armgcc_force_cpp.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/mcux_config.cmake' projectpath='cmake_toolchain_files'/>
        <file category='other' name='tools/cmake_toolchain_files/riscvllvm.cmake' projectpath='cmake_toolchain_files'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='one_i2c_mux_device_enabled' Cversion='1.0.0' condition='component.at_least_one_i2c_mux_device_enabled.condition_id'>
      <description>Component at_least_one_i2c_mux_device_enabled</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.at_least_one_i2c_mux_device_enabled_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='button' Cversion='1.0.0' condition='component.button.condition_id'>
      <description>Component button</description>
      <files>
        <file category='header' name='components/button/fsl_component_button.h' projectpath='component/button'/>
        <file category='sourceC' name='components/button/fsl_component_button.c' projectpath='component/button'/>
        <file category='include' name='components/button/'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='cmsis_drivers' Cversion='2.0.0' condition='component.cmsis_drivers.condition_id'>
      <description>CMSIS driver</description>
      <files>
        <file category='doc' name='components/cmsis_drivers/component.cmsis_drivers_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_adapter' Cversion='1.0.0' condition='component.ctimer_adapter.condition_id'>
      <description>Component ctimer_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_CTIMER
#define TIMER_PORT_TYPE_CTIMER 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ctimer.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer_time_stamp_adapter' Cversion='1.0.0' condition='component.ctimer_time_stamp_adapter.condition_id'>
      <description>Component ctimer time stamp adapter</description>
      <files>
        <file category='header' name='components/time_stamp/fsl_adapter_time_stamp.h' projectpath='component/time_stamp'/>
        <file category='sourceC' name='components/time_stamp/fsl_adapter_ctimer_time_stamp.c' projectpath='component/time_stamp'/>
        <file category='include' name='components/time_stamp/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9544' Cversion='1.0.0' condition='component.enable_pca9544.condition_id'>
      <description>Component enable_pca9544</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9544
#define MCUX_ENABLE_PCA9544 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9544_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='enable_pca9548' Cversion='1.0.0' condition='component.enable_pca9548.condition_id'>
      <description>Component enable_pca9548</description>
      <RTE_Components_h>
#ifndef MCUX_ENABLE_PCA9548
#define MCUX_ENABLE_PCA9548 
#endif
</RTE_Components_h>
      <files>
        <file category='doc' name='components/i2c/muxes/component.enable_pca9548_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_adapter' Cversion='1.0.0' condition='component.flash_adapter.condition_id'>
      <description>Component flash_adapter</description>
      <files>
        <file category='doc' name='components/internal_flash/component.flash_adapter_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flash_nor_lpspi' Cversion='1.0.0' condition='component.flash_nor_lpspi.condition_id'>
      <description>Component flash_nor_lpspi</description>
      <files>
        <file category='header' name='components/flash/nor/fsl_nor_flash.h' projectpath='nor_flash/nor'/>
        <file category='sourceC' name='components/flash/nor/lpspi/fsl_lpspi_nor_flash.c' projectpath='nor_flash/nor/lpspi'/>
        <file category='header' name='components/flash/nor/lpspi/fsl_lpspi_nor_flash.h' projectpath='nor_flash/nor/lpspi'/>
        <file category='sourceC' name='components/flash/nor/lpspi/fsl_lpspi_mem_adapter.c' projectpath='nor_flash/nor/lpspi'/>
        <file category='header' name='components/flash/nor/lpspi/fsl_lpspi_mem_adapter.h' projectpath='nor_flash/nor/lpspi'/>
        <file category='include' name='components/flash/nor/'/>
        <file category='include' name='components/flash/nor/lpspi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio_adapter' Cversion='1.0.1' condition='component.gpio_adapter.condition_id'>
      <description>Component gpio_adapter</description>
      <files>
        <file category='header' name='components/gpio/fsl_adapter_gpio.h' projectpath='component/gpio'/>
        <file category='sourceC' name='components/gpio/fsl_adapter_gpio.c' projectpath='component/gpio'/>
        <file category='include' name='components/gpio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_adapter_interface' Cversion='1.0.0' condition='component.i2c_adapter_interface.condition_id'>
      <description>Component i2c_adapter_interface</description>
      <files>
        <file category='header' name='components/i2c/fsl_adapter_i2c.h' projectpath='component/i2c'/>
        <file category='include' name='components/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux' Cversion='1.0.0' condition='component.i2c_mux.condition_id'>
      <description>Component i2c_mux</description>
      <files>
        <file category='doc' name='components/i2c/muxes/component.i2c_mux_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_mux_pca954x' Cversion='1.0.0' condition='component.i2c_mux_pca954x.condition_id'>
      <description>Component i2c_mux_pca954x</description>
      <files>
        <file category='header' name='components/i2c/muxes/fsl_pca954x.h' projectpath='component/i2c/muxes'/>
        <file category='sourceC' name='components/i2c/muxes/fsl_pca954x.c' projectpath='component/i2c/muxes'/>
        <file category='include' name='components/i2c/muxes/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_adapter' Cversion='1.0.0' condition='component.i3c_adapter.condition_id'>
      <description>Component i3c_adapter</description>
      <RTE_Components_h>
        #ifndef SDK_I3C_BASED_COMPONENT_USED
        #define SDK_I3C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_i3c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_bus' Cversion='1.0.1' condition='component.i3c_bus.condition_id'>
      <description>Component i3c_bus</description>
      <files>
        <file category='header' name='components/i3c_bus/fsl_component_i3c.h' projectpath='component/i3c_bus'/>
        <file category='sourceC' name='components/i3c_bus/fsl_component_i3c.c' projectpath='component/i3c_bus'/>
        <file category='include' name='components/i3c_bus/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_bus_adapter' Cversion='1.1.0' condition='component.i3c_bus_adapter.condition_id'>
      <description>Component i3c_bus_adapter</description>
      <files>
        <file category='header' name='components/i3c_bus/fsl_component_i3c_adapter.h' projectpath='component/i3c_bus'/>
        <file category='sourceC' name='components/i3c_bus/fsl_component_i3c_adapter.c' projectpath='component/i3c_bus'/>
        <file category='include' name='components/i3c_bus/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='led' Cversion='1.0.0' condition='component.led.condition_id'>
      <description>Component led</description>
      <files>
        <file category='header' name='components/led/fsl_component_led.h' projectpath='component/led'/>
        <file category='sourceC' name='components/led/fsl_component_led.c' projectpath='component/led'/>
        <file category='include' name='components/led/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lists' Cversion='1.0.0' condition='component.lists.condition_id'>
      <description>Component lists</description>
      <files>
        <file category='header' name='components/lists/fsl_component_generic_list.h' projectpath='component/lists'/>
        <file category='sourceC' name='components/lists/fsl_component_generic_list.c' projectpath='component/lists'/>
        <file category='include' name='components/lists/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpi2c_adapter' Cversion='1.0.0' condition='component.lpi2c_adapter.condition_id'>
      <description>Component lpi2c_adapter</description>
      <files>
        <file category='sourceC' name='components/i2c/fsl_adapter_lpi2c.c' projectpath='component/i2c'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_adapter' Cversion='1.0.0' condition='component.lpspi_adapter.condition_id'>
      <description>Component lpspi_adapter</description>
      <files>
        <file category='header' name='components/spi/fsl_adapter_spi.h' projectpath='component/spi'/>
        <file category='sourceC' name='components/spi/fsl_adapter_lpspi.c' projectpath='component/spi'/>
        <file category='include' name='components/spi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr_adapter' Cversion='1.0.0' condition='component.lptmr_adapter.condition_id'>
      <description>Component lptmr_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_LPTMR
#define TIMER_PORT_TYPE_LPTMR 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_lptmr.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr_time_stamp_adapter' Cversion='1.0.0' condition='component.lptmr_time_stamp_adapter.condition_id'>
      <description>Component lptmr time stamp adapter</description>
      <files>
        <file category='header' name='components/time_stamp/fsl_adapter_time_stamp.h' projectpath='component/time_stamp'/>
        <file category='sourceC' name='components/time_stamp/fsl_adapter_lptmr_time_stamp.c' projectpath='component/time_stamp'/>
        <file category='include' name='components/time_stamp/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_adapter' Cversion='1.0.0' condition='component.lpuart_adapter.condition_id'>
      <description>Component lpuart_adapter</description>
      <files>
        <file category='header' name='components/uart/fsl_adapter_uart.h' projectpath='component/uart'/>
        <file category='sourceC' name='components/uart/fsl_adapter_lpuart.c' projectpath='component/uart'/>
        <file category='include' name='components/uart/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mcxw_flash_adapter' Cversion='1.0.0' condition='component.mcxw_flash_adapter.condition_id'>
      <description>Component mcxw_flash_adapter</description>
      <files>
        <file category='header' name='components/internal_flash/fsl_adapter_flash.h' projectpath='component/internal_flash'/>
        <file category='sourceC' name='components/internal_flash/fsl_adapter_mcxw_flash.c' projectpath='component/internal_flash'/>
        <file category='include' name='components/internal_flash/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager' Cversion='1.0.0' condition='component.mem_manager.condition_id'>
      <description>Component mem_manager</description>
      <files>
        <file category='header' name='components/mem_manager/fsl_component_mem_manager.h' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_legacy' Cversion='1.0.0' condition='component.mem_manager_legacy.condition_id'>
      <description>Component mem_manager_legacy</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mem_manager_light' Cversion='1.0.0' condition='component.mem_manager_light.condition_id'>
      <description>Component mem_manager_light</description>
      <files>
        <file category='sourceC' name='components/mem_manager/fsl_component_mem_manager_light.c' projectpath='component/mem_manager'/>
        <file category='include' name='components/mem_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='messaging' Cversion='1.0.0' condition='component.messaging.condition_id'>
      <description>Component messaging</description>
      <files>
        <file category='header' name='components/messaging/fsl_component_messaging.h' projectpath='component/messaging'/>
        <file category='sourceC' name='components/messaging/fsl_component_messaging.c' projectpath='component/messaging'/>
        <file category='include' name='components/messaging/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_offchip' Cversion='1.0.0' condition='component.mflash_offchip.condition_id'>
      <description>mflash offchip</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_offchip_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip' Cversion='1.0.0' condition='component.mflash_onchip.condition_id'>
      <description>mflash onchip</description>
      <RTE_Components_h>
#ifndef MFLASH_FILE_BASEADDR
#define MFLASH_FILE_BASEADDR 122880
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/flash/mflash/mflash_common.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mflash_file.c' projectpath='flash/mflash'/>
        <file category='header' name='components/flash/mflash/mflash_file.h' projectpath='flash/mflash'/>
        <file category='sourceC' name='components/flash/mflash/mcxa/mflash_drv.c' projectpath='flash/mflash/mcxa'/>
        <file category='header' name='components/flash/mflash/mcxa/mflash_drv.h' projectpath='flash/mflash/mcxa'/>
        <file category='include' name='components/flash/mflash/'/>
        <file category='include' name='components/flash/mflash/mcxa/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mflash_onchip_fmu' Cversion='1.0.0' condition='component.mflash_onchip_fmu.condition_id'>
      <description>mflash onchip_fmu</description>
      <files>
        <file category='doc' name='components/flash/mflash/component.mflash_onchip_fmu_dummy.txt'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer_adapter' Cversion='1.0.0' condition='component.ostimer_adapter.condition_id'>
      <description>Component ostimer_adapter</description>
      <RTE_Components_h>
#ifndef TIMER_PORT_TYPE_OSTIMER
#define TIMER_PORT_TYPE_OSTIMER 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/timer/fsl_adapter_timer.h' projectpath='component/timer'/>
        <file category='sourceC' name='components/timer/fsl_adapter_ostimer.c' projectpath='component/timer'/>
        <file category='include' name='components/timer/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer_time_stamp_adapter' Cversion='1.0.0' condition='component.ostimer_time_stamp_adapter.condition_id'>
      <description>Component ostimer time stamp adapter</description>
      <files>
        <file category='header' name='components/time_stamp/fsl_adapter_time_stamp.h' projectpath='component/time_stamp'/>
        <file category='sourceC' name='components/time_stamp/fsl_adapter_ostimer_time_stamp.c' projectpath='component/time_stamp'/>
        <file category='include' name='components/time_stamp/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='panic' Cversion='1.0.0' condition='component.panic.condition_id'>
      <description>Component panic</description>
      <files>
        <file category='header' name='components/panic/fsl_component_panic.h' projectpath='component/panic'/>
        <file category='sourceC' name='components/panic/fsl_component_panic.c' projectpath='component/panic'/>
        <file category='include' name='components/panic/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset_adapter' Cversion='1.0.0' condition='component.reset_adapter.condition_id'>
      <description>Component reset_adapter</description>
      <files>
        <file category='header' name='components/reset/fsl_adapter_reset.h' projectpath='component/reset'/>
        <file category='sourceC' name='components/reset/fsl_adapter_reset.c' projectpath='component/reset'/>
        <file category='include' name='components/reset/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager' Cversion='1.0.1' condition='component.serial_manager.condition_id'>
      <description>Component serial_manager</description>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_manager.h' projectpath='component/serial_manager'/>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_internal.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_manager.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_spi' Cversion='1.0.0' condition='component.serial_manager_spi.condition_id'>
      <description>Component serial_manager_spi</description>
      <RTE_Components_h>
        #ifndef SERIAL_PORT_TYPE_SPI
        #define SERIAL_PORT_TYPE_SPI 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_MASTER
        #define SERIAL_PORT_TYPE_SPI_MASTER 1
        #endif
        #ifndef SERIAL_PORT_TYPE_SPI_SLAVE
        #define SERIAL_PORT_TYPE_SPI_SLAVE 1
        #endif
        #ifndef SERIAL_MANAGER_NON_BLOCKING_MODE
        #define SERIAL_MANAGER_NON_BLOCKING_MODE 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_spi.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_spi.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_swo' Cversion='1.0.0' condition='component.serial_manager_swo.condition_id'>
      <description>Component serial_manager_swo</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_SWO
#define SERIAL_PORT_TYPE_SWO 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_swo.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_swo.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_uart' Cversion='1.0.0' condition='component.serial_manager_uart.condition_id'>
      <description>Component serial_manager_uart</description>
      <RTE_Components_h>
#ifndef SERIAL_PORT_TYPE_UART
#define SERIAL_PORT_TYPE_UART 1
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_uart.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_uart.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='serial_manager_virtual' Cversion='1.0.0' condition='component.serial_manager_virtual.condition_id'>
      <description>Component serial_manager_virtual</description>
      <RTE_Components_h>
        #ifndef SSERIAL_PORT_TYPE_VIRTUAL
        #define SSERIAL_PORT_TYPE_VIRTUAL 1
        #endif
        #ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
        #define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/serial_manager/fsl_component_serial_port_virtual.h' projectpath='component/serial_manager'/>
        <file category='sourceC' name='components/serial_manager/fsl_component_serial_port_virtual.c' projectpath='component/serial_manager'/>
        <file category='include' name='components/serial_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='timer_manager' Cversion='1.0.0' condition='component.timer_manager.condition_id'>
      <description>Component timer_manager</description>
      <files>
        <file category='header' name='components/timer_manager/fsl_component_timer_manager.h' projectpath='component/timer_manager'/>
        <file category='sourceC' name='components/timer_manager/fsl_component_timer_manager.c' projectpath='component/timer_manager'/>
        <file category='include' name='components/timer_manager/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MCXA156_header' Cversion='1.0.0' condition='device.CMSIS.condition_id'>
      <description>Device MCXA156_cmsis</description>
      <files>
        <file category='header' name='devices/MCXA156/fsl_device_registers.h' projectpath='device'/>
        <file category='header' name='devices/MCXA156/MCXA156.h' projectpath='device'/>
        <file category='header' name='devices/MCXA156/MCXA156_features.h' projectpath='device'/>
        <file category='header' name='devices/MCXA156/MCXA156_COMMON.h' projectpath='device'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_ADC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_AOI.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_CAN.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_CDOG.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_CMC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_CRC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_CTIMER.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_DEBUGMAILBOX.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_DMA.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_EIM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_EQDC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_ERM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_FLEXIO.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_FMC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_FMU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_FMUTEST.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_FREQME.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_GLIKEY.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_GPIO.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_I3C.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_INPUTMUX.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_LPCMP.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_LPDAC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_LPI2C.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_LPSPI.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_LPTMR.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_LPUART.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_MRCC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_OPAMP.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_OSTIMER.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_PORT.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_PWM.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_SCG.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_SPC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_SYSCON.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_TRDC.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_USB.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_UTICK.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_VBAT.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_WAKETIMER.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_WUU.h' projectpath='device/periph1'/>
        <file category='header' name='devices/MCXA156/periph1/PERI_WWDT.h' projectpath='device/periph1'/>
        <file category='include' name='devices/MCXA156/'/>
        <file category='include' name='devices/MCXA156/periph1/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Project Template' Csub='RTE_Device' Cversion='1.0.0' condition='device.RTE.condition_id'>
      <description>Rte_device</description>
      <files>
        <file category='header' attr='config' name='devices/MCXA156/template/RTE_Device.h' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MCXA156/template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='MCXA156_linker' Cversion='1.0.0' condition='device.linker.condition_id'>
      <description>Device MCXA156_linker</description>
      <files>
        <file condition='allOf.toolchains=mdk, device_ids=MCXA156.condition_id' category='linkerScript' name='devices/MCXA156/arm/MCXA156_flash.scf' projectpath='MCXA156/arm'/>
        <file condition='allOf.toolchains=mdk, device_ids=MCXA156.condition_id' category='linkerScript' name='devices/MCXA156/arm/MCXA156_ram.scf' projectpath='MCXA156/arm'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MCXA156.condition_id' category='linkerScript' name='devices/MCXA156/gcc/MCXA156_flash.ld' projectpath='MCXA156/gcc'/>
        <file condition='allOf.toolchains=armgcc, device_ids=MCXA156.condition_id' category='linkerScript' name='devices/MCXA156/gcc/MCXA156_ram.ld' projectpath='MCXA156/gcc'/>
        <file condition='allOf.toolchains=iar, device_ids=MCXA156.condition_id' category='linkerScript' name='devices/MCXA156/iar/MCXA156_flash.icf' projectpath='MCXA156/iar'/>
        <file condition='allOf.toolchains=iar, device_ids=MCXA156.condition_id' category='linkerScript' name='devices/MCXA156/iar/MCXA156_ram.icf' projectpath='MCXA156/iar'/>
      </files>
    </component>
    <component Cclass='Board Support' Cgroup='SDK Project Template' Csub='project_template' Cvariant='MCXA156' Cversion='1.0.0' condition='device.project_template.condition_id' isDefaultVariant='1'>
      <description>Devices_project_template MCXA156</description>
      <files>
        <file category='header' attr='config' name='devices/MCXA156/project_template/board.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXA156/project_template/board.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXA156/project_template/clock_config.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXA156/project_template/clock_config.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXA156/project_template/pin_mux.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXA156/project_template/pin_mux.c' version='1.0.0' projectpath='board'/>
        <file category='header' attr='config' name='devices/MCXA156/project_template/peripherals.h' version='1.0.0' projectpath='board'/>
        <file category='sourceC' attr='config' name='devices/MCXA156/project_template/peripherals.c' version='1.0.0' projectpath='board'/>
        <file category='include' name='devices/MCXA156/project_template/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='Startup' Csub='' Cversion='1.0.0' condition='device.startup.condition_id'>
      <description>Device MCXA156_startup</description>
      <files>
        <file condition='allOf.toolchains=iar.condition_id' category='sourceAsm' attr='config' name='devices/MCXA156/iar/startup_MCXA156.s' version='1.0.0' projectpath='startup/iar'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceAsm' attr='config' name='devices/MCXA156/gcc/startup_MCXA156.S' version='1.0.0' projectpath='startup/gcc'/>
        <file condition='allOf.toolchains=mdk.condition_id' category='sourceAsm' attr='config' name='devices/MCXA156/arm/startup_MCXA156.S' version='1.0.0' projectpath='startup/arm'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='CMSIS' Csub='MCXA156_system' Cversion='1.0.0' condition='device.system.condition_id'>
      <description>Device MCXA156_system</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/system_MCXA156.c' projectpath='device'/>
        <file category='header' name='devices/MCXA156/system_MCXA156.h' projectpath='device'/>
        <file category='include' name='devices/MCXA156/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='aoi' Cversion='2.0.2' condition='driver.aoi.condition_id'>
      <description>AOI Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_aoi.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_aoi.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cache_lpcac' Cversion='2.2.0' condition='driver.cache_lpcac_n4a_mcxn.condition_id'>
      <description>CACHE Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_cache_lpcac.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_cache_lpcac.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-common' Cversion='1.0.0' condition='driver.camera-common.condition_id'>
      <description>Driver camera-common</description>
      <files>
        <file category='header' name='components/video/camera/fsl_camera.h' projectpath='video'/>
        <file category='include' name='components/video/camera/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ap1302' Cversion='1.0.1' condition='driver.camera-device-ap1302.condition_id'>
      <description>Driver camera-device-ap1302</description>
      <files>
        <file category='header' name='components/video/camera/device/ap1302/fsl_ap1302.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ap1302/fsl_ap1302.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ap1302/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-common' Cversion='1.0.0' condition='driver.camera-device-common.condition_id'>
      <description>Driver camera-device-common</description>
      <files>
        <file category='header' name='components/video/camera/device/fsl_camera_device.h' projectpath='video'/>
        <file category='include' name='components/video/camera/device/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-max9286' Cversion='1.0.2' condition='driver.camera-device-max9286.condition_id'>
      <description>Driver camera-device-max9286</description>
      <files>
        <file category='header' name='components/video/camera/device/max9286/fsl_max9286.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/max9286/fsl_max9286.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/max9286/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-mt9m114' Cversion='1.0.2' condition='driver.camera-device-mt9m114.condition_id'>
      <description>Driver camera-device-mt9m114</description>
      <files>
        <file category='header' name='components/video/camera/device/mt9m114/fsl_mt9m114.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/mt9m114/fsl_mt9m114.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/mt9m114/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov5640' Cversion='1.0.1' condition='driver.camera-device-ov5640.condition_id'>
      <description>Driver camera-device-ov5640</description>
      <files>
        <file category='header' name='components/video/camera/device/ov5640/fsl_ov5640.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov5640/fsl_ov5640.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov5640/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7670' Cversion='1.0.2' condition='driver.camera-device-ov7670.condition_id'>
      <description>Driver camera-device-ov7670</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7670/fsl_ov7670.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7670/fsl_ov7670.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7670/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-ov7725' Cversion='1.0.1' condition='driver.camera-device-ov7725.condition_id'>
      <description>Driver camera-device-ov7725</description>
      <files>
        <file category='header' name='components/video/camera/device/ov7725/fsl_ov7725.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/ov7725/fsl_ov7725.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/ov7725/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-device-sccb' Cversion='1.0.1' condition='driver.camera-device-sccb.condition_id'>
      <description>Driver camera-device-sccb</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/camera/device/sccb/fsl_sccb.h' projectpath='video'/>
        <file category='sourceC' name='components/video/camera/device/sccb/fsl_sccb.c' projectpath='video'/>
        <file category='include' name='components/video/camera/device/sccb/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='camera-receiver-common' Cversion='1.0.0' condition='driver.camera-receiver-common.condition_id'>
      <description>Driver camera-receiver-common</description>
      <files>
        <file category='header' name='components/video/camera/receiver/fsl_camera_receiver.h' projectpath='video'/>
        <file category='include' name='components/video/camera/receiver/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='cdog' Cversion='2.1.3' condition='driver.cdog.condition_id'>
      <description>cdog Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_cdog.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_cdog.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='clock' Cversion='2.0.0' condition='driver.clock.condition_id'>
      <description>Clock Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_clock.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_clock.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='GPIO' Csub='gpio_cmsis' Cversion='1.0.0' Capiversion='1.0.0' condition='driver.cmsis_gpio.condition_id'>
      <description>GPIO CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/cmsis_drivers/fsl_gpio_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/cmsis_drivers/fsl_gpio_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='I2C' Csub='lpi2c_cmsis' Cversion='2.6.0' Capiversion='2.3.0' condition='driver.cmsis_lpi2c.condition_id'>
      <description>LPI2C CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/cmsis_drivers/fsl_lpi2c_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/cmsis_drivers/fsl_lpi2c_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='SPI' Csub='lpspi_cmsis' Cversion='2.12.0' Capiversion='2.2.0' condition='driver.cmsis_lpspi.condition_id'>
      <description>LPSPI CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/cmsis_drivers/fsl_lpspi_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/cmsis_drivers/fsl_lpspi_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='CMSIS Driver' Cgroup='USART' Csub='lpuart_cmsis' Cversion='2.7.0' Capiversion='2.3.0' condition='driver.cmsis_lpuart.condition_id'>
      <description>LPUART CMSIS Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/cmsis_drivers/fsl_lpuart_cmsis.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/cmsis_drivers/fsl_lpuart_cmsis.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/cmsis_drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='common' Cversion='2.6.0' condition='driver.common.condition_id'>
      <description>common Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_common.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_common.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm33.condition_id' category='sourceC' name='devices/MCXA156/drivers/fsl_common_arm.c' projectpath='drivers'/>
        <file condition='allOf.cores=cm33.condition_id' category='header' name='devices/MCXA156/drivers/fsl_common_arm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='crc' Cversion='2.0.4' condition='driver.crc.condition_id'>
      <description>CRC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_crc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_crc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ctimer' Cversion='2.3.3' condition='driver.ctimer.condition_id'>
      <description>CTimer Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_ctimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_ctimer.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dac' Cversion='2.1.2' condition='driver.dac_1.condition_id'>
      <description>DAC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_dac.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_dac.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi' Cversion='1.0.0' condition='driver.dbi.condition_id'>
      <description>Driver dbi</description>
      <files>
        <file category='header' name='components/video/display/dbi/fsl_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/fsl_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dbi_flexio_edma' Cversion='1.0.1' condition='driver.dbi_flexio_edma.condition_id'>
      <description>Driver dbi_flexio_edma</description>
      <files>
        <file category='header' name='components/video/display/dbi/flexio/fsl_dbi_flexio_edma.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dbi/flexio/fsl_dbi_flexio_edma.c' projectpath='video'/>
        <file category='include' name='components/video/display/dbi/flexio/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-common' Cversion='1.0.0' condition='driver.dc-fb-common.condition_id'>
      <description>Driver dc-fb-common</description>
      <files>
        <file category='header' name='components/video/display/dc/fsl_dc_fb.h' projectpath='video'/>
        <file category='include' name='components/video/display/dc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-dbi' Cversion='1.0.0' condition='driver.dc-fb-dbi.condition_id'>
      <description>Driver dc-fb-dbi</description>
      <RTE_Components_h>
#ifndef MCUX_DBI_LEGACY
#define MCUX_DBI_LEGACY 0
#endif
</RTE_Components_h>
      <files>
        <file category='header' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/dbi/fsl_dc_fb_dbi.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/dbi/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='dc-fb-ssd1963' Cversion='1.0.2' condition='driver.dc-fb-ssd1963.condition_id'>
      <description>Driver dc-fb-ssd1963</description>
      <files>
        <file category='header' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/dc/ssd1963/fsl_dc_fb_ssd1963.c' projectpath='video'/>
        <file category='include' name='components/video/display/dc/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-adv7535' Cversion='1.0.1' condition='driver.display-adv7535.condition_id'>
      <description>Driver display-adv7535</description>
      <files>
        <file category='header' name='components/video/display/adv7535/fsl_adv7535.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/adv7535/fsl_adv7535.c' projectpath='video'/>
        <file category='include' name='components/video/display/adv7535/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-common' Cversion='1.0.0' condition='driver.display-common.condition_id'>
      <description>Driver display-common</description>
      <files>
        <file category='header' name='components/video/display/fsl_display.h' projectpath='video'/>
        <file category='include' name='components/video/display/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6161' Cversion='1.0.0' condition='driver.display-it6161.condition_id'>
      <description>Driver display-it6161</description>
      <files>
        <file category='header' name='components/video/display/it6161/fsl_it6161.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/fsl_it6161.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/hdmi_tx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/hdmi_tx.c' projectpath='video'/>
        <file category='header' name='components/video/display/it6161/mipi_rx.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6161/mipi_rx.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6161/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-it6263' Cversion='1.0.1' condition='driver.display-it6263.condition_id'>
      <description>Driver display-it6263</description>
      <files>
        <file category='header' name='components/video/display/it6263/fsl_it6263.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/it6263/fsl_it6263.c' projectpath='video'/>
        <file category='include' name='components/video/display/it6263/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='display-sn65dsi83' Cversion='1.0.0' condition='driver.display-sn65dsi83.condition_id'>
      <description>Driver display-sn65dsi83</description>
      <files>
        <file category='header' name='components/video/display/sn65dsi83/fsl_sn65dsi83.h' projectpath='video'/>
        <file category='sourceC' name='components/video/display/sn65dsi83/fsl_sn65dsi83.c' projectpath='video'/>
        <file category='include' name='components/video/display/sn65dsi83/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='edma' Cversion='2.10.5' condition='driver.edma4.condition_id'>
      <description>EDMA Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_edma.h' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_edma_core.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='edma_soc' Cversion='2.0.0' condition='driver.edma_soc.condition_id'>
      <description>EDMA SOC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_edma_soc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_edma_soc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eim' Cversion='2.0.1' condition='driver.eim.condition_id'>
      <description>EIM Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_eim.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_eim.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ektf2k' Cversion='1.0.0' condition='driver.ektf2k.condition_id'>
      <description>Driver ektf2k</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/ektf2k/fsl_ektf2k.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ektf2k/fsl_ektf2k.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ektf2k/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='eqdc' Cversion='2.3.1' condition='driver.eqdc.condition_id'>
      <description>EQDC Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_eqdc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_eqdc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='erm' Cversion='2.0.1' condition='driver.erm.condition_id'>
      <description>ERM Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_erm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_erm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan' Cversion='2.14.1' condition='driver.flexcan.condition_id'>
      <description>FLEXCAN Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexcan.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexcan.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexcan_edma' Cversion='2.12.0' condition='driver.flexcan_edma.condition_id'>
      <description>FLEXCAN Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexcan_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexcan_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio' Cversion='2.3.0' condition='driver.flexio.condition_id'>
      <description>FLEXIO Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_i2c_master' Cversion='2.6.1' condition='driver.flexio_i2c_master.condition_id'>
      <description>FLEXIO I2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_i2c_master.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_i2c_master.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd' Cversion='2.3.0' condition='driver.flexio_mculcd.condition_id'>
      <description>FLEXIO MCULCD Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_mculcd.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_mculcd.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_mculcd_edma' Cversion='2.0.6' condition='driver.flexio_mculcd_edma.condition_id'>
      <description>FLEXIO MCULCD EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_mculcd_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_mculcd_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi' Cversion='2.4.2' condition='driver.flexio_spi.condition_id'>
      <description>FLEXIO SPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_spi.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_spi.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_spi_edma' Cversion='2.3.0' condition='driver.flexio_spi_edma.condition_id'>
      <description>FLEXIO SPI EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_spi_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_spi_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart' Cversion='2.6.2' condition='driver.flexio_uart.condition_id'>
      <description>FLEXIO UART Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_uart.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_uart.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='flexio_uart_edma' Cversion='2.4.1' condition='driver.flexio_uart_edma.condition_id'>
      <description>FLEXIO UART EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_flexio_uart_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_flexio_uart_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft3267' Cversion='1.0.1' condition='driver.ft3267.condition_id'>
      <description>Touch panel controller FT3267 driver</description>
      <files>
        <file category='header' name='components/touch/ft3267/fsl_ft3267.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft3267/fsl_ft3267.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft3267/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406' Cversion='1.0.0' condition='driver.ft5406.condition_id'>
      <description>Driver ft5406</description>
      <files>
        <file category='header' name='components/touch/ft5406/fsl_ft5406.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406/fsl_ft5406.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft5406_rt' Cversion='1.0.0' condition='driver.ft5406_rt.condition_id'>
      <description>Driver ft5406_rt</description>
      <files>
        <file category='header' name='components/touch/ft5406_rt/fsl_ft5406_rt.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft5406_rt/fsl_ft5406_rt.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft5406_rt/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ft6x06' Cversion='1.0.0' condition='driver.ft6x06.condition_id'>
      <description>Driver ft6x06</description>
      <files>
        <file category='header' name='components/touch/ft6x06/fsl_ft6x06.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/ft6x06/fsl_ft6x06.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/ft6x06/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxas21002cq' Cversion='1.0.0' condition='driver.fxas21002cq.condition_id'>
      <description>Driver fxas21002cq</description>
      <files>
        <file category='header' name='components/sensor/fxas21002cq/fsl_fxas.h' projectpath='gyroscope'/>
        <file category='sourceC' name='components/sensor/fxas21002cq/fsl_fxas.c' projectpath='gyroscope'/>
        <file category='include' name='components/sensor/fxas21002cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxls8974cf' Cversion='1.0.0' condition='driver.fxls8974cf.condition_id'>
      <description>Driver fxls8974cf</description>
      <files>
        <file category='header' name='components/sensor/fxls8974cf/fsl_fxls.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxls8974cf/fsl_fxls.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxls8974cf/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='fxos8700cq' Cversion='1.0.0' condition='driver.fxos8700cq.condition_id'>
      <description>Driver fxos8700cq</description>
      <files>
        <file category='header' name='components/sensor/fxos8700cq/fsl_fxos.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/fxos8700cq/fsl_fxos.c' projectpath='accel'/>
        <file category='include' name='components/sensor/fxos8700cq/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='glikey' Cversion='2.0.1' condition='driver.glikey.condition_id'>
      <description>GLIKEY Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_glikey.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_glikey.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gpio' Cversion='2.8.2' condition='driver.gpio.condition_id'>
      <description>GPIO Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_gpio.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_gpio.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='gt911' Cversion='1.0.4' condition='driver.gt911.condition_id'>
      <description>Driver gt911</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/touch/gt911/fsl_gt911.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/gt911/fsl_gt911.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/gt911/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='htu21d' Cversion='1.0.0' condition='driver.htu21d.condition_id'>
      <description>Driver htu21d</description>
      <files>
        <file category='header' name='components/sensor/htu21d/fsl_htu21d.h' projectpath='htu'/>
        <file category='sourceC' name='components/sensor/htu21d/fsl_htu21d.c' projectpath='htu'/>
        <file category='include' name='components/sensor/htu21d/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c' Cversion='2.14.1' condition='driver.i3c.condition_id'>
      <description>I3C Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_i3c.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_i3c.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i3c_edma' Cversion='2.2.10' condition='driver.i3c_edma.condition_id'>
      <description>I3C EDMA Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_i3c_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_i3c_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='icm42688p' Cversion='1.0.0' condition='driver.icm42688p.condition_id'>
      <description>Driver icm42688p</description>
      <files>
        <file category='header' name='components/sensor/icm42688p/fsl_icm42688p.h' projectpath='icm42688p'/>
        <file category='sourceC' name='components/sensor/icm42688p/fsl_icm42688p.c' projectpath='icm42688p'/>
        <file category='include' name='components/sensor/icm42688p/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ili9341' Cversion='1.0.2' condition='driver.ili9341.condition_id'>
      <description>Driver ili9341</description>
      <files>
        <file category='header' name='components/display/ili9341/fsl_ili9341.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ili9341/fsl_ili9341.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ili9341/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux' Cversion='2.0.9' condition='driver.inputmux.condition_id'>
      <description>INPUTMUX Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_inputmux.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_inputmux.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='inputmux_connections' Cversion='2.0.0' condition='driver.inputmux_connections.condition_id'>
      <description>Inputmux_connections Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_inputmux_connections.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpadc' Cversion='2.9.3' condition='driver.lpadc.condition_id'>
      <description>LPADC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpadc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpadc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpc_freqme' Cversion='2.1.2' condition='driver.lpc_freqme.condition_id'>
      <description>lpc_freqme Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_freqme.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_freqme.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpcmp' Cversion='2.3.2' condition='driver.lpcmp.condition_id'>
      <description>LPCMP Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpcmp.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpcmp.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c' Cversion='2.6.1' condition='driver.lpi2c.condition_id'>
      <description>LPI2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpi2c.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpi2c.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='i2c_edma' Cversion='2.4.4' condition='driver.lpi2c_edma.condition_id'>
      <description>LPI2C Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpi2c_edma.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpi2c_edma.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi' Cversion='2.7.1' condition='driver.lpspi.condition_id'>
      <description>LPSPI Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpspi.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpspi.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpspi_edma' Cversion='2.4.6' condition='driver.lpspi_edma.condition_id'>
      <description>LPSPI Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpspi_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpspi_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lptmr' Cversion='2.2.0' condition='driver.lptmr.condition_id'>
      <description>LPTMR Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lptmr.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_lptmr.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart' Cversion='2.9.1' condition='driver.lpuart.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpuart.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpuart.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lpuart_edma' Cversion='2.6.0' condition='driver.lpuart_edma.condition_id'>
      <description>LPUART Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_lpuart_edma.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_lpuart_edma.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='lsm6dso' Cversion='1.0.0' condition='driver.lsm6dso.condition_id'>
      <description>Driver lsm6dso</description>
      <files>
        <file category='header' name='components/sensor/lsm6dso/fsl_lsm.h' projectpath='tilt_pedo'/>
        <file category='sourceC' name='components/sensor/lsm6dso/fsl_lsm.c' projectpath='tilt_pedo'/>
        <file category='include' name='components/sensor/lsm6dso/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='max30101' Cversion='1.0.0' condition='driver.max30101.condition_id'>
      <description>Driver max30101</description>
      <files>
        <file category='header' name='components/sensor/max30101/fsl_max.h' projectpath='heartrate'/>
        <file category='sourceC' name='components/sensor/max30101/fsl_max.c' projectpath='heartrate'/>
        <file category='include' name='components/sensor/max30101/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mcx_cmc' Cversion='2.3.0' condition='driver.mcx_cmc.condition_id'>
      <description>MCX_CMC Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_cmc.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_cmc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mcx_spc' Cversion='2.8.0' condition='driver.mcx_spc.condition_id'>
      <description>MCX SPC Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_spc.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_spc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mcx_vbat' Cversion='2.3.1' condition='driver.mcx_vbat.condition_id'>
      <description>MCX VBAT Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_vbat.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_vbat.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8451q' Cversion='1.0.0' condition='driver.mma8451q.condition_id'>
      <description>Driver mma8451q</description>
      <files>
        <file category='header' name='components/sensor/mma8451q/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8451q/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8451q/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='mma8652fc' Cversion='1.0.0' condition='driver.mma8652fc.condition_id'>
      <description>Driver mma8652fc</description>
      <files>
        <file category='header' name='components/sensor/mma8652fc/fsl_mma.h' projectpath='accel'/>
        <file category='sourceC' name='components/sensor/mma8652fc/fsl_mma.c' projectpath='accel'/>
        <file category='include' name='components/sensor/mma8652fc/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='nmh1000' Cversion='1.0.0' condition='driver.nmh1000.condition_id'>
      <description>Driver nmh1000</description>
      <files>
        <file category='header' name='components/sensor/nmh1000/fsl_nmh1000.h' projectpath='nmh1000'/>
        <file category='sourceC' name='components/sensor/nmh1000/fsl_nmh1000.c' projectpath='nmh1000'/>
        <file category='include' name='components/sensor/nmh1000/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='opamp' Cversion='2.2.1' condition='driver.opamp.condition_id'>
      <description>OPAMP Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_opamp.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_opamp.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ostimer' Cversion='2.2.4' condition='driver.ostimer.condition_id'>
      <description>OSTimer Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_ostimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_ostimer.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='p3t1755' Cversion='2.0.0' condition='driver.p3t1755.condition_id'>
      <description>Driver p3t1755</description>
      <files>
        <file category='header' name='components/sensor/p3t1755/fsl_p3t1755.h' projectpath='component/p3t1755'/>
        <file category='sourceC' name='components/sensor/p3t1755/fsl_p3t1755.c' projectpath='component/p3t1755'/>
        <file category='include' name='components/sensor/p3t1755/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='port' Cversion='2.5.1' condition='driver.port.condition_id'>
      <description>PORT Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_port.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='psp27801' Cversion='1.0.0' condition='driver.psp27801.condition_id'>
      <description>Driver psp27801</description>
      <files>
        <file category='header' name='components/display/psp27801/fsl_psp27801.h' projectpath='oled'/>
        <file category='sourceC' name='components/display/psp27801/fsl_psp27801.c' projectpath='oled'/>
        <file category='include' name='components/display/psp27801/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='pwm' Cversion='2.9.0' condition='driver.pwm.condition_id'>
      <description>PWM Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_pwm.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_pwm.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='reset' Cversion='2.4.0' condition='driver.reset.condition_id'>
      <description>Reset Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_reset.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_reset.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='romapi' Cversion='2.0.1' condition='driver.romapi.condition_id'>
      <description>ROMAPI Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_romapi.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtt' Cversion='7.22.0' condition='driver.rtt.condition_id'>
      <description>SEGGER Real Time Transfer(RTT)</description>
      <files>
        <file category='doc' name='components/rtt/License.txt' projectpath='rtt'/>
        <file category='doc' name='components/rtt/README.txt' projectpath='rtt'/>
        <file category='sourceC' name='components/rtt/RTT/SEGGER_RTT.c' projectpath='rtt/RTT'/>
        <file category='header' name='components/rtt/RTT/SEGGER_RTT.h' projectpath='rtt/RTT'/>
        <file category='sourceAsm' name='components/rtt/RTT/SEGGER_RTT_ASM_ARMv7M.S' projectpath='rtt/RTT'/>
        <file category='sourceC' name='components/rtt/RTT/SEGGER_RTT_printf.c' projectpath='rtt/RTT'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_GCC.c' projectpath='rtt/Syscalls'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_IAR.c' projectpath='rtt/Syscalls'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_KEIL.c' projectpath='rtt/Syscalls'/>
        <file category='sourceC' name='components/rtt/Syscalls/SEGGER_RTT_Syscalls_SES.c' projectpath='rtt/Syscalls'/>
        <file category='doc' name='components/rtt/License.txt' projectpath='rtt'/>
        <file category='doc' name='components/rtt/README.txt' projectpath='rtt'/>
        <file category='include' name='components/rtt/RTT/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='rtt template' Cversion='7.22.0' condition='driver.rtt.template.condition_id'>
      <description>RTT template configuration</description>
      <files>
        <file category='header' attr='config' name='components/rtt/template/SEGGER_RTT_Conf.h' version='7.22.0' projectpath='rtt/template'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='ssd1963' Cversion='1.2.0' condition='driver.ssd1963.condition_id'>
      <description>Driver ssd1963</description>
      <files>
        <file category='header' name='components/display/ssd1963/fsl_ssd1963.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/ssd1963/fsl_ssd1963.c' projectpath='lcdc'/>
        <file category='include' name='components/display/ssd1963/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='st7796s' Cversion='1.0.0' condition='driver.st7796s.condition_id'>
      <description>Driver st7796s</description>
      <files>
        <file category='header' name='components/display/st7796s/fsl_st7796s.h' projectpath='lcdc'/>
        <file category='sourceC' name='components/display/st7796s/fsl_st7796s.c' projectpath='lcdc'/>
        <file category='include' name='components/display/st7796s/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tma525b' Cversion='1.0.0' condition='driver.tma525b.condition_id'>
      <description>Driver tma525b</description>
      <files>
        <file category='header' name='components/touch/tma525b/fsl_tma525b.h' projectpath='touchpanel'/>
        <file category='sourceC' name='components/touch/tma525b/fsl_tma525b.c' projectpath='touchpanel'/>
        <file category='include' name='components/touch/tma525b/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='trdc' Cversion='2.2.1' condition='driver.trdc_1.condition_id'>
      <description>TRDC Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_trdc.h' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_trdc_core.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_trdc.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='trdc_soc' Cversion='2.0.0' condition='driver.trdc_soc.condition_id'>
      <description>TRDC SOC Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_trdc_soc.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='tsl2561' Cversion='1.0.0' condition='driver.tsl2561.condition_id'>
      <description>Driver tsl2561</description>
      <files>
        <file category='header' name='components/sensor/tsl2561/fsl_tsl2561.h' projectpath='tsl'/>
        <file category='sourceC' name='components/sensor/tsl2561/fsl_tsl2561.c' projectpath='tsl'/>
        <file category='include' name='components/sensor/tsl2561/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='utick' Cversion='2.0.5' condition='driver.utick.condition_id'>
      <description>UTICK Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_utick.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_utick.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-common' Cversion='1.1.0' condition='driver.video-common.condition_id'>
      <description>Driver video-common</description>
      <files>
        <file category='header' name='components/video/fsl_video_common.h' projectpath='video'/>
        <file category='sourceC' name='components/video/fsl_video_common.c' projectpath='video'/>
        <file category='include' name='components/video/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='video-i2c' Cversion='1.0.1' condition='driver.video-i2c.condition_id'>
      <description>Driver video-i2c</description>
      <RTE_Components_h>
        #ifndef SDK_I2C_BASED_COMPONENT_USED
        #define SDK_I2C_BASED_COMPONENT_USED 1
        #endif
      </RTE_Components_h>
      <files>
        <file category='header' name='components/video/i2c/fsl_video_i2c.h' projectpath='video'/>
        <file category='sourceC' name='components/video/i2c/fsl_video_i2c.c' projectpath='video'/>
        <file category='include' name='components/video/i2c/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='waketimer' Cversion='2.0.1' condition='driver.waketimer.condition_id'>
      <description>WAKETIMER Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_waketimer.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_waketimer.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wuu' Cversion='2.4.0' condition='driver.wuu.condition_id'>
      <description>WUU Driver</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_wuu.c' projectpath='drivers'/>
        <file category='header' name='devices/MCXA156/drivers/fsl_wuu.h' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Drivers' Csub='wwdt' Cversion='2.1.9' condition='driver.wwdt.condition_id'>
      <description>WWDT Driver</description>
      <files>
        <file category='header' name='devices/MCXA156/drivers/fsl_wwdt.h' projectpath='drivers'/>
        <file category='sourceC' name='devices/MCXA156/drivers/fsl_wwdt.c' projectpath='drivers'/>
        <file category='include' name='devices/MCXA156/drivers/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='misc_utilities' Cversion='1.1.1' condition='utilities.misc_utilities.condition_id'>
      <description>
        Utilities which is needed for particular toolchain like the SBRK function required to address limitation between HEAP and STACK in GCC toolchain library.
      </description>
      <files>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MCXA156/utilities/fsl_syscall_stub.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc.condition_id' category='sourceC' name='devices/MCXA156/utilities/fsl_sbrk.c' projectpath='utilities'/>
        <file condition='allOf.toolchains=armgcc, mdk, cores=cm33.condition_id' category='sourceAsm' name='devices/MCXA156/utilities/fsl_memcpy.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert' Cversion='1.0.0' condition='utility.assert.condition_id'>
      <description>Utility assert</description>
      <files>
        <file category='header' name='devices/MCXA156/utilities/debug_console/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXA156/utilities/debug_console/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXA156/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='assert_lite' Cversion='1.0.0' condition='utility.assert_lite.condition_id'>
      <description>Utility assert_lite</description>
      <files>
        <file category='header' name='devices/MCXA156/utilities/debug_console_lite/fsl_assert.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXA156/utilities/debug_console_lite/fsl_assert.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXA156/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console' Cversion='1.0.1' condition='utility.debug_console.condition_id'>
      <description>Utility debug_console</description>
      <files>
        <file category='header' name='devices/MCXA156/utilities/debug_console/fsl_debug_console.h' projectpath='utilities/debug_console'/>
        <file category='sourceC' name='devices/MCXA156/utilities/debug_console/fsl_debug_console.c' projectpath='utilities/debug_console'/>
        <file category='include' name='devices/MCXA156/utilities/debug_console/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug_console_lite' Cversion='1.0.0' condition='utility.debug_console_lite.condition_id'>
      <description>Utility debug_console_lite</description>
      <files>
        <file category='header' name='devices/MCXA156/utilities/debug_console_lite/fsl_debug_console.h' projectpath='utilities/debug_console_lite'/>
        <file category='sourceC' name='devices/MCXA156/utilities/debug_console_lite/fsl_debug_console.c' projectpath='utilities/debug_console_lite'/>
        <file category='include' name='devices/MCXA156/utilities/debug_console_lite/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='debug console template config' Cversion='1.0.0' condition='utility.debug_console_template_config.condition_id'>
      <description>Utility debug_console Template Config</description>
      <files>
        <file category='header' attr='config' name='devices/MCXA156/utilities/debug_console/config/fsl_debug_console_conf.h' version='1.0.0' projectpath='utilities/debug_console/config'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='format' Cversion='1.0.0' condition='utility.format.condition_id'>
      <description>Used to format convertion</description>
      <files>
        <file category='sourceC' name='devices/MCXA156/utilities/format/fsl_format.c' projectpath='utilities'/>
        <file category='header' name='devices/MCXA156/utilities/format/fsl_format.h' projectpath='utilities'/>
        <file category='include' name='devices/MCXA156/utilities/format/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='incbin' Cversion='1.0.0' condition='utility.incbin.condition_id'>
      <description>Used to include slave core binary into master core binary.</description>
      <files>
        <file condition='allOf.toolchains=armgcc, mdk.condition_id' category='sourceAsm' name='devices/MCXA156/utilities/incbin/fsl_incbin.S' projectpath='utilities'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='shell' Cversion='1.0.0' condition='utility.shell.condition_id'>
      <description>Utility shell</description>
      <files>
        <file category='header' name='devices/MCXA156/utilities/shell/fsl_shell.h' projectpath='utilities'/>
        <file category='sourceC' name='devices/MCXA156/utilities/shell/fsl_shell.c' projectpath='utilities'/>
        <file category='include' name='devices/MCXA156/utilities/shell/'/>
      </files>
    </component>
    <component Cclass='Device' Cgroup='SDK Utilities' Csub='str' Cversion='1.0.0' condition='utility.str.condition_id'>
      <description>Utility str</description>
      <files>
        <file category='header' name='devices/MCXA156/utilities/str/fsl_str.h' projectpath='utilities/str'/>
        <file category='sourceC' name='devices/MCXA156/utilities/str/fsl_str.c' projectpath='utilities/str'/>
        <file category='include' name='devices/MCXA156/utilities/str/'/>
      </files>
    </component>
  </components>
</package>