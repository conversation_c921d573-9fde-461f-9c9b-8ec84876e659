<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>TWR-KL28Z72M_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for TWRKL28Z72M</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="11.0.1">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <boards>
    <board vendor="NXP" name="TWR-KL28Z72M">
      <description>TWR-KL28Z72M: TWR-KL28Z72M: Kinetis KL28 MCU Tower System Module</description>
      <mountedDevice Dname="MKL28Z512xxx7" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKL28Z7_AND_component.lpuart_adapter_AND_component.serial_manager_uart_AND_device.MKL28Z7_startup_AND_driver.clock_AND_driver.common_AND_driver.flash_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_utility.debug_console">
      <accept Dname="MKL28Z512???7" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="flash"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MKL28Z7_startup"/>
    </condition>
  </conditions>
  <examples>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="driver_examples/gpio/input_interrupt" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example uses the software button to control/toggle the LED.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_interrupt" folder="driver_examples/adc16/interrupt" doc="readme.txt">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value. Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the ADC16 interrupt configuration is set when configuring the ADC16's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing aconversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc16_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="driver_examples/adc16/polling" doc="readme.txt">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.Note, the default setting of initialization for the ADC converter is just an available configuration. User can changethe configuration structure's setting in application to fit the special requirement.The auto-calibration is not essential but strongly recommended. It can help to adjust the converter itself and improvethe ADC16's performance.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" load="iar/adc16_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="driver_examples/cmp/polling" doc="readme.txt">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input. On the negative side, the internal 6-bit DAC is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user's voltage crosses the internal DAC's value. The endless loop in main() functionwould detect the logic value of comparator's output, and change the LED. The LED would be turned on when the compareoutput is logic one, or turned off when zero.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="driver_examples/cmp/interrupt" doc="readme.txt">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input. On the negative side, the internal 6-bit DAC is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user-defined channel's voltage crosses the internal DAC's value. The change ofcomparator's output would generate the falling and rising edge events with their interrupts enabled. When any CMP interrupt happens, the CMP's ISR would turn on the LED light if detecting the output's rising edge, or turn off it whendetecting the output's falling edge.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_basic" folder="driver_examples/dac/basic" doc="readme.txt">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's buffer feature is not enabled, the first item of the buffer is used as the DAC output data register.The converter would always output the value of the first item. In this example, it gets the value from terminal,outputs the DAC output voltage through DAC output pin.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_basic.uvprojx"/>
        <environment name="iar" load="iar/dac_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_buffer_interrupt" folder="driver_examples/dac/buffer_interrupt" doc="readme.txt">
      <description>The dac_buffer_interrupt example shows how to use DAC buffer with interrupts.When the DAC's buffer feature is enabled, user can benefit from the automation of updating DAC output by hardware/software trigger. As we know, the DAC converter outputs the value of item pointed by current read pointer. Once the buffer is triggered by software or hardware, the buffer's read pointer would move automatically as the work mode is set,like normal (cycle) mode, swing mode, one-time-scan mode or FIFO mode.In this example, it captures the user's type-in operation from terminal and does the software trigger to the buffer.The terminal would also display the log that shows the current buffer pointer's position with buffer events.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_buffer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dac_buffer_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="driver_examples/edma/memory_to_memory" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/edma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="driver_examples/edma/scatter_gather" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" load="iar/edma_scatter_gather.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="driver_examples/lptmr" doc="readme.txt">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="driver_examples/rtc" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" load="iar/rtc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" load="iar/pflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_read_accel_value_transfer" folder="driver_examples/flexio/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>The flexio_i2c_read_accel_value example shows how to use FLEXIO I2C  Master driver to communicate with an i2c device: 1. How to use the flexio i2c master driver to read a i2c device who_am_I register. 2. How to use the flexio i2c master driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer" folder="driver_examples/flexio/i2c/interrupt_lpi2c_transfer" doc="readme.txt">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_interrupt_transfer" folder="driver_examples/flexio/i2s/interrupt_transfer" doc="readme.txt">
      <description>The flexio_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2s_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_edma_transfer" folder="driver_examples/flexio/i2s/edma_transfer" doc="readme.txt">
      <description>The flexio_i2s_EDMA example shows how to use flexio_i2s driver with EDMA:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using EDMA.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2s_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master" folder="driver_examples/flexio/spi/int_lpspi_transfer/master" doc="readme.txt">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave" folder="driver_examples/flexio/spi/int_lpspi_transfer/slave" doc="readme.txt">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master" folder="driver_examples/flexio/spi/edma_lpspi_transfer/master" doc="readme.txt">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave" folder="driver_examples/flexio/spi/edma_lpspi_transfer/slave" doc="readme.txt">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="driver_examples/flexio/uart/polling_transfer" doc="readme.txt">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="driver_examples/flexio/uart/interrupt_transfer" doc="readme.txt">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board. Note: two queued transfer in this example, so please input even number characters.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="driver_examples/flexio/uart/int_rb_transfer" doc="readme.txt">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer" folder="driver_examples/flexio/uart/edma_transfer" doc="readme.txt">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt" folder="driver_examples/lpi2c/interrupt" doc="readme.txt">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as lpi2c slave .1. lpi2c master send data using interrupt to lpi2c slave in interrupt .2. lpi2c master read data using interrupt from lpi2c slave in interrupt .3. The example assumes that the connection is OK between master and slave, so there's NO error handling code.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_read_accel_value_transfer" folder="driver_examples/lpi2c/read_accel_value_transfer" doc="readme.txt">
      <description>The lpi2c_read_accel_value example shows how to use LPI2C driver to communicate with an lpi2c device: 1. How to use the lpi2c driver to read a lpi2c device who_am_I register. 2. How to use the lpi2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpit" folder="driver_examples/lpit" doc="readme.txt">
      <description>The LPIT project is a simple example of the SDK LPIT driver. It sets up the LPIThardware block to trigger a periodic interrupt after every 1 second. When the LPIT interrupt is triggereda message a printed on the serial terminal and an LED is toggled on the board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpit.uvprojx"/>
        <environment name="iar" load="iar/lpit.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="scg" folder="driver_examples/scg" doc="readme.txt">
      <description>The SCG example shows how to use SCG driver: 1. How to setup the SCG clock source. 2. How to use SCG clock while power mode switch. 3. How to use SCG APIs to get clock frequency.This example prints the clock frequency through the terminal using the SDK driver.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/scg.uvprojx"/>
        <environment name="iar" load="iar/scg.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="driver_examples/lpuart/interrupt" doc="readme.txt">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="driver_examples/lpuart/polling" doc="readme.txt">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="driver_examples/lpuart/interrupt_rb_transfer" doc="readme.txt">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt" folder="driver_examples/lpspi/interrupt" doc="readme.txt">
      <description>The lpspi_functional_interrupt example shows how to use LPSPI driver in interrupt way:In this example , one lpspi instance used as LPSPI master and another lpspi instance used as LPSPI slave .1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mmdvsq" folder="driver_examples/mmdvsq" doc="readme.txt">
      <description>The MMDVSQ Example project is a demonstration program that uses the KSDK software to Calculation square root and QuotientMMDVSQ Peripheral Driver ExampleStart MMDVSQ ExampleCalculation square root, please enter radicandSquare root of 9 is 3Calculation division to get remainder and quotientEnter dividend and divisorRemainder of 10 and 5 is 0Quotient of 10 and 5 is 2</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mmdvsq.uvprojx"/>
        <environment name="iar" load="iar/mmdvsq.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer" folder="driver_examples/sai/interrupt_transfer" doc="readme.txt">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/sai_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback" folder="driver_examples/sai/interrupt_record_playback" doc="readme.txt">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback.uvprojx"/>
        <environment name="iar" load="iar/sai_interrupt_record_playback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer" folder="driver_examples/sai/edma_transfer" doc="readme.txt">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/sai_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback" folder="driver_examples/sai/edma_record_playback" doc="readme.txt">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback.uvprojx"/>
        <environment name="iar" load="iar/sai_edma_record_playback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt" folder="driver_examples/sai/interrupt" doc="readme.txt">
      <description>The sai_interrupt example shows how to use sai fucntional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt.uvprojx"/>
        <environment name="iar" load="iar/sai_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tstmr" folder="driver_examples/tstmr" doc="readme.txt">
      <description>The tstmr example shows the usage of TSTMR driver in application. The TSTMR module is a free running incrementing counter that starts running after system reset de-assertion and can be read at any time by the software for determining the software ticks.The TSTMR runs off the 1 MHz clock and resets on every system reset.In this example, it would output a time stamp information when the application is ready. And then, delay for 1 second with TSTMR_DelayUs() function. Before and after the delay, it would output the two time stamps information again.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tstmr.uvprojx"/>
        <environment name="iar" load="iar/tstmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="trng_random" folder="driver_examples/trng/random" doc="readme.txt">
      <description>The True Random Number Generator (TRNG) is a hardware accelerator module that generates a 512-bitentropy as needed by an entropy consuming module or by other post processing functions. The TRNGExample project is a demonstration program that uses the KSDK software to generate random numbersand prints them to the terminal.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/trng_random.uvprojx"/>
        <environment name="iar" load="iar/trng_random.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog32" folder="driver_examples/wdog32" doc="readme.txt">
      <description>The WDOG32 Example project is to demonstrate usage of the KSDK wdog32 driver.In this example, fast testing is first implemented to test the wdog32.After this, refreshing the watchdog in None-window mode and window mode is executed.Note wdog32 is disabled in SystemInit function which means wdog32 is disabledafter chip emerges from reset.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog32.uvprojx"/>
        <environment name="iar" load="iar/wdog32.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power" folder="demo_apps/adc16_low_power" doc="readme.txt">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the ADC module andreads the current temperature of the microcontroller. While the temperature remains within boundaries, both LEDs are on.If the core temperature is higher or lower than average, the LEDs change state respectively.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="demo_apps/rtc_func" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="iar" load="iar/rtc_func.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_adc" folder="demo_apps/dac_adc" doc="readme.txt">
      <description>The DAC / ADC demo application demonstrates the use of the DAC and ADC peripherals. This application demonstrates how toconfigure the DAC and set the output on the DAC. This demo also demonstrates how to configure the ADC in 'Blocking Mode'and how to read ADC values.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_adc.uvprojx"/>
        <environment name="iar" load="iar/dac_adc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="demo_apps/power_manager" doc="readme.txt">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a specific power mode. The usercan also set the wakeup source by following the debug console prompts. The purpose of this demo is to demonstrate theimplementation of a power mode manager. The callback can be registered to the framework. If a power mode transition happens,the callback will be called and user can do something. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" load="iar/power_manager.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="demo_apps/power_mode_switch" doc="readme.txt">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user can also set the wakeupsource by following the debug console prompts. The purpose of this demo is to show how to switch between different power modes, and how to configure a wakeup source and wakeup the MCU from low power modes. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - Debug pins(e.g SWD_DIO) would consume addtional power, had better to disable related pins or disconnect them. </description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_pwm_twochannel" folder="driver_examples/tpm/pwm_twochannel" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The PWM dutycycle on both channelsis manually updated. On boards that have an LED connected to the TPM pins, the user will seea change in LED brightness if user enter different values.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/tpm_pwm_twochannel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_simple_pwm" folder="driver_examples/tpm/simple_pwm" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver. It sets up the TPMhardware block to output a center-aligned PWM signal. The PWM dutycycle is manually updated.On boards that have an LED connected to the TPM pins, the user will see a change in LEDbrightness if user enter different values.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/tpm_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_timer" folder="driver_examples/tpm/timer" doc="readme.txt">
      <description>The TPM project is a simple demonstration program of the SDK TPM driver to use TPM as a timer.It sets up the TPM hardware block to trigger an interrupt every 1 millisecond.When the TPM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_timer.uvprojx"/>
        <environment name="iar" load="iar/tpm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_combine_pwm" folder="driver_examples/tpm/combine_pwm" doc="readme.txt">
      <description>The TPM project is a demonstration program of generating a combined PWM signal by the SDK TPM driver. It sets up the TPMhardware block to output PWM signals on two TPM channels. The example also show's the complementary mode of operationand deadtime insertion.On boards that have 2 LEDs connected to the TPM pins, the user will seea change in LED brightness if user enter different values.And if the board do not support LEDs to show, the outputs can be observed by oscilloscope.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_combine_pwm.uvprojx"/>
        <environment name="iar" load="iar/tpm_combine_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_output_compare" folder="driver_examples/tpm/output_compare" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's output compare feature.It sets up one TPM channel to toggle the output when a match occurs with the channel value. The usershould probe the TPM output with a oscilloscope to see the signal toggling.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/tpm_output_compare.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_dual_edge_capture" folder="driver_examples/tpm/dual_edge_capture" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a TPM channel-pair for dual-edge capture. Once the input signal is received,this example will print the capture values and period of the input signal on the terminal window.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_dual_edge_capture.uvprojx"/>
        <environment name="iar" load="iar/tpm_dual_edge_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tpm_input_capture" folder="driver_examples/tpm/input_capture" doc="readme.txt">
      <description>The TPM project is a demonstration program of the SDK TPM driver's input capture feature.The example sets up a TPM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tpm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/tpm_input_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v4_normal_mode" folder="driver_examples/tsi_v4/normal" doc="readme.txt">
      <description>The tsi_v4_normal example shows how to use TSI_V4 driver in normal modes:In this example , we make use of the available electrodes on board to show driver usage.1. Firstly, we get the non-touch calibration results as baseline electrode counter;2. Then, we start the Software-Trigger scan using polling method and interrupt method;3. Then, we start the Hardware-Trigger scan using interrupt method.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v4_normal_mode.uvprojx"/>
        <environment name="iar" load="iar/tsi_v4_normal_mode.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="tsi_v4_low_power_mode" folder="driver_examples/tsi_v4/low_power" doc="readme.txt">
      <description>The tsi_v4_low_power example shows how to use TSI_V4 driver in low power modes:In this example , we make use of the available electrodes on board to show driver usage.1. Firstly, we get the non-touch calibration results as baseline electrode counter;2. Then, we start the Hardware-Trigger scan using interrupt method to wakeup from low power   modes through pad touch;3. Note: you can select which low power mode you want to enter into.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/tsi_v4_low_power_mode.uvprojx"/>
        <environment name="iar" load="iar/tsi_v4_low_power_mode.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble" folder="demo_apps/bubble" doc="readme.txt">
      <description>The EEPROM flash demo application demonstrates the use of the LPI2C IP and driver to read and write data from anexternal EEPROM device.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble.uvprojx"/>
        <environment name="iar" load="iar/bubble.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="driver_examples/flexio/pwm" doc="readme.txt">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="cmsis_driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="demo_apps/shell" doc="readme.txt">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="iar" load="iar/shell.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master" folder="driver_examples/lpi2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave" folder="driver_examples/lpi2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master" folder="driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.txt">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave" folder="driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_transfer_master" folder="driver_examples/lpi2c/polling_b2b_transfer/master" doc="readme.txt">
      <description>The lpi2c_polling_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_transfer_slave" folder="driver_examples/lpi2c/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The lpi2c_polling_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master" folder="cmsis_driver_examples/lpi2c/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The lpi2c_int_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpi2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/lpi2c/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The lpi2c_int_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpi2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master" folder="cmsis_driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_edma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with EDMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpi2c_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave" folder="cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_edma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with EDMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpi2c_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master" folder="driver_examples/lpspi/polling_b2b_transfer/master" doc="readme.txt">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_polling_b2b_transfer_master.c' includes the LPSPI master code.1. LPSPI master send/received data to/from LPSPI slave in polling . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave" folder="driver_examples/lpspi/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_polling_b2b_transfer_slave.c' includes the LPSPI slave code.1. LPSPI master send/received data to/from LPSPI slave in polling . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master" folder="driver_examples/lpspi/interrupt_b2b/master" doc="readme.txt">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_interrupt_b2b_master.c' includes the LPSPI master code.This example does not use the transactional API in LPSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave" folder="driver_examples/lpspi/interrupt_b2b/slave" doc="readme.txt">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file 'dspi_interrupt_b2b_slave.c' includes the LPSPI slave code.This example does not use the transactional API in LPSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_master" folder="driver_examples/lpspi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_interrupt_b2b_transfer_master.c' includes the LPSPI master code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_transfer_slave" folder="driver_examples/lpspi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_interrupt_b2b_transfer example shows how to use LPSPI driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_interrupt_b2b_transfer_slave.c' includes the LPSPI slave code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . (LPSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master" folder="driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.txt">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_edma_b2b_transfer_master.c' includes the LPSPI master code.1. LPSPI master send/received data to/from LPSPI slave in edma . (LPSPI Slave using edma to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave" folder="driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'lpspi_edma_b2b_transfer_slave.c' includes the LPSPI slave code.1. LPSPI master send/received data to/from LPSPI slave in edma . (LPSPI Slave using edma to receive/send the data)</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master" folder="cmsis_driver_examples/lpspi/int_b2b_transfer/master" doc="readme.txt">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_int_b2b_transfer_master.c' includes the LPSPI master code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . </description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpspi_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave" folder="cmsis_driver_examples/lpspi/int_b2b_transfer/slave" doc="readme.txt">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_int_b2b_transfer_slave.c' includes the LPSPI slave code.This example uses the transactional API in LPSPI driver.1. LPSPI master send/received data to/from LPSPI slave in interrupt . </description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpspi_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master" folder="cmsis_driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.txt">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_edma_b2b_transfer_master.c' includes the LPSPI master code.1. LPSPI master send/received data to/from LPSPI slave in edma . </description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpspi_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave" folder="cmsis_driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file 'cmsis_lpspi_edma_b2b_transfer_slave.c' includes the LPSPI slave code.1. LPSPI master send/received data to/from LPSPI slave in edma . </description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpspi_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="TWR-KL28Z72M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="twrkl28z72m" Cversion="1.0.0" condition="device.MKL28Z7_AND_component.lpuart_adapter_AND_component.serial_manager_uart_AND_device.MKL28Z7_startup_AND_driver.clock_AND_driver.common_AND_driver.flash_AND_driver.gpio_AND_driver.lpuart_AND_driver.port_AND_driver.smc_AND_utility.debug_console">
      <description/>
      <files>
        <file category="header" attr="config" name="project_template/board.h"/>
        <file category="sourceC" attr="config" name="project_template/board.c"/>
        <file category="header" attr="config" name="project_template/clock_config.h"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c"/>
        <file category="header" attr="config" name="project_template/pin_mux.h"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c"/>
        <file category="header" attr="config" name="project_template/peripherals.h"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c"/>
      </files>
    </component>
  </components>
</package>
