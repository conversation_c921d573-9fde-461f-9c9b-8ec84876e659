<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso51U68_USB_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware usb Examples Pack for LPCXpresso51U68</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPCXpresso51U68_BSP" vendor="NXP" version="19.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="LPC51U68_DFP" vendor="NXP" version="19.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="dev_cdc_vcom_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_cdc_vcom/bm" doc="readme.txt">
      <description>The Virtual COM project is enumerated as a COM port, which the users can open using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vcom_bm.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vcom_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vcom_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_cdc_vcom/freertos" doc="readme.txt">
      <description>The Virtual COM project is  enumerated as a COM port, which the users can open using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vcom_freertos.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vcom_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vcom_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_cdc_vcom_lite/bm" doc="readme.txt">
      <description>The Virtual COM project enumerated as a COM port, which the users can open using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vcom_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vcom_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_cdc_msc/bm" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_bm.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_cdc_msc/freertos" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_freertos.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_cdc_msc_lite/bm" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_vcom_cdc_vcom_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_cdc_vcom_cdc_vcom/bm" doc="readme.txt">
      <description>The Composite CDC_VCOM_CDC_VCOM project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as two COM port, which can be opened using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_vcom_cdc_vcom_bm.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_vcom_cdc_vcom_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_vcom_cdc_vcom_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_cdc_vcom_cdc_vcom/freertos" doc="readme.txt">
      <description>The Composite CDC_VCOM_CDC_VCOM project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as two COM port, which can be opened using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_vcom_cdc_vcom_freertos.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_vcom_cdc_vcom_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_vcom_cdc_vcom_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_cdc_vcom_cdc_vcom_lite/bm" doc="readme.txt">
      <description>The Composite CDC_VCOM_CDC_VCOM project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as two COM port, which can be opened using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_vcom_cdc_vcom_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_vcom_cdc_vcom_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_mouse_hid_keyboard_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_hid_mouse_hid_keyboard/bm" doc="readme.txt">
      <description>The application is a simple demonstration program based on the MCUXpresso SDK. The application is enumerated as HID-compliant mouse and keyboard devices.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_mouse_hid_keyboard_bm.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_mouse_hid_keyboard_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_mouse_hid_keyboard_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_hid_mouse_hid_keyboard/freertos" doc="readme.txt">
      <description>The application is a simple demonstration program based on the MCUXpresso SDK. The application is enumerated as HID-compliant mouse and keyboard devices.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_mouse_hid_keyboard_freertos.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_mouse_hid_keyboard_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_mouse_hid_keyboard_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_composite_hid_mouse_hid_keyboard_lite/bm" doc="readme.txt">
      <description>The application is a simple demonstration program based on the MCUXpresso SDK. The application is enumerated as HID-compliant mouse and keyboard devices.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_mouse_hid_keyboard_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_mouse_hid_keyboard_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_generic_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_generic/bm" doc="readme.txt">
      <description>The USB HID generic application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a HID-compliant device. A PC application can be used to exchange data with the device.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_generic_bm.uvprojx"/>
        <environment name="csolution" load="dev_hid_generic_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_generic_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_generic/freertos" doc="readme.txt">
      <description>The USB HID generic application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a HID-compliant device. A PC application can be used to exchange data with the device.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_generic_freertos.uvprojx"/>
        <environment name="csolution" load="dev_hid_generic_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_generic_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_generic_lite/bm" doc="readme.txt">
      <description>The USB HID generic application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a HID-compliant device. A PC application can be used to exchange data with the device.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_generic_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_hid_generic_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_mouse/bm" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_bm.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_mouse/freertos" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_freertos.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_freertos_static" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_mouse/freertos_static" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_freertos_static.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_freertos_static.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_hid_mouse_lite/bm" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_ramdisk_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_msc_ramdisk/bm" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk and can be read and written to as a normal U-disk .</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_ramdisk_bm.uvprojx"/>
        <environment name="csolution" load="dev_msc_ramdisk_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_ramdisk_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_msc_ramdisk/freertos" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk and can be read and written to as a normal U-disk .</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_ramdisk_freertos.uvprojx"/>
        <environment name="csolution" load="dev_msc_ramdisk_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_ramdisk_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_msc_ramdisk_lite/bm" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk and can be read and written to as a normal U-disk .</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_ramdisk_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_msc_ramdisk_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_phdc_weighscale_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_phdc_weighscale/bm" doc="readme.txt">
      <description>The USB PHDC WeighScale application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a weight scale device and the HealthLink software is used to interact with thisdevice to simulate...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_phdc_weighscale_bm.uvprojx"/>
        <environment name="csolution" load="dev_phdc_weighscale_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_phdc_weighscale_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_phdc_weighscale/freertos" doc="readme.txt">
      <description>The USB PHDC WeighScale application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a weight scale device and the HealthLink software is used to interact with thisdevice to simulate...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_phdc_weighscale_freertos.uvprojx"/>
        <environment name="csolution" load="dev_phdc_weighscale_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_phdc_weighscale_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_phdc_weighscale_lite/bm" doc="readme.txt">
      <description>The USB PHDC WeighScale application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a weight scale device and the HealthLink software is used to interact with thisdevice to simulate...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_phdc_weighscale_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_phdc_weighscale_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_printer_virtual_plain_text_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_printer_virtual_plain_text/bm" doc="readme.txt">
      <description>The USB virtual printer application is a simple application to demonstrate a virtual printer functionality.Because there is no printer language parsing, the received raw data is output directly in the debug...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_printer_virtual_plain_text_bm.uvprojx"/>
        <environment name="csolution" load="dev_printer_virtual_plain_text_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_printer_virtual_plain_text_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_printer_virtual_plain_text/freertos" doc="readme.txt">
      <description>The USB virtual printer application is a simple application to demonstrate a virtual printer functionality.Because there is no printer language parsing, the received raw data is output directly in the debug...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_printer_virtual_plain_text_freertos.uvprojx"/>
        <environment name="csolution" load="dev_printer_virtual_plain_text_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_printer_virtual_plain_text_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_printer_virtual_plain_text_lite/bm" doc="readme.txt">
      <description>The USB virtual printer application is a simple application to demonstrate a virtual printer functionality.Because there is no printer language parsing, the received raw data is output directly in the debug...See more details in readme document.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_printer_virtual_plain_text_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_printer_virtual_plain_text_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_video_virtual_camera_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_video_virtual_camera/bm" doc="readme.txt">
      <description>The USB video virtual camera application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a camera and users can see the video of the device by using a PC test tool.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_video_virtual_camera_bm.uvprojx"/>
        <environment name="csolution" load="dev_video_virtual_camera_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_video_virtual_camera_freertos" folder="boards/lpcxpresso51u68/usb_examples/usb_device_video_virtual_camera/freertos" doc="readme.txt">
      <description>The USB video virtual camera application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a camera and users can see the video of the device by using a PC test tool.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_video_virtual_camera_freertos.uvprojx"/>
        <environment name="csolution" load="dev_video_virtual_camera_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_video_virtual_camera_lite_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_device_video_virtual_camera_lite/bm" doc="readme.txt">
      <description>The USB video virtual camera application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a camera and users can see the video of the device by using a PC test tool.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_video_virtual_camera_lite_bm.uvprojx"/>
        <environment name="csolution" load="dev_video_virtual_camera_lite_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_audio_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_rom_device_audio/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create and USB AUDIO class device. This device supports 2 channel audio in (MIC/LINE_IN) and 2 channel audio out (SPEAKERS/HEADPHONE).</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_audio_bm.uvprojx"/>
        <environment name="csolution" load="rom_dev_audio_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_cdc_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_rom_device_cdc/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create a virtual comm port. When the USB on J3 is connected to a PC, the host would recognize the USB connection as a new serial port.</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_cdc_bm.uvprojx"/>
        <environment name="iar" load="iar/rom_dev_cdc_bm.ewp"/>
        <environment name="csolution" load="rom_dev_cdc_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rom_dev_msc_ram_bm" folder="boards/lpcxpresso51u68/usb_examples/usb_rom_device_msc_ram/bm" doc="readme.txt">
      <description>The example shows how to use USBD ROM stack to create a RAM disk. When the USB on J3 is connected to a PC, the host would recognize the USB connection as a mass storage device. Note that the storage happens on the RAM and is not a persistent storage..</description>
      <board name="LPCXpresso51U68" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rom_dev_msc_ram_bm.uvprojx"/>
        <environment name="iar" load="iar/rom_dev_msc_ram_bm.ewp"/>
        <environment name="csolution" load="rom_dev_msc_ram_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
