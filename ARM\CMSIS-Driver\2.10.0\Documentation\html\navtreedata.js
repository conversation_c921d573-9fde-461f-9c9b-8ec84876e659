/*
 @licstart  The following is the entire license notice for the JavaScript code in this file.

 The MIT License (MIT)

 Copyright (C) 1997-2020 by <PERSON>

 Permission is hereby granted, free of charge, to any person obtaining a copy of this software
 and associated documentation files (the "Software"), to deal in the Software without restriction,
 including without limitation the rights to use, copy, modify, merge, publish, distribute,
 sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in all copies or
 substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
 BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
 DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

 @licend  The above is the entire license notice for the JavaScript code in this file
*/
var NAVTREE =
[
  [ "CMSIS-Driver Implementations", "index.html", [
    [ "Overview", "index.html", "index" ],
    [ "Ethernet", "driver_eth.html", [
      [ "Driver Implementations", "driver_eth.html#driver_eth_devices", null ],
      [ "Multiple Driver Instances", "driver_eth.html#driver_eth_multiple", null ]
    ] ],
    [ "USB", "driver_USB.html", [
      [ "Driver Implementations", "driver_USB.html#driver_usb", [
        [ "EHCI", "driver_USB.html#driver_EHCI", null ],
        [ "OHCI", "driver_USB.html#driver_OHCI", null ]
      ] ]
    ] ],
    [ "WiFi", "driver_WiFi.html", [
      [ "Driver Implementations", "driver_WiFi.html#driver_wifi_devices", [
        [ "DA16200", "driver_WiFi.html#driver_DA16200", null ],
        [ "ESP32", "driver_WiFi.html#driver_ESP32", null ],
        [ "ESP8266", "driver_WiFi.html#driver_ESP8266", null ],
        [ "ISM43362", "driver_WiFi.html#driver_ISM43362", [
          [ "Flashing a different firmware to ISMART43362-E Shield", "driver_WiFi.html#ismart43362_e_firmware_download", null ]
        ] ],
        [ "WizFi360", "driver_WiFi.html#driver_WizFi360", null ]
      ] ]
    ] ],
    [ "Flash", "driver_Flash.html", [
      [ "Driver Implementations", "driver_Flash.html#driver_Flash_devices", null ],
      [ "Multiple Driver Instances", "driver_Flash.html#driver_Flash_multiple", null ]
    ] ],
    [ "I2C", "driver_I2C.html", null ],
    [ "NAND", "driver_NAND.html", null ],
    [ "SPI", "driver_SPI.html", null ],
    [ "Shield layer", "shield_layer.html", [
      [ "WiFi Shields", "shield_layer.html#shield_WiFi", [
        [ "Inventek ISMART43362-E", "shield_layer.html#shield_Inventek_ISMART43362-E", null ],
        [ "Sparkfun DA16200", "shield_layer.html#shield_Sparkfun_DA16200", null ],
        [ "Sparkfun ESP8266", "shield_layer.html#shield_Sparkfun_ESP8266", null ],
        [ "WizNet WizFi360-EVB", "shield_layer.html#shield_WizNet_WizFi360-EVB", null ]
      ] ]
    ] ]
  ] ]
];

var NAVTREEINDEX =
[
"driver_Flash.html"
];

var SYNCONMSG = 'click to disable panel synchronisation';
var SYNCOFFMSG = 'click to enable panel synchronisation';