<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1060-EVKB_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for MIMXRT1060-EVKB</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.1.0" date="2023-03-23">NXP CMSIS Packs based on MCUXpresso SDK 2.13.1</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.1.0" date="2022-09-28">NXP CMSIS Packs based on MCUXpresso SDK 2.12.1</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.0.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1062_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT1060-EVKB">
      <description>i.MX RT1060 Evaluation Kit</description>
      <image small="boards/evkbmimxrt1060/evkbmimxrt1060.png"/>
      <book category="overview" name="https://www.nxp.com/pip/MIMXRT1060-EVK" title="i.MX RT1060 Evaluation Kit" public="true"/>
      <mountedDevice Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MIMXRT1061.internal_condition">
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1062.internal_condition">
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.MIMXRT1062xxxxB.internal_condition">
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.evkbmimxrt1060.condition_id">
      <require condition="allOf.board=evkbmimxrt1060, component.lpuart_adapter, device_id=MIMXRT1062xxxxB, device.MIMXRT1062_startup, driver.clock, driver.common, driver.igpio, driver.iomuxc, driver.lpuart, driver.nic301, driver.xip_board.evkbmimxrt1060, driver.xip_device, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=evkbmimxrt1060, component.lpuart_adapter, device_id=MIMXRT1062xxxxB, device.MIMXRT1062_startup, driver.clock, driver.common, driver.igpio, driver.iomuxc, driver.lpuart, driver.nic301, driver.xip_board.evkbmimxrt1060, driver.xip_device, anyOf=allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.evkbmimxrt1060.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require condition="device_id.MIMXRT1062xxxxB.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iomuxc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="nic301"/>
      <require Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkbmimxrt1060 xip"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xip_device"/>
      <require condition="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="board.evkbmimxrt1060.internal_condition">
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=component.serial_manager, utility.assert, utility.debug_console, allOf=utility.assert_lite, utility.debug_console_lite.internal_condition">
      <accept condition="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition"/>
      <accept condition="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.component.serial_manager, utility.assert, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="allOf.utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="driver.xip_board.evkbmimxrt1060.condition_id">
      <require condition="allOf.driver.common, device=MIMXRT1062.internal_condition"/>
    </condition>
    <condition id="allOf.driver.common, device=MIMXRT1062.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require condition="device.MIMXRT1062.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc_12b1msps_sar_interrupt" folder="boards/evkbmimxrt1060/driver_examples/adc/interrupt" doc="readme.md">
      <description>The adc_interrupt example shows how to use interrupt with adc driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the adc'ssample input. When running the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_12b1msps_sar_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc_12b1msps_sar_interrupt.ewp"/>
        <environment name="csolution" load="adc_12b1msps_sar_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_12b1msps_sar_polling" folder="boards/evkbmimxrt1060/driver_examples/adc/polling" doc="readme.md">
      <description>The adc_polling example shows the simplest way to use adc driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the adc'ssample input. When running the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_12b1msps_sar_polling.uvprojx"/>
        <environment name="iar" load="iar/adc_12b1msps_sar_polling.ewp"/>
        <environment name="csolution" load="adc_12b1msps_sar_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_etc_hardware_trigger_conv" folder="boards/evkbmimxrt1060/driver_examples/adc_etc/adc_etc_hardware_trigger_conv" doc="readme.md">
      <description>The adc_etc_software_trigger_conv example shows how to use the ADC_ETC to generate a ADC trigger by PIT channel0 trigger.Every 1 second, PIT channel0 would send a trigger signal to ADC_ETC, which can arbitrate and...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_etc_hardware_trigger_conv.uvprojx"/>
        <environment name="iar" load="iar/adc_etc_hardware_trigger_conv.ewp"/>
        <environment name="csolution" load="adc_etc_hardware_trigger_conv.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="adc_etc_software_trigger_conv" folder="boards/evkbmimxrt1060/driver_examples/adc_etc/adc_etc_software_trigger_conv" doc="readme.md">
      <description>The adc_etc_software_trigger_conv example shows how to use the ADC_ETC to generate a ADC trigger by software trigger.In this example, the ADC is configured with hardware trigger. Once ADC gets the trigger from the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc_etc_software_trigger_conv.uvprojx"/>
        <environment name="iar" load="iar/adc_etc_software_trigger_conv.ewp"/>
        <environment name="csolution" load="adc_etc_software_trigger_conv.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bee" folder="boards/evkbmimxrt1060/driver_examples/bee" doc="readme.md">
      <description>This driver example should demonstrate how to setup BEE driver for on the fly decryption of data stored in QSPI memory region. The BEE is configuerd to decrypt all data starting from location 0x6000_0000 to...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bee.uvprojx"/>
        <environment name="iar" load="iar/bee.ewp"/>
        <environment name="csolution" load="bee.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral" folder="boards/evkbmimxrt1060/demo_apps/bubble_peripheral" doc="readme.md">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis....See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral.ewp"/>
        <environment name="csolution" load="bubble_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cache" folder="boards/evkbmimxrt1060/driver_examples/cache" doc="readme.md">
      <description>The cache example shows how to use memory cache driver.In this example, many memory (such as SDRAM, etc) and DMA will be used to show the example.Those memory is both accessible for cpu and DMA. For the memory data...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cache.uvprojx"/>
        <environment name="iar" load="iar/cache.ewp"/>
        <environment name="csolution" load="cache.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/canfd/interrupt_transfer" doc="readme.md">
      <description>The canfd_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/canfd_interrupt_transfer.ewp"/>
        <environment name="csolution" load="canfd_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback" folder="boards/evkbmimxrt1060/driver_examples/canfd/loopback" doc="readme.md">
      <description>The canfd_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback.ewp"/>
        <environment name="csolution" load="canfd_loopback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_loopback_transfer" folder="boards/evkbmimxrt1060/driver_examples/canfd/loopback_transfer" doc="readme.md">
      <description>The canfd_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx Message...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_loopback_transfer.uvprojx"/>
        <environment name="iar" load="iar/canfd_loopback_transfer.ewp"/>
        <environment name="csolution" load="canfd_loopback_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="canfd_ping_pong_buffer_transfer" folder="boards/evkbmimxrt1060/driver_examples/canfd/ping_pong_buffer_transfer" doc="readme.md">
      <description>The canfd_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CANFD frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/canfd_ping_pong_buffer_transfer.uvprojx"/>
        <environment name="iar" load="iar/canfd_ping_pong_buffer_transfer.ewp"/>
        <environment name="csolution" load="canfd_ping_pong_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ccm_clockout" folder="boards/evkbmimxrt1060/driver_examples/clockout" doc="readme.md">
      <description>The ccm_clockout driver example shows how to output the internal clock signal. In this driver example, users can choose the clock signal to be outputted, and the divider of the output clock signal. By probing the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ccm_clockout.uvprojx"/>
        <environment name="csolution" load="ccm_clockout.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="boards/evkbmimxrt1060/driver_examples/cmp/interrupt" doc="readme.md">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input....See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
        <environment name="csolution" load="cmp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="boards/evkbmimxrt1060/driver_examples/cmp/polling" doc="readme.md">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
        <environment name="csolution" load="cmp_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_enet_txrx_transfer" folder="boards/evkbmimxrt1060/cmsis_driver_examples/enet/txrx_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_enet_txrx_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_enet_txrx_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_master" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_edma_b2b_transfer_slave" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_master" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpi2c/int_b2b_transfer/master" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpi2c_int_b2b_transfer_slave" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpi2c/int_b2b_transfer/slave" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpi2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpi2c_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_master" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_edma_b2b_transfer_slave" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The cmsis_lpspi_edma_b2b_transfer example shows how to use LPSPI CMSIS driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_master" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpspi/int_b2b_transfer/master" doc="readme.md">
      <description>The cmsis_lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpspi_int_b2b_transfer_slave" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpspi/int_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_int_b2b_transfer example shows how to use LPSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="csolution" load="cmsis_lpspi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpuart/edma_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="boards/evkbmimxrt1060/cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="cmsis_lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_ccir656" folder="boards/evkbmimxrt1060/driver_examples/csi/ccir656" doc="readme.md">
      <description>The CSI CCIR656 project shows how to receive the camera data using CSI driver,the camera interface is CCIR656. In this example, you will see the camera inputimage shown in the LCD. Please note that the camera input...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_ccir656.uvprojx"/>
        <environment name="csolution" load="csi_ccir656.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_raw8" folder="boards/evkbmimxrt1060/driver_examples/csi/raw8" doc="readme.md">
      <description>This project shows how to receive the camera RAW8 data using CSI driver. In this example, the RAW8 data is convert to RGB565 data, then shown in the panel.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_raw8.uvprojx"/>
        <environment name="csolution" load="csi_raw8.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="csi_rgb565" folder="boards/evkbmimxrt1060/driver_examples/csi/rgb565" doc="readme.md">
      <description>The CSI RGB565 project shows how to receive the camera data using CSI driver.In this example, you will see the camera input image shown in the LCD. Pleasenote that the camera input image resolution might be smaller...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/csi_rgb565.uvprojx"/>
        <environment name="csolution" load="csi_rgb565.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dcp" folder="boards/evkbmimxrt1060/driver_examples/dcp" doc="readme.md">
      <description>The DCP Example project is a demonstration program that uses the KSDK software implementseveral cryptography algorithms using the DCP software driver.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dcp.uvprojx"/>
        <environment name="iar" load="iar/dcp.ewp"/>
        <environment name="csolution" load="dcp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_channel_link" folder="boards/evkbmimxrt1060/driver_examples/edma/channel_link" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_channel_link.uvprojx"/>
        <environment name="iar" load="iar/edma_channel_link.ewp"/>
        <environment name="csolution" load="edma_channel_link.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_interleave_transfer" folder="boards/evkbmimxrt1060/driver_examples/edma/interleave_transfer" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma_interleave_transfer.ewp"/>
        <environment name="csolution" load="edma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="boards/evkbmimxrt1060/driver_examples/edma/memory_to_memory" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/edma_memory_to_memory.ewp"/>
        <environment name="csolution" load="edma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_ping_pong_transfer" folder="boards/evkbmimxrt1060/driver_examples/edma/ping_pong_transfer" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_ping_pong_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma_ping_pong_transfer.ewp"/>
        <environment name="csolution" load="edma_ping_pong_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="boards/evkbmimxrt1060/driver_examples/edma/scatter_gather" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" load="iar/edma_scatter_gather.ewp"/>
        <environment name="csolution" load="edma_scatter_gather.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_wrap_transfer" folder="boards/evkbmimxrt1060/driver_examples/edma/wrap_transfer" doc="readme.md">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/edma_wrap_transfer.ewp"/>
        <environment name="csolution" load="edma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="elcdif_lut" folder="boards/evkbmimxrt1060/driver_examples/elcdif/lut" doc="readme.md">
      <description>The ELCDIF LUT project shows how to use the ELCDIF LUT to convert 8-bit input pixelto 24-bit output pixel. There are two LUT memories, this project uses one inputframe buffer, and swithes between the two memories, so...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/elcdif_lut.uvprojx"/>
        <environment name="iar" load="iar/elcdif_lut.ewp"/>
        <environment name="csolution" load="elcdif_lut.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="elcdif_rgb" folder="boards/evkbmimxrt1060/driver_examples/elcdif/rgb" doc="readme.md">
      <description>The ELCDIF RGB project shows how to drive the RGB interface LCD using eLCDIF driver.If this example runs correctly, a rectangle is moving in the screen, and the colorchanges every time it reaches the edges of the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/elcdif_rgb.uvprojx"/>
        <environment name="iar" load="iar/elcdif_rgb.ewp"/>
        <environment name="csolution" load="elcdif_rgb.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_basic" folder="boards/evkbmimxrt1060/driver_examples/enc/basic" doc="readme.md">
      <description>The enc_basic example shows how to quickly start using ENC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the basic application. When...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_basic.uvprojx"/>
        <environment name="iar" load="iar/enc_basic.ewp"/>
        <environment name="csolution" load="enc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enc_index_interrupt" folder="boards/evkbmimxrt1060/driver_examples/enc/index_interrupt" doc="readme.md">
      <description>The enc_index_interrupt example shows how to use the interrupt of ENC module with ENC driver.In this example, user needs to connect a real encoder to the board. Actually, only PHASE A and PHASE B are enough for the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enc_index_interrupt.uvprojx"/>
        <environment name="iar" load="iar/enc_index_interrupt.ewp"/>
        <environment name="csolution" load="enc_index_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_ptp1588_transfer" folder="boards/evkbmimxrt1060/driver_examples/enet/txrx_ptp1588_transfer" doc="readme.md">
      <description>The enet_rxtx_ptp1588 example shows the way to use ENET driver to receive and transmit frame in the 1588 feature required cases.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_ptp1588_transfer.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_ptp1588_transfer.ewp"/>
        <environment name="csolution" load="enet_txrx_ptp1588_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="enet_txrx_transfer" folder="boards/evkbmimxrt1060/driver_examples/enet/txrx_transfer" doc="readme.md">
      <description>The enet_rxtx example shows the simplest way to use ENET driver for simple frame receive and transmit.1. This example shows how to initialize the ENET MAC.2. How to use ENET MAC to receive and transmit frame.The...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/enet_txrx_transfer.uvprojx"/>
        <environment name="iar" load="iar/enet_txrx_transfer.ewp"/>
        <environment name="csolution" load="enet_txrx_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm" folder="boards/evkbmimxrt1060/driver_examples/ewm" doc="readme.md">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm.uvprojx"/>
        <environment name="iar" load="iar/ewm.ewp"/>
        <environment name="csolution" load="ewm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flash_component_nor" folder="boards/evkbmimxrt1060/component_examples/flash_component/flexspi_nor" doc="readme.md">
      <description>nor flash demo shows the use of nor flash component to erase, program, and read an external nor flash device.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flash_component_nor.uvprojx"/>
        <environment name="csolution" load="flash_component_nor.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexcan/interrupt_transfer" doc="readme.md">
      <description>The flexcan_interrupt example shows how to use FlexCAN driver in none-blocking interrupt way:In this example, 2 boards are connected through CAN bus. Endpoint A(board A) send a CAN Message toEndpoint B(board B) when...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexcan_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback" folder="boards/evkbmimxrt1060/driver_examples/flexcan/loopback" doc="readme.md">
      <description>The flexcan_loopback_functional example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback.ewp"/>
        <environment name="csolution" load="flexcan_loopback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_edma_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexcan/loopback_edma_transfer" doc="readme.md">
      <description>The flexcan_loopback_edma example shows how to use the EDMA version transactional driver to receiveCAN Message from Rx FIFO:To demonstrates this example, only one board is needed. The example will config one FlexCAN...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_edma_transfer.ewp"/>
        <environment name="csolution" load="flexcan_loopback_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_loopback_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexcan/loopback_transfer" doc="readme.md">
      <description>The flexcan_loopback example shows how to use the loopback test mode to debug your CAN Bus design:To demonstrates this example, only one board is needed. The example will config one FlexCAN MessageBuffer to Rx...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_loopback_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_loopback_transfer.ewp"/>
        <environment name="csolution" load="flexcan_loopback_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexcan_ping_pong_buffer_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexcan/ping_pong_buffer_transfer" doc="readme.md">
      <description>The flexcan_ping_pong_buffer_transfer example shows how to use the FlexCAN queue feature to create 2 simulate FIFOs that can receive CAN frames:In this example, 2 boards are connected through CAN bus. Endpoint...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexcan_ping_pong_buffer_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexcan_ping_pong_buffer_transfer.ewp"/>
        <environment name="csolution" load="flexcan_ping_pong_buffer_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_i2c_interrupt_lpi2c_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio3/i2c/interrupt_lpi2c_transfer" doc="readme.md">
      <description>The flexio3_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_i2c_interrupt_lpi2c_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio3_i2c_interrupt_lpi2c_transfer.ewp"/>
        <environment name="csolution" load="flexio3_i2c_interrupt_lpi2c_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_i2s_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio3/i2s/interrupt_transfer" doc="readme.md">
      <description>The flexio3_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_i2s_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="flexio3_i2s_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_pwm" folder="boards/evkbmimxrt1060/driver_examples/flexio3/pwm" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_pwm.uvprojx"/>
        <environment name="iar" load="iar/flexio3_pwm.ewp"/>
        <environment name="csolution" load="flexio3_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_spi_int_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/flexio3/spi/int_b2b_transfer/master" doc="readme.md">
      <description>The flexio3_spi_int_b2b_transfer_master example shows how to use flexio spi driver as master in interrupt way. In this example, a flexio simulated master is connected to a flexio simulated spi slave on another board.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio3_spi_int_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="flexio3_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_spi_int_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/flexio3/spi/int_b2b_transfer/slave" doc="readme.md">
      <description>The flexio3_spi_int_b2b_transfer_slave example shows how to use flexio spi driver as slave in interrupt way. In this example, a flexio simulated slave is connected to a flexio simulated spi master on another board.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio3_spi_int_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio3_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_uart_int_rb_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio3/uart/int_rb_transfer" doc="readme.md">
      <description>The flexio3_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio3_uart_int_rb_transfer.ewp"/>
        <environment name="csolution" load="flexio3_uart_int_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_uart_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio3/uart/interrupt_transfer" doc="readme.md">
      <description>The flexio3_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio3_uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexio3_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio3_uart_polling_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio3/uart/polling_transfer" doc="readme.md">
      <description>The flexio3_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio3_uart_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio3_uart_polling_transfer.ewp"/>
        <environment name="csolution" load="flexio3_uart_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2c_interrupt_lpi2c_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/i2c/interrupt_lpi2c_transfer" doc="readme.md">
      <description>The flexio_lpi2c_interrupt example shows how to use flexio i2c master  driver in interrupt way:In this example, a flexio simulated i2c master connect to a LPI2C slave</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2c_interrupt_lpi2c_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_i2c_interrupt_lpi2c_transfer.ewp"/>
        <environment name="csolution" load="flexio_i2c_interrupt_lpi2c_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_edma_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/i2s/edma_transfer" doc="readme.md">
      <description>The flexio_i2s_EDMA example shows how to use flexio_i2s driver with EDMA:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using EDMA.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_edma_transfer.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_i2s_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/i2s/interrupt_transfer" doc="readme.md">
      <description>The flexio_i2s_interrupt example shows how to use flexio_i2s driver with interrupt:In this example, flexio acts as I2S module to record data from line-in line and playbacks the recorded data at the same time using interrupt.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_i2s_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="flexio_i2s_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_pwm" folder="boards/evkbmimxrt1060/driver_examples/flexio/pwm" doc="readme.md">
      <description>This demo describes how to use SDK drivers to implement the PWM feature by FLEXIO IP module. It outputs the PWM singal with fixed frequency defined by "DEMO_FLEXIO_FREQUENCY" in source code and dynamic duty from 99 to 1 to one of the FLEXIO pin.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_pwm.uvprojx"/>
        <environment name="iar" load="iar/flexio_pwm.ewp"/>
        <environment name="csolution" load="flexio_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/edma_b2b_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_edma example shows how to use flexio spi master  driver in edma way:In this example, a flexio simulated master connect to a flexio simulated spi slave .</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_edma example shows how to use flexio spi slave  driver in dma way:In this example, a flexio simulated slave connect to a flexio simulated spi master.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/edma_lpspi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_edma_lpspi_slave example shows how to use flexio spi master driver in edma way:In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/edma_lpspi_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_edma_lpspi_master example shows how to use flexio spi slave driver in edma way:In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_edma_lpspi_transfer_slave_dynamic" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/edma_lpspi_transfer/slave_dynamic" doc="readme.md">
      <description>The flexio_spi_slave_dynamic example shows how to use flexio spi slave driver in edma way, In this example, a flexio simulated slave connect to a lpspi master. The CS signal remains low during transfer, after master...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_edma_lpspi_transfer_slave_dynamic.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_edma_lpspi_transfer_slave_dynamic.ewp"/>
        <environment name="csolution" load="flexio_spi_edma_lpspi_transfer_slave_dynamic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/int_b2b_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_interrupt example shows how to use flexio spi master  driver in interrupt way:In this example, a flexio simulated master connect to a flexio simulated spi slave .</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_int_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/int_b2b_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_interrupt example shows how to use flexio spi slave  driver in interrupt way:In this example, a flexio simulated slave connect to a flexio simulated spi master.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_int_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/int_lpspi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_interrupt_lpspi_slave example shows how to use flexio spi master driver in interrupt way. In this example, a flexio simulated master connect to a lpspi slave .</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_int_lpspi_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/int_lpspi_transfer/slave" doc="readme.md">
      <description>The flexio_spi_slave_interrupt_lpspi_master example shows how to use flexio spi slave driver in interrupt way. In this example, a flexio simulated slave connect to a lpspi master.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_int_lpspi_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_int_lpspi_transfer_slave.ewp"/>
        <environment name="csolution" load="flexio_spi_int_lpspi_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_spi_polling_lpspi_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/flexio/spi/polling_lpspi_transfer/master" doc="readme.md">
      <description>The flexio_spi_master_pooling_lpspi_slave example shows how to use flexio spi master driver in polling way. In this example, a flexio simulated master connect to a lpspi slave.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_spi_polling_lpspi_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/flexio_spi_polling_lpspi_transfer_master.ewp"/>
        <environment name="csolution" load="flexio_spi_polling_lpspi_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_edma_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/uart/edma_transfer" doc="readme.md">
      <description>The flexio_uart_edma example shows how to use flexio uart driver in edma way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to the board.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_edma_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_int_rb_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/uart/int_rb_transfer" doc="readme.md">
      <description>The flexio_uart_interrupt_ring_buffer example shows how to use flexio uart driver in interrupt way withRX ring buffer enabled:In this example, a flexio simulated uart connect to PC through USB-Serial, the board...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_int_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_int_rb_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_int_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/uart/interrupt_transfer" doc="readme.md">
      <description>The flexio_uart_interrupt example shows how to use flexio uart driver in interrupt way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send back all charactersthat PC send to...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexio_uart_polling_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexio/uart/polling_transfer" doc="readme.md">
      <description>The flexio_uart_polling example shows how to use flexio uart driver in polling way:In this example, a flexio simulated uart connect to PC through USB-Serial, the board will send backall characters that PC send to the board.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexio_uart_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexio_uart_polling_transfer.ewp"/>
        <environment name="csolution" load="flexio_uart_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexram_ram_access" folder="boards/evkbmimxrt1060/driver_examples/flexram/ram_access" doc="readme.md">
      <description>The FLEXRAM project is a simple demonstration program of the SDK FLEXRAM driver. It allocate the on-chip ram and then access the OCRAM to demo magic address and access error interrupt.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexram_ram_access.uvprojx"/>
        <environment name="iar" load="iar/flexram_ram_access.ewp"/>
        <environment name="csolution" load="flexram_ram_access.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexram_ram_allocate" folder="boards/evkbmimxrt1060/driver_examples/flexram/ram_allocate" doc="readme.md">
      <description>The FLEXRAM project is a simple demonstration program of the SDK FLEXRAM driver. It allocate the on-chip ram and then access the OCRAM to demo magic address and access error interrupt.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexram_ram_allocate.uvprojx"/>
        <environment name="iar" load="iar/flexram_ram_allocate.ewp"/>
        <environment name="csolution" load="flexram_ram_allocate.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_hyper_flash_polling_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexspi/hyper_flash/polling_transfer" doc="readme.md">
      <description>The flexspi_hyper_flash_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Hyper flash connected with FLEXSPI. Some simple flash...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_hyper_flash_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexspi_hyper_flash_polling_transfer.ewp"/>
        <environment name="csolution" load="flexspi_hyper_flash_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_edma_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexspi/nor/edma_transfer" doc="readme.md">
      <description>The flexspi_nor_edma_transfer example shows how to use flexspi driver with dma:In this example, flexspi will send data and operate the external nor flash connected with FLEXSPI. Some simple flash command will be...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_edma_transfer.uvprojx"/>
        <environment name="csolution" load="flexspi_nor_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="flexspi_nor_polling_transfer" folder="boards/evkbmimxrt1060/driver_examples/flexspi/nor/polling_transfer" doc="readme.md">
      <description>The flexspi_nor_polling_transfer example shows how to use flexspi driver with polling:In this example, flexspi will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexspi_nor_polling_transfer.uvprojx"/>
        <environment name="iar" load="iar/flexspi_nor_polling_transfer.ewp"/>
        <environment name="csolution" load="flexspi_nor_polling_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fsl_romapi" folder="boards/evkbmimxrt1060/driver_examples/fsl_romapi" doc="readme.md">
      <description>The fsl_romapi example shows how to use flexspi rom api In this example, rom api will send data and operate the external Nor flash connected with FLEXSPI. Some simple flash command willbe executed, such as Write...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fsl_romapi.uvprojx"/>
        <environment name="iar" load="iar/fsl_romapi.ewp"/>
        <environment name="csolution" load="fsl_romapi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_capture" folder="boards/evkbmimxrt1060/driver_examples/gpt/capture" doc="readme.md">
      <description>The gpt_capture project is a simple demonstration program of the SDK GPT driver's input capture feature.The example sets up a GPT channel for rise-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_capture.uvprojx"/>
        <environment name="iar" load="iar/gpt_capture.ewp"/>
        <environment name="csolution" load="gpt_capture.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpt_timer" folder="boards/evkbmimxrt1060/driver_examples/gpt/timer" doc="readme.md">
      <description>The gpt_timer project is a simple demonstration program of the SDK GPT driver. It sets up the GPThardware block to trigger a periodic interrupt after every 1 second. When the GPT interrupt is triggereda message a printed on the UART terminal.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpt_timer.uvprojx"/>
        <environment name="iar" load="iar/gpt_timer.ewp"/>
        <environment name="csolution" load="gpt_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/evkbmimxrt1060/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world_swo" folder="boards/evkbmimxrt1060/demo_apps/hello_world_swo" doc="readme.md">
      <description>The Hello World SWO demo prints the "SWO: Hello World" string to the SWO viewer. The purpose of this demo is to show how to use the swo, and to provide a simple project for debugging and further development.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world_swo.uvprojx"/>
        <environment name="csolution" load="hello_world_swo.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_input_interrupt" folder="boards/evkbmimxrt1060/driver_examples/gpio/input_interrupt" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear registers for each GPIO pin output register. </description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/igpio_input_interrupt.ewp"/>
        <environment name="csolution" load="igpio_input_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="igpio_led_output" folder="boards/evkbmimxrt1060/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/igpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/igpio_led_output.ewp"/>
        <environment name="csolution" load="igpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iled_blinky" folder="boards/evkbmimxrt1060/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iled_blinky.uvprojx"/>
        <environment name="csolution" load="iled_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="kpp" folder="boards/evkbmimxrt1060/driver_examples/kpp" doc="readme.md">
      <description>The KPP Example project is a demonstration program that uses the KSDK software to manipulate the Keypad MATRIX.The example is use the continuous column and rows as 4*4 or 8*8 matrix to show the example.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/kpp.uvprojx"/>
        <environment name="iar" load="iar/kpp.ewp"/>
        <environment name="csolution" load="kpp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/edma_b2b_transfer/master" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with EDMA:In this example, one lpi2c instance as master and another lpi2c instance on the other board...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_edma_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/edma_b2b_transfer/slave" doc="readme.md">
      <description>The lpi2c_edma_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with a EDMA master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/interrupt" doc="readme.md">
      <description>The lpi2c_functional_interrupt example shows how to use lpi2c functional driver to build a interrupt based application:In this example , one lpi2c instance used as lpi2c master and another lpi2c instance used as...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/interrupt_b2b_transfer/master" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_master example shows how to use lpi2c driver as master to do board to board transfer with interrupt:In this example, one lpi2c instance as master and another lpi2c instance on the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_interrupt_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/interrupt_b2b_transfer/slave" doc="readme.md">
      <description>The lpi2c_interrupt_b2b_transfer_slave example shows how to use lpi2c driver as slave to do board to board transfer with interrupt:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpi2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_master" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/polling_b2b/master" doc="readme.md">
      <description>The lpi2c_polling_b2b_master example shows how to use lpi2c driver as master to do board to board transfer using polling method:In this example, one lpi2c instance as master and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpi2c_polling_b2b_slave" folder="boards/evkbmimxrt1060/driver_examples/lpi2c/polling_b2b/slave" doc="readme.md">
      <description>The lpi2c_polling_b2b_slave example shows how to use lpi2c driver as slave to do board to board transfer with a polling master:In this example, one lpi2c instance as slave and another lpi2c instance on the other...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpi2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpi2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="lpi2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/lpspi/edma_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_edma_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/lpspi/edma_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_edma_b2b_transfer example shows how to use LPSPI driver in edma way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_edma_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_edma_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_master" folder="boards/evkbmimxrt1060/driver_examples/lpspi/interrupt_b2b/master" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_master.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_interrupt_b2b_slave" folder="boards/evkbmimxrt1060/driver_examples/lpspi/interrupt_b2b/slave" doc="readme.md">
      <description>The lpspi_interrupt_b2b example shows how to use LPSPI driver in interrupt way:In this example , we need two boards , one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_interrupt_b2b_slave.ewp"/>
        <environment name="csolution" load="lpspi_interrupt_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_loopback" folder="boards/evkbmimxrt1060/demo_apps/lpspi_loopback" doc="readme.md">
      <description>The lpspi_loopback demo shows how the lpspi do a loopback transfer, LPSPImaster will transmit data to itself, so please connect the SOUT pin to SIN pin directly.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_loopback.uvprojx"/>
        <environment name="iar" load="iar/lpspi_loopback.ewp"/>
        <environment name="csolution" load="lpspi_loopback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_master" folder="boards/evkbmimxrt1060/driver_examples/lpspi/polling_b2b_transfer/master" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpspi_polling_b2b_transfer_slave" folder="boards/evkbmimxrt1060/driver_examples/lpspi/polling_b2b_transfer/slave" doc="readme.md">
      <description>The lpspi_polling_b2b_transfer example shows how to use LPSPI driver in polling way:In this example , we need two boards, one board used as LPSPI master and another board used as LPSPI slave.The file...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpspi_polling_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpspi_polling_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_9bit_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/lpuart/9bit_interrupt_transfer" doc="readme.md">
      <description>The lpuart_9bit_interrupt_transfer example shows how to use lpuart driver in 9-bit mode in multi-slave system. Master can send data to slave with certain address specifically, and slave can only receive data when it...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_9bit_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_9bit_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_9bit_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_rb_transfer" folder="boards/evkbmimxrt1060/driver_examples/lpuart/edma_rb_transfer" doc="readme.md">
      <description>The lpuart_edma Ring Buffer Example project is to demonstrate usage of the KSDK lpuart driver.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_edma_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="boards/evkbmimxrt1060/driver_examples/lpuart/edma_transfer" doc="readme.md">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer.ewp"/>
        <environment name="csolution" load="lpuart_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_hardware_flow_control" folder="boards/evkbmimxrt1060/driver_examples/lpuart/hardware_flow_control" doc="readme.md">
      <description>The lpuart_hardware_flow_control Example project is to demonstrate usage of the hardware flow control function.This example will send data to itself(loopback), and hardware flow control will be enabled in the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_hardware_flow_control.uvprojx"/>
        <environment name="iar" load="iar/lpuart_hardware_flow_control.ewp"/>
        <environment name="csolution" load="lpuart_hardware_flow_control.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="boards/evkbmimxrt1060/driver_examples/lpuart/interrupt" doc="readme.md">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
        <environment name="csolution" load="lpuart_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="boards/evkbmimxrt1060/driver_examples/lpuart/interrupt_rb_transfer" doc="readme.md">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_rb_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/lpuart/interrupt_transfer" doc="readme.md">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer_seven_bits" folder="boards/evkbmimxrt1060/driver_examples/lpuart/interrupt_transfer_seven_bits" doc="readme.md">
      <description>The lpuart_interrupt_transfer_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_interrupt_transfer_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="boards/evkbmimxrt1060/driver_examples/lpuart/polling" doc="readme.md">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
        <environment name="csolution" load="lpuart_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling_seven_bits" folder="boards/evkbmimxrt1060/driver_examples/lpuart/polling_seven_bits" doc="readme.md">
      <description>The lpuart_polling_seven_bits Example project is to demonstrate usage of the KSDK lpuart driver with seven data bits feature enabled.In the example, you can send characters to the console back and they will be...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling_seven_bits.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling_seven_bits.ewp"/>
        <environment name="csolution" load="lpuart_polling_seven_bits.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/evkbmimxrt1060/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ocotp_example" folder="boards/evkbmimxrt1060/driver_examples/ocotp" doc="readme.md">
      <description>The ocotp driver example shows how to operation the OCOTP register with driver API.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ocotp_example.uvprojx"/>
        <environment name="iar" load="iar/ocotp_example.ewp"/>
        <environment name="csolution" load="ocotp_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="boards/evkbmimxrt1060/driver_examples/pit" doc="readme.md">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" load="iar/pit.ewp"/>
        <environment name="csolution" load="pit.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_bm" folder="boards/evkbmimxrt1060/demo_apps/power_mode_switch/bm" doc="readme.md">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_bm.uvprojx"/>
        <environment name="csolution" load="power_mode_switch_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm" folder="boards/evkbmimxrt1060/driver_examples/pwm" doc="readme.md">
      <description>The PWM project is a simple demonstration program of the SDK PWM driver.The pulse width modulator (PWM) module contains PWM submodules, each of which is set up to control a single half-bridge power stage.Fault...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm.uvprojx"/>
        <environment name="iar" load="iar/pwm.ewp"/>
        <environment name="csolution" load="pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pwm_fault" folder="boards/evkbmimxrt1060/demo_apps/pwm_fault" doc="readme.md">
      <description>This demo application demonstrates the EflexPWM fault demo.This application demonstrates the pulse with modulation function of EflexPWM module. It outputs thePWM to control the intensity of the LED. PWM shut down...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pwm_fault.uvprojx"/>
        <environment name="iar" load="iar/pwm_fault.ewp"/>
        <environment name="csolution" load="pwm_fault.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_blend" folder="boards/evkbmimxrt1060/driver_examples/pxp/blend" doc="readme.md">
      <description>The PXP blend project shows how to blend process surface and alpha surface usingPXP. If this example runs correctly, you will see two rectangles moving in thescreen. One rectangle is process surface output, and the...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_blend.uvprojx"/>
        <environment name="iar" load="iar/pxp_blend.ewp"/>
        <environment name="csolution" load="pxp_blend.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_color_key" folder="boards/evkbmimxrt1060/driver_examples/pxp/color_key" doc="readme.md">
      <description>The PXP color key project shows how to use the AS color key together with the alpha blend. In this example, the AS pixel format is RGB565, the global alpha is used for alpha blend.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_color_key.uvprojx"/>
        <environment name="iar" load="iar/pxp_color_key.ewp"/>
        <environment name="csolution" load="pxp_color_key.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_copy_pic" folder="boards/evkbmimxrt1060/driver_examples/pxp/copy_pic" doc="readme.md">
      <description>The PXP copy_pic project shows how to use the PXP to copy image from one buffer to another buffer.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_copy_pic.uvprojx"/>
        <environment name="iar" load="iar/pxp_copy_pic.ewp"/>
        <environment name="csolution" load="pxp_copy_pic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_fill_rectangle" folder="boards/evkbmimxrt1060/driver_examples/pxp/fill_rectangle" doc="readme.md">
      <description>The PXP fill rectangle project shows how to use the PXP to draw a solid rectangle with configured color. If this example runs correctly, you will see the panel filled with red, green and blue in a row.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_fill_rectangle.uvprojx"/>
        <environment name="iar" load="iar/pxp_fill_rectangle.ewp"/>
        <environment name="csolution" load="pxp_fill_rectangle.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_flip" folder="boards/evkbmimxrt1060/driver_examples/pxp/flip" doc="readme.md">
      <description>The PXP flip project shows how to use the PXP flip function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The flip mode is changing.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_flip.uvprojx"/>
        <environment name="iar" load="iar/pxp_flip.ewp"/>
        <environment name="csolution" load="pxp_flip.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_lcdif_handshake" folder="boards/evkbmimxrt1060/driver_examples/pxp/lcdif_handshake" doc="readme.md">
      <description>The PXP LCDIF hand shake project shows how to enable the hand shake betweenPXP and LCDIF. If this example runs correctly, you will see two rectangles moving in thescreen. One rectangle is process surface output, and...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_lcdif_handshake.uvprojx"/>
        <environment name="iar" load="iar/pxp_lcdif_handshake.ewp"/>
        <environment name="csolution" load="pxp_lcdif_handshake.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_porter_duff" folder="boards/evkbmimxrt1060/driver_examples/pxp/porter_duff" doc="readme.md">
      <description>This example shows how to use the PXP Porter Duff compositing. In this example, A blue rectangle is in the left up corner of the destination surface (also named PS surface, or s0 in reference mannal). A red rectangle...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_porter_duff.uvprojx"/>
        <environment name="iar" load="iar/pxp_porter_duff.ewp"/>
        <environment name="csolution" load="pxp_porter_duff.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_queue" folder="boards/evkbmimxrt1060/driver_examples/pxp/queue" doc="readme.md">
      <description>The PXP queue project shows how to use the PXP command queue. This exampleuse the command queue to rotate the process surface. If this exampleruns correctly, you will see a square with three color(red, green and blue).The rotate degree is changing.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_queue.uvprojx"/>
        <environment name="iar" load="iar/pxp_queue.ewp"/>
        <environment name="csolution" load="pxp_queue.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_rotate" folder="boards/evkbmimxrt1060/driver_examples/pxp/rotate" doc="readme.md">
      <description>The PXP rotate project shows how to use the PXP rotate function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The rotate degree is changing.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_rotate.uvprojx"/>
        <environment name="iar" load="iar/pxp_rotate.ewp"/>
        <environment name="csolution" load="pxp_rotate.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pxp_scale" folder="boards/evkbmimxrt1060/driver_examples/pxp/scale" doc="readme.md">
      <description>The PXP scale project shows how to use the PXP scale function. If this exampleruns correctly, you will see a square with three color(red, green and blue).The square size is changing.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pxp_scale.uvprojx"/>
        <environment name="iar" load="iar/pxp_scale.ewp"/>
        <environment name="csolution" load="pxp_scale.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm" folder="boards/evkbmimxrt1060/driver_examples/qtmr/inputcapture_outputpwm" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature.The example sets up a QTMR channel for input capture. Once the input signal is received,this example will...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_inputcapture_outputpwm_dma" folder="boards/evkbmimxrt1060/driver_examples/qtmr/inputcapture_outputpwm_dma" doc="readme.md">
      <description>The QTMR project is a demonstration program of the SDK QTMR driver's input capture and output pwm feature by DMA.The example sets up a QTMR channel for input capture. Once the input signal is received,this example...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_inputcapture_outputpwm_dma.uvprojx"/>
        <environment name="iar" load="iar/qtmr_inputcapture_outputpwm_dma.ewp"/>
        <environment name="csolution" load="qtmr_inputcapture_outputpwm_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="qtmr_timer" folder="boards/evkbmimxrt1060/driver_examples/qtmr/timer" doc="readme.md">
      <description>The QTMR project is a simple demonstration program of the SDK QTMR driver to use QTMR as a timer.The quad-timer module provides four timer channels with a variety of controls affecting both individualand...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/qtmr_timer.uvprojx"/>
        <environment name="iar" load="iar/qtmr_timer.ewp"/>
        <environment name="csolution" load="qtmr_timer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="rtwdog" folder="boards/evkbmimxrt1060/driver_examples/rtwdog" doc="readme.md">
      <description>The RTWDOG Example project is to demonstrate usage of the KSDK rtwdog driver.In this example, fast testing is first implemented to test the rtwdog.After this, refreshing the watchdog in None-window mode and window...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtwdog.uvprojx"/>
        <environment name="iar" load="iar/rtwdog.ewp"/>
        <environment name="csolution" load="rtwdog.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_multi_channel_transfer" folder="boards/evkbmimxrt1060/driver_examples/sai/edma_multi_channel_transfer" doc="readme.md">
      <description>The sai_edma_multi_channel_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_multi_channel_transfer.uvprojx"/>
        <environment name="csolution" load="sai_edma_multi_channel_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer" folder="boards/evkbmimxrt1060/driver_examples/sai/edma_ping_pong_buffer" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_ping_pong_buffer_half_interrupt" folder="boards/evkbmimxrt1060/driver_examples/sai/edma_ping_pong_buffer_half_interrupt" doc="readme.md">
      <description>The sai_edma_ping_pong_buffer_half_interrupt example shows how to use sai driver with EDMA half interrupt feature: In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_ping_pong_buffer_half_interrupt.uvprojx"/>
        <environment name="csolution" load="sai_edma_ping_pong_buffer_half_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback" folder="boards/evkbmimxrt1060/driver_examples/sai/edma_record_playback" doc="readme.md">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback.uvprojx"/>
        <environment name="csolution" load="sai_edma_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_tdm_record_playback" folder="boards/evkbmimxrt1060/driver_examples/sai/edma_tdm_record_playback" doc="readme.md">
      <description>The sai_edma_tdm_record_plyback example shows how to use sai driver to generate TDM format with EDMA:In this example, one sai instance record and playbacks the audio data in TDM format.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_tdm_record_playback.uvprojx"/>
        <environment name="csolution" load="sai_edma_tdm_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer" folder="boards/evkbmimxrt1060/driver_examples/sai/edma_transfer" doc="readme.md">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer.uvprojx"/>
        <environment name="csolution" load="sai_edma_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt" folder="boards/evkbmimxrt1060/driver_examples/sai/interrupt" doc="readme.md">
      <description>The sai_interrupt example shows how to use sai functional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt.uvprojx"/>
        <environment name="csolution" load="sai_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback" folder="boards/evkbmimxrt1060/driver_examples/sai/interrupt_record_playback" doc="readme.md">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_record_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer" folder="boards/evkbmimxrt1060/driver_examples/sai/interrupt_transfer" doc="readme.md">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer.uvprojx"/>
        <environment name="csolution" load="sai_interrupt_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="semc" folder="boards/evkbmimxrt1060/driver_examples/semc/sdram" doc="readme.md">
      <description>The sdramc example shows how to use SEMC controller driver to initialize the external SDRAM chip.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/semc.uvprojx"/>
        <environment name="iar" load="iar/semc.ewp"/>
        <environment name="csolution" load="semc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="boards/evkbmimxrt1060/demo_apps/shell" doc="readme.md">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="csolution" load="shell.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_hp_hac" folder="boards/evkbmimxrt1060/driver_examples/snvs/snvs_hp_hac" doc="readme.md">
      <description>The SNVS HP HAC project shows how to use the High Assurance Counter (HAC) based on SDK SNVS HP driver. In this example, The HAC is enabled and set a initial value. Software fatal security violation is triggered, and...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_hp_hac.uvprojx"/>
        <environment name="iar" load="iar/snvs_hp_hac.ewp"/>
        <environment name="csolution" load="snvs_hp_hac.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_hp_rtc" folder="boards/evkbmimxrt1060/driver_examples/snvs/snvs_hp_rtc" doc="readme.md">
      <description>The SNVS HP RTC project is a simple demonstration program of the SDK SNVS HP driver. The test will set up RTC date and time to a predefined value and starts the counter. RTC then triggers an alarm after a user...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_hp_rtc.uvprojx"/>
        <environment name="iar" load="iar/snvs_hp_rtc.ewp"/>
        <environment name="csolution" load="snvs_hp_rtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_mc" folder="boards/evkbmimxrt1060/driver_examples/snvs/snvs_lp_mc" doc="readme.md">
      <description>The SNVS LP MC project shows how to use the Monotonic Counter (MC) based on SDK SNVS LP driver. In this example, the MC value is increased and checked several times.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_mc.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_mc.ewp"/>
        <environment name="csolution" load="snvs_lp_mc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_lp_srtc" folder="boards/evkbmimxrt1060/driver_examples/snvs/snvs_lp_srtc" doc="readme.md">
      <description>The SNVS LP SRTC project is a simple demonstration program of the SDK SNVS LP driver. The test will set up secure RTC (SRTC) date and time to a predefined value and starts the counter, then the SRTC counter value is...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_lp_srtc.uvprojx"/>
        <environment name="iar" load="iar/snvs_lp_srtc.ewp"/>
        <environment name="csolution" load="snvs_lp_srtc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="snvs_sw_zmk" folder="boards/evkbmimxrt1060/driver_examples/snvs/snvs_sw_zmk" doc="readme.md">
      <description>The SNVS SW ZMK project shows how to provision the zeroizable master key (ZMK) by software based on SDK SNVS driver. In this example, ZMK key value is set and ECC is enabled. When change the ZMK key value, violation detected and ZMK key is zeroized.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/snvs_sw_zmk.uvprojx"/>
        <environment name="iar" load="iar/snvs_sw_zmk.ewp"/>
        <environment name="csolution" load="snvs_sw_zmk.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="src_reset_source" folder="boards/evkbmimxrt1060/driver_examples/src/src_reset_source" doc="readme.md">
      <description>The src_reset_source example shows how to check the reset source and boot option with SRC driver.When run this example firstly on the board after power up, the POR reset or IPP reset flag would be asserted. But when...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/src_reset_source.uvprojx"/>
        <environment name="iar" load="iar/src_reset_source.ewp"/>
        <environment name="csolution" load="src_reset_source.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="temperaturemonitor" folder="boards/evkbmimxrt1060/driver_examples/tempmon" doc="readme.md">
      <description>The TEMPMON project is a simple demonstration program of the SDK TEMPMON driver.The temperatue monitor (TEMPMON) module features alarm functions that can raise independent interrupt signals if the temperature is...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/temperaturemonitor.uvprojx"/>
        <environment name="iar" load="iar/temperaturemonitor.ewp"/>
        <environment name="csolution" load="temperaturemonitor.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="trng_random" folder="boards/evkbmimxrt1060/driver_examples/trng/random" doc="readme.md">
      <description>The True Random Number Generator (TRNG) is a hardware accelerator module that generates a 512-bitentropy as needed by an entropy consuming module or by other post processing functions. The TRNGExample project is a...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/trng_random.uvprojx"/>
        <environment name="iar" load="iar/trng_random.ewp"/>
        <environment name="csolution" load="trng_random.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog01" folder="boards/evkbmimxrt1060/driver_examples/wdog" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,implemented to test the wdog.And then after 10 times of refreshing the watchdog, a timeout reset is generated.We also try to...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog01.uvprojx"/>
        <environment name="iar" load="iar/wdog01.ewp"/>
        <environment name="csolution" load="wdog01.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar_aoi" folder="boards/evkbmimxrt1060/demo_apps/xbar_aoi" doc="readme.md">
      <description>The XBAR AOI demo application demonstrates the usage of the XBAR, AOI peripheral to combine interruptfrom CMP and PIT. If the button is pressed(CMP interrupt) and the PIT periodic interrupt occurs a message is printed.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar_aoi.uvprojx"/>
        <environment name="iar" load="iar/xbar_aoi.ewp"/>
        <environment name="csolution" load="xbar_aoi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbar_aoi_peripheral" folder="boards/evkbmimxrt1060/demo_apps/xbar_aoi_peripheral" doc="readme.md">
      <description>The XBAR AOI demo application demonstrates the usage of the XBAR, AOI peripheral to combine interruptfrom CMP and PIT. If the button is pressed(CMP interrupt) and the PIT periodic interrupt occurs a message is printed.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbar_aoi_peripheral.uvprojx"/>
        <environment name="iar" load="iar/xbar_aoi_peripheral.ewp"/>
        <environment name="csolution" load="xbar_aoi_peripheral.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="xbara" folder="boards/evkbmimxrt1060/driver_examples/xbara" doc="readme.md">
      <description>The Xbara project is a simple demonstration program of the SDK Xbara driver.The intended applicationof this module is to provide a flexible crossbar switch function that allows any input to beconnected to any output...See more details in readme document.</description>
      <board name="MIMXRT1060-EVKB" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/xbara.uvprojx"/>
        <environment name="iar" load="iar/xbara.ewp"/>
        <environment name="csolution" load="xbara.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="evkbmimxrt1060" Cversion="1.0.0" condition="BOARD_Project_Template.evkbmimxrt1060.condition_id">
      <description>Board_project_template evkbmimxrt1060</description>
      <RTE_Components_h>
#ifndef XIP_EXTERNAL_FLASH
#define XIP_EXTERNAL_FLASH 1
#endif
#ifndef XIP_BOOT_HEADER_ENABLE
#define XIP_BOOT_HEADER_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="header" name="boards/evkbmimxrt1060/project_template/dcd.h" projectpath="board"/>
        <file category="sourceC" name="boards/evkbmimxrt1060/project_template/dcd.c" projectpath="board"/>
        <file category="header" attr="config" name="boards/evkbmimxrt1060/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/evkbmimxrt1060/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/evkbmimxrt1060/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/evkbmimxrt1060/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/evkbmimxrt1060/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/evkbmimxrt1060/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/evkbmimxrt1060/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/evkbmimxrt1060/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/evkbmimxrt1060/project_template/"/>
      </files>
    </component>
    <component Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkbmimxrt1060 xip" Cversion="2.0.1" condition="driver.xip_board.evkbmimxrt1060.condition_id">
      <description>XIP Board Driver</description>
      <files>
        <file category="sourceC" name="boards/evkbmimxrt1060/xip/evkbmimxrt1060_flexspi_nor_config.c" projectpath="xip"/>
        <file category="header" name="boards/evkbmimxrt1060/xip/evkbmimxrt1060_flexspi_nor_config.h" projectpath="xip"/>
        <file category="include" name="boards/evkbmimxrt1060/xip/"/>
      </files>
    </component>
  </components>
</package>
