Shield: Sparkfun ESP8266 WiFi
-----------------------------

The Sparkfun ESP8266 WiFi Shield based on [ESP8266 SoC](https://www.espressif.com/en/products/socs/esp8266) is connected via an Arduino header using a USART interface.
It exposes a [CMSIS-Driver WiFi](https://arm-software.github.io/CMSIS_6/latest/Driver/group__wifi__interface__gr.html).

This module was tested with the ESP8266 AT command set firmware revision **1.6.2**.
Refer to [Espressif Product Support Download](https://www.espressif.com/en/support/download/all) for more information.
