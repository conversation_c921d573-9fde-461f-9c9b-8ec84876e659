<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-MCXA153_MOTOR_CONTROL_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware motor_control Examples Pack for FRDM-MCXA153</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="FRDM-MCXA153_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MCXA153_DFP" vendor="NXP" version="19.0.0"/>
      <package name="MOTOR_CONTROL" vendor="NXP" version="2.0.0"/>
      <package name="FREEMASTER" vendor="NXP" version="2.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="mc_pmsm" folder="boards/frdmmcxa153/demo_apps/mc_pmsm/pmsm_enc" doc="PMSMFRDMMCXA153.pdf">
      <description>This example demonstrates the control of the PMSM.</description>
      <board name="FRDM-MCXA153" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mc_pmsm.uvprojx"/>
        <environment name="csolution" load="mc_pmsm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
