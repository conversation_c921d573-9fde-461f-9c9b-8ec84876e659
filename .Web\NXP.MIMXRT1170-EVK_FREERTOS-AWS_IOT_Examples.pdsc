<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1170-EVK_FREERTOS-AWS_IOT_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware freertos-aws_iot Examples Pack for MIMXRT1170-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1170-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MIMXRT1176_DFP" vendor="NXP" version="19.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
      <package name="LWIP" vendor="NXP" version="3.0.0"/>
      <package name="FREERTOS-AWS_IOT" vendor="NXP" version="2.0.0"/>
      <package name="PKCS11" vendor="NXP" version="2.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="WIFI" vendor="NXP" version="3.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="TINYCBOR" vendor="NXP" version="2.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="aws_shadow_enet_cm7" folder="boards/evkmimxrt1170/aws_examples/shadow_enet/cm7" doc="readme.md">
      <description>Demo for showing how to use the Device Shadow library's API.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/aws_shadow_enet_cm7.uvprojx"/>
        <environment name="csolution" load="aws_shadow_enet_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="aws_shadow_wifi_nxp_cm7" folder="boards/evkmimxrt1170/aws_examples/shadow_wifi_nxp/cm7" doc="readme.md">
      <description>Demo for showing how to use the Device Shadow library's API.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/aws_shadow_wifi_nxp_cm7.uvprojx"/>
        <environment name="csolution" load="aws_shadow_wifi_nxp_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ota_demo_enet" folder="boards/evkmimxrt1170/aws_examples/ota_demo_enet/cm7" doc="readme.md">
      <description>Demo showing over-the-air update using AWS OTA service.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ota_demo_enet.uvprojx"/>
        <environment name="csolution" load="ota_demo_enet.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ota_demo_wifi" folder="boards/evkmimxrt1170/aws_examples/ota_demo_wifi/cm7" doc="readme.md">
      <description>Demo showing over-the-air update using AWS OTA service.</description>
      <board name="MIMXRT1170-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ota_demo_wifi.uvprojx"/>
        <environment name="csolution" load="ota_demo_wifi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
