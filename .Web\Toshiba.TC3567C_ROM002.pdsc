<?xml version="1.0" encoding="utf-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss/shared/docs/product/bluetooth-publishing-program/</url>
  <name>TC3567C_ROM002</name>
  <description>Toshiba TC3567C/TC3567D ROM002 Device Support</description>

  <releases>
    <release version="0.0.2">
      Updated processor settings.
    </release>
    <release version="0.0.1">
      Initial Release of TOSHIBA TC3567C/TC3567D ROM002 Series Device Family Pack.
    </release>
  </releases>
  <license>License/SOFTWARE_LICENSE_AGREEMENT.rtf</license>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>TOSHIBA</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package TOSHIBA</keyword>
    <keyword>TC3567C</keyword>
    <keyword>TC3567D</keyword>
  </keywords>

  <devices>
    <family Dfamily="TC3567CD_ROM002" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="26000000"/>
      <description>TOSHIBA TC3567C/TC3567D ROM002 supports BLE V4.2 with ultra low power consumption.</description>
      <feature type="UART"          n="2"/>
      <feature type="I2C"           n="1"/>
      <feature type="SPI"           n="1"/>
      <feature type="ADC"           n="1"       m="5"/>
      <!-- *************************  Device 'TC3567C_ROM002'  ***************************** -->
      <device Dname="TC3567CFSG-002">
        <description>TC3567C_ROM002 supports BLE V4.2 and has built-in NVM. 40pin QFN Package</description>
        <compile header="Device/Include/TC3567C.h" define="TC3567C"/>
        <debug svd="SVD/TC3567C.svd"/>
        <memory id="IRAM1" start="0x813000" size="0xC170" default="1"/>
        <algorithm name="Flash/TC3567C-002_NVM.FLM" start="0x813000"  size="0x000D000"   default="1"/>
      </device>
      <!-- *************************  Device 'TC3567D_ROM002'  ***************************** -->
      <device Dname="TC3567DFSG-002">
        <description>TC3567D_ROM002 supports BLE V4.2. 40pin QFN Package</description>
        <compile header="Device/Include/TC3567C.h" define="TC3567D"/>
        <debug svd="SVD/TC3567C.svd"/>
        <memory id="IRAM1" start="0x813000" size="0xC170" default="1"/>
        <algorithm name="Flash/TC3567D-002_EEPROM.FLM" start="0x813000"  size="0x000D000"   default="1"/>
      </device>
    </family>
  </devices>

  <conditions>
    <condition id="TC3567CD_ROM002">
      <!-- condition selecting devices -->
      <require Dvendor="Toshiba:92"/>
      <require Dfamily="TC3567CD_ROM002"/>
    </condition>
    <condition id="Compiler ARM">
      <!-- condition selecting ARM Compiler -->
      <require Tcompiler="ARMCC"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="0.0.1" condition="TC3567CD_ROM002">
      <description>System Startup for TC3567C/TC3567D ROM002</description>
      <files>
        <file category="header" name="Device/Include/TC3567C.h"/>
        <file category="source" name="Device/Source/startup_TC3567C.s" attr="config" version="0.0.1" condition="Compiler ARM"/>
        <file category="source" name="Device/Source/system_TC3567C.c"  attr="config" version="0.0.1"/>
      </files>
    </component>
  </components>
</package>
