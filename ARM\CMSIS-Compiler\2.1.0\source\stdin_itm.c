/*
 * Copyright (C) 2023 ARM Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdint.h>
#include "retarget_stdin.h"

#include "RTE_Components.h"
#include CMSIS_device_header

#ifndef CMSIS_Compiler_CORE_ITM_RXBUFFER_DISABLE
/* Value identifying ITM_RxBuffer is ready for next character */
#define ITM_RXBUFFER_EMPTY  ((int32_t)0x5AA55AA5U)

/* Variable to receive ITM characters */
extern
volatile int32_t ITM_RxBuffer;
volatile int32_t ITM_RxBuffer = ITM_RXBUFFER_EMPTY;
#endif /* CMSIS_Compiler_CORE_ITM_RXBUFFER_DISABLE */

/**
  Get a character from the stdio

  \return     The next character from the input, or -1 on read error.
*/
int stdin_getchar (void) {
  int32_t ch;

  do {
    ch = ITM_ReceiveChar();
  } while (ch == -1);
  return (ch);
}
