<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>MGM21_DFP</name>
  <description>Silicon Labs MGM21 Mighty Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_MGM21_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_MGM21_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>MGM21</keyword>
    <keyword>MGM21</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="MGM21 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p3" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="38400000"/>
      <book name="Documents/efr32xg21-rm.pdf"  title="MGM21 Reference Manual"/>
      <description>
32-bit ARM Cortex-M33 core with 80 MHz maximum operating frequency.
      </description>

      <subFamily DsubFamily="MGM21">
        <!-- *************************  Device 'MGM210L022JIF'  ***************************** -->
        <device Dname="MGM210L022JIF">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210L022JIF"/>
          <debug      svd="SVD/MGM21/MGM210L022JIF.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210L022JNF'  ***************************** -->
        <device Dname="MGM210L022JNF">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210L022JNF"/>
          <debug      svd="SVD/MGM21/MGM210L022JNF.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210LA22JIF'  ***************************** -->
        <device Dname="MGM210LA22JIF">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210LA22JIF"/>
          <debug      svd="SVD/MGM21/MGM210LA22JIF.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210LA22JNF'  ***************************** -->
        <device Dname="MGM210LA22JNF">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210LA22JNF"/>
          <debug      svd="SVD/MGM21/MGM210LA22JNF.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210P022JIA'  ***************************** -->
        <device Dname="MGM210P022JIA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210P022JIA"/>
          <debug      svd="SVD/MGM21/MGM210P022JIA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210P022JNA'  ***************************** -->
        <device Dname="MGM210P022JNA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210P022JNA"/>
          <debug      svd="SVD/MGM21/MGM210P022JNA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210P032JIA'  ***************************** -->
        <device Dname="MGM210P032JIA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210P032JIA"/>
          <debug      svd="SVD/MGM21/MGM210P032JIA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210P032JNA'  ***************************** -->
        <device Dname="MGM210P032JNA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210P032JNA"/>
          <debug      svd="SVD/MGM21/MGM210P032JNA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210PA22JIA'  ***************************** -->
        <device Dname="MGM210PA22JIA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210PA22JIA"/>
          <debug      svd="SVD/MGM21/MGM210PA22JIA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210PA22JNA'  ***************************** -->
        <device Dname="MGM210PA22JNA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210PA22JNA"/>
          <debug      svd="SVD/MGM21/MGM210PA22JNA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210PA32JIA'  ***************************** -->
        <device Dname="MGM210PA32JIA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210PA32JIA"/>
          <debug      svd="SVD/MGM21/MGM210PA32JIA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'MGM210PA32JNA'  ***************************** -->
        <device Dname="MGM210PA32JNA">
          <compile header="Device/SiliconLabs/MGM21/Include/em_device.h"  define="MGM210PA32JNA"/>
          <debug      svd="SVD/MGM21/MGM210PA32JNA.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00018000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="MGM21">
      <description>Silicon Labs MGM21 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="MGM21*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="MGM21">
      <description>System Startup for Silicon Labs MGM21 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/MGM21/Include/"/>
        <file category="header"  name="Device/SiliconLabs/MGM21/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/MGM21/Source/GCC/startup_mgm21.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/MGM21/Source/GCC/mgm21.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/MGM21/Source/system_mgm21.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
