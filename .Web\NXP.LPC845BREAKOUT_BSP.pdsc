<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPC845BREAKOUT_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPC845BREAKOUT</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC845_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPC845BREAKOUT">
      <description>LPC845 Breakout Board for LPC84x family MCUs</description>
      <image small="boards/lpc845breakout/lpc845breakout.png"/>
      <book category="overview" name="https://www.nxp.com/pip/LPC845-BRK" title="LPC845 Breakout Board for LPC84x family MCUs" public="true"/>
      <mountedDevice Dname="LPC845M301JBD48" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC845M301JBD64" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC845M301JHI33" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC845M301JHI48" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC845.internal_condition">
      <accept Dname="LPC845M301JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.LPC845.internal_condition">
      <accept Dname="LPC845M301JBD48" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC845M301JHI48" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.lpc845breakout.condition_id">
      <require condition="allOf.board=lpc845breakout, component.miniusart_adapter, device_id=LPC845, device.LPC845_startup, driver.clock, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.power_no_lib, driver.swm, utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpc845breakout, component.miniusart_adapter, device_id=LPC845, device.LPC845_startup, driver.clock, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.power_no_lib, driver.swm, utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.lpc845breakout.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require condition="device_id.LPC845.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="swm"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="board.lpc845breakout.internal_condition">
      <accept condition="device.LPC845.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="acomp_basic" folder="boards/lpc845breakout/driver_examples/acomp/acomp_basic" doc="readme.md">
      <description>The ACOMP Basic Example shows the simplest way to use ACOMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_basic.uvprojx"/>
        <environment name="iar" load="iar/acomp_basic.ewp"/>
        <environment name="csolution" load="acomp_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acomp_interrupt" folder="boards/lpc845breakout/driver_examples/acomp/acomp_interrupt" doc="readme.md">
      <description>The ACOMP Interrupt Example shows how to use interrupt with ACOMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the ACOMP's negative channel...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/acomp_interrupt.ewp"/>
        <environment name="csolution" load="acomp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_acomp" folder="boards/lpc845breakout/driver_examples/capt/capt_acomp" doc="readme.md">
      <description>This example shows how to capture touch data using CAPT poll-now mode. This example uses ACOMP instead of GPIO in measurement stage.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_acomp.uvprojx"/>
        <environment name="iar" load="iar/capt_acomp.ewp"/>
        <environment name="csolution" load="capt_acomp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_acomp_continuous" folder="boards/lpc845breakout/driver_examples/capt/capt_acomp_continuous" doc="readme.md">
      <description>This example shows how to capture touch data using CAPT continuous mode. This example uses ACOMP instead of GPIO in measurement stage.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_acomp_continuous.uvprojx"/>
        <environment name="iar" load="iar/capt_acomp_continuous.ewp"/>
        <environment name="csolution" load="capt_acomp_continuous.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_basic" folder="boards/lpc845breakout/driver_examples/capt/capt_basic" doc="readme.md">
      <description>This example shows how to capture touch data using CAPT poll-now mode.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_basic.uvprojx"/>
        <environment name="iar" load="iar/capt_basic.ewp"/>
        <environment name="csolution" load="capt_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_basic_continuous" folder="boards/lpc845breakout/driver_examples/capt/capt_basic_continuous" doc="readme.md">
      <description>This example shows how to capture touch data using CAPT continuous mode.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_basic_continuous.uvprojx"/>
        <environment name="iar" load="iar/capt_basic_continuous.ewp"/>
        <environment name="csolution" load="capt_basic_continuous.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_key" folder="boards/lpc845breakout/touch_examples/capt_key" doc="readme.md">
      <description>This example shows how to use CAPT to detect key touch event. When the key is touched, LED on the board is on accordingly.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_key.uvprojx"/>
        <environment name="iar" load="iar/capt_key.ewp"/>
        <environment name="csolution" load="capt_key.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/lpc845breakout/driver_examples/crc" doc="readme.md">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/lpc845breakout/driver_examples/ctimer/simple_match" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example.ewp"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/lpc845breakout/driver_examples/ctimer/simple_match_interrupt" doc="readme.md">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/lpc845breakout/driver_examples/ctimer/simple_pwm" doc="readme.md">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/lpc845breakout/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.md">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="boards/lpc845breakout/driver_examples/dma/channel_chain" doc="readme.md">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="iar" load="iar/dma_channel_chain.ewp"/>
        <environment name="csolution" load="dma_channel_chain.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="boards/lpc845breakout/driver_examples/dma/interleave_transfer" doc="readme.md">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_interleave_transfer.ewp"/>
        <environment name="csolution" load="dma_interleave_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="boards/lpc845breakout/driver_examples/dma/linked_transfer" doc="readme.md">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_linked_transfer.ewp"/>
        <environment name="csolution" load="dma_linked_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_m2m_polling" folder="boards/lpc845breakout/driver_examples/dma/m2m_polling" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot polling transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_m2m_polling.uvprojx"/>
        <environment name="iar" load="iar/dma_m2m_polling.ewp"/>
        <environment name="csolution" load="dma_m2m_polling.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="boards/lpc845breakout/driver_examples/dma/memory_to_memory" doc="readme.md">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
        <environment name="csolution" load="dma_memory_to_memory.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="boards/lpc845breakout/driver_examples/dma/wrap_transfer" doc="readme.md">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes a wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_wrap_transfer.ewp"/>
        <environment name="csolution" load="dma_wrap_transfer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/lpc845breakout/driver_examples/gpio/led_output" doc="readme.md">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/lpc845breakout/demo_apps/hello_world" doc="readme.md">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_basic" folder="boards/lpc845breakout/driver_examples/iap/iap_basic" doc="readme.md">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It reads part id, boot code version, unique id and reinvoke ISP. A message a printed on the UART terminal as various bascial iap operations are performed.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_basic.uvprojx"/>
        <environment name="iar" load="iar/iap_basic.ewp"/>
        <environment name="csolution" load="iap_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_faim" folder="boards/lpc845breakout/driver_examples/iap/iap_faim" doc="readme.md">
      <description>The IAP FAIM project is a simple demonstration program of the SDK IAP driver. It writes and reads the FAIM page. A message a printed on the UART terminal as FAIM read and write operations are performed.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_faim.uvprojx"/>
        <environment name="iar" load="iar/iap_faim.ewp"/>
        <environment name="csolution" load="iap_faim.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="boards/lpc845breakout/driver_examples/iap/iap_flash" doc="readme.md">
      <description>The IAP Flash project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="iar" load="iar/iap_flash.ewp"/>
        <environment name="csolution" load="iap_flash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/lpc845breakout/demo_apps/led_blinky" doc="readme.md">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_basic" folder="boards/lpc845breakout/driver_examples/adc/lpc_adc_basic" doc="readme.md">
      <description>The lpc_adc_basic example shows how to use LPC ADC driver in the simplest way.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_basic.ewp"/>
        <environment name="csolution" load="lpc_adc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_burst" folder="boards/lpc845breakout/driver_examples/adc/lpc_adc_burst" doc="readme.md">
      <description>The lpc_adc_burst example shows how to use LPC ADC driver with the burst mode.In this example, the internal temperature sensor is used to created the input analog signal.When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_burst.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_burst.ewp"/>
        <environment name="csolution" load="lpc_adc_burst.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_dma" folder="boards/lpc845breakout/driver_examples/adc/lpc_adc_dma" doc="readme.md">
      <description>The lpc_adc_dma example shows how to use LPC ADC driver with DMA.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_dma.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_dma.ewp"/>
        <environment name="csolution" load="lpc_adc_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_interrupt" folder="boards/lpc845breakout/driver_examples/adc/lpc_adc_interrupt" doc="readme.md">
      <description>The lpc_adc_interrupt example shows how to use interrupt with LPC ADC driver.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_interrupt.ewp"/>
        <environment name="csolution" load="lpc_adc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_dac_basic" folder="boards/lpc845breakout/driver_examples/dac/lpc_dac_basic" doc="readme.md">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's double-buffer feature is not enabled, the CR register is used as the DAC output data register directly.The...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_dac_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_dac_basic.ewp"/>
        <environment name="csolution" load="lpc_dac_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_dac_dma" folder="boards/lpc845breakout/driver_examples/dac/lpc_dac_dma" doc="readme.md">
      <description>The dac_dma example shows how to use DAC with dma and produce an arbitrary, user-defined waveform ofselectable frequency.The output can be observed with an oscilloscope. When the DAC's double-buffer feature is...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_dac_dma.uvprojx"/>
        <environment name="iar" load="iar/lpc_dac_dma.ewp"/>
        <environment name="csolution" load="lpc_dac_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_dac_interrupt" folder="boards/lpc845breakout/driver_examples/dac/lpc_dac_interrupt" doc="readme.md">
      <description>The dac_interrupt example shows how to use DAC with interrupts and produce an arbitrary, user-defined waveform ofselectable frequency.The output can be observed with an oscilloscope. When the DAC's double-buffer...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_dac_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_dac_interrupt.ewp"/>
        <environment name="csolution" load="lpc_dac_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/lpc845breakout/driver_examples/mrt" doc="readme.md">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/lpc845breakout/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="iar" load="iar/new_project.ewp"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/lpc845breakout/driver_examples/pint/pattern_match" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/lpc845breakout/driver_examples/pint/pin_interrupt" doc="readme.md">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_lpc" folder="boards/lpc845breakout/demo_apps/power_mode_switch_lpc" doc="readme.md">
      <description>The power_mode_switch application shows the usage of normal power mode control APIs for entering the three kinds oflow power mode: Sleep mode, Deep Sleep mode and Power Down mode, deep power down mode. When the...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_lpc.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch_lpc.ewp"/>
        <environment name="csolution" load="power_mode_switch_lpc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="boards/lpc845breakout/driver_examples/sctimer/16bit_counter" doc="readme.md">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" load="iar/sctimer_16bit_counter.ewp"/>
        <environment name="csolution" load="sctimer_16bit_counter.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="boards/lpc845breakout/driver_examples/sctimer/multi_state_pwm" doc="readme.md">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_multi_state_pwm.ewp"/>
        <environment name="csolution" load="sctimer_multi_state_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="boards/lpc845breakout/driver_examples/sctimer/pwm_with_dutycyle_change" doc="readme.md">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
        <environment name="csolution" load="sctimer_pwm_with_dutycyle_change.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="boards/lpc845breakout/driver_examples/sctimer/simple_pwm" doc="readme.md">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_simple_pwm.ewp"/>
        <environment name="csolution" load="sctimer_simple_pwm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_master" folder="boards/lpc845breakout/driver_examples/spi/interrupt/master" doc="readme.md">
      <description>The spi_interrupt_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_slave" folder="boards/lpc845breakout/driver_examples/spi/interrupt/slave" doc="readme.md">
      <description>The spi_interrupt_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_master" folder="boards/lpc845breakout/driver_examples/spi/polling/master" doc="readme.md">
      <description>The spi_polling_transfer_master example shows how to use spi driver as master to do board to boardtransfer with polling:In this example, one spi instance as master and another spi instance on othere board as slave....See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_master.ewp"/>
        <environment name="csolution" load="spi_polling_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_slave" folder="boards/lpc845breakout/driver_examples/spi/polling/slave" doc="readme.md">
      <description>The spi_polling_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_slave.ewp"/>
        <environment name="csolution" load="spi_polling_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_dma_master" folder="boards/lpc845breakout/driver_examples/spi/transfer_dma/master" doc="readme.md">
      <description>The spi_dma_transfer_master example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_dma_master.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_dma_master.ewp"/>
        <environment name="csolution" load="spi_transfer_dma_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_dma_slave" folder="boards/lpc845breakout/driver_examples/spi/transfer_dma/slave" doc="readme.md">
      <description>The spi_dma_transfer_slave example shows how to use driver API to transfer in DMA way. In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_dma_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_dma_slave.ewp"/>
        <environment name="csolution" load="spi_transfer_dma_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_master" folder="boards/lpc845breakout/driver_examples/spi/transfer_interrupt/master" doc="readme.md">
      <description>The spi_interrupt_transfer_master example shows how to use spi driver as master to do board to boardtransfer in interrupt way:In this example, one spi instance as master and another spi instance on othere board as...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_master.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_interrupt_master.ewp"/>
        <environment name="csolution" load="spi_transfer_interrupt_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_slave" folder="boards/lpc845breakout/driver_examples/spi/transfer_interrupt/slave" doc="readme.md">
      <description>The spi_interrupt_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_interrupt_slave.ewp"/>
        <environment name="csolution" load="spi_transfer_interrupt_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling_example" folder="boards/lpc845breakout/driver_examples/usart/polling" doc="readme.md">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC, the board will send back all characters that PCsend to the board. </description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling_example.uvprojx"/>
        <environment name="iar" load="iar/usart_polling_example.ewp"/>
        <environment name="csolution" load="usart_polling_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_terminal" folder="boards/lpc845breakout/driver_examples/usart/terminal" doc="readme.md">
      <description>This example demonstrate configuration and use of the USART module in interrupt-driven asynchronous mode on communication with a terminal emulator calling the USART transactional APIs. USART will echo back every...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_terminal.uvprojx"/>
        <environment name="iar" load="iar/usart_terminal.ewp"/>
        <environment name="csolution" load="usart_terminal.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_dma" folder="boards/lpc845breakout/driver_examples/usart/transfer_dma" doc="readme.md">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USART The example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_dma.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_dma.ewp"/>
        <environment name="csolution" load="usart_transfer_dma.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_interrupt" folder="boards/lpc845breakout/driver_examples/usart/transfer_interrupt" doc="readme.md">
      <description>usart_transfer_interrupt</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_interrupt.ewp"/>
        <environment name="csolution" load="usart_transfer_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_ring_buffer" folder="boards/lpc845breakout/driver_examples/usart/transfer_ring_buffer" doc="readme.md">
      <description>The usart_interrupt_rb_transfer example shows how to use usart driver in interrupt way withRX ring buffer enabled.In this example, one uart instance connect to PC through, the board will send back all charactersthat...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_ring_buffer.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_ring_buffer.ewp"/>
        <environment name="csolution" load="usart_transfer_ring_buffer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_sync_mode" folder="boards/lpc845breakout/driver_examples/usart/transfer_sync_mode" doc="readme.md">
      <description>The usart_interrupt_sync_transfer example shows how to use usart API in synchronous mode:In this example, one usart instance will be selected as master ,and another as slave. The master will send data to slave in...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_sync_mode.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_sync_mode.ewp"/>
        <environment name="csolution" load="usart_transfer_sync_mode.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wkt_example" folder="boards/lpc845breakout/driver_examples/wkt" doc="readme.md">
      <description>The WKT project is a simple demonstration program of the SDK WKT driver. It sets up the WKT hardware block to trigger a periodic interrupt after loading a counter value and counting down to 0. When the WKT interrupt...See more details in readme document.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wkt_example.uvprojx"/>
        <environment name="iar" load="iar/wkt_example.ewp"/>
        <environment name="csolution" load="wkt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/lpc845breakout/driver_examples/wwdt" doc="readme.md">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPC845BREAKOUT" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpc845breakout" Cversion="1.0.0" condition="BOARD_Project_Template.lpc845breakout.condition_id">
      <description>Board_project_template lpc845breakout</description>
      <files>
        <file category="header" attr="config" name="boards/lpc845breakout/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpc845breakout/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpc845breakout/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpc845breakout/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpc845breakout/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpc845breakout/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpc845breakout/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpc845breakout/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/lpc845breakout/project_template/"/>
      </files>
    </component>
  </components>
</package>
