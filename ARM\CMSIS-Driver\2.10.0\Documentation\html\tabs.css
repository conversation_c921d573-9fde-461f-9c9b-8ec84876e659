:root {
  --arm_light_blue: #00C1DE;
  --arm_blue: #11809F;
  --arm_blue1: #0091BD;
  --arm_dark_blue: #002B49;
  --arm_light_gray: #E5ECEB;
  --arm_light_gray1: #EFF5F4;
  --arm_light_gray2: #EBEBEB;
  --arm_light_gray3: #F7F7F7;
  --arm_dark_gray: #7D868C;
  --arm_black: #333E48;
  --arm_orange: #FF6B00;
  --arm_yellow: #FFC700;
}

/* in Doxygen 1.9.2 'tabs' is assigned to second navigation row (navrow1) with
 'Main Page', 'Namespaces', etc */

.tabs, .tabs1, .tabs2, .tabs3, .main-nav {
    background-color: var(--arm_light_gray);
    color: var(--arm_black);
    width: 100%;
    z-index: 101;
    font-family: 'Futura PT W01 Medium', 'Lato Light', Lato, Calibri, sans-serif;
    font-size: 14px;
    font-weight: 800;
}

.tabs1 {
    background-color: var(--arm_black);
    font-size: 16px;
}

.tabs {
    background-color: var(--arm_light_gray);
}

.tablist, .main-menu {
    margin: 0;
    padding: 0;
    display: table;
    line-height: 28px;
}

.tablist li {
    float: left;
    display: table-cell;
    background-color: var(--arm_light_gray);
    border-right-style:solid;
    border-right-width:1px;
    border-right-color:var(--arm_dark_gray);
    list-style: none;
    margin:0px;
}

.tablist .MSearchBoxInactive {
    opacity:0.75;
}

.tablist .MSearchBoxInactive:hover {
    opacity:1.0;
}


.tabs1 .tablist li {
    background-color: var(--arm_black);
    font-weight: 1000;
}

.tablist a {
    display: block;
    padding: 0 10px;
    color: var(--arm_dark_gray);
    font-weight: 600;
    outline: none;
}

.tabs1 .tablist a {
    padding: 3px 20px;
    color: white;
    background-color:var(--arm_black);
}

.tablist li.current a {
    background-color: var(--arm_dark_gray);
    color: white;
}

.tabs1 .tablist li.current a {
    background-color: var(--arm_blue);
}

.tablist a:hover {
    color: var(--arm_orange);
}

.tabs1 a:hover {
    color: var(--arm_yellow);
}

.tablist li.current :hover {
    color: white;
}
