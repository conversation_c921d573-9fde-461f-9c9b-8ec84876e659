<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>SERIAL_MWM</name>
  <vendor>NXP</vendor>
  <description>Software Pack for serial_mwm</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <components>
    <component Cclass="component" Cgroup="serial_mwm" Csub="serial_mwm" Cversion="1.0.1">
      <description>UART port layer</description>
      <files>
        <file category="header" name="components/serial_mwm/serial_mwm_port.h" projectpath="component/serial_mwm"/>
        <file category="include" name="components/serial_mwm/"/>
      </files>
    </component>
  </components>
</package>
