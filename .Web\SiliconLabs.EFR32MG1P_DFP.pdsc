<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EFR32MG1P_DFP</name>
  <description>Silicon Labs EFR32MG1P Mighty Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EFR32MG1P_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EFR32MG1P_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32MG1P</keyword>
    <keyword>EFR32</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32MG1P Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="38400000"/>
      <book name="Documents/cortex_m4_dgug.pdf"      title="Cortex-M4 Generic User Guide"/>
      <book name="Documents/efr32xg1-rm.pdf"  title="EFR32MG1P Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M4 core with 40 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in several footprint compatible QFN packages&#xD;&#xA;- 12-channel Peripheral Reflex System enabling autonomous interaction of MCU peripherals&#xD;&#xA;- Autonomous Hardware Crypto Accelerator and Random Number Generator&#xD;&#xA;- Integrated 2.4 GHz balun and PA with up to 19.5 dBm transmit power&#xD;&#xA;- Integrated DC-DC with RF noise mitigation&#xD;&#xA;&#xD;&#xA;The Wireless Gecko portfolio of SoCs (EFR32) include Mighty Gecko (EFR32MG1), Blue Gecko (EFR32BG1), and Flex Gecko (EFR32FG1) families. With support for Bluetooth Smart (BLE), ZigBee, Thread and proprietary protocols, the Wireless Gecko portfolio is ideal for enabling energy-friendly wireless networking for IoT devices. The single-die solution provides industry-leading energy efficiency, ultra-fast wakeup times, a scalable high-power amplifier, an integrated balun and no-compromise MCU features.
      </description>

      <subFamily DsubFamily="EFR32MG1P131">
        <book         name="Documents/efr32mg1-datasheet.pdf"      title="EFR32MG1P131 Data Sheet"/>
        <book         name="Documents/efr32mg1-errata.pdf"         title="EFR32MG1P131 Errata"/>
        <!-- *************************  Device 'EFR32MG1P131F256GM48'  ***************************** -->
        <device Dname="EFR32MG1P131F256GM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P131F256GM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P131F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P132">
        <book         name="Documents/efr32mg1-datasheet.pdf"      title="EFR32MG1P132 Data Sheet"/>
        <book         name="Documents/efr32mg1-errata.pdf"         title="EFR32MG1P132 Errata"/>
        <!-- *************************  Device 'EFR32MG1P132F256GJ43'  ***************************** -->
        <device Dname="EFR32MG1P132F256GJ43">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P132F256GJ43"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P132F256GJ43.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P132F256GM32'  ***************************** -->
        <device Dname="EFR32MG1P132F256GM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P132F256GM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P132F256GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P132F256GM48'  ***************************** -->
        <device Dname="EFR32MG1P132F256GM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P132F256GM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P132F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P132F256IM32'  ***************************** -->
        <device Dname="EFR32MG1P132F256IM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P132F256IM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P132F256IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P132F256IM48'  ***************************** -->
        <device Dname="EFR32MG1P132F256IM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P132F256IM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P132F256IM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P133">
        <book         name="Documents/efr32mg1-datasheet.pdf"      title="EFR32MG1P133 Data Sheet"/>
        <book         name="Documents/efr32mg1-errata.pdf"         title="EFR32MG1P133 Errata"/>
        <!-- *************************  Device 'EFR32MG1P133F256GM48'  ***************************** -->
        <device Dname="EFR32MG1P133F256GM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P133F256GM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P133F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P231">
        <book         name="Documents/efr32mg1-datasheet.pdf"      title="EFR32MG1P231 Data Sheet"/>
        <book         name="Documents/efr32mg1-errata.pdf"         title="EFR32MG1P231 Errata"/>
        <!-- *************************  Device 'EFR32MG1P231F256GM48'  ***************************** -->
        <device Dname="EFR32MG1P231F256GM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P231F256GM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P231F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P232">
        <book         name="Documents/efr32mg1-datasheet.pdf"      title="EFR32MG1P232 Data Sheet"/>
        <book         name="Documents/efr32mg1-errata.pdf"         title="EFR32MG1P232 Errata"/>
        <!-- *************************  Device 'EFR32MG1P232F256GJ43'  ***************************** -->
        <device Dname="EFR32MG1P232F256GJ43">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P232F256GJ43"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P232F256GJ43.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P232F256GM32'  ***************************** -->
        <device Dname="EFR32MG1P232F256GM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P232F256GM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P232F256GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P232F256GM48'  ***************************** -->
        <device Dname="EFR32MG1P232F256GM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P232F256GM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P232F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P232F256IM32'  ***************************** -->
        <device Dname="EFR32MG1P232F256IM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P232F256IM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P232F256IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P232F256IM48'  ***************************** -->
        <device Dname="EFR32MG1P232F256IM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P232F256IM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P232F256IM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P233">
        <book         name="Documents/efr32mg1-datasheet.pdf"      title="EFR32MG1P233 Data Sheet"/>
        <book         name="Documents/efr32mg1-errata.pdf"         title="EFR32MG1P233 Errata"/>
        <!-- *************************  Device 'EFR32MG1P233F256GM48'  ***************************** -->
        <device Dname="EFR32MG1P233F256GM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P233F256GM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P233F256GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P233F256IM48'  ***************************** -->
        <device Dname="EFR32MG1P233F256IM48">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P233F256IM48"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P233F256IM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P632">
        <book         name="Documents/EFR32MG1-SF-DataSheet.pdf"      title="EFR32MG1P632 Data Sheet"/>
        <book         name="Documents/efr32mg1-sf-errata.pdf"         title="EFR32MG1P632 Errata"/>
        <book         name="Documents/efr32mg1-sf-erratahistory.pdf" title="EFR32MG1P632 Errata History"/>
        <!-- *************************  Device 'EFR32MG1P632F256GM32'  ***************************** -->
        <device Dname="EFR32MG1P632F256GM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P632F256GM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P632F256GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P632F256IM32'  ***************************** -->
        <device Dname="EFR32MG1P632F256IM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P632F256IM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P632F256IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG1P732">
        <book         name="Documents/EFR32MG1-SF-DataSheet.pdf"      title="EFR32MG1P732 Data Sheet"/>
        <book         name="Documents/efr32mg1-sf-errata.pdf"         title="EFR32MG1P732 Errata"/>
        <book         name="Documents/efr32mg1-sf-erratahistory.pdf" title="EFR32MG1P732 Errata History"/>
        <!-- *************************  Device 'EFR32MG1P732F256GM32'  ***************************** -->
        <device Dname="EFR32MG1P732F256GM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P732F256GM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P732F256GM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32MG1P732F256IM32'  ***************************** -->
        <device Dname="EFR32MG1P732F256IM32">
          <compile header="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"  define="EFR32MG1P732F256IM32"/>
          <debug      svd="SVD/EFR32MG1P/EFR32MG1P732F256IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00040000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00007C00"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOP2.FLM"  start="0x00000000"  size="0x00040000"  RAMstart="0x20000000" RAMsize="0x1000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOP2.flash"  start="0x00000000"  size="0x00040000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32MG1P">
      <description>Silicon Labs EFR32MG1P device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32MG1P*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EFR32MG1P">
      <description>System Startup for Silicon Labs EFR32MG1P device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32MG1P/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32MG1P/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFR32MG1P/Source/ARM/startup_efr32mg1p.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/EFR32MG1P/Source/GCC/startup_efr32mg1p.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFR32MG1P/Source/GCC/efr32mg1p.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32MG1P/Source/system_efr32mg1p.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
