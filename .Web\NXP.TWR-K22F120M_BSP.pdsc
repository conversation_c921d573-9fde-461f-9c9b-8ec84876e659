<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>TWR-K22F120M_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for TWRK22F120M</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MK22F51212_DFP" vendor="NXP"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="TWR-K22F120M">
      <description>The TWR-K22F120M is a development board for the Kinetis K22 32-bit ARM® Cortex®-M4 MCUs</description>
      <mountedDevice Dname="MK22FN512xxx12" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MK22F51212_AND_component.serial_manager_AND_component.uart_adapter_AND_device.MK22F51212_startup_AND_driver.adc16_AND_driver.clock_AND_driver.common_AND_driver.dspi_AND_driver.gpio_AND_driver.i2c_AND_driver.port_AND_driver.rtc_AND_driver.smc_AND_driver.uart_AND_utility.debug_console">
      <accept Dname="MK22FN512xxx12" Dvariant="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VDC12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512xxx12" Dvariant="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLL12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512xxx12" Dvariant="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VLH12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512xxx12" Dvariant="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VFX12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512xxx12" Dvariant="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512CAP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512xxx12" Dvariant="MK22FN512VMP12" Dvendor="NXP:11"/>
      <accept Dname="MK22FN512VMP12" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="uart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="Startup" Csub="MK22F51212_startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dspi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="rtc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="adc"/>
    </condition>
  </conditions>
  <examples>
    <example name="adc16_continuous_edma" folder="driver_examples/adc16/continuous_edma" doc="readme.txt">
      <description>The ADC16 continuous EDMA demo application demonstrates the usage of the ADC and EDMA peripheral while in a continuous mode. TheADC16 is first set to continuous mode. In continuous convert configuration, only the initial rising-edge to launch continuous conversions isobserved, and until conversion is aborted, the ADC16 continues to do conversions on the same SCn register that initiated the conversion. EDMA request will be asserted during an ADC16 conversion complete event noted when any of the SC1n[COCO] flags is asserted. EDMA will transferADC16 results to memory and if users press any key, demo will average ADC16 results stored in memory and print average on the terminal.  </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_continuous_edma.uvprojx"/>
        <environment name="iar" load="iar/adc16_continuous_edma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_interrupt" folder="driver_examples/adc16/interrupt" doc="readme.txt">
      <description>The adc16_interrupt example shows how to use interrupt with ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. ADC interrupt would be asserted once the conversion is completed. In ADC ISR, the conversion completed flag would be cleared by reading the conversion result value. Also, the conversion result value is stored, and the ISR counter is increased. These information would be printed when the execution return to the main loop.The point is that the ADC16 interrupt configuration is set when configuring the ADC16's conversion channel. When in software trigger mode, the conversion would be launched by the operation of configuring channel, just like writing aconversion command. So if user wants to generate the interrupt every time the conversion is completed, the channel's configuration with enabling interrupt setting would be used for each conversion.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_interrupt.uvprojx"/>
        <environment name="iar" load="iar/adc16_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_low_power_peripheral" folder="demo_apps/adc16_low_power_peripheral" doc="readme.txt">
      <description>The ADC Low Power demo application demonstrates the usage of the ADC peripheral while in a low power mode. Themicrocontroller is first set to very low power stop (VLPS) mode. Every 500 ms, an interrupt wakes up the ADC module andreads the current temperature of the microcontroller. While the temperature remains within boundaries, both LEDs are on.If the core temperature is higher or lower than average, the LEDs change state respectively. You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_low_power_peripheral.uvprojx"/>
        <environment name="iar" load="iar/adc16_low_power_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="adc16_polling" folder="driver_examples/adc16/polling" doc="readme.txt">
      <description>The adc16_polling example shows the simplest way to use ADC16 driver.In this example, user should indicate a channel to provide a voltage signal (can be controlled by user) as the ADC16'ssample input. When running the project, typing any key into debug console would trigger the conversion. The execution would check the conversion completed flag in loop until the flag is asserted, which means the conversion is completed. Then read the conversion result value and print it to debug console.Note, the default setting of initialization for the ADC converter is just an available configuration. User can changethe configuration structure's setting in application to fit the special requirement.The auto-calibration is not essential but strongly recommended. It can help to adjust the converter itself and improvethe ADC16's performance.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/adc16_polling.uvprojx"/>
        <environment name="iar" load="iar/adc16_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="bubble_peripheral" folder="demo_apps/bubble_peripheral" doc="readme.txt">
      <description>The bubble level demo demonstrates basic usage of the on-board accelerometer to implement a bubble level. A bubble level utilizes two axes to visually show deviation from a level plane (0 degrees) on a given axis. You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/bubble_peripheral.uvprojx"/>
        <environment name="iar" load="iar/bubble_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_interrupt" folder="driver_examples/cmp/interrupt" doc="readme.txt">
      <description>The CMP interrupt Example shows how to use interrupt with CMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input. On the negative side, the internal 6-bit DAC is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user-defined channel's voltage crosses the internal DAC's value. The change ofcomparator's output would generate the falling and rising edge events with their interrupts enabled. When any CMP interrupt happens, the CMP's ISR would turn on the LED light if detecting the output's rising edge, or turn off it whendetecting the output's falling edge.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/cmp_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmp_polling" folder="driver_examples/cmp/polling" doc="readme.txt">
      <description>The CMP polling Example shows the simplest way to use CMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the CMP's positive channel input. On the negative side, the internal 6-bit DAC is used to generate the fixed voltage abouthalf value of reference voltage.When running the project, change the input voltage of user-defined channel, then the comparator's output would changebetween logic one and zero when the user's voltage crosses the internal DAC's value. The endless loop in main() functionwould detect the logic value of comparator's output, and change the LED. The LED would be turned on when the compareoutput is logic one, or turned off when zero.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmp_polling.uvprojx"/>
        <environment name="iar" load="iar/cmp_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_dspi_edma_b2b_transfer_master" folder="cmsis_driver_examples/dspi/edma_b2b_transfer/master" doc="readme.txt">
      <description>The dspi_edma_b2b_transfer example shows how to use DSPI CMSIS driver in edma way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_edma_b2b_transfer_master.c' includes the DSPI master code.1. DSPI master send/received data to/from DSPI slave in edma . </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_dspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_dspi_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_dspi_edma_b2b_transfer_slave" folder="cmsis_driver_examples/dspi/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The dspi_edma_b2b_transfer example shows how to use DSPI CMSIS driver in edma way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_edma_b2b_transfer_slave.c' includes the DSPI slave code.1. DSPI master send/received data to/from DSPI slave in edma . </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_dspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_dspi_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_dspi_int_b2b_transfer_master" folder="cmsis_driver_examples/dspi/int_b2b_transfer/master" doc="readme.txt">
      <description>The dspi_int_b2b_transfer example shows how to use DSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_int_b2b_transfer_master.c' includes the DSPI master code.This example uses the transactional API in DSPI driver.1. DSPI master send/received data to/from DSPI slave in interrupt . </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_dspi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_dspi_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_dspi_int_b2b_transfer_slave" folder="cmsis_driver_examples/dspi/int_b2b_transfer/slave" doc="readme.txt">
      <description>The dspi_int_b2b_transfer example shows how to use DSPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_int_b2b_transfer_slave.c' includes the DSPI slave code.This example uses the transactional API in DSPI driver.1. DSPI master send/received data to/from DSPI slave in interrupt . </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_dspi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_dspi_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_edma_b2b_transfer_master" folder="cmsis_driver_examples/i2c/edma_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_edma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with EDMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_i2c_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_edma_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/edma_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_edma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a EDMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_i2c_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_master" folder="cmsis_driver_examples/i2c/int_b2b_transfer/master" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_i2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/int_b2b_transfer/slave" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_i2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_i2c_read_accel_value_transfer" folder="cmsis_driver_examples/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_read_accel_value example shows how to use CMSIS I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_edma_transfer" folder="cmsis_driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpuart_interrupt_transfer" folder="cmsis_driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_edma_transfer" folder="cmsis_driver_examples/uart/edma_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_uart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_uart_interrupt_transfer" folder="cmsis_driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_adc_peripheral" folder="demo_apps/dac_adc_peripheral" doc="readme.txt">
      <description>The DAC / ADC demo application demonstrates the use of the DAC and ADC peripherals. This application demonstrates how toconfigure the DAC and set the output on the DAC. This demo also demonstrates how to configure the ADC in 'Blocking Mode'and how to read ADC values.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_adc_peripheral.uvprojx"/>
        <environment name="iar" load="iar/dac_adc_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_basic" folder="driver_examples/dac/basic" doc="readme.txt">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's buffer feature is not enabled, the first item of the buffer is used as the DAC output data register.The converter would always output the value of the first item. In this example, it gets the value from terminal,outputs the DAC output voltage through DAC output pin.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_basic.uvprojx"/>
        <environment name="iar" load="iar/dac_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_buffer_interrupt" folder="driver_examples/dac/buffer_interrupt" doc="readme.txt">
      <description>The dac_buffer_interrupt example shows how to use DAC buffer with interrupts.When the DAC's buffer feature is enabled, user can benefit from the automation of updating DAC output by hardware/software trigger. As we know, the DAC converter outputs the value of item pointed by current read pointer. Once the buffer is triggered by software or hardware, the buffer's read pointer would move automatically as the work mode is set,like normal (cycle) mode, swing mode, one-time-scan mode or FIFO mode.In this example, it captures the user's type-in operation from terminal and does the software trigger to the buffer.The terminal would also display the log that shows the current buffer pointer's position with buffer events.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_buffer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dac_buffer_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dac_continuous_pdb_edma" folder="driver_examples/dac/continuous_pdb_edma" doc="readme.txt">
      <description>The demo shows how to use the PDB to generate a DAC trigger and use the DMA to transfer data into DAC buffer.In this example, DAC is first set to normal buffer mode. PDB is as DAC hardware trigger source and DMA would work when DAC read pointer is zero. When run the example, the DAC is triggered by PDB and the read pointer increases by one,every time the trigger occurs. When the read pointer reaches the upper limit, it goes to zero directly in the next trigger event.while read pointer goes to zero, DMA request will be triggered and transfer data into DAC buffer. The user should probethe DAC output with a oscilloscope to see the Half-sine signal.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dac_continuous_pdb_edma.uvprojx"/>
        <environment name="iar" load="iar/dac_continuous_pdb_edma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_edma_b2b_transfer_master" folder="driver_examples/dspi/edma_b2b_transfer/master" doc="readme.txt">
      <description>The dspi_edma_b2b_transfer example shows how to use DSPI driver in edma way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_edma_b2b_transfer_master.c' includes the DSPI master code.1. DSPI master send/received data to/from DSPI slave in edma . (DSPI Slave using edma to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_edma_b2b_transfer_slave" folder="driver_examples/dspi/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The dspi_edma_b2b_transfer example shows how to use DSPI driver in edma way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_edma_b2b_transfer_slave.c' includes the DSPI slave code.1. DSPI master send/received data to/from DSPI slave in edma . (DSPI Slave using edma to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_half_duplex_edma_master" folder="driver_examples/dspi/half_duplex_transfer/edma/master" doc="readme.txt">
      <description>The dspi_half_duplex_edma_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sendsa piece of data to slave, and receive a piece of data from slave. This example checks if the data received fromslave is correct.Besides, master will transfer in EDMA way. </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_half_duplex_edma_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_half_duplex_edma_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_half_duplex_edma_slave" folder="driver_examples/dspi/half_duplex_transfer/edma/slave" doc="readme.txt">
      <description>The dspi_half_duplex_edma_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses edma mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the begain address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_half_duplex_edma_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_half_duplex_edma_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_half_duplex_int_master" folder="driver_examples/dspi/half_duplex_transfer/int/master" doc="readme.txt">
      <description>The dspi_half_duplex_int_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer in interrupt way. </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_half_duplex_int_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_half_duplex_int_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_half_duplex_int_slave" folder="driver_examples/dspi/half_duplex_transfer/int/slave" doc="readme.txt">
      <description>The dspi_half_duplex_int_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the begain address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_half_duplex_int_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_half_duplex_int_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_half_duplex_polling_master" folder="driver_examples/dspi/half_duplex_transfer/polling/master" doc="readme.txt">
      <description>The dspi_half_duplex_polling_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends apiece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct. Besides, master will transfer in polling way. </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_half_duplex_polling_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_half_duplex_polling_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_half_duplex_polling_slave" folder="driver_examples/dspi/half_duplex_transfer/polling/slave" doc="readme.txt">
      <description>The dspi_half_duplex_polling_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_half_duplex_polling_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_half_duplex_polling_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_interrupt" folder="driver_examples/dspi/interrupt" doc="readme.txt">
      <description>The dspi_interrupt example shows how to use DSPI driver in interrupt way:In this example , one dspi instance used as DSPI master and another dspi instance used as DSPI slave in the same board.This example does not use the transactional API in DSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. DSPI master send/received data to/from DSPI slave in interrupt . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/dspi_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_interrupt_b2b_master" folder="driver_examples/dspi/interrupt_b2b/master" doc="readme.txt">
      <description>The dspi_interrupt_b2b example shows how to use DSPI driver in interrupt way:In this example , we need two boards , one board used as DSPI master and another board used as DSPI slave.The file 'dspi_interrupt_b2b_master.c' includes the DSPI master code.This example does not use the transactional API in DSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. DSPI master send/received data to/from DSPI slave in interrupt . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_interrupt_b2b_slave" folder="driver_examples/dspi/interrupt_b2b/slave" doc="readme.txt">
      <description>The dspi_interrupt_b2b example shows how to use DSPI driver in interrupt way:In this example , we need two boards , one board used as DSPI master and another board used as DSPI slave.The file 'dspi_interrupt_b2b_slave.c' includes the DSPI slave code.This example does not use the transactional API in DSPI driver. It's a demonstration that how to use the interrupt in KSDK driver.1. DSPI master send/received data to/from DSPI slave in interrupt . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_interrupt_b2b_transfer_master" folder="driver_examples/dspi/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The dspi_interrupt_b2b_transfer example shows how to use DSPI driver in interrupt way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_interrupt_b2b_transfer_master.c' includes the DSPI master code.This example uses the transactional API in DSPI driver.1. DSPI master send/received data to/from DSPI slave in interrupt . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_interrupt_b2b_transfer_slave" folder="driver_examples/dspi/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The dspi_interrupt_b2b_transfer example shows how to use DSPI driver in interrupt way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_interrupt_b2b_transfer_slave.c' includes the DSPI slave code.This example uses the transactional API in DSPI driver.1. DSPI master send/received data to/from DSPI slave in interrupt . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_polling_b2b_transfer_master" folder="driver_examples/dspi/polling_b2b_transfer/master" doc="readme.txt">
      <description>The dspi_polling_b2b_transfer example shows how to use DSPI driver in polling way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_polling_b2b_transfer_master.c' includes the DSPI master code.1. DSPI master send/received data to/from DSPI slave in polling . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/dspi_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dspi_polling_b2b_transfer_slave" folder="driver_examples/dspi/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The dspi_polling_b2b_transfer example shows how to use DSPI driver in polling way:In this example , we need two boards, one board used as DSPI master and another board used as DSPI slave.The file 'dspi_polling_b2b_transfer_slave.c' includes the DSPI slave code.1. DSPI master send/received data to/from DSPI slave in polling . (DSPI Slave using interrupt to receive/send the data)</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dspi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/dspi_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ecompass_peripheral" folder="demo_apps/ecompass_peripheral" doc="readme.txt">
      <description>The E-Compass demo application demonstrates the use of the FXOS8700 sensor. The tilt-compensated algorithm calculatesall three angles (pitch, roll, and yaw or compass heading). You can open the mex file with MCUXpresso Config Tool to do further configuration of pin, clock and peripheral.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ecompass_peripheral.uvprojx"/>
        <environment name="iar" load="iar/ecompass_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_memory_to_memory" folder="driver_examples/edma/memory_to_memory" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/edma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="edma_scatter_gather" folder="driver_examples/edma/scatter_gather" doc="readme.txt">
      <description>The EDMA memory to memory example is a simple demonstration program that uses the SDK software.It excuates one shot transfer from source buffer to destination buffer using the SDK EDMA drivers.The purpose of this example is to show how to use the EDMA and to provide a simple example fordebugging and further development.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/edma_scatter_gather.uvprojx"/>
        <environment name="iar" load="iar/edma_scatter_gather.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ewm" folder="driver_examples/ewm" doc="readme.txt">
      <description>The EWM Example project is to demonstrate usage of the KSDK EWM driver.In the example, EWM counter is continuously refreshed until button is pressed.Once the button is pressed, EWM counter will expire and interrupt will be generated.After the first pressing, another interrupt can be triggered by pressing button again.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ewm.uvprojx"/>
        <environment name="iar" load="iar/ewm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flexbus" folder="driver_examples/flexbus" doc="readme.txt">
      <description>The FLEXBUS (External Bus Interface) example show how to write/read to external memories (MRAM)by using FLEXBUS driver.In this example, FLEXBUS driver is configured with  some specific parameter:    - 1 byte port size of transfer    - Wait 2 states    - MRAM address for using FlexBus    - 512 Kbytes memory size</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flexbus.uvprojx"/>
        <environment name="iar" load="iar/flexbus.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_combine_pwm" folder="driver_examples/ftm/combine_pwm" doc="readme.txt">
      <description>The FTM project is a demonstration program of generating a combined PWM signal by the SDK FTM driver. It sets up the FTMhardware block to output PWM signals on two TPM channels. The example also shows the complementary mode of operationand deadtime insertion.On boards that have 2 LEDs connected to the FTM pins, the user will see a change in LED brightness.And if the board do not support LEDs to show, the outputs can be observed by oscilloscope.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_combine_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_combine_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_dual_edge_capture" folder="driver_examples/ftm/dual_edge_capture" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's dual-edge capture feature.This feature is available only on certain SoC's.The example sets up a FTM channel-pair for dual-edge capture. Once the input signal is received,this example will print the capture values and period of the input signal on the terminal window.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_dual_edge_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_dual_edge_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_input_capture" folder="driver_examples/ftm/input_capture" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's input capture feature.The example sets up a FTM channel for dual-edge capture. Once the input signal is received,this example will print the capture value.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_input_capture.uvprojx"/>
        <environment name="iar" load="iar/ftm_input_capture.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_output_compare" folder="driver_examples/ftm/output_compare" doc="readme.txt">
      <description>The FTM project is a demonstration program of the SDK FTM driver's output compare feature.It sets up one FTM channel to toggle the output when a match occurs with the channel value. The usershould probe the FTM output with a oscilloscope to see the signal toggling.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_output_compare.uvprojx"/>
        <environment name="iar" load="iar/ftm_output_compare.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_pwm_twochannel" folder="driver_examples/ftm/pwm_twochannel" doc="readme.txt">
      <description>The FTM pwm two channel Example project is a demonstration program that uses the KSDK software to generate a square pulse PWM on 2 channel to control the LED brightness.- FTM generates a PWM with the increasing and decreasing duty cycle.- LED brightness is increasing and then dimming. This is a continuous process.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_pwm_twochannel.uvprojx"/>
        <environment name="iar" load="iar/ftm_pwm_twochannel.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_simple_pwm" folder="driver_examples/ftm/simple_pwm" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver. It sets up the FTMhardware block to output a center-aligned PWM signal. The PWM dutycycle is periodically updated.On boards that have an LED connected to the FTM pins, the user will see a change in LED brightness.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/ftm_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ftm_timer" folder="driver_examples/ftm/timer" doc="readme.txt">
      <description>The FTM project is a simple demonstration program of the SDK FTM driver to use FTM as a timer.It sets up the FTM hardware block to trigger an interrupt every 1 millisecond.When the FTM interrupt is triggered a message a printed on the UART terminal.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ftm_timer.uvprojx"/>
        <environment name="iar" load="iar/ftm_timer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_input_interrupt" folder="driver_examples/gpio/input_interrupt" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example uses the software button to control/toggle the LED.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_input_interrupt.uvprojx"/>
        <environment name="iar" load="iar/gpio_input_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs.The example is supported by the set, clear, and toggle write-only registers for each port output data register. The example take turns to shine the LED.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_edma_b2b_transfer_master" folder="driver_examples/i2c/edma_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_edma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with EDMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_edma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_edma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_edma_b2b_transfer_slave" folder="driver_examples/i2c/edma_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_edma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a EDMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_edma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_edma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt" folder="driver_examples/i2c/interrupt" doc="readme.txt">
      <description>The i2c_functional_interrupt example shows how to use I2C functional driver to build a interrupt based application:In this example , one i2c instance used as I2C master and another i2c instance used as I2C slave .1. I2C master send data to I2C slave in interrupt . (I2C Slave using interrupt to receive the data)2. I2C master read data from I2C slave in interrupt . (I2C Slave using interrupt to send the data)3. The example assumes that the connection is OK between master and slave, so there's NO error handling code.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_master" folder="driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_interrupt_b2b_transfer_slave" folder="driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_master" folder="driver_examples/i2c/polling_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_polling_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_polling_b2b_transfer_slave" folder="driver_examples/i2c/polling_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_polling_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/i2c_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="i2c_read_accel_value_transfer" folder="driver_examples/i2c/read_accel_value_transfer" doc="readme.txt">
      <description>The i2c_read_accel_value example shows how to use I2C driver to communicate with an i2c device: 1. How to use the i2c driver to read a i2c device who_am_I register. 2. How to use the i2c driver to write/read the device registers.In this example, the values of three-axis accelerometer print to the serial terminal on PC throughthe virtual serial port on board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/i2c_read_accel_value_transfer.uvprojx"/>
        <environment name="iar" load="iar/i2c_read_accel_value_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lptmr" folder="driver_examples/lptmr" doc="readme.txt">
      <description>The LPTMR project is a simple demonstration program of the SDK LPTMR driver. It sets up the LPTMRhardware block to trigger a periodic interrupt after every 1 second. When the LPTMR interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lptmr.uvprojx"/>
        <environment name="iar" load="iar/lptmr.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_rb_transfer" folder="driver_examples/lpuart/edma_rb_transfer" doc="readme.txt">
      <description>The lpuart_edma Ring Buffer Example project is to demonstrate usage of the KSDK lpuart driver.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_edma_transfer" folder="driver_examples/lpuart/edma_transfer" doc="readme.txt">
      <description>The lpuart_edma Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt" folder="driver_examples/lpuart/interrupt" doc="readme.txt">
      <description>The lpuart_functioncal_interrupt example shows how to use lpuart driver functionalAPI to receive data with interrupt method:In this example, one lpuart instance connect to PC, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_rb_transfer" folder="driver_examples/lpuart/interrupt_rb_transfer" doc="readme.txt">
      <description>The lpuart_interrupt_ring_buffer Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_interrupt_transfer" folder="driver_examples/lpuart/interrupt_transfer" doc="readme.txt">
      <description>The lpuart_interrupt Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console in a group of 8 characters.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpuart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpuart_polling" folder="driver_examples/lpuart/polling" doc="readme.txt">
      <description>The lpuart_polling Example project is to demonstrate usage of the KSDK lpuart driver.In the example, you can send characters to the console back and they will be printed out onto console instantly.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpuart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpuart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fee_blpe" folder="driver_examples/mcg/fee_blpe" doc="readme.txt">
      <description>The fee_bple example shows how to use MCG driver to change from FEE mode to BLPE mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work flow    Boot to Fee mode from default reset mode    Change from FEE -&gt; FBE -&gt; BLPE    Change back BLPE -&gt; FBE -&gt; FEE    Get System clock in FEE mode to blink LEDIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fee_blpe.uvprojx"/>
        <environment name="iar" load="iar/mcg_fee_blpe.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fee_blpi" folder="driver_examples/mcg/fee_blpi" doc="readme.txt">
      <description>The fee_bpli example shows how to use MCG driver to change from FEE mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work flow    Boot to FEE mode from default reset mode    Change from FEE -&gt; FBI -&gt; BLPI    Change back BLPI -&gt; FBI -&gt; FEE    Get System clock in FEE mode to blink LEDIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fee_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_fee_blpi.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_fei_blpi" folder="driver_examples/mcg/fei_blpi" doc="readme.txt">
      <description>The fei_bpli example shows how to use MCG driver to change from FEI mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work flow    Boot to FEI mode from default reset mode    Change mode FEI -&gt; FBI -&gt; BLPI    Change back BLPE -&gt; FBI -&gt; FEI    Get System clock in FEI mode to blink LEDIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_fei_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_fei_blpi.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_pee_blpe" folder="driver_examples/mcg/pee_blpe" doc="readme.txt">
      <description>The pee_bple example shows how to use MCG driver to change from PEE mode to BLPE mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work flow    Boot to PEE mode from default reset mode    Change mode PEE -&gt; PBE -&gt; BLPE    Change back BLPE -&gt; PBE -&gt; PEE    Get System clock in PEE mode to blink LEDIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_pee_blpe.uvprojx"/>
        <environment name="iar" load="iar/mcg_pee_blpe.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mcg_pee_blpi" folder="driver_examples/mcg/pee_blpi" doc="readme.txt">
      <description>The pee_bpli example shows how to use MCG driver to change from PEE mode to BLPI mode: 1. How to use the mode functions for MCG mode switch. 2. How to use the frequency functions to get current MCG frequency. 3. Work flow    Boot to PEE mode from default reset mode    Change mode PEE -&gt; PBE -&gt;FBE -&gt; FBI -&gt; BLPI    Change back BLPI -&gt; FBI -&gt; FBE -&gt; PBE -&gt; PEE    Get System clock in PEE mode to blink LEDIn this example, because the debug console's clock frequency may change,so the example running information is not output from debug console. Here theLED blinks to show that the example finished successfully.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mcg_pee_blpi.uvprojx"/>
        <environment name="iar" load="iar/mcg_pee_blpi.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_adc16_trigger" folder="driver_examples/pdb/adc16_trigger" doc="readme.txt">
      <description>The pdb_adc16_trigger example shows how to use the PDB to generate a ADC trigger.Based on the basic counter, to use the ADC trigger, just to enable the ADC trigger's "milestone" and set the user-defined value for it.After the PDB counter is triggered to start, when the counter pass the "milestone", the ADC's Pre-Trigger would begenerated and sent to the ADC module.In this example, the ADC16 is configured with hardware trigger and conversion complete interrupt enabled.Once it gets the trigger from the PDB, the conversion goes, then the ISR would be executed.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_adc16_trigger.uvprojx"/>
        <environment name="iar" load="iar/pdb_adc16_trigger.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_dac_trigger" folder="driver_examples/pdb/dac_trigger" doc="readme.txt">
      <description>The pdb_dac_trigger example shows how to use the PDB to generate a DAC trigger.Based on the basic counter, to use the DAC trigger, just to enable the DAC trigger's "milestone" and set the user-defined value for it.The DAC's "milestone" is called as "interval". Multiple DAC trigger intervals can be included into one PDB counter's cycle.DAC trigger's counter would reset after the trigger is created and start counting again to the interval value.In this example, the DAC is configured with hardware buffer enabled in normal work mode. Once it gets the trigger from the PDB, the buffer read pointer increases.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_dac_trigger.uvprojx"/>
        <environment name="iar" load="iar/pdb_dac_trigger.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pdb_delay_interrupt" folder="driver_examples/pdb/delay_interrupt" doc="readme.txt">
      <description>The pdb_delay_interrupt example show how to use the PDB as a general programmable interrupt timer.The PDB is triggered by software, and other external triggers are generated from PDB in this project,so that user can see just a general counter is working with interrupt.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pdb_delay_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pdb_delay_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pflash" folder="driver_examples/flash/pflash" doc="readme.txt">
      <description>The pflash example shows how to use flash driver to operate program flash:</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pflash.uvprojx"/>
        <environment name="iar" load="iar/pflash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pit" folder="driver_examples/pit" doc="readme.txt">
      <description>The PIT project is a simple demonstration program of the SDK PIT driver. It sets up the PIThardware block to trigger a periodic interrupt after every 1 second. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pit.uvprojx"/>
        <environment name="iar" load="iar/pit.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager" folder="demo_apps/power_manager" doc="readme.txt">
      <description>The Power manager demo application demonstrates the use of power modes in the KSDK. The demo use the notification mechanismand prints the power mode menu through the debug console, where the user can set the MCU to a specific power mode. The usercan also set the wakeup source by following the debug console prompts. The purpose of this demo is to demonstrate theimplementation of a power mode manager. The callback can be registered to the framework. If a power mode transition happens,the callback will be called and user can do something. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager.uvprojx"/>
        <environment name="iar" load="iar/power_manager.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch" folder="demo_apps/power_mode_switch" doc="readme.txt">
      <description>The Power mode switch demo application demonstrates the use of power modes in the KSDK. The demo prints the power mode menuthrough the debug console, where the user can set the MCU to a specific power mode. The user can also set the wakeupsource by following the debug console prompts. The purpose of this demo is to show how to switch between different power modes, and how to configure a wakeup source and wakeup the MCU from low power modes. Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - Debug pins(e.g SWD_DIO) would consume addtional power, had better to disable related pins or disconnect them. </description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rnga_random" folder="driver_examples/rnga/random" doc="readme.txt">
      <description>The RNGA is a digital integrated circuit capable of generating the 32-bit random numbers. The RNGAExample project is a demonstration program that uses the KSDK software to generate random numbersand prints them to the terminal.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rnga_random.uvprojx"/>
        <environment name="iar" load="iar/rnga_random.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc" folder="driver_examples/rtc" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc.uvprojx"/>
        <environment name="iar" load="iar/rtc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func" folder="demo_apps/rtc_func" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func.uvprojx"/>
        <environment name="iar" load="iar/rtc_func.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_func_peripheral" folder="demo_apps/rtc_func_peripheral" doc="readme.txt">
      <description>The RTC demo application demonstrates the important features of the RTC Module by using the RTC Peripheral Driver.The RTC demo supports the following features:- Calendar  + Get the current date time with Year, Month, Day, Hour, Minute, and Second.  + Set the current date time with Year, Month, Day, Hour, Minute, and Second.- Alarm  + Set the alarm based on the current time.  + Application prints a notification when the alarm expires.- Seconds interrupt  + Use second interrupt function to display a digital time blink every second.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_func_peripheral.uvprojx"/>
        <environment name="iar" load="iar/rtc_func_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai" folder="demo_apps/sai" doc="readme.txt">
      <description>The SAI Demo application demonstrates complicated digital audio playback and record case. The demo provide these features below:1. Duplex audio transfer, record and playback at same time.2. Playback a 250 Hz sine wave data generated by CMSIS-DSP library. Also compute the fundamental frequency using Fast Fourier Transform with CMSIS-DSP library.3. Record a 5 seconds audio to sdcard with a FatFs system. After record, playback it. This feature may only existed in platform which support sdhc.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai.uvprojx"/>
        <environment name="iar" load="iar/sai.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_record_playback" folder="driver_examples/sai/edma_record_playback" doc="readme.txt">
      <description>The sai_edma_record_playback example shows how to use sai driver with EDMA: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_record_playback.uvprojx"/>
        <environment name="iar" load="iar/sai_edma_record_playback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_edma_transfer" folder="driver_examples/sai/edma_transfer" doc="readme.txt">
      <description>The sai_edma_transfer example shows how to use sai driver with EDMA:In this example, one sai instance playbacks the audio data stored in flash/SRAM using EDMA channel.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/sai_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt" folder="driver_examples/sai/interrupt" doc="readme.txt">
      <description>The sai_interrupt example shows how to use sai functional API to implement interrupt playback:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt.uvprojx"/>
        <environment name="iar" load="iar/sai_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_record_playback" folder="driver_examples/sai/interrupt_record_playback" doc="readme.txt">
      <description>The sai_interrupt_record_playback example shows how to use sai driver with record and playback features: In this example, one sai instance record the audio data from input and playbacks the audio data.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_record_playback.uvprojx"/>
        <environment name="iar" load="iar/sai_interrupt_record_playback.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_interrupt_transfer" folder="driver_examples/sai/interrupt_transfer" doc="readme.txt">
      <description>The sai_interrupt_transfer example shows how to use sai driver with interrupt:In this example, one sai instance playbacks the audio data stored in flash/SRAM using interrupt.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/sai_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sai_peripheral" folder="demo_apps/sai_peripheral" doc="readme.txt">
      <description>The SAI Demo application demonstrates complicated digital audio playback and record case. The demo provide these features below:1. Duplex audio transfer, record and playback at same time.2. Playback a 250 Hz sine wave data generated by CMSIS-DSP library. Also compute the fundamental frequency using Fast Fourier Transform with CMSIS-DSP library.3. Record a 5 seconds audio to sdcard with a FatFs system. After record, playback it. This feature may only existed in platform which support sdhc.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sai_peripheral.uvprojx"/>
        <environment name="iar" load="iar/sai_peripheral.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="demo_apps/shell" doc="readme.txt">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="iar" load="iar/shell.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_edma_rb_transfer" folder="driver_examples/uart/edma_rb_transfer" doc="readme.txt">
      <description>The uart_edma ring buffer example shows how to use uart driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_edma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_edma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_edma_transfer" folder="driver_examples/uart/edma_transfer" doc="readme.txt">
      <description>The uart_edma example shows how to use uart driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_edma_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_edma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt" folder="driver_examples/uart/interrupt" doc="readme.txt">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_rb_transfer" folder="driver_examples/uart/interrupt_rb_transfer" doc="readme.txt">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_interrupt_transfer" folder="driver_examples/uart/interrupt_transfer" doc="readme.txt">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/uart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="uart_polling" folder="driver_examples/uart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/uart_polling.uvprojx"/>
        <environment name="iar" load="iar/uart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="vref_example" folder="driver_examples/vref" doc="readme.txt">
      <description>In this example, the adc16 module is initiealized and used to measure the VREF output voltage. So, it cannot use interal VREF as the reference voltage. Then, user should configure the VREF output pin as the ADC16's sample input. When running the project, it will firstly measure the VREF output voltage within the default (factory) trim value. Then, it will measure the VREF output voltage under different trim value.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/vref_example.uvprojx"/>
        <environment name="iar" load="iar/vref_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wdog" folder="driver_examples/wdog" doc="readme.txt">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 10 times of refreshing the watchdog in None-window mode, a timeout reset is generated.We also try to refresh out of window to trigger reset after 10 times of refreshing in Window mode.</description>
      <board name="TWR-K22F120M" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wdog.uvprojx"/>
        <environment name="iar" load="iar/wdog.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="twrk22f120m" Cversion="1.0.0" condition="device.MK22F51212_AND_component.serial_manager_AND_component.uart_adapter_AND_device.MK22F51212_startup_AND_driver.adc16_AND_driver.clock_AND_driver.common_AND_driver.dspi_AND_driver.gpio_AND_driver.i2c_AND_driver.port_AND_driver.rtc_AND_driver.smc_AND_driver.uart_AND_utility.debug_console">
      <description/>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_usb_config" Cversion="1.0.0">
      <description/>
      <files>
        <file category="header" name="component_examples/config/usb_device_config.h"/>
      </files>
    </component>
  </components>
</package>
