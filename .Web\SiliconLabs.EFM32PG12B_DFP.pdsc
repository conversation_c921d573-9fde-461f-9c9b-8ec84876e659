<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EFM32PG12B_DFP</name>
  <description>Silicon Labs EFM32PG12B Pearl Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EFM32PG12B_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EFM32PG12B_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFM32PG12B</keyword>
    <keyword>EFM32</keyword>
    <keyword>Pearl Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFM32PG12B Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <processor Dclock="40000000"/>
      <book name="Documents/cortex_m4_dgug.pdf"      title="Cortex-M4 Generic User Guide"/>
      <book name="Documents/efm32pg12-rm.pdf"  title="EFM32PG12B Reference Manual"/>
      <description>
- ARM Cortex-M4 at 40 MHz&#xD;&#xA;- Ultra low energy operation in active and sleep modes&#xD;&#xA;- Hardware cryptographic engine (AES, ECC, and SHA) and TRNG&#xD;&#xA;- Autonomous low energy sensor interface (LESENSE)&#xD;&#xA;- Rich analog features including ADC, VDAC, OPAMPs, and capacitive touch sense&#xD;&#xA;- Integrated DC-DC converter&#xD;&#xA;- 5 V tolerant I/O&#xD;&#xA;&#xD;&#xA;The EFM32PG12 Pearl Gecko MCUs are the world's most energyfriendly microcontrollers. EFM32PG12 features a powerful 32-bit ARM Cortex-M4 and a wide selection of peripherals, including a unique cryptographic hardware engine, True Random Number Generator, and robust capacitive touch sense unit. These features, combined with ultra-low current active and sleep modes, make EFM32PG12 microcontrollers well suited for any battery-powered application, as well as other systems requiring high performance and lowenergy consumption.
      </description>

      <subFamily DsubFamily="EFM32PG12B500">
        <book         name="Documents/efm32pg12-datasheet.pdf"      title="EFM32PG12B500 Data Sheet"/>
        <book         name="Documents/efm32pg12-errata.pdf"         title="EFM32PG12B500 Errata"/>
        <!-- *************************  Device 'EFM32PG12B500F1024GL125'  ***************************** -->
        <device Dname="EFM32PG12B500F1024GL125">
          <compile header="Device/SiliconLabs/EFM32PG12B/Include/em_device.h"  define="EFM32PG12B500F1024GL125"/>
          <debug      svd="SVD/EFM32PG12B/EFM32PG12B500F1024GL125.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32PG12B500F1024GM48'  ***************************** -->
        <device Dname="EFM32PG12B500F1024GM48">
          <compile header="Device/SiliconLabs/EFM32PG12B/Include/em_device.h"  define="EFM32PG12B500F1024GM48"/>
          <debug      svd="SVD/EFM32PG12B/EFM32PG12B500F1024GM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32PG12B500F1024IL125'  ***************************** -->
        <device Dname="EFM32PG12B500F1024IL125">
          <compile header="Device/SiliconLabs/EFM32PG12B/Include/em_device.h"  define="EFM32PG12B500F1024IL125"/>
          <debug      svd="SVD/EFM32PG12B/EFM32PG12B500F1024IL125.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32PG12B500F1024IM48'  ***************************** -->
        <device Dname="EFM32PG12B500F1024IM48">
          <compile header="Device/SiliconLabs/EFM32PG12B/Include/em_device.h"  define="EFM32PG12B500F1024IM48"/>
          <debug      svd="SVD/EFM32PG12B/EFM32PG12B500F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS1.FLM"  start="0x00000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x4000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS1.flash"  start="0x00000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFM32PG12B">
      <description>Silicon Labs EFM32PG12B device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFM32PG12B*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EFM32PG12B">
      <description>System Startup for Silicon Labs EFM32PG12B device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFM32PG12B/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFM32PG12B/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFM32PG12B/Source/ARM/startup_efm32pg12b.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/EFM32PG12B/Source/GCC/startup_efm32pg12b.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFM32PG12B/Source/GCC/efm32pg12b.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFM32PG12B/Source/system_efm32pg12b.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
