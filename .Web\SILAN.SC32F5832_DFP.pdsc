<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
	<vendor>SILAN</vendor>
	<name>SC32F5832_DFP</name>
	<description>Silan SC32F58xx Device Family Pack</description>
	<url>https://github.com/Silanpack/Pack/raw/main/</url>
	<releases>
		<release version="1.1.4" date="2023-10-17">
			Update the IAP_Type of SC32F5832 and SC32F5864 header files
		</release>
	
		<release version="1.1.3" date="2023-07-13">
			Update Flash algorithm
		</release>
	
		<release version="1.1.2" date="2021-07-29">
			Update svd
		</release>
		
		<release version="1.1.1" date="2021-03-16">
			Add SC32F5832_BOOT、SC32F5864_BOOT
		</release>	
	
		<release version="1.1.0" date="2020-12-04">
			Initial Version
		</release>	
	</releases>
	<keywords>
		<!-- keywords for indexing -->
		<keyword>SC32F58xx</keyword>
		<keyword>SC32F5832</keyword>
		<keyword>SC32F5864</keyword>
		<keyword>SC32F5832_BOOT</keyword>
		<keyword>SC32F5864_BOOT</keyword>
	</keywords>
	<!-- devices section (mandatory for Device Family Packs) -->
	<devices>
		<family Dfamily="SC32F58 Series" Dvendor="SILAN:164">
			<!-- ************************  Subfamily 'Silan_SC32F58xx'  **************************** -->
			<subFamily DsubFamily="SC32F58xx">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="72000000"/>
				<feature type="VCC"				n="2.00"	m="5.50"/>
				<feature type="Temp"			n="-40"		m="85"/>
				<feature type="UART"			n="2"/>
				<feature type="SPI"				n="1"/>
				<feature type="Timer"			n="1"       m="16"		name="ETIMER T0"/>
				<feature type="Timer"			n="2"       m="32"		name="General Purpose Timer TIM6"/>
				<feature type="WDT"    			n="2"/>
				<feature type="PWM"    			n="8"       m="16"/>
				<feature type="ADC"    			n="16"      m="12"/>
				
				<!-- *************************  Device 'SC32F5832'  ***************************** -->
				<device Dname="SC32F5832">
					<compile header="Device/Include/SC32F5832.h"/>
					<memory id="IROM1" start="0x00000000" size="0x8000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1000" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F58xx.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<debug svd="SVD/SC32F5832.svd"/>
					<description>
SC32F5832 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 72MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5832 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-32 kB on-chip FLASH programming memory, data retention >10 years. 
-4 kB SRAM, with parity check 
					</description>
				</device>
				<!-- *************************  Device 'SC32F5864'  ***************************** -->
				<device Dname="SC32F5864">
					<compile header="Device/Include/SC32F5864.h"/>
					<memory id="IROM1" start="0x00000000" size="0x10000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1800" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F58xx.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<debug svd="SVD/SC32F5864.svd"/>
					<description>
SC32F5864 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 72MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5864 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-64 kB on-chip FLASH programming memory, data retention >10 years. 
-6 kB SRAM, with parity check 
					</description>
				</device>
				<!-- *************************  Device 'SC32F5832_BOOT'  ***************************** -->
				<device Dname="SC32F5832_BOOT">
					<compile header="Device/Include/SC32F5832.h"/>
					<memory id="IROM1" start="0x00000000" size="0x8000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1000" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F58xx_BOOT.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<debug svd="SVD/SC32F5832.svd"/>
					<description>
SC32F5832 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 72MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5832 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-32 kB on-chip FLASH programming memory, data retention >10 years. 
-4 kB SRAM, with parity check 
					</description>
				</device>
				<!-- *************************  Device 'SC32F5864_BOOT'  ***************************** -->
				<device Dname="SC32F5864_BOOT">
					<compile header="Device/Include/SC32F5864.h"/>
					<memory id="IROM1" start="0x00000000" size="0x10000" startup="1" default="1"/>
					<memory id="IRAM1" start="0x20000000" size="0x1800" init   ="0" default="1"/>
					<algorithm name="Flash/SC32F58xx_BOOT.FLM" start="0x00000000" size="0x8000" RAMstart="0x20000000" RAMsize="0x0800" default="1"/>
					<debug svd="SVD/SC32F5864.svd"/>
					<description>
SC32F5864 is designed intended for motor control and digital control power supply applications. It uses Cortex-M0 core with maximum operating frequency 72MHz. and DMA can meet a variety of multiplication, division, shift operation.
SC32F5864 supports single power, and intigrates high precision high speed and low speed oscillators, and supports a variety of low power mode. The  integration of multi-channel enhanced PWM, multi-channel high precision, high speed ADC, analog comparator and multi channel high speed operational amplifier, and enhanced timer with a variety of power mode, can meet a variety of motor and power control system application requirements.
-64 kB on-chip FLASH programming memory, data retention >10 years. 
-6 kB SRAM, with parity check 
					</description>
				</device>
			</subFamily>
		</family>
	</devices>
	<!-- examples section (optional for all Software Packs)-->
	<!--
  <examples>
  </examples>
  -->
	<!-- conditions section (optional for all Software Packs)-->
	<conditions>
		<!-- Compiler Conditions -->
		<condition id="Compiler ARMCC">
			<require Tcompiler="ARMCC"/>
		</condition>
		<condition id="SC32F5832">
			<description>Slian SC32F58xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5832"/>
		</condition>
		<condition id="SC32F5864">
			<description>Slian SC32F58xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5864"/>
		</condition>
		<condition id="SC32F5832_BOOT">
			<description>Slian SC32F58xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5832_BOOT"/>
		</condition>
		<condition id="SC32F5864_BOOT">
			<description>Slian SC32F58xx Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5864_BOOT"/>
		</condition>
		<condition id="Startup ARM">
			<description>Startupassembler file for ARMCC</description>
			<require Tcompiler="ARMCC"/>
		</condition>
		<condition id="Startup GCC">
			<description>Startupassembler file for GCC</description>
			<require Tcompiler="GCC"/>
		</condition>
		<condition id="Startup IAR">
			<description>Startupassembler file for IAR</description>
			<require Tcompiler="IAR"/>
		</condition>
	</conditions>
	<!-- component section (optional for all Software Packs)-->
	<components>
		<!-- Startup SC32F5832 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5832">
			<description>System Startup for Silan SC32F5832 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5832.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5832.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5832.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5832.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		<!-- Startup SC32F5864 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5864">
			<description>System Startup for Silan SC32F5864 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5864.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5864.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5864.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5864.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		<!-- Startup SC32F5832_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5832_BOOT">
			<description>System Startup for Silan SC32F5832_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5832.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5832.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5832.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5832.c" attr="config" version="1.0.0"/>
			</files>
		</component>
		<!-- Startup SC32F5864_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5864_BOOT">
			<description>System Startup for Silan SC32F5864_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F5864.s" attr="config" version="1.0.1" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F5864.S" attr="config" version="1.0.1" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F5864.s" attr="config" version="1.0.1" condition="Startup IAR"/>
				<file category="source" name="Device/Source/system_SC32F5864.c" attr="config" version="1.0.0"/>
			</files>
		</component>
	</components>
</package>
