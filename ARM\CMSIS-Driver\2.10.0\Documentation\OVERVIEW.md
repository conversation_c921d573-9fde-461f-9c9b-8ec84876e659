# Introduction

This pack contains:

- Driver implementations that work across many different microcontroller.
  - [Ethernet drivers](https://arm-software.github.io/CMSIS-Driver/latest/driver_eth.html): KSZ8851, LAN9220, DP83848C, KSZ8061RNB, KSZ8081RNA, LAN8710A, LAN8720, LAN8740A, LAN8742A, ST802RT1
  - [USB drivers](https://arm-software.github.io/CMSIS-Driver/latest/driver_USB.html): EHCI and OHCI
  - [WiFi drivers](https://arm-software.github.io/CMSIS-Driver/latest/driver_WiFi.html#driver_wifi_devices): DA16200, ESP32, ESP8266, ISM43362, WizFi360
  - [Flash drivers](https://arm-software.github.io/CMSIS-Driver/latest/driver_WiFi.html#driver_wifi_devices): AM29x800BB, AT45DB641E, AT45DB642D, M29EW28F128, M29W640FB, N25Q032A, S29GL064Nx2
  - [I2C MultiSlave wrapper](https://arm-software.github.io/CMSIS-Driver/latest/driver_I2C.html)
  - [NAND Flash Memory Bus driver](https://arm-software.github.io/CMSIS-Driver/latest/driver_NAND.html)
  - [SPI MultiSlave wrapper](https://arm-software.github.io/CMSIS-Driver/latest/driver_SPI.html)
- [Shield layer](https://github.com/Open-CMSIS-Pack/cmsis-toolbox/blob/main/docs/ReferenceApplications.md#shield-layer) implementations for various plugin shields.
  - [WiFi Shield layers](https://arm-software.github.io/CMSIS-Driver/latest/shield_layer.html#shield_WiFi): Inventek ISMART43362-E, Sparkfun DA16200, Sparkfun ESP8266, WizNet WizFi360-EVB

## Links

- [Documentation](https://arm-software.github.io/CMSIS-Driver/latest/index.html)
- [Repository](https://github.com/ARM-software/CMSIS-Driver)
- [Issues](https://github.com/ARM-software/CMSIS-Driver/issues)
