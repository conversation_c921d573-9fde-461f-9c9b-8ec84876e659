<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32SG28_DFP</name>
  <description>Silicon Labs EFR32SG28 Sidewalk Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32SG28</keyword>
    <keyword>EFR32</keyword>
    <keyword>Sidewalk Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32SG28 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="39000000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
32-bit ARM Cortex-M33 core with 78 MHz maximum operating frequency.
      </description>

      <subFamily DsubFamily="EFR32SG28B320">
        <!-- *************************  Device 'EFR32SG28B320F1024IM48'  ***************************** -->
        <device Dname="EFR32SG28B320F1024IM48">
          <compile header="Device/SiliconLabs/EFR32SG28/Include/em_device.h"  define="EFR32SG28B320F1024IM48"/>
          <debug      svd="SVD/EFR32SG28/EFR32SG28B320F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32SG28B320F1024IM68'  ***************************** -->
        <device Dname="EFR32SG28B320F1024IM68">
          <compile header="Device/SiliconLabs/EFR32SG28/Include/em_device.h"  define="EFR32SG28B320F1024IM68"/>
          <debug      svd="SVD/EFR32SG28/EFR32SG28B320F1024IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32SG28B322">
        <!-- *************************  Device 'EFR32SG28B322F1024IM48'  ***************************** -->
        <device Dname="EFR32SG28B322F1024IM48">
          <compile header="Device/SiliconLabs/EFR32SG28/Include/em_device.h"  define="EFR32SG28B322F1024IM48"/>
          <debug      svd="SVD/EFR32SG28/EFR32SG28B322F1024IM48.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

        <!-- *************************  Device 'EFR32SG28B322F1024IM68'  ***************************** -->
        <device Dname="EFR32SG28B322F1024IM68">
          <compile header="Device/SiliconLabs/EFR32SG28/Include/em_device.h"  define="EFR32SG28B322F1024IM68"/>
          <debug      svd="SVD/EFR32SG28/EFR32SG28B322F1024IM68.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x40000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32SG28">
      <description>Silicon Labs EFR32SG28 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32SG28*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32SG28">
      <description>System Startup for Silicon Labs EFR32SG28 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32SG28/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32SG28/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32SG28/Source/system_efr32sg28.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
