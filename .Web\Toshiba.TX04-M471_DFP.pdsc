<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss-v3/master/en/semiconductor/product/dl/device-family-pack</url>
  <name>TX04-M471_DFP</name>
  <description>Toshiba TX04 Series TMPM471 Group Device Support</description>

  <releases>
    <release version="1.0.0" date="2025-01-21">
      First Release version of TX04 Series Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4</keyword>
    <keyword>TX04</keyword>
  </keywords>

  <devices>
    <family Dfamily="TX04 Series" Dvendor="Toshiba:92">
      <description>
The ARM Cortex-M4F core-based TX04 Series supports the Thumb-2 and Thumb instruction sets and
provides DSP extensions and a floating-point unit (FPU) in order to deliver high energy efficiency.
The TX04 Series includes microcontrollers that also integrate a selection of peripheral circuits
such as high-speed NANO FLASH-100 (M440 Group) and high-performance coprocessors,
making them ideal for high-speed data processing.
      </description>
      <!-- ************************  Subfamily 'M471'  **************************** -->
      <subFamily DsubFamily="M471">
        <!-- *************************  Device 'TMPM471F10'  **************************** -->
        <device Dname="TMPM471F10">
          <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian" Dclock="160000000"/>
          <compile header="Device/Include/TMPM471F10.h"  define="TMPM471F10"/>
          <debug svd="SVD/M471F10.svd"/>
          <memory id="IROM1"                            start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                            start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM471_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->
          
          <feature type="IOs"           n="81"                          name="I/O Ports (PORT)"/>
          <feature type="ExtInt"        n="16"                          name="External interrupt (INT)"/>
          <feature type="CoreOther"     n="16"                          name="Digital Noise Filter (DNF)"/>
          <feature type="DMA"           n="30"                          name="Direct Memory Access Controllor (DMAC)"/>
          <feature type="Timer"         n="5"     m="32"                name="32bit Timer Event counter (T32A)"/>


          <feature type="UART"          n="5"                           name="Universal Asynchronous Receiver Transmitter (UART)"/>
          <feature type="I2C"           n="2"                           name="Inter-Integrated Circuit Version A(EI2C)"/>
          <feature type="SPI"           n="4"                           name="Toshiba Serial Peripheral Interface (TSPI/SIO)"/>

          <feature type="ADC"           n="12"     m="12"               name="Analog to Digital Converter A (ADC)"/>
          <feature type="ADC"           n="11"     m="12"               name="Analog to Digital Converter B (ADC)"/>
                   
          <feature type="CoreOther"     n="2"                           name="Programmable Motor Driver  (PMD)"/>
          <feature type="CoreOther"     n="2"                           name="Advanced Encoder input Circuit (32-bit) (A-ENC32)"/>

          <feature type="PowerOther"    n="1"                           name="Low-Voltage Detector (LVD)"/>
          <feature type="WDT"           n="1"                           name="Selectable clock Watch dog timer (SIWDT)"/>
          <feature type="Other"         n="1"                           name="Oscillation Frequency Detector (OFD)"/>
          <feature type="PowerOther"    n="1"                           name="Power On Reset Circuit (POR)"/>

          <feature type="VCC"           n="4.50"    m="5.50"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="QFP"           n="100"                         name="P-LQFP100-1414-0.50-002"/>
        </device>
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="TMPM471 Compiler">
      <accept condition="Compiler ARMCC"/>
    </condition>
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="TMPM471 CMSIS">
      <description>Toshiba TMPM471 Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM471*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <!-- Startup TMPM471 -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM471 CMSIS">
      <description>System Startup for Toshiba TMPM471 Devices</description>
      <files>
        <file category="include"      name="Device/Include/" condition="TMPM471 Compiler"/>
        <file category="source"       name="Device/Source/ARM/startup_TMPM471F10.s"  attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="linkerScript" name="Device/Source/ARM/TMPM471F10.scat"       attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"       name="Device/Source/system_TMPM471F10.c"       attr="config" version="1.0.0" condition="TMPM471 Compiler"/>
      </files>
    </component>
  </components>

</package>
