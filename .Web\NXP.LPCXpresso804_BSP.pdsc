<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso804_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXpresso804</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="19.0.0" date="2024-07-17">NXP CMSIS Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="18.0.0" date="2024-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.15.0</release>
    <release version="17.0.0" date="2023-07-27">NXP CMSIS Packs based on MCUXpresso SDK 2.14.0</release>
    <release version="16.0.0" date="2023-01-16">NXP CMSIS Packs based on MCUXpresso SDK 2.13.0</release>
    <release version="15.0.1" date="2022-08-19">
      PDSC update:
      1. Updated "mountedDevice" element to use the exact part name instead of device variants.
      2. Added "compatibleDevice" element to clarify board-compatible part name list.</release>
    <release version="15.0.0" date="2022-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.12.0</release>
    <release version="14.0.0" date="2022-01-05">NXP CMSIS Packs based on MCUXpresso SDK 2.11.0</release>
    <release version="13.1.0" date="2021-07-13">NXP CMSIS Packs based on MCUXpresso SDK 2.10.0</release>
    <release version="13.0.0" date="2021-02-05">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0 with restructured startup component files</release>
    <release version="12.3.0" date="2021-01-14">NXP CMSIS Packs based on MCUXpresso SDK 2.9.0</release>
    <release version="12.2.0" date="2020-07-20">NXP CMSIS Packs based on MCUXpresso SDK 2.8.0</release>
    <release version="12.1.0" date="2019-12-19">NXP CMSIS Packs based on MCUXpresso SDK 2.7.0</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC804_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso804">
      <description>LPCXpresso Development Board for LPC804</description>
      <image small="boards/lpcxpresso804/lpcxpresso804.png"/>
      <book category="overview" name="https://www.nxp.com/pip/OM40001" title="LPCXpresso Development Board for LPC804" public="true"/>
      <mountedDevice Dname="LPC804M101JDH24" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC804M101JDH20" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC804M101JHI33" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC804M111JDH24" Dvendor="NXP:11"/>
      <compatibleDevice Dname="LPC804UK" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC804.internal_condition">
      <accept Dname="LPC804M101JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC804M101JDH24" Dvendor="NXP:11"/>
      <accept Dname="LPC804M101JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC804M111JDH24" Dvendor="NXP:11"/>
      <accept Dname="LPC804UK" Dvendor="NXP:11"/>
    </condition>
    <condition id="device_id.LPC804.internal_condition">
      <accept Dname="LPC804M101JDH20" Dvendor="NXP:11"/>
      <accept Dname="LPC804M101JDH24" Dvendor="NXP:11"/>
      <accept Dname="LPC804M101JHI33" Dvendor="NXP:11"/>
      <accept Dname="LPC804M111JDH24" Dvendor="NXP:11"/>
      <accept Dname="LPC804UK" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.lpcxpresso804.condition_id">
      <require condition="allOf.board=lpcxpresso804, component.miniusart_adapter, device_id=LPC804, device.LPC804_startup, driver.clock, driver.common, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.power_no_lib, driver.swm, utility.assert_lite, utility.debug_console_lite.internal_condition"/>
    </condition>
    <condition id="allOf.board=lpcxpresso804, component.miniusart_adapter, device_id=LPC804, device.LPC804_startup, driver.clock, driver.common, driver.lpc_gpio, driver.lpc_iocon_lite, driver.lpc_miniusart, driver.power_no_lib, driver.swm, utility.assert_lite, utility.debug_console_lite.internal_condition">
      <require condition="board.lpcxpresso804.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="miniusart_adapter"/>
      <require condition="device_id.LPC804.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="swm"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="assert_lite"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console_lite"/>
    </condition>
    <condition id="board.lpcxpresso804.internal_condition">
      <accept condition="device.LPC804.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="acomp_basic" folder="boards/lpcxpresso804/driver_examples/acomp/acomp_basic" doc="readme.txt">
      <description>The ACOMP Basic Example shows the simplest way to use ACOMP driver and help user with a quick start.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_basic.uvprojx"/>
        <environment name="iar" load="iar/acomp_basic.ewp"/>
        <environment name="csolution" load="acomp_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="acomp_interrupt" folder="boards/lpcxpresso804/driver_examples/acomp/acomp_interrupt" doc="readme.txt">
      <description>The ACOMP Interrupt Example shows how to use interrupt with ACOMP driver.In this example, user should indicate an input channel to capture a voltage signal (can be controlled by user) as the ACOMP's negative channel...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/acomp_interrupt.uvprojx"/>
        <environment name="iar" load="iar/acomp_interrupt.ewp"/>
        <environment name="csolution" load="acomp_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_acomp" folder="boards/lpcxpresso804/driver_examples/capt/capt_acomp" doc="readme.txt">
      <description>This example shows how to capture touch data using CAPT poll-now mode. This example uses ACOMP instead of GPIO in measurement stage.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_acomp.uvprojx"/>
        <environment name="iar" load="iar/capt_acomp.ewp"/>
        <environment name="csolution" load="capt_acomp.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_acomp_continuous" folder="boards/lpcxpresso804/driver_examples/capt/capt_acomp_continuous" doc="readme.txt">
      <description>This example shows how to capture touch data using CAPT continuous mode. This example uses ACOMP instead of GPIO in measurement stage.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_acomp_continuous.uvprojx"/>
        <environment name="iar" load="iar/capt_acomp_continuous.ewp"/>
        <environment name="csolution" load="capt_acomp_continuous.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_basic" folder="boards/lpcxpresso804/driver_examples/capt/capt_basic" doc="readme.txt">
      <description>This example shows how to capture touch data using CAPT poll-now mode.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_basic.uvprojx"/>
        <environment name="iar" load="iar/capt_basic.ewp"/>
        <environment name="csolution" load="capt_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_basic_continuous" folder="boards/lpcxpresso804/driver_examples/capt/capt_basic_continuous" doc="readme.txt">
      <description>This example shows how to capture touch data using CAPT continuous mode.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_basic_continuous.uvprojx"/>
        <environment name="iar" load="iar/capt_basic_continuous.ewp"/>
        <environment name="csolution" load="capt_basic_continuous.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="capt_key" folder="boards/lpcxpresso804/touch_examples/capt_key" doc="readme.txt">
      <description>This example shows how to use CAPT to detect key touch event. When the key is touched, LED on the board is on accordingly.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/capt_key.uvprojx"/>
        <environment name="iar" load="iar/capt_key.ewp"/>
        <environment name="csolution" load="capt_key.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="boards/lpcxpresso804/driver_examples/crc" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
        <environment name="csolution" load="crc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="boards/lpcxpresso804/driver_examples/ctimer/simple_match" doc="readme.txt">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example.ewp"/>
        <environment name="csolution" load="ctimer_match_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="boards/lpcxpresso804/driver_examples/ctimer/simple_match_interrupt" doc="readme.txt">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset,...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_match_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="boards/lpcxpresso804/driver_examples/ctimer/simple_pwm" doc="readme.txt">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="boards/lpcxpresso804/driver_examples/ctimer/simple_pwm_interrupt" doc="readme.txt">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
        <environment name="csolution" load="ctimer_pwm_interrupt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="boards/lpcxpresso804/driver_examples/gpio/led_output" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
        <environment name="csolution" load="gpio_led_output.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="boards/lpcxpresso804/demo_apps/hello_world" doc="readme.txt">
      <description>The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers and repeat what user input. The purpose of this demo is to show how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
        <environment name="csolution" load="hello_world.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_basic" folder="boards/lpcxpresso804/driver_examples/iap/iap_basic" doc="readme.md">
      <description>The IAP project is a simple demonstration program of the SDK IAP driver. It reads part id, boot code version, unique id and reinvoke ISP. A message a printed on the UART terminal as various bascial iap operations are performed.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_basic.uvprojx"/>
        <environment name="iar" load="iar/iap_basic.ewp"/>
        <environment name="csolution" load="iap_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="boards/lpcxpresso804/driver_examples/iap/iap_flash" doc="readme.md">
      <description>The IAP Flash project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="iar" load="iar/iap_flash.ewp"/>
        <environment name="csolution" load="iap_flash.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="boards/lpcxpresso804/demo_apps/led_blinky" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
        <environment name="csolution" load="led_blinky.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_basic" folder="boards/lpcxpresso804/driver_examples/adc/lpc_adc_basic" doc="readme.txt">
      <description>The lpc_adc_basic example shows how to use LPC ADC driver in the simplest way.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_basic.ewp"/>
        <environment name="csolution" load="lpc_adc_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_burst" folder="boards/lpcxpresso804/driver_examples/adc/lpc_adc_burst" doc="readme.txt">
      <description>The lpc_adc_burst example shows how to use LPC ADC driver with the burst mode.In this example, the internal temperature sensor is used to created the input analog signal.When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_burst.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_burst.ewp"/>
        <environment name="csolution" load="lpc_adc_burst.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_interrupt" folder="boards/lpcxpresso804/driver_examples/adc/lpc_adc_interrupt" doc="readme.txt">
      <description>The lpc_adc_interrupt example shows how to use interrupt with LPC ADC driver.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard,...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_interrupt.ewp"/>
        <environment name="csolution" load="lpc_adc_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_bod" folder="boards/lpcxpresso804/driver_examples/bod" doc="readme.txt">
      <description>The bod example shows how to use LPC BOD(Brown-out detector) in the simplest way. To run this example, user should remove the jumper for the power source selector, and connect the adjustable input voltage to the...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_bod.uvprojx"/>
        <environment name="iar" load="iar/lpc_bod.ewp"/>
        <environment name="csolution" load="lpc_bod.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_dac_basic" folder="boards/lpcxpresso804/driver_examples/dac/lpc_dac_basic" doc="readme.txt">
      <description>The dac_basic example shows how to use DAC module simply as the general DAC converter.When the DAC's double-buffer feature is not enabled, the CR register is used as the DAC output data register directly.The...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_dac_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_dac_basic.ewp"/>
        <environment name="csolution" load="lpc_dac_basic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_dac_interrupt" folder="boards/lpcxpresso804/driver_examples/dac/lpc_dac_interrupt" doc="readme.txt">
      <description>The dac_interrupt example shows how to use DAC with interrupts and produce an arbitrary, user-defined waveform ofselectable frequency.The output can be observed with an oscilloscope. When the DAC's double-buffer...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_dac_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_dac_interrupt.ewp"/>
        <environment name="csolution" load="lpc_dac_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_master" folder="boards/lpcxpresso804/driver_examples/i2c/interrupt_b2b_transfer/master" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_master.ewp"/>
        <environment name="csolution" load="lpc_i2c_interrupt_b2b_transfer_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_slave" folder="boards/lpcxpresso804/driver_examples/i2c/interrupt_b2b_transfer/slave" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_slave.ewp"/>
        <environment name="csolution" load="lpc_i2c_interrupt_b2b_transfer_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_master" folder="boards/lpcxpresso804/driver_examples/i2c/polling_b2b/master" doc="readme.txt">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_polling_b2b_master.ewp"/>
        <environment name="csolution" load="lpc_i2c_polling_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_slave" folder="boards/lpcxpresso804/driver_examples/i2c/polling_b2b/slave" doc="readme.txt">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_polling_b2b_slave.ewp"/>
        <environment name="csolution" load="lpc_i2c_polling_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="boards/lpcxpresso804/driver_examples/mrt" doc="readme.txt">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
        <environment name="csolution" load="mrt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="new_project" folder="boards/lpcxpresso804/demo_apps/new_project" doc="readme.md">
      <description>The new project is provided as starting point for user to add with customized flow.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/new_project.uvprojx"/>
        <environment name="iar" load="iar/new_project.ewp"/>
        <environment name="csolution" load="new_project.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="boards/lpcxpresso804/driver_examples/pint/pattern_match" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
        <environment name="csolution" load="pint_pattern_match.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="boards/lpcxpresso804/driver_examples/pint/pin_interrupt" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
        <environment name="csolution" load="pint_pin_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="plu_combination" folder="boards/lpcxpresso804/driver_examples/plu/combination" doc="readme.txt">
      <description>The PLU combination project is a simple demonstration program of the SDK PLU driver. In this example, a number of GPIO pins are connected to PLU inputs and the LED is used to monitor the PLU outputs to demonstrate...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/plu_combination.uvprojx"/>
        <environment name="iar" load="iar/plu_combination.ewp"/>
        <environment name="csolution" load="plu_combination.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="power_mode_switch_lpc" folder="boards/lpcxpresso804/demo_apps/power_mode_switch_lpc" doc="readme.txt">
      <description>The power_mode_switch application shows the usage of normal power mode control APIs for entering the three kinds oflow power mode: Sleep mode, Deep Sleep mode and Power Down mode, deep power down mode. When the...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_mode_switch_lpc.uvprojx"/>
        <environment name="iar" load="iar/power_mode_switch_lpc.ewp"/>
        <environment name="csolution" load="power_mode_switch_lpc.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_master" folder="boards/lpcxpresso804/driver_examples/spi/interrupt/master" doc="readme.txt">
      <description>The spi_interrupt_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_master.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_master.ewp"/>
        <environment name="csolution" load="spi_interrupt_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_interrupt_slave" folder="boards/lpcxpresso804/driver_examples/spi/interrupt/slave" doc="readme.txt">
      <description>The spi_interrupt_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece of...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_interrupt_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_interrupt_slave.ewp"/>
        <environment name="csolution" load="spi_interrupt_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_master" folder="boards/lpcxpresso804/driver_examples/spi/polling/master" doc="readme.txt">
      <description>The spi_polling_transfer_master example shows how to use spi driver as master to do board to boardtransfer with polling:In this example, one spi instance as master and another spi instance on othere board as slave....See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_master.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_master.ewp"/>
        <environment name="csolution" load="spi_polling_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_polling_slave" folder="boards/lpcxpresso804/driver_examples/spi/polling/slave" doc="readme.txt">
      <description>The spi_polling_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_polling_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_polling_slave.ewp"/>
        <environment name="csolution" load="spi_polling_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_master" folder="boards/lpcxpresso804/driver_examples/spi/transfer_interrupt/master" doc="readme.txt">
      <description>The spi_interrupt_transfer_master example shows how to use spi driver as master to do board to boardtransfer in interrupt way:In this example, one spi instance as master and another spi instance on othere board as...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_master.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_interrupt_master.ewp"/>
        <environment name="csolution" load="spi_transfer_interrupt_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="spi_transfer_interrupt_slave" folder="boards/lpcxpresso804/driver_examples/spi/transfer_interrupt/slave" doc="readme.txt">
      <description>The spi_interrupt_transfer_slave example shows how to use spi driver as slave to receive data from master.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/spi_transfer_interrupt_slave.uvprojx"/>
        <environment name="iar" load="iar/spi_transfer_interrupt_slave.ewp"/>
        <environment name="csolution" load="spi_transfer_interrupt_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_polling_example" folder="boards/lpcxpresso804/driver_examples/usart/polling" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC, the board will send back all characters that PCsend to the board. </description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_polling_example.uvprojx"/>
        <environment name="iar" load="iar/usart_polling_example.ewp"/>
        <environment name="csolution" load="usart_polling_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_terminal" folder="boards/lpcxpresso804/driver_examples/usart/terminal" doc="readme.txt">
      <description>This example demonstrate configuration and use of the USART module in interrupt-driven asynchronous mode on communication with a terminal emulator calling the USART transactional APIs. USART will echo back every...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_terminal.uvprojx"/>
        <environment name="iar" load="iar/usart_terminal.ewp"/>
        <environment name="csolution" load="usart_terminal.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_interrupt" folder="boards/lpcxpresso804/driver_examples/usart/transfer_interrupt" doc="readme.txt">
      <description>usart_transfer_interrupt</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_interrupt.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_interrupt.ewp"/>
        <environment name="csolution" load="usart_transfer_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_ring_buffer" folder="boards/lpcxpresso804/driver_examples/usart/transfer_ring_buffer" doc="readme.txt">
      <description>The usart_interrupt_rb_transfer example shows how to use usart driver in interrupt way withRX ring buffer enabled.In this example, one uart instance connect to PC through, the board will send back all charactersthat...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_ring_buffer.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_ring_buffer.ewp"/>
        <environment name="csolution" load="usart_transfer_ring_buffer.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_sync_b2b_master" folder="boards/lpcxpresso804/driver_examples/usart/transfer_sync_b2b/master" doc="readme.txt">
      <description>The usart_interrupt_sync_transfer example shows how to use usart API in synchronous mode:In this example, one usart instance will be selected as master ,and another as slave. The master will send data to slave in...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_sync_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_sync_b2b_master.ewp"/>
        <environment name="csolution" load="usart_transfer_sync_b2b_master.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usart_transfer_sync_b2b_slave" folder="boards/lpcxpresso804/driver_examples/usart/transfer_sync_b2b/slave" doc="readme.txt">
      <description>The usart_interrupt_sync_transfer example shows how to use usart API in synchronous mode:In this example, one usart instance will be selected as master ,and another as slave. The master will send data to slave in...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usart_transfer_sync_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/usart_transfer_sync_b2b_slave.ewp"/>
        <environment name="csolution" load="usart_transfer_sync_b2b_slave.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wkt_example" folder="boards/lpcxpresso804/driver_examples/wkt" doc="readme.txt">
      <description>The WKT project is a simple demonstration program of the SDK WKT driver. It sets up the WKT hardware block to trigger a periodic interrupt after loading a counter value and counting down to 0. When the WKT interrupt...See more details in readme document.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wkt_example.uvprojx"/>
        <environment name="iar" load="iar/wkt_example.ewp"/>
        <environment name="csolution" load="wkt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="boards/lpcxpresso804/driver_examples/wwdt" doc="readme.txt">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso804" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
        <environment name="csolution" load="wwdt_example.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso804" Cversion="1.0.0" condition="BOARD_Project_Template.lpcxpresso804.condition_id">
      <description>Board_project_template lpcxpresso804</description>
      <files>
        <file category="header" attr="config" name="boards/lpcxpresso804/project_template/board.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso804/project_template/board.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso804/project_template/clock_config.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso804/project_template/clock_config.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso804/project_template/pin_mux.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso804/project_template/pin_mux.c" version="1.0.0" projectpath="board"/>
        <file category="header" attr="config" name="boards/lpcxpresso804/project_template/peripherals.h" version="1.0.0" projectpath="board"/>
        <file category="sourceC" attr="config" name="boards/lpcxpresso804/project_template/peripherals.c" version="1.0.0" projectpath="board"/>
        <file category="include" name="boards/lpcxpresso804/project_template/"/>
      </files>
    </component>
  </components>
</package>
