<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDMKE15-PD7250_ISSDK_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware issdk Examples Pack for FRDMKE15-PD7250</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="ISSDK" vendor="NXP" version="3.0.0"/>
      <package name="MKE15Z7_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDMKE15-PD7250">
      <description>Freedom Development Board for Kinetis KE15 MCUs with sensor shield board FRDM-STBA-PD7250 having FXPS7250D4 high-performance, high-precision, barometric absolute pressure sensor</description>
      <image small="boards/frdmke15z_pd7250/frdmke15z_pd7250.png"/>
      <book category="overview" name="https://www.nxp.com/pip/FRDM-KE15Z" title="Freedom Development Board for Kinetis KE15 MCUs with sensor shield board FRDM-STBA-PD7250 having FXPS7250D4 high-performance, high-precision, barometric absolute pressure sensor" public="true"/>
      <mountedDevice Dname="MKE15Z256VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z128VLH7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z128VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z256VLH7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE14Z256VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE15Z128VLH7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE15Z128VLL7" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MKE15Z256VLH7" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MKE15Z7.internal_condition">
      <accept Dname="MKE15Z256VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z256VLL7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z128VLH7" Dvendor="NXP:11"/>
      <accept Dname="MKE15Z128VLL7" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.frdmke15z_pd7250.condition_id">
      <require condition="allOf.device.MKE15Z7_startup, driver.lpuart, driver.port, driver.common, utility.debug_console, driver.gpio, driver.smc, component.lpuart_adapter, component.serial_manager_uart, component.serial_manager, device=MKE15Z7.internal_condition"/>
    </condition>
    <condition id="allOf.device.MKE15Z7_startup, driver.lpuart, driver.port, driver.common, utility.debug_console, driver.gpio, driver.smc, component.lpuart_adapter, component.serial_manager_uart, component.serial_manager, device=MKE15Z7.internal_condition">
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="port"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="smc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require condition="device.MKE15Z7.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="fxps7250d4_app" folder="boards/frdmke15z_pd7250/issdk_examples/sensors/fxps7250d4/fxps7250d4_app" doc="readme.txt">
      <description>The FXPS7250D4 example application demonstrates the use of the FXPS7250D4 sensor in poll Mode using I2C. The example demonstrates configuration of all registers reguired to put the sensor into polling mode to keep...See more details in readme document.</description>
      <board name="FRDMKE15-PD7250" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxps7250d4_app.uvprojx"/>
        <environment name="csolution" load="fxps7250d4_app.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Sensors" Cgroup="ISSDK project template" Csub="frdmke15z_pd7250" Cversion="1.0.0" condition="BOARD_Project_Template.frdmke15z_pd7250.condition_id">
      <description>Board_project_template frdmke15z_pd7250</description>
      <files>
        <file category="sourceC" attr="config" name="boards/frdmke15z_pd7250/project_template/board.c" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="header" attr="config" name="boards/frdmke15z_pd7250/project_template/board.h" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z_pd7250/project_template/clock_config.c" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="header" attr="config" name="boards/frdmke15z_pd7250/project_template/clock_config.h" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z_pd7250/project_template/pin_mux.c" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="header" attr="config" name="boards/frdmke15z_pd7250/project_template/pin_mux.h" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="sourceC" attr="config" name="boards/frdmke15z_pd7250/project_template/peripherals.c" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="header" attr="config" name="boards/frdmke15z_pd7250/project_template/peripherals.h" version="1.0.0" projectpath="frdmke15z_pd7250/project_template"/>
        <file category="include" name="boards/frdmke15z_pd7250/project_template/"/>
      </files>
    </component>
  </components>
</package>
