<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
	<vendor>SILAN</vendor>
	<name>SC32F57xx_DFP</name>
	<description>Silan SC32F57xx Device Family Pack</description>
	<url>https://github.com/Silanpack/Pack/raw/main/</url>
	
	<releases>
		<release version="1.0.5" date="2025-03-25">
			Add SC32F57768
		</release>
	
		<release version="1.0.4" date="2024-10-15">
			Add SC32F57xxH
		</release>
	
		<release version="1.0.3" date="2023-07-12">
			update Flash algorithm
			update StdPeriph Driver
		</release>
	
		<release version="1.0.2" date="2023-02-13">
			update Std<PERSON><PERSON>ph Driver
		</release>

		<release version="1.0.1" date="2021-09-13">
			Add StdPeriph Driver
		</release>
	
		<release version="1.0.0" date="2020-12-01">
			Initial Version
		</release>
	</releases>
  
	<keywords>
		<!-- keywords for indexing -->
		<keyword>SC32F57xx</keyword>
		<keyword>SC32F57128</keyword>
		<keyword>SC32F5764</keyword>
		<keyword>SC32F57xxH</keyword>
		<keyword>SC32F57256</keyword>
		<keyword>SC32F57128H</keyword>
		<keyword>SC32F5764H</keyword>
		<keyword>SC32F57768</keyword>
		<keyword>SC32F57512</keyword>
	</keywords>

	<!-- devices section (mandatory for Device Family Packs) -->
	<devices>
		<family Dfamily="SC32F57 Series" Dvendor="SILAN:164">
		<!-- ************************  Subfamily 'SC32F57xx'  **************************** -->
			<subFamily DsubFamily="SC32F57xx">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="48000000"/>
				<feature type="VCC"				n="2.00"	m="5.50"/>
				<feature type="Temp"			n="-40"		m="85"/>
				<feature type="UART"			n="6"/>
				<feature type="SPI"				n="2"/>
				<feature type="Timer"			n="8"       m="16"/>
				<feature type="WDT"    			n="2"/>
				<feature type="ADC"    			n="16"      m="14"/>
			  
				<!-- *************************  Device 'SC32F57128'  ***************************** -->
				<device Dname="SC32F57128">
					<compile header="Device/Include/SC32F57xx.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57xx.FLM"        start="0x00000000"  size="0x20000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F57xx.svd"/>
				</device>
			
				<!-- *************************  Device 'SC32F57128_BOOT'  ***************************** -->
				<device Dname="SC32F57128_BOOT">
					<compile header="Device/Include/SC32F57xx.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57128_BOOT.FLM"        start="0x00000000"  size="0x20000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F57xx.svd"/>
				</device>
				
				<!-- *************************  Device 'SC32F5764'  ***************************** -->
				<device Dname="SC32F5764">
					<compile header="Device/Include/SC32F57xx.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x1800"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57xx.FLM"        start="0x00000000"  size="0x10000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F57xx.svd"/>
				</device>
			
				<!-- *************************  Device 'SC32F5764_BOOT'  ***************************** -->
				<device Dname="SC32F5764_BOOT">
					<compile header="Device/Include/SC32F57xx.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x1800"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F5764_BOOT.FLM"        start="0x00000000"  size="0x10000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
					<debug svd="SVD/SC32F57xx.svd"/>
				</device>
			</subFamily>
			
			<subFamily DsubFamily="SC32F57xxH">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="48000000"/>
				<feature type="VCC"				n="2.00"	m="5.50"/>
				<feature type="Temp"			n="-40"		m="85"/>
				<feature type="UART"			n="6"/>
				<feature type="SPI"				n="2"/>
				<feature type="Timer"			n="8"       m="16"/>
				<feature type="WDT"    			n="2"/>
				<feature type="ADC"    			n="16"      m="14"/>
				<debug svd="SVD/SC32F57xxH.svd"/>
				
				<!-- *************************  Device 'SC32F5764H'  ***************************** -->
				<device Dname="SC32F5764H">
					<compile header="Device/Include/SC32F57xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x1800"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F5764H.FLM"        start="0x00000000"  size="0x10000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F5764H_BOOT'  ***************************** -->
				<device Dname="SC32F5764H_BOOT">
					<compile header="Device/Include/SC32F57xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x10000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x1800"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F5764H_BOOT.FLM"        start="0x00000000"  size="0x10000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F57128H'  ***************************** -->
				<device Dname="SC32F57128H">
					<compile header="Device/Include/SC32F57xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57128H.FLM"        start="0x00000000"  size="0x20000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F57128H_BOOT'  ***************************** -->
				<device Dname="SC32F57128H_BOOT">
					<compile header="Device/Include/SC32F57xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x20000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x2000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57128H_BOOT.FLM"        start="0x00000000"  size="0x20000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
				</device>
				
				<!-- *************************  Device 'SC32F57256'  ***************************** -->
				<device Dname="SC32F57256">
					<compile header="Device/Include/SC32F57xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x40000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x5000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57256.FLM"        start="0x00000000"  size="0x40000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
				</device>		
				
				<!-- *************************  Device 'SC32F57256_BOOT'  ***************************** -->
				<device Dname="SC32F57256_BOOT">
					<compile header="Device/Include/SC32F57xxH.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x40000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x5000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57256_BOOT.FLM"        start="0x00000000"  size="0x40000"  RAMstart="0x20000000"  RAMsize="0x0800"  default="1"/>
				</device>
			</subFamily>
			
			<subFamily DsubFamily="SC32F57768">
				<processor Dcore="Cortex-M0" DcoreVersion="r0p0" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
				<processor Dclock="72000000"/>
				<feature type="VCC"				n="2.00"	m="5.50"/>
				<feature type="Temp"			n="-40"		m="85"/>
				<feature type="UART"			n="6"/>
				<feature type="SPI"				n="2"/>
				<feature type="Timer"			n="8"       m="16"/>
				<feature type="WDT"    			n="2"/>
				<feature type="ADC"    			n="16"      m="14"/>
				
				<!-- *************************  Device 'SC32F57768'  ***************************** -->
				<device Dname="SC32F57768">
					<compile header="Device/Include/SC32F57768.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0xC0000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x10000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57768.FLM"        start="0x00000000"  size="0xC0000"  RAMstart="0x20000000"  RAMsize="0x8000"  default="1"/>
					<debug svd="SVD/SC32F57768.svd"/>
				</device>
				
				<!-- *************************  Device 'SC32F57512'  ***************************** -->
				<device Dname="SC32F57512">
					<compile header="Device/Include/SC32F57512.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x80000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x8000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57512.FLM"        start="0x00000000"  size="0x80000"  RAMstart="0x20000000"  RAMsize="0x8000"  default="1"/>
					<debug svd="SVD/SC32F57512.svd"/>
				</device>
				
				<!-- *************************  Device 'SC32F57768_BOOT'  ***************************** -->
				<device Dname="SC32F57768_BOOT">
					<compile header="Device/Include/SC32F57768.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0xC0000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x10000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57768_BOOT.FLM"        start="0x00000000"  size="0xC0000"  RAMstart="0x20000000"  RAMsize="0x8000"  default="1"/>
					<debug svd="SVD/SC32F57768.svd"/>
				</device>
				
				<!-- *************************  Device 'SC32F57512_BOOT'  ***************************** -->
				<device Dname="SC32F57512_BOOT">
					<compile header="Device/Include/SC32F57512.h"/>          
					<memory     id="IROM1"                      start="0x00000000"  size="0x80000"     startup="1"   default="1"/>
					<memory     id="IRAM1"                      start="0x20000000"  size="0x8000"     init   ="0"   default="1"/>
					<algorithm  name="Flash/SC32F57512_BOOT.FLM"        start="0x00000000"  size="0x80000"  RAMstart="0x20000000"  RAMsize="0x8000"  default="1"/>
					<debug svd="SVD/SC32F57512.svd"/>
				</device>
			</subFamily>
		</family>
	</devices>

	<!-- examples section (optional for all Software Packs)-->
	<!--
	<examples>
	</examples>
	-->
	
	<!-- conditions section (optional for all Software Packs)-->
	<conditions>
		<!-- Compiler Conditions -->
		<condition id="Compiler ARMCC">
			<require Tcompiler="ARMCC"/>
		</condition>
    
		<condition id="SC32F57128">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57128"/>
		</condition>
	
		<condition id="SC32F57128_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57128_BOOT"/>
		</condition>
		
		<condition id="SC32F5764">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5764"/>
		</condition>
	
		<condition id="SC32F5764_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5764_BOOT"/>
		</condition>
		
		<condition id="SC32F5764H">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5764H"/>
		</condition>
		
		<condition id="SC32F5764H_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F5764H_BOOT"/>
		</condition>
		
		<condition id="SC32F57128H">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57128H"/>
		</condition>
		
		<condition id="SC32F57128H_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57128H_BOOT"/>
		</condition>
		
		<condition id="SC32F57256">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57256"/>
		</condition>
		
		<condition id="SC32F57256_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57256_BOOT"/>
		</condition>
		
		<condition id="SC32F57768">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57768"/>
		</condition>
		
		<condition id="SC32F57768_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57768_BOOT"/>
		</condition>
		
		<condition id="SC32F57512">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57512"/>
		</condition>
		
		<condition id="SC32F57512_BOOT">
			<description>Silan SC32F57 Series devices</description>
			<require Cclass="CMSIS" Cgroup="CORE"/>
			<require Dvendor="SILAN:164" Dname="SC32F57512_BOOT"/>
		</condition>
    
		<condition id="Startup ARM">
			<description>Startupassembler file for ARMCC</description>
			<require Tcompiler="ARMCC"/>
		</condition>

		<condition id="Startup GCC">
			<description>Startupassembler file for GCC</description>
			<require Tcompiler="GCC"/>
		</condition>

		<condition id="Startup IAR">
			<description>Startupassembler file for IAR</description>
			<require Tcompiler="IAR"/>
		</condition>
		
		<condition id="SC32F57xx">
			<description>Silan Microelectronics SC32F57xx Device</description>
			<accept Dvendor="SILAN:164" Dname="SC32F5764"/>
			<accept Dvendor="SILAN:164" Dname="SC32F57128"/>
			<accept Dvendor="SILAN:164" Dname="SC32F5764_BOOT"/>
			<accept Dvendor="SILAN:164" Dname="SC32F57128_BOOT"/>
		</condition>
		
		<condition id="SC32F57xx STDPERIPH">
			<description>Silan Microelectronics SC32F57xx with Standard Peripherals Drivers Framework</description>
			<require condition="SC32F57xx"/>
			<require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework"/>
		</condition>
	</conditions>
  
	<!-- component section (optional for all Software Packs)-->
	<components>
		<!-- Startup SC32F57128 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.2.0" condition="SC32F57128">
			<description>System Startup for Slian SC32F57128 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xx.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xx.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57128_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.2.0" condition="SC32F57128_BOOT">
			<description>System Startup for Slian SC32F57128_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xx.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xx.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5764 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.2.0" condition="SC32F5764">
			<description>System Startup for Slian SC32F5764 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xx.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xx.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5764_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.2.0" condition="SC32F5764_BOOT">
			<description>System Startup for Slian SC32F5764_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xx.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xx.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xx.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5764H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5764H">
			<description>System Startup for Slian SC32F5764H Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F5764H_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F5764H_BOOT">
			<description>System Startup for Slian SC32F5764H_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57128H -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57128H">
			<description>System Startup for Slian SC32F57128H Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57128H_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57128H_BOOT">
			<description>System Startup for Slian SC32F57128H_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57256 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57256">
			<description>System Startup for Slian SC32F57256 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57256_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57256_BOOT">
			<description>System Startup for Slian SC32F57256_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57xxH.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57xxH.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57xxH.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57512 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57512">
			<description>System Startup for Slian SC32F57512 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57512.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57512.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57512.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57512.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57512_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57512_BOOT">
			<description>System Startup for Slian SC32F57512_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57512.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57512.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57512.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57512.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57768 -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57768">
			<description>System Startup for Slian SC32F57768 Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57768.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57768.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57768.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57768.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- Startup SC32F57512_BOOT -->
		<component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="SC32F57768_BOOT">
			<description>System Startup for Slian SC32F57768_BOOT Series</description>
			<files>
				<!-- include folder / file -->
				<file category="include" name="Device/Include/"/>
				<!-- startup / system file -->
				<file category="source" name="Device/Source/ARM/startup_SC32F57768.s" attr="config" version="1.0.0" condition="Startup ARM"/>
				<file category="source" name="Device/Source/GCC/startup_SC32F57768.S" attr="config" version="1.0.0" condition="Startup GCC"/>
				<file category="source" name="Device/Source/IAR/startup_SC32F57768.s" attr="config" version="1.0.0" condition="Startup IAR"/>
				
				<!-- system file -->
				<file category="source" name="Device/Source/system_SC32F57768.c" attr="config" version="1.0.0" />
			</files>
		</component>
		
		<!-- START: Silan Microelectronics Standard Peripherals Drivers -->
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>Standard Peripherals Drivers Framework</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_FRAMEWORK
      		</RTE_Components_h>
			<files>
				<!-- 
				<file category="doc" name="Device/StdPeriph_Driver/sc32f57xx_stdperiph_lib_um.pdf"/>
				-->
				<file category="include" name="Device/StdPeriph_Driver/inc/"/>
				<file category="header"  name="Device/StdPeriph_Driver/inc/misc.h"/>
				<file category="source"  name="Device/StdPeriph_Driver/src/misc.c"/>
				<file category="header" name="Device/StdPeriph_Driver/templates/sc32f57xx_conf.h" attr="config" version="1.0.0"/>
				<file category="header" name="Device/StdPeriph_Driver/templates/sc32f57xx_it.h" attr="template" select="Interrupt Service Routines"/>
				<file category="source" name="Device/StdPeriph_Driver/templates/sc32f57xx_it.c" attr="template" select="Interrupt Service Routines"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="ADC" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>ADC driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_ADC
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_adc.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_adc.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="BT16" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>BT16 driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_BT16
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_bt16.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_bt16.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CHIPCTRL" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>CHIPCTRL driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_CHIPCTRL
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_chipctrl.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_chipctrl.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CLOCK" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>CLOCK driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_CLOCK
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_clock.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_clock.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CRC" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>CRC driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_CRC
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_crc.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_crc.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="DMA" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>DMA driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_DMA
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_dma.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_dma.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="ERU" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>ERU driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_ERU
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_eru.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_eru.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="FLASH" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>FLASH driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_FLASH
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_flash.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_flash.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="GPIO" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>GPIO driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_GPIO
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_gpio.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_gpio.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="GTA" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>GTA driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_GTA
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_gta.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_gta.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="I2C" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>I2C driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_I2C
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_i2c.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_i2c.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="KBI" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>KBI driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_KBI
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_kbi.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_kbi.c"/>
			</files>
		</component>		
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="NORMAL_UART" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>NORMAL UART driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_NORMAL_UART
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_normal_uart.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_normal_uart.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="PPU" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>PPU driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_PPU
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_ppu.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_ppu.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SPI" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>SPI driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_SPI
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_spi.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_spi.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SSP" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>SSP driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_SSP
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_ssp.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_ssp.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SYSCFG" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>SYSCFG driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_SYSCFG
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_syscfg.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_syscfg.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="TKS" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>TKS driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_TKS
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_tks.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_tks.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="UART0" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>UART0 driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_UART0
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_uart0.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_uart0.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="WDT" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>WDT driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_WDT
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_wdt.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_wdt.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="IWDT" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>IWDT driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_IWDT
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_iwdt.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_iwdt.c"/>
			</files>
		</component>
		<component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="WT" Cversion="1.2.0" condition="SC32F57xx STDPERIPH">
			<description>WT driver for SC32F57xx</description>
			<RTE_Components_h>
        		#define RTE_DEVICE_STDPERIPH_WT
      		</RTE_Components_h>
			<files>
				<file category="header" name="Device/StdPeriph_Driver/inc/sc32f57xx_wt.h"/>
				<file category="source" name="Device/StdPeriph_Driver/src/sc32f57xx_wt.c"/>
			</files>
		</component>
		<!-- END: Slian Microelectronics Standard Peripherals Drivers -->
	</components>

</package>
