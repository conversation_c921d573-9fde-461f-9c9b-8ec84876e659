<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: Shield layer</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('shield_layer.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Shield layer </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p >The <b><a href="https://github.com/Open-CMSIS-Pack/cmsis-toolbox/blob/main/docs/ReferenceApplications.md#shield-layer">Shield layer</a></b> adds support for additional hardware via plugin shields.</p>
<h1><a class="anchor" id="shield_WiFi"></a>
WiFi Shields</h1>
<p >The <a class="el" href="index.html#driver_pack_content">Pack Content</a> provides implementations of <b>Shield layers</b> for the following Arduino Uno WiFi shields: </p><table class="cmtable" summary="Shield layers">
<tr>
<th>Shield layer </th><th>Description  </th></tr>
<tr>
<td><a class="el" href="shield_layer.html#shield_Inventek_ISMART43362-E">Inventek ISMART43362-E</a> </td><td>Shield layer for Inventek ISMART43362-E WiFi Shield.   </td></tr>
<tr>
<td><a class="el" href="shield_layer.html#shield_Sparkfun_DA16200">Sparkfun DA16200</a> </td><td>Shield layer for Sparkfun DA16200WiFi Shield.   </td></tr>
<tr>
<td><a class="el" href="shield_layer.html#shield_Sparkfun_ESP8266">Sparkfun ESP8266</a> </td><td>Shield layer for Sparkfun ESP8266 WiFi Shield.   </td></tr>
<tr>
<td><a class="el" href="shield_layer.html#shield_WizNet_WizFi360-EVB">WizNet WizFi360-EVB</a> </td><td>Shield layer for WizNet WizFi360-EVB WiFi Shield.   </td></tr>
</table>
<h2><a class="anchor" id="shield_Inventek_ISMART43362-E"></a>
Inventek ISMART43362-E</h2>
<p >Shield layer for <a href="https://www.inventeksys.com/ismart43362-arduino-shields-wi-fi">Inventek ISMART43362-E</a> Arduino Uno WiFi Shield. The shield is connected via an Arduino header using an SPI interface.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Provided API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">CMSIS_WIFI   </td><td class="markdownTableBodyNone">CMSIS-Driver WIFI (instance 0)   </td></tr>
</table>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Consumed API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">ARDUINO_UNO_SPI   </td><td class="markdownTableBodyNone">CMSIS-Driver SPI connected to Arduino SPI pins D11..D13    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">ARDUINO_UNO_D9,D10   </td><td class="markdownTableBodyNone">CMSIS-Driver GPIO connected to Arduino digital I/O pins D9,D10    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">CMSIS-RTOS2   </td><td class="markdownTableBodyNone">CMSIS-RTOS2 compliant RTOS   </td></tr>
</table>
<h2><a class="anchor" id="shield_Sparkfun_DA16200"></a>
Sparkfun DA16200</h2>
<p >Shield layer for <a href="https://www.sparkfun.com/products/18567">Sparkfun DA16200</a> Arduino Uno WiFi Shield. The shield is connected via an Arduino header using an UART interface.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Provided API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">CMSIS_WIFI   </td><td class="markdownTableBodyNone">CMSIS-Driver WIFI (instance 0)   </td></tr>
</table>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Consumed API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">ARDUINO_UNO_UART   </td><td class="markdownTableBodyNone">CMSIS-Driver USART connected to Arduino UART pins D0..D1    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">CMSIS-RTOS2   </td><td class="markdownTableBodyNone">CMSIS-RTOS2 compliant RTOS   </td></tr>
</table>
<h2><a class="anchor" id="shield_Sparkfun_ESP8266"></a>
Sparkfun ESP8266</h2>
<p >Shield layer for <a href="https://www.sparkfun.com/products/13287">Sparkfun ESP8266</a> Arduino Uno WiFi Shield. The shield is connected via an Arduino header using an UART interface.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Provided API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">CMSIS_WIFI   </td><td class="markdownTableBodyNone">CMSIS-Driver WIFI (instance 0)   </td></tr>
</table>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Consumed API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">ARDUINO_UNO_UART   </td><td class="markdownTableBodyNone">CMSIS-Driver USART connected to Arduino UART pins D0..D1    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">CMSIS-RTOS2   </td><td class="markdownTableBodyNone">CMSIS-RTOS2 compliant RTOS   </td></tr>
</table>
<h2><a class="anchor" id="shield_WizNet_WizFi360-EVB"></a>
WizNet WizFi360-EVB</h2>
<p >Shield layer for <a href="https://docs.wiznet.io/Product/Wi-Fi-Module/WizFi360/wizfi360_evb_shield">WizNet WizFi360-EVB</a> Arduino Uno WiFi Shield. The shield is connected via an Arduino header using an UART interface.</p>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Provided API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">CMSIS_WIFI   </td><td class="markdownTableBodyNone">CMSIS-Driver WIFI (instance 0)   </td></tr>
</table>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Consumed API Interface   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">ARDUINO_UNO_UART   </td><td class="markdownTableBodyNone">CMSIS-Driver USART connected to Arduino UART pins D0..D1    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">CMSIS-RTOS2   </td><td class="markdownTableBodyNone">CMSIS-RTOS2 compliant RTOS   </td></tr>
</table>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
