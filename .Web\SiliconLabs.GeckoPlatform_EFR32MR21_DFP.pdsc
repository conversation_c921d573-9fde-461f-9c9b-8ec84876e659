<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32MR21_DFP</name>
  <description>Silicon Labs EFR32MR21 Mighty Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
    <release version="2024.12.0" date="2024-12-26">
      New OPNs for Caracal SoC EFR32BG26, EFR32MG26, EFM32PG26 and Caracal module MGM26. New OPN for Soc EFR32BG27.
    </release>
    <release version="2024.6.0" date="2024-06-19">
      New device family for Caracal EFM32PG26, EFR32BG26, EFR32MG26
    </release>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32MR21</keyword>
    <keyword>EFR32</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32MR21 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p3" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="38400000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
32-bit ARM Cortex-M33 core with 80 MHz maximum operating frequency.
      </description>

      <subFamily DsubFamily="EFR32MR21A020">
        <book         name="Documents/efr32mr21-revision-C-datasheet.pdf"      title="EFR32MR21A020 Data Sheet"/>
        <book         name="Documents/efr32mr21-errata.pdf"         title="EFR32MR21A020 Errata"/>
        <!-- *************************  Device 'EFR32MR21A020F512IM32'  ***************************** -->
        <device Dname="EFR32MR21A020F512IM32">
          <compile header="Device/SiliconLabs/EFR32MR21/Include/em_device.h"  define="EFR32MR21A020F512IM32"/>
          <debug      svd="SVD/EFR32MR21/EFR32MR21A020F512IM32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00080000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00010000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2.FLM"  start="0x00000000"  size="0x00080000"  RAMstart="0x20000000" RAMsize="0x10000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2.flash"  start="0x00000000"  size="0x00080000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32MR21">
      <description>Silicon Labs EFR32MR21 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32MR21*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32MR21">
      <description>System Startup for Silicon Labs EFR32MR21 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32MR21/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32MR21/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32MR21/Source/system_efr32mr21.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
