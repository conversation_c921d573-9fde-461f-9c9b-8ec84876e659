<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>SE_HOSTLIB</name>
  <vendor>NXP</vendor>
  <description>Software Pack for se_hostlib</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="FREERTOS-AWS_IOT" vendor="NXP" version="2.0.0"/>
      <package name="MBEDTLS" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.se_hostlib.a71ch.condition_id">
      <require condition="allOf.middleware.se_hostlib.common_A71CH.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.common_A71CH.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="common_A71CH"/>
    </condition>
    <condition id="middleware.se_hostlib.commonCloudDemos.condition_id">
      <require condition="allOf.middleware.freertos-kernel.heap_4, middleware.freertos.coremqtt, middleware.pkcs11, middleware.freertos.backoffalgorithm.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.freertos-kernel.heap_4, middleware.freertos.coremqtt, middleware.pkcs11, middleware.freertos.backoffalgorithm.internal_condition">
      <require Cclass="RTOS" Cgroup="Heap" Cvariant="Heap_4"/>
      <require Cclass="FreeRTOS" Cgroup="coreMQTT"/>
      <require Cclass="AWS IoT" Cgroup="OASIS PKCS Standard 11" Csub="pkcs11"/>
      <require Cclass="FreeRTOS" Cgroup="backoffAlgorithm"/>
    </condition>
    <condition id="middleware.se_hostlib.commonSe050.condition_id">
      <require condition="allOf.middleware.se_hostlib.commonSe050_ksdk, middleware.se_hostlib.commonSe050smCom, middleware.se_hostlib.commonSe050infra, middleware.se_hostlib.commonSe050_sss_ex, middleware.se_hostlib.commonSe050_CurrentApplet, middleware.se_hostlib.commonSe050_Scp03, middleware.se_hostlib.mwlog.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.commonSe050_ksdk, middleware.se_hostlib.commonSe050smCom, middleware.se_hostlib.commonSe050infra, middleware.se_hostlib.commonSe050_sss_ex, middleware.se_hostlib.commonSe050_CurrentApplet, middleware.se_hostlib.commonSe050_Scp03, middleware.se_hostlib.mwlog.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_ksdk"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050smCom"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050infra"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_sss_ex"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_CurrentApplet"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_Scp03"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="mwlog"/>
    </condition>
    <condition id="middleware.se_hostlib.commonSe050CloudDemos.condition_id">
      <require condition="allOf.middleware.se_hostlib.commonSe050, middleware.se_hostlib.commonCloudDemos, middleware.se_hostlib.mbedtls_sss.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.commonSe050, middleware.se_hostlib.commonCloudDemos, middleware.se_hostlib.mbedtls_sss.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonCloudDemos"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="mbedtls_sss"/>
    </condition>
    <condition id="device.RW610.internal_condition">
      <accept Dname="RW610ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW610UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.RW612.internal_condition">
      <accept Dname="RW612ETA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612HNA2I" Dvendor="NXP:11"/>
      <accept Dname="RW612UKA2I" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.se_hostlib.el2go_blob_test.condition_id">
      <require condition="anyOf.allOf=middleware.se_hostlib.el2go_blob_test.reader.inline, middleware.se_hostlib.el2go_blob_test.psa, board=rdrw612bga, frdmrw612.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=middleware.se_hostlib.el2go_blob_test.reader.inline, middleware.se_hostlib.el2go_blob_test.psa, board=rdrw612bga, frdmrw612.internal_condition">
      <accept condition="allOf.middleware.se_hostlib.el2go_blob_test.reader.inline, middleware.se_hostlib.el2go_blob_test.psa, board=rdrw612bga, frdmrw612.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.el2go_blob_test.reader.inline, middleware.se_hostlib.el2go_blob_test.psa, board=rdrw612bga, frdmrw612.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="el2go_blob_test_reader_inline"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="el2go_blob_test_psa"/>
      <require condition="board.rdrw612bga, frdmrw612.internal_condition"/>
    </condition>
    <condition id="board.rdrw612bga, frdmrw612.internal_condition">
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
    </condition>
    <condition id="middleware.se_hostlib.mbedtls_altA71CH.condition_id">
      <require condition="allOf.middleware.se_hostlib.common_A71CH, middleware.se_hostlib.A71CHhostCrypto.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.common_A71CH, middleware.se_hostlib.A71CHhostCrypto.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="common_A71CH"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="A71CHhostCrypto"/>
    </condition>
    <condition id="middleware.se_hostlib.mbedtls_sss.condition_id">
      <require condition="allOf.middleware.se_hostlib.commonSe050, middleware.se_hostlib.mbedtls_alt_demo_common.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.commonSe050, middleware.se_hostlib.mbedtls_alt_demo_common.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="mbedtls_alt_demo_common"/>
    </condition>
    <condition id="device.MIMXRT1041.internal_condition">
      <accept Dname="MIMXRT1041DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1041XJM5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1042.internal_condition">
      <accept Dname="MIMXRT1042DFP6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042DJM6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XFP5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1042XJM5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1061.internal_condition">
      <accept Dname="MIMXRT1061CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1061XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1062.internal_condition">
      <accept Dname="MIMXRT1062CVJ5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVJ5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062CVL5B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVJ6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVL6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062DVN6B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1062XVN5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1171.internal_condition">
      <accept Dname="MIMXRT1171AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1171DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1172.internal_condition">
      <accept Dname="MIMXRT1172AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1172DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1173.internal_condition">
      <accept Dname="MIMXRT1173CVM8A" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1175.internal_condition">
      <accept Dname="MIMXRT1175AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1175DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1176.internal_condition">
      <accept Dname="MIMXRT1176AVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176CVM8A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1176DVMAA" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S66.internal_condition">
      <accept Dname="LPC55S66JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S66JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.LPC55S69.internal_condition">
      <accept Dname="LPC55S69JBD100" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JBD64" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV59" Dvendor="NXP:11"/>
      <accept Dname="LPC55S69JEV98" Dvendor="NXP:11"/>
    </condition>
    <condition id="middleware.se_hostlib.nxp_iot_agent.condition_id">
      <require condition="allOf.anyOf=middleware.se_hostlib.nxp_iot_agent.lwip_enet, middleware.se_hostlib.nxp_iot_agent.lwip_wifi, anyOf=allOf=middleware.se_hostlib.nxp_iot_agent.psa, board=rdrw612bga, frdmrw612, allOf=middleware.se_hostlib.nxp_iot_agent.sss, board=evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="allOf.anyOf=middleware.se_hostlib.nxp_iot_agent.lwip_enet, middleware.se_hostlib.nxp_iot_agent.lwip_wifi, anyOf=allOf=middleware.se_hostlib.nxp_iot_agent.psa, board=rdrw612bga, frdmrw612, allOf=middleware.se_hostlib.nxp_iot_agent.sss, board=evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition">
      <require condition="anyOf.middleware.se_hostlib.nxp_iot_agent.lwip_enet, middleware.se_hostlib.nxp_iot_agent.lwip_wifi.internal_condition"/>
      <require condition="anyOf.allOf=middleware.se_hostlib.nxp_iot_agent.psa, board=rdrw612bga, frdmrw612, allOf=middleware.se_hostlib.nxp_iot_agent.sss, board=evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="anyOf.middleware.se_hostlib.nxp_iot_agent.lwip_enet, middleware.se_hostlib.nxp_iot_agent.lwip_wifi.internal_condition">
      <accept Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_lwip_enet"/>
      <accept Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_lwip_wifi"/>
    </condition>
    <condition id="anyOf.allOf=middleware.se_hostlib.nxp_iot_agent.psa, board=rdrw612bga, frdmrw612, allOf=middleware.se_hostlib.nxp_iot_agent.sss, board=evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition">
      <accept condition="allOf.middleware.se_hostlib.nxp_iot_agent.psa, board=rdrw612bga, frdmrw612.internal_condition"/>
      <accept condition="allOf.middleware.se_hostlib.nxp_iot_agent.sss, board=evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.nxp_iot_agent.psa, board=rdrw612bga, frdmrw612.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_psa"/>
      <require condition="board.rdrw612bga, frdmrw612.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.nxp_iot_agent.sss, board=evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_sss"/>
      <require condition="board.evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition"/>
    </condition>
    <condition id="board.evkmimxrt1040, evkmimxrt1060, evkmimxrt1170, evkbmimxrt1170, evkbmimxrt1060, lpcxpresso55s69.internal_condition">
      <accept condition="device.MIMXRT1041.internal_condition"/>
      <accept condition="device.MIMXRT1042.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1171.internal_condition"/>
      <accept condition="device.MIMXRT1172.internal_condition"/>
      <accept condition="device.MIMXRT1173.internal_condition"/>
      <accept condition="device.MIMXRT1175.internal_condition"/>
      <accept condition="device.MIMXRT1176.internal_condition"/>
      <accept condition="device.MIMXRT1061.internal_condition"/>
      <accept condition="device.MIMXRT1062.internal_condition"/>
      <accept condition="device.LPC55S66.internal_condition"/>
      <accept condition="device.LPC55S69.internal_condition"/>
    </condition>
    <condition id="middleware.se_hostlib.nxp_iot_agent.flash_config.condition_id">
      <require condition="anyOf.allOf=middleware.se_hostlib.nxp_iot_agent.flash_config.frdmrw612, board=frdmrw612, allOf=middleware.se_hostlib.nxp_iot_agent.flash_config.rdrw612, board=rdrw612bga.internal_condition"/>
    </condition>
    <condition id="anyOf.allOf=middleware.se_hostlib.nxp_iot_agent.flash_config.frdmrw612, board=frdmrw612, allOf=middleware.se_hostlib.nxp_iot_agent.flash_config.rdrw612, board=rdrw612bga.internal_condition">
      <accept condition="allOf.middleware.se_hostlib.nxp_iot_agent.flash_config.frdmrw612, board=frdmrw612.internal_condition"/>
      <accept condition="allOf.middleware.se_hostlib.nxp_iot_agent.flash_config.rdrw612, board=rdrw612bga.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.nxp_iot_agent.flash_config.frdmrw612, board=frdmrw612.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_fl_cfg_frdmrw612"/>
      <require condition="board.frdmrw612.internal_condition"/>
    </condition>
    <condition id="board.frdmrw612.internal_condition">
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.nxp_iot_agent.flash_config.rdrw612, board=rdrw612bga.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_fl_cfg_rdrw612"/>
      <require condition="board.rdrw612bga.internal_condition"/>
    </condition>
    <condition id="board.rdrw612bga.internal_condition">
      <accept condition="device.RW610.internal_condition"/>
      <accept condition="device.RW612.internal_condition"/>
    </condition>
    <condition id="middleware.se_hostlib.nxp_iot_agent.lwip_enet.condition_id">
      <require condition="allOf.middleware.se_hostlib.nxp_iot_agent.common.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.nxp_iot_agent.common.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_common"/>
    </condition>
    <condition id="middleware.se_hostlib.nxp_iot_agent.lwip_wifi.condition_id">
      <require condition="allOf.middleware.se_hostlib.nxp_iot_agent.common.internal_condition"/>
    </condition>
    <condition id="middleware.se_hostlib.nxp_iot_agent.psa.condition_id">
      <require condition="allOf.middleware.se_hostlib.nxp_iot_agent.common.internal_condition"/>
    </condition>
    <condition id="middleware.se_hostlib.nxp_iot_agent.sss.condition_id">
      <require condition="allOf.middleware.se_hostlib.nxp_iot_agent.common, middleware.se_hostlib.commonSe050CloudDemos.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.se_hostlib.nxp_iot_agent.common, middleware.se_hostlib.commonSe050CloudDemos.internal_condition">
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_common"/>
      <require Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050CloudDemos"/>
    </condition>
    <condition id="middleware.se_hostlib.serial_manager_usb_cdc.condition_id">
      <require condition="allOf.middleware.usb.device.cdc.external, driver.common.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.usb.device.cdc.external, driver.common.internal_condition">
      <require Cclass="USB" Cgroup="USB Device" Csub="cdc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="middleware.se_hostlib.tstutils.condition_id">
      <require condition="allOf.middleware.se_hostlib.common_A71CH.internal_condition"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="A71CHApisrc" Cversion="1.0.0">
      <description>se_hostlib A71CHApisrc</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/A71HLSEWrapper.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_crypto_aes_key.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_crypto_ecc.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_crypto_rng.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_scp.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_sss_scp.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_sst.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_switch.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_util.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="A71CHhostCrypto" Cversion="1.0.0">
      <description>se_hostlib A71CHhostCrypto</description>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/axHostCrypto.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/hostCrypto"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/axHostCryptombedtls.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/hostCrypto"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/hcAsn.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/hostCrypto"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/hcAsn.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/hostCrypto"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/HostCryptoAPI.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/hostCrypto"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/HostCryptoAPImbedtls.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/hostCrypto"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="a71ch" Cversion="1.0.0" condition="middleware.se_hostlib.a71ch.condition_id">
      <description>se_hostlib a71ch</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/a71ch/src/a71ch_com_scp.c" projectpath="se_hostlib/hostlib/hostLib/a71ch/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/a71ch/src/a71ch_crypto_derive.c" projectpath="se_hostlib/hostlib/hostLib/a71ch/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/a71ch/src/a71ch_crypto_ecc.c" projectpath="se_hostlib/hostlib/hostLib/a71ch/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/a71ch/src/a71ch_module.c" projectpath="se_hostlib/hostlib/hostLib/a71ch/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/a71ch/src/a71ch_sst.c" projectpath="se_hostlib/hostlib/hostLib/a71ch/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/A71HLSEWrapper.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_crypto_aes_key.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_crypto_ecc.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_crypto_rng.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_scp.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_switch.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/api/src/ax_util.c" projectpath="se_hostlib/hostlib/hostLib/api/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/a71_debug.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/a71_debug.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/"/>
        <file category="include" name="middleware/se_hostlib/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/port/ksdk/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/inc/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonA71CHsmCom" Cversion="1.0.0">
      <description>se_hostlib commonA71CHsmCom</description>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/apduComm.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/sci2c.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/sci2c.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/sci2c_cfg.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smCom.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smCom.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smComSCI2C.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smComSCI2C.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smComT1oI2C.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonCloudDemos" Cversion="1.0.0" condition="middleware.se_hostlib.commonCloudDemos.condition_id">
      <description>se_hostlib commonCloudDemos</description>
      <Pre_Include_Global_h>
#ifndef SSS_USE_FTR_FILE
#define SSS_USE_FTR_FILE 
#endif
#ifndef MBEDTLS
#define MBEDTLS 
#endif
#ifndef SDK_OS_FREE_RTOS
#define SDK_OS_FREE_RTOS 
#endif
#ifndef USE_RTOS
#define USE_RTOS 
#endif
#ifndef mqttconfigENABLE_METRICS
#define mqttconfigENABLE_METRICS 0
#endif
#ifndef SE_CLOUD_MCU_SDK
#define SE_CLOUD_MCU_SDK 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ledHandler.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_demo_utils.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_demo_utils_rtos.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_pal_core.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_pal.h" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/pkcs11/nxLog_pkcs11.h" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_pal_helpers.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_pal_object.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_pal_asymm.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_pal_symm.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_utils.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/pkcs11/sss_pkcs11_utils.h" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/pkcs11/base64_decode.c" projectpath="se_hostlib/sss/plugin/pkcs11"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tstHostCrypto.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/plugin/pkcs11/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonInfra" Cversion="1.0.0">
      <description>se_hostlib commonInfra</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_connect.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/global_platf.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/a71_debug.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/app_boot.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/app_boot_nfc.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/cm_commands.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/global_platf.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_apdu.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_api.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_demo_utils.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_errors.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_types.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_apdu.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_errors.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_printf.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/a71_debug.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050" Cversion="1.0.0" condition="middleware.se_hostlib.commonSe050.condition_id">
      <description>se_hostlib commonSe050</description>
      <Pre_Include_Global_h>
#ifndef T1oI2C
#define T1oI2C 
#endif
#ifndef T1oI2C_UM11225
#define T1oI2C_UM11225 
#endif
#ifndef SSS_USE_FTR_FILE
#define SSS_USE_FTR_FILE 
#endif
#ifndef SDK_DEBUGCONSOLE_UART
#define SDK_DEBUGCONSOLE_UART 
#endif
#ifndef NO_SECURE_CHANNEL_SUPPORT
#define NO_SECURE_CHANNEL_SUPPORT 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="doc" name="middleware/se_hostlib/ChangeLogKSDK.txt" projectpath="se_hostlib"/>
        <file category="doc" name="middleware/se_hostlib/EULA.pdf" projectpath="se_hostlib"/>
        <file category="doc" name="middleware/se_hostlib/Third_Party_License.pdf" projectpath="se_hostlib"/>
        <file category="doc" name="middleware/se_hostlib/sss/version_info.txt" projectpath="se_hostlib/sss"/>
        <file category="doc" name="middleware/se_hostlib/hostlib/version_info.txt" projectpath="se_hostlib/hostlib"/>
        <file category="doc" name="middleware/se_hostlib/demos/version_info.txt" projectpath="se_hostlib/demos"/>
        <file category="doc" name="middleware/se_hostlib/nxp_iot_agent/version_info.txt" projectpath="se_hostlib/nxp_iot_agent"/>
        <file category="header" name="middleware/se_hostlib/sss/port/ksdk/fsl_sss_types.h" projectpath="se_hostlib/sss/port/ksdk"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/Applet_SE050_Ver.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEAPI.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEComm.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSECrypto.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEMisc.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEObjects.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSETypes.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/PlugAndTrust_HostLib_Ver.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/PlugAndTrust_Pkg_Ver.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/SE_HAL_Lib.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71ch_api.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71ch_util.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71cl_api.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71cl_util.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_api.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common_a71ch.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common_a71cl.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common_private.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_scp.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_scp_private.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_sss_scp.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_util.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ledHandler.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/modules.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxEnsure.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxScp03_Apis.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxScp03_Const.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxScp03_Types.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/scp.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_const.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ecc_curves.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ecc_curves_inc.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ecc_curves_values.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_enums.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ftr.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_tlv.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/sm_const.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/se05x/src/se05x_ECC_curves.c" projectpath="se_hostlib/hostlib/hostLib/se05x/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/se05x/src/se05x_mw.c" projectpath="se_hostlib/hostlib/hostLib/se05x/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/se05x/src/se05x_tlv.c" projectpath="se_hostlib/hostlib/hostLib/se05x/src"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_a71ch.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_a71cl.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_commands.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_mu.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_api.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_api_ver.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_config.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_ftr_default.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_keyid_map.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_lpc55s_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_lpc55s_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_mbedtls_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_mbedtls_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_openssl_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_openssl_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_policy.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_policy.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_scp03.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_sscp.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_sscp_config.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_user_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_user_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_util_asn1_der.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_util_rsa_sign_utils.h" projectpath="se_hostlib/sss/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/fsl_sss_apis.c" projectpath="se_hostlib/sss/src"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/fsl_sss_util_asn1_der.c" projectpath="se_hostlib/sss/src"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/fsl_sss_util_rsa_sign_utils.c" projectpath="se_hostlib/sss/src"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/se05x/fsl_sss_se05x_apis.c" projectpath="se_hostlib/sss/src/se05x"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/se05x/fsl_sss_se05x_eckey.c" projectpath="se_hostlib/sss/src/se05x"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/se05x/fsl_sss_se05x_mw.c" projectpath="se_hostlib/sss/src/se05x"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/se05x/fsl_sss_se05x_policy.c" projectpath="se_hostlib/sss/src/se05x"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/se05x/fsl_sss_se05x_scp03.c" projectpath="se_hostlib/sss/src/se05x"/>
        <file category="include" name="middleware/se_hostlib/sss/port/ksdk/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/inc/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/se05x/src/"/>
        <file category="include" name="middleware/se_hostlib/sss/inc/"/>
        <file category="include" name="middleware/se_hostlib/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050CloudDemos" Cversion="1.0.0" condition="middleware.se_hostlib.commonSe050CloudDemos.condition_id">
      <description>se_hostlib commonSe050CloudDemos</description>
      <files>
        <file category="doc" name="middleware/se_hostlib/ChangeLogKSDK.txt" projectpath="se_hostlib"/>
        <file category="include" name="middleware/se_hostlib/sss/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/ex/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/port/ksdk/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/"/>
        <file category="include" name="middleware/se_hostlib/sss/plugin/mbedtls/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/inc/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_CurrentApplet" Cversion="1.0.0">
      <description>se_hostlib commonSe050_CurrentApplet</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/se05x_APDU.c" projectpath="se_hostlib/hostlib/hostLib/se05x_03_xx_xx"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/se05x_APDU.h" projectpath="se_hostlib/hostlib/hostLib/se05x_03_xx_xx"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/se05x_APDU_apis.h" projectpath="se_hostlib/hostlib/hostLib/se05x_03_xx_xx"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/se05x_APDU_impl.h" projectpath="se_hostlib/hostlib/hostLib/se05x_03_xx_xx"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/se05x_04_xx_APDU_apis.h" projectpath="se_hostlib/hostlib/hostLib/se05x_03_xx_xx"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/se05x_04_xx_APDU_impl.h" projectpath="se_hostlib/hostlib/hostLib/se05x_03_xx_xx"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/se05x_03_xx_xx/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_Scp03" Cversion="1.0.0">
      <description>se_hostlib commonSe050_Scp03</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/nxScp/nxScp03_Com.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/nxScp"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/scp/scp.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/scp"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_ksdk" Cversion="1.0.0">
      <description>se_hostlib commonSe050_ksdk</description>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/ax_reset.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/i2c_a7.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/se05x_apis.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/se_pit_config.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/se_reset_config.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/sm_printf.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/sm_timer.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/ax_reset.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/i2c_frdm.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/i2c_imxrt.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/i2c_lpc54xxx.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/i2c_lpc55sxx.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/se05x_reset.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis_bm.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis_freertos.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis_threadx.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050_sss_ex" Cversion="1.0.0">
      <description>se_hostlib commonSe050_sss_ex</description>
      <files>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_a71ch_scp03.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_scp03_puf.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_auth.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_boot.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_cc_inc.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_freeRTOS_inc.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_frdmk64f.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_imx_rt.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_ksdk.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_linux.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_lpcxpresso55s.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_nrf.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_main_inc_qn9090.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_objid.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_ports.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_scp03_keys.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/inc/ex_sss_tp_scp03_keys.h" projectpath="se_hostlib/sss/ex/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/ex/src/ex_sss_boot.c" projectpath="se_hostlib/sss/ex/src"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/ex/src/ex_sss_boot_connectstring.c" projectpath="se_hostlib/sss/ex/src"/>
        <file category="header" name="middleware/se_hostlib/sss/ex/src/ex_sss_boot_int.h" projectpath="se_hostlib/sss/ex/src"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/ex/src/ex_sss_se05x.c" projectpath="se_hostlib/sss/ex/src"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/ex/src/ex_sss_se05x_auth.c" projectpath="se_hostlib/sss/ex/src"/>
        <file category="include" name="middleware/se_hostlib/sss/ex/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/ex/src/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050infra" Cversion="1.0.0">
      <description>se_hostlib commonSe050infra</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/global_platf.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_connect.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/a71_debug.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/app_boot.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/app_boot_nfc.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/cm_commands.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/global_platf.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_apdu.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_api.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_demo_utils.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_errors.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_types.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_apdu.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_errors.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_printf.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="commonSe050smCom" Cversion="1.0.0">
      <description>se_hostlib commonSe050smCom</description>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/apduComm.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/sci2c_cfg.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smCom.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smCom.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smComT1oI2C.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/smComT1oI2C.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phEseStatus.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phEseTypes.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEsePal_i2c.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEseProto7816_3.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEse_Api.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEse_Internal.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEsePal_i2c.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEseProto7816_3.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/phNxpEse_Api.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/smCom/T1oI2C/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="common_A71CH" Cversion="1.0.0">
      <description>se_hostlib common</description>
      <Pre_Include_Global_h>
#ifndef TGT_A71CH
#define TGT_A71CH 
#endif
#ifndef SCI2C
#define SCI2C 
#endif
#ifndef SSS_USE_FTR_FILE
#define SSS_USE_FTR_FILE 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="doc" name="middleware/se_hostlib/ChangeLogKSDK.txt" projectpath="se_hostlib"/>
        <file category="doc" name="middleware/se_hostlib/EULA.pdf" projectpath="se_hostlib"/>
        <file category="doc" name="middleware/se_hostlib/Third_Party_License.pdf" projectpath="se_hostlib"/>
        <file category="doc" name="middleware/se_hostlib/sss/version_info.txt" projectpath="se_hostlib/sss"/>
        <file category="doc" name="middleware/se_hostlib/hostlib/version_info.txt" projectpath="se_hostlib/hostlib"/>
        <file category="doc" name="middleware/se_hostlib/demos/version_info.txt" projectpath="se_hostlib/demos"/>
        <file category="doc" name="middleware/se_hostlib/nxp_iot_agent/version_info.txt" projectpath="se_hostlib/nxp_iot_agent"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/Applet_SE050_Ver.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEAPI.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEComm.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSECrypto.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEMisc.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSEObjects.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/HLSETypes.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/PlugAndTrust_HostLib_Ver.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/PlugAndTrust_Pkg_Ver.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/SE_HAL_Lib.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71ch_api.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71ch_util.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71cl_api.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/a71cl_util.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_api.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common_a71ch.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common_a71cl.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_common_private.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_scp.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_scp_private.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_sss_scp.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ax_util.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/ledHandler.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/modules.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxEnsure.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxScp03_Apis.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxScp03_Const.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/nxScp03_Types.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/scp.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_const.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ecc_curves.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ecc_curves_inc.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ecc_curves_values.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_enums.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_ftr.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/se05x_tlv.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/inc/sm_const.h" projectpath="se_hostlib/hostlib/hostLib/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/scp/scp_a7x.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/scp"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/ax_reset.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/i2c_a7.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/se05x_apis.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/se_pit_config.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/se_reset_config.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/sm_printf.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/sm_timer.h" projectpath="se_hostlib/hostlib/hostLib/platform/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/ax_reset.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/i2c_frdm.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/i2c_imxrt.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis_bm.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/timer_kinetis_freertos.c" projectpath="se_hostlib/hostlib/hostLib/platform/ksdk"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_a71ch.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_a71cl.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_commands.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sscp_mu.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_api.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_api_ver.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_config.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_ftr_default.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_keyid_map.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_lpc55s_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_lpc55s_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_mbedtls_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_mbedtls_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_openssl_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_openssl_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_policy.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_policy.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_scp03.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_se05x_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_sscp.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_sscp_config.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_user_apis.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_user_types.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_util_asn1_der.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/inc/fsl_sss_util_rsa_sign_utils.h" projectpath="se_hostlib/sss/inc"/>
        <file category="header" name="middleware/se_hostlib/sss/port/ksdk/fsl_sss_types.h" projectpath="se_hostlib/sss/port/ksdk"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/inc/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/platform/inc/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/platform/ksdk/"/>
        <file category="include" name="middleware/se_hostlib/sss/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/port/ksdk/"/>
        <file category="include" name="middleware/se_hostlib/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/scp/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="el2go_blob_test" Cversion="1.0.0" condition="middleware.se_hostlib.el2go_blob_test.condition_id">
      <description>se_hostlib el2go_blob_test</description>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test_parser.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test_reader.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test_suite_generic.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test_suite_generic.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="el2go_blob_test_psa" Cversion="1.0.0">
      <description>se_hostlib el2go_blob_test_psa</description>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test_executor_psa.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test_suite_external.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc/el2go_blob_test_suite_internal.h" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test_executor_psa.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test_parser_psa.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test_suite_external.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test_suite_internal.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="el2go_blob_test_reader_inline" Cversion="1.0.0">
      <description>se_hostlib el2go_blob_test_reader_inline</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src/el2go_blob_test_reader_inline.c" projectpath="se_hostlib/nxp_iot_agent/tst/el2go_blob_test/src"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="mbedtls_altA71CH" Cversion="1.0.0" condition="middleware.se_hostlib.mbedtls_altA71CH.condition_id">
      <description>se_hostlib mbedtls_altA71CH</description>
      <Pre_Include_Global_h>
#ifndef MBEDTLS_CONFIG_FILE
#define MBEDTLS_CONFIG_FILE "ksdk_mbedtls_config.h"
#endif
#ifndef MBEDTLS_USER_CONFIG_FILE
#define MBEDTLS_USER_CONFIG_FILE "sss_ksdk_mbedtls_config.h"
#endif
#ifndef MBEDTLS
#define MBEDTLS 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/se_hostlib/sss/port/ksdk/sss_ksdk_mbedtls_config.h" projectpath="se_hostlib/sss/port/ksdk"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/inc/ax_mbedtls.h" projectpath="se_hostlib/hostlib/hostLib/mbedtls/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/src/ecdh_alt.c" projectpath="se_hostlib/hostlib/hostLib/mbedtls/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/src/ecdh_alt_ax.c" projectpath="se_hostlib/hostlib/hostLib/mbedtls/src"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/inc/"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/hostCrypto/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="mbedtls_alt_demo_common" Cversion="1.0.0">
      <description>se_hostlib mbedtls_alt_demo_common</description>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/inc/ax_mbedtls.h" projectpath="se_hostlib/hostlib/hostLib/mbedtls/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/src/ecdh_alt.c" projectpath="se_hostlib/hostlib/hostLib/mbedtls/src"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/src/rsa_alt.c" projectpath="se_hostlib/hostlib/hostLib/mbedtls/src"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/mbedtls/ecdsa_verify_alt.h" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/mbedtls/ecp_alt.h" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/mbedtls/rsa_alt.h" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="header" name="middleware/se_hostlib/sss/plugin/mbedtls/sss_mbedtls.h" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="header" name="middleware/se_hostlib/sss/port/ksdk/fsl_sss_types.h" projectpath="se_hostlib/sss/port/ksdk"/>
        <file category="header" name="middleware/se_hostlib/sss/port/ksdk/sss_ksdk_mbedcrypto_config.h" projectpath="se_hostlib/sss/port/ksdk"/>
        <file category="header" name="middleware/se_hostlib/sss/port/ksdk/sss_ksdk_mbedtls_config.h" projectpath="se_hostlib/sss/port/ksdk"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/src/mbedtls/fsl_sss_mbedtls_apis.c" projectpath="se_hostlib/sss/src/mbedtls"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/mbedtls/inc/"/>
        <file category="include" name="middleware/se_hostlib/sss/plugin/mbedtls/"/>
        <file category="include" name="middleware/se_hostlib/sss/port/ksdk/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="mbedtls_sss" Cversion="1.0.0" condition="middleware.se_hostlib.mbedtls_sss.condition_id">
      <description>se_hostlib mbedtls_sss</description>
      <Pre_Include_Global_h>
#ifndef MBEDTLS_CONFIG_FILE
#define MBEDTLS_CONFIG_FILE "ksdk_mbedtls_config.h"
#endif
#ifndef MBEDTLS_USER_CONFIG_FILE
#define MBEDTLS_USER_CONFIG_FILE "sss_ksdk_mbedtls_config.h"
#endif
#ifndef MBEDTLS
#define MBEDTLS 
#endif
#ifndef SCP_MODE
#define SCP_MODE C_MAC_C_ENC_R_MAC_R_ENC
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/mbedtls/ecdh_alt_ax.c" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/mbedtls/ecdsa_verify_alt.c" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/mbedtls/ecp_alt_sss.c" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/mbedtls/sss_mbedtls.c" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/sss/plugin/mbedtls/sss_mbedtls_rsa.c" projectpath="se_hostlib/sss/plugin/mbedtls"/>
        <file category="include" name="middleware/se_hostlib/sss/plugin/mbedtls/"/>
        <file category="include" name="middleware/se_hostlib/sss/port/ksdk/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="mwlog" Cversion="1.0.0">
      <description>se_hostlib mwlog</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_App.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_DefaultConfig.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_UseCases.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_VCOM.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_hostLib.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_mbedtls.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_scp.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_smCom.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_sscp.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/nxLog_sss.h" projectpath="se_hostlib/hostlib/hostLib/libCommon/log"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/libCommon/log/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent" Cversion="1.0.0" condition="middleware.se_hostlib.nxp_iot_agent.condition_id">
      <description>se_hostlib nxp_iot_agent</description>
      <files>
        <file category="doc" name="middleware/se_hostlib/middleware.se_hostlib.nxp_iot_agent_dummy.txt"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_claimcode_encrypt" Cversion="1.0.0">
      <description>se_hostlib nxp_iot_agent_claimcode_encrypt</description>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_common.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_config.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_config_certificates.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_config_credentials.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_context.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_datastore.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_datastore_fs.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_datastore_plain.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_dispatcher.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_endpoint.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_keystore.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_keystore_psa.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_keystore_sss_se05x.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_log.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_macros.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_service.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_session.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_status.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_time.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_utils.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_utils_protobuf.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_ver.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_claimcode_encrypt.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_demo_config.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/utils/iot_agent_claimcode_encrypt_els.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/utils"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/inc/"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_common" Cversion="1.0.0">
      <description>se_hostlib nxp_iot_agent_common</description>
      <Pre_Include_Global_h>
#ifndef PB_FIELD_32BIT
#define PB_FIELD_32BIT 
#endif
#ifndef EXTERNAL_CUSTOMER_BUILD_CONFIGURATION
#define EXTERNAL_CUSTOMER_BUILD_CONFIGURATION 1
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_common.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_config.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_config_certificates.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_config_credentials.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_context.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_datastore.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_datastore_fs.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_datastore_plain.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_dispatcher.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_endpoint.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_keystore.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_keystore_psa.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_keystore_sss_se05x.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_log.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_macros.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_service.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_session.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_status.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_time.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_utils.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_utils_protobuf.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/nxp_iot_agent_ver.h" projectpath="se_hostlib/nxp_iot_agent/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_demo_config.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_mqtt_freertos.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_network.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/utils/iot_agent_mqtt_freertos.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/utils"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/platform/network.h" projectpath="se_hostlib/nxp_iot_agent/platform"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/platform/mbedtls/network_mbedtls.h" projectpath="se_hostlib/nxp_iot_agent/platform/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/platform/mbedtls/network_mbedtls.c" projectpath="se_hostlib/nxp_iot_agent/platform/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/platform/mbedtls/net_lwip.c" projectpath="se_hostlib/nxp_iot_agent/platform/mbedtls"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_common.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_config.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_datastore.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_datastore_fs.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_datastore_plain.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_keystore.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_keystore_psa.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_keystore_sss_se05x.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_service.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_session.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_time.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_utils.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/nxp_iot_agent_utils_protobuf_socket.c" projectpath="se_hostlib/nxp_iot_agent/src"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/common/nxp_iot_agent_dispatcher.c" projectpath="se_hostlib/nxp_iot_agent/src/common"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/common/nxp_iot_agent_utils_protobuf.c" projectpath="se_hostlib/nxp_iot_agent/src/common"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Agent.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Apdu.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Datastore.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Dispatcher.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Hostcmd.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/PSA.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/RPC.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/ServiceDescriptor.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Types.pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb_common.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb_decode.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb_encode.h" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Agent.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Apdu.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Datastore.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Dispatcher.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Hostcmd.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/PSA.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/RPC.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/ServiceDescriptor.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/Types.pb.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb_common.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb_decode.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/pb_encode.c" projectpath="se_hostlib/nxp_iot_agent/src/protobuf"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/inc/"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/platform/"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/platform/mbedtls/"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/src/protobuf/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_flash_config" Cversion="1.0.0" condition="middleware.se_hostlib.nxp_iot_agent.flash_config.condition_id">
      <description>se_hostlib nxp_iot_agent_flash_config</description>
      <files>
        <file category="doc" name="middleware/se_hostlib/middleware.se_hostlib.nxp_iot_agent.flash_config_dummy.txt"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_fl_cfg_frdmrw612" Cversion="1.0.0">
      <description>se_hostlib nxp_iot_agent_flash_config_frdmrw612</description>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/boards/frdmrw612/nxp_iot_agent_flash_config.h" projectpath="se_hostlib/nxp_iot_agent/inc/boards/frdmrw612"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/inc/boards/frdmrw612/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_fl_cfg_rdrw612" Cversion="1.0.0">
      <description>se_hostlib nxp_iot_agent_flash_config_rdrw612</description>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/inc/boards/rdrw612/nxp_iot_agent_flash_config.h" projectpath="se_hostlib/nxp_iot_agent/inc/boards/rdrw612"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/inc/boards/rdrw612/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_lwip_enet" Cversion="1.0.0" condition="middleware.se_hostlib.nxp_iot_agent.lwip_enet.condition_id">
      <description>se_hostlib nxp_iot_agent_lwip_enet</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/network/iot_agent_network_lwip.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/network"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_lwip_wifi" Cversion="1.0.0" condition="middleware.se_hostlib.nxp_iot_agent.lwip_wifi.condition_id">
      <description>se_hostlib nxp_iot_agent_lwip_wifi</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/network/iot_agent_network_lwip_wifi.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/network"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_psa" Cversion="1.0.0" condition="middleware.se_hostlib.nxp_iot_agent.psa.condition_id">
      <description>se_hostlib nxp_iot_agent_psa</description>
      <files>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_claimcode_import.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/mbedtls_psa/iot_agent_psa_sign_test.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc/mbedtls_psa"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/utils/iot_agent_claimcode_import.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/utils"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/utils/mbedtls_psa/iot_agent_psa_sign_test.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/utils/mbedtls_psa"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/mbedtls_psa/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="nxp_iot_agent_sss" Cversion="1.0.0" condition="middleware.se_hostlib.nxp_iot_agent.sss.condition_id">
      <description>se_hostlib nxp_iot_agent_sss</description>
      <files>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_sm_time.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_sm_time_kinetis.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="header" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/iot_agent_claimcode_inject.h" projectpath="se_hostlib/nxp_iot_agent/ex/inc"/>
        <file category="sourceC" name="middleware/se_hostlib/nxp_iot_agent/ex/src/utils/iot_agent_claimcode_inject.c" projectpath="se_hostlib/nxp_iot_agent/ex/src/utils"/>
        <file category="include" name="middleware/se_hostlib/nxp_iot_agent/ex/inc/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="serial_manager_usb_cdc" Cversion="1.0.0" condition="middleware.se_hostlib.serial_manager_usb_cdc.condition_id">
      <description>se_hostlib serial_manager_usb_cdc</description>
      <Pre_Include_Global_h>
#ifndef DEBUG_CONSOLE_TRANSFER_NON_BLOCKING
#define DEBUG_CONSOLE_TRANSFER_NON_BLOCKING 
#endif
#ifndef USB_DEVICE_CONFIG_CDC_ACM
#define USB_DEVICE_CONFIG_CDC_ACM 1
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/vcom/ksdk/usb_device_descriptor.c" projectpath="se_hostlib/hostlib/hostLib/vcom/ksdk"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/vcom/ksdk/usb_device_descriptor.h" projectpath="se_hostlib/hostlib/hostLib/vcom/ksdk"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/vcom/ksdk/"/>
      </files>
    </component>
    <component Cclass="Security Host Library" Cgroup="Secure Element" Csub="tstutils" Cversion="1.0.0" condition="middleware.se_hostlib.tstutils.condition_id">
      <description>se_hostlib tstutils</description>
      <files>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_app_boot.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/axWebUtil.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/hkdf_mbedtls.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/hkdf_mbedtls.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_a71ch_util.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_a71ch_util.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_sm_time.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_sm_time_kinetis.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_sm_util.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tst_sm_util.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_demo_utils.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/libCommon/infra/sm_demo_utils_rtos.c" projectpath="se_hostlib/hostlib/hostLib/libCommon/infra"/>
        <file category="header" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tstHostCrypto.h" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tstHostCrypto_mbedtls.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="sourceC" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/tstHostCrypto_mbedtls_aes.c" projectpath="se_hostlib/hostlib/hostLib/tstUtil"/>
        <file category="include" name="middleware/se_hostlib/hostlib/hostLib/tstUtil/"/>
      </files>
    </component>
  </components>
</package>
