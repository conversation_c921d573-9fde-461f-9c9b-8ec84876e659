<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1180-EVK_USB_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware usb Examples Pack for MIMXRT1180-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1180-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
      <package name="MIMXRT1189_DFP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="FATFS" vendor="NXP" version="3.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="dev_audio_generator_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_audio_generator/bm/cm33" doc="readme.txt">
      <description>The USB Audio Generator application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a recording device and users can record the sound from this device via the "Sound Recorder" in the Windows Accessories.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_audio_generator_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_audio_generator_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_audio_generator_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_audio_generator/freertos/cm33" doc="readme.txt">
      <description>The USB Audio Generator application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a recording device and users can record the sound from this device via the "Sound Recorder" in the Windows Accessories.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_audio_generator_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_audio_generator_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_audio_generator_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_audio_generator_lite/bm/cm33" doc="readme.txt">
      <description>The USB Audio Generator application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a recording device and users can record the sound from this device via the "Sound Recorder" in the Windows Accessories.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_audio_generator_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_audio_generator_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_audio_speaker_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_audio_speaker/bm/cm33" doc="readme.txt">
      <description>The USB Audio Speaker application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a playback device and users can play music using the device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_audio_speaker_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_audio_speaker_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_audio_speaker_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_audio_speaker/freertos/cm33" doc="readme.txt">
      <description>The USB Audio Speaker application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a playback device and users can play music using the device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_audio_speaker_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_audio_speaker_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_audio_speaker_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_audio_speaker_lite/bm/cm33" doc="readme.txt">
      <description>The USB Audio Speaker application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a playback device and users can play music using the device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_audio_speaker_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_audio_speaker_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vcom_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_cdc_vcom/bm/cm33" doc="readme.txt">
      <description>The Virtual COM project is enumerated as a COM port, which the users can open using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vcom_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vcom_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vcom_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_cdc_vcom/freertos/cm33" doc="readme.txt">
      <description>The Virtual COM project is  enumerated as a COM port, which the users can open using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vcom_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vcom_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vcom_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_cdc_vcom_lite/bm/cm33" doc="readme.txt">
      <description>The Virtual COM project enumerated as a COM port, which the users can open using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vcom_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vcom_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vnic_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_cdc_vnic/bm/cm33" doc="readme.txt">
      <description>The Virtual NIC project is enumerated as a network adapter. Users can access the network by properly configuring this network adapter.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vnic_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vnic_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vnic_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_cdc_vnic/freertos/cm33" doc="readme.txt">
      <description>The Virtual NIC project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as a network adapter. Users can access the network by properly configuring this network adapter.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vnic_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vnic_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_cdc_vnic_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_cdc_vnic_lite/bm/cm33" doc="readme.txt">
      <description>The Virtual NIC project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as a network adapter. Users can access the network by properly configuring this network adapter.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_cdc_vnic_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_cdc_vnic_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_msc/bm/cm33" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_disk_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_msc_disk/bm/cm33" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_disk_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_disk_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_disk_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_msc_disk/freertos/cm33" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_disk_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_disk_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_disk_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_msc_disk_lite/bm/cm33" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a U-disk. The COM port can be opened using terminal tools, such as TeraTerm. The demo...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_disk_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_disk_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_msc/freertos/cm33" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_msc_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_msc_lite/bm/cm33" doc="readme.txt">
      <description>The Composite CDC_MSC project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a COM port and a RAM disk, which can be opened using terminal tools, such as TeraTerm. The demo echoes...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_msc_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_msc_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_vcom_cdc_vcom_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_vcom_cdc_vcom/bm/cm33" doc="readme.txt">
      <description>The Composite CDC_VCOM_CDC_VCOM project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as two COM port, which can be opened using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_vcom_cdc_vcom_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_vcom_cdc_vcom_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_vcom_cdc_vcom_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_vcom_cdc_vcom/freertos/cm33" doc="readme.txt">
      <description>The Composite CDC_VCOM_CDC_VCOM project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as two COM port, which can be opened using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_vcom_cdc_vcom_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_vcom_cdc_vcom_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_cdc_vcom_cdc_vcom_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_cdc_vcom_cdc_vcom_lite/bm/cm33" doc="readme.txt">
      <description>The Composite CDC_VCOM_CDC_VCOM project is a simple demonstration program based on the MCUXpresso SDK.  It is enumerated as two COM port, which can be opened using terminal tools, such as TeraTerm. The demo echoes back any character it receives.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_cdc_vcom_cdc_vcom_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_cdc_vcom_cdc_vcom_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_audio_unified_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_hid_audio_unified/bm/cm33" doc="readme.txt">
      <description>The USB Composite device application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a recording device. Users can record the sound from this device via the "Sound Recorder" in the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_audio_unified_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_audio_unified_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_audio_unified_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_hid_audio_unified/freertos/cm33" doc="readme.txt">
      <description>The USB Composite device application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a recording device. Users can record the sound from this device via the "Sound Recorder" in the...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_audio_unified_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_audio_unified_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_audio_unified_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_hid_audio_unified_lite/bm/cm33" doc="readme.txt">
      <description>The USB Composite HID and Audio Unified application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a playback and recording device. Users can record the sound from this device via...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_audio_unified_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_audio_unified_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_mouse_hid_keyboard_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_hid_mouse_hid_keyboard/bm/cm33" doc="readme.txt">
      <description>The application is a simple demonstration program based on the MCUXpresso SDK. The application is enumerated as HID-compliant mouse and keyboard devices.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_mouse_hid_keyboard_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_mouse_hid_keyboard_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_mouse_hid_keyboard_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_hid_mouse_hid_keyboard/freertos/cm33" doc="readme.txt">
      <description>The application is a simple demonstration program based on the MCUXpresso SDK. The application is enumerated as HID-compliant mouse and keyboard devices.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_mouse_hid_keyboard_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_mouse_hid_keyboard_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_composite_hid_mouse_hid_keyboard_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_composite_hid_mouse_hid_keyboard_lite/bm/cm33" doc="readme.txt">
      <description>The application is a simple demonstration program based on the MCUXpresso SDK. The application is enumerated as HID-compliant mouse and keyboard devices.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_composite_hid_mouse_hid_keyboard_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_composite_hid_mouse_hid_keyboard_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_dfu_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_dfu/bm/cm33" doc="readme.pdf">
      <description>The DFU project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a compoiste dfu device, users can download one firmware to the dfu device with the tool called "dfu-util". the dfu...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_dfu_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_dfu_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_dfu_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_dfu/freertos/cm33" doc="readme.pdf">
      <description>The DFU project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a compoiste dfu device, users can download one firmware to the dfu device with the tool called "dfu-util". the dfu...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_dfu_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_dfu_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_dfu_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_dfu_lite/bm/cm33" doc="readme.pdf">
      <description>The DFU project is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as a compoiste dfu device, users can download one firmware to the dfu device with the tool called "dfu-util". the dfu...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_dfu_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_dfu_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_generic_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_generic/bm/cm33" doc="readme.txt">
      <description>The USB HID generic application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a HID-compliant device. A PC application can be used to exchange data with the device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_generic_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_hid_generic_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_generic_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_generic/freertos/cm33" doc="readme.txt">
      <description>The USB HID generic application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a HID-compliant device. A PC application can be used to exchange data with the device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_generic_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_hid_generic_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_generic_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_generic_lite/bm/cm33" doc="readme.txt">
      <description>The USB HID generic application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a HID-compliant device. A PC application can be used to exchange data with the device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_generic_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_hid_generic_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_mouse/bm/cm33" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_mouse/freertos/cm33" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_mouse_lite/bm/cm33" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_disk_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_msc_disk/bm/cm33" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk. Users can read and write the SD card or MMC as a standard U-disk.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_disk_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_msc_disk_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_disk_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_msc_disk/freertos/cm33" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk. Users can read and write the SD card or MMC as a standard U-disk.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_disk_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_msc_disk_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_disk_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_msc_disk_lite/bm/cm33" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk. Users can read and write the SD card or MMC as a standard U-disk.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_disk_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_msc_disk_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_ramdisk_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_msc_ramdisk/bm/cm33" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk and can be read and written to as a normal U-disk .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_ramdisk_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_msc_ramdisk_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_ramdisk_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_msc_ramdisk/freertos/cm33" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk and can be read and written to as a normal U-disk .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_ramdisk_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_msc_ramdisk_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_msc_ramdisk_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_msc_ramdisk_lite/bm/cm33" doc="readme.txt">
      <description>The USB MSC RAM disk application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a U-disk and can be read and written to as a normal U-disk .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_msc_ramdisk_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_msc_ramdisk_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_mtp_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_mtp/bm/cm33" doc="readme.txt">
      <description>The USB MTP application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as portable media device. Users can read and write the SD card or MMC by MTP operations.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_mtp_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_mtp_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_mtp_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_mtp/freertos/cm33" doc="readme.txt">
      <description>The USB MTP application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as portable media device. Users can read and write the SD card or MMC by MTP operations.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_mtp_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_mtp_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_mtp_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_mtp_lite/bm/cm33" doc="readme.txt">
      <description>The USB MTP application is a simple demonstration program based on the MCUXpresso SDK. It is enumerated as portable media device. Users can read and write the SD card or MMC by MTP operations.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_mtp_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_mtp_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_phdc_weighscale_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_phdc_weighscale/bm/cm33" doc="readme.txt">
      <description>The USB PHDC WeighScale application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a weight scale device and the HealthLink software is used to interact with thisdevice to simulate...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_phdc_weighscale_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_phdc_weighscale_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_phdc_weighscale_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_phdc_weighscale/freertos/cm33" doc="readme.txt">
      <description>The USB PHDC WeighScale application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a weight scale device and the HealthLink software is used to interact with thisdevice to simulate...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_phdc_weighscale_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_phdc_weighscale_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_phdc_weighscale_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_phdc_weighscale_lite/bm/cm33" doc="readme.txt">
      <description>The USB PHDC WeighScale application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a weight scale device and the HealthLink software is used to interact with thisdevice to simulate...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_phdc_weighscale_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_phdc_weighscale_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_printer_virtual_plain_text_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_printer_virtual_plain_text/bm/cm33" doc="readme.txt">
      <description>The USB virtual printer application is a simple application to demonstrate a virtual printer functionality.Because there is no printer language parsing, the received raw data is output directly in the debug...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_printer_virtual_plain_text_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_printer_virtual_plain_text_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_printer_virtual_plain_text_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_printer_virtual_plain_text/freertos/cm33" doc="readme.txt">
      <description>The USB virtual printer application is a simple application to demonstrate a virtual printer functionality.Because there is no printer language parsing, the received raw data is output directly in the debug...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_printer_virtual_plain_text_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_printer_virtual_plain_text_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_printer_virtual_plain_text_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_printer_virtual_plain_text_lite/bm/cm33" doc="readme.txt">
      <description>The USB virtual printer application is a simple application to demonstrate a virtual printer functionality.Because there is no printer language parsing, the received raw data is output directly in the debug...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_printer_virtual_plain_text_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_printer_virtual_plain_text_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_suspend_resume_hid_mouse_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_suspend_resume_device_hid_mouse/bm/cm33" doc="readme.txt">
      <description>the application is enumerated as a mouse. When host releases the USB Bus, the device will enter into low power mode. The device can be waked up when resume signal is detected on the bus. Or the device can remote...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_suspend_resume_hid_mouse_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_suspend_resume_hid_mouse_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_suspend_resume_hid_mouse_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_suspend_resume_device_hid_mouse/freertos/cm33" doc="readme.txt">
      <description>the application is enumerated as a mouse. When host releases the USB Bus, the device will enter into low power mode. The device can be waked up when resume signal is detected on the bus. Or the device can remote...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_suspend_resume_hid_mouse_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_suspend_resume_hid_mouse_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_suspend_resume_hid_mouse_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_suspend_resume_device_hid_mouse_lite/bm/cm33" doc="readme.txt">
      <description>the application is enumerated as a mouse. When host releases the USB Bus, the device will enter into low power mode. The device can be waked up when resume signal is detected on the bus. Or the device can remote...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_suspend_resume_hid_mouse_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_suspend_resume_hid_mouse_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_video_virtual_camera_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_video_virtual_camera/bm/cm33" doc="readme.txt">
      <description>The USB video virtual camera application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a camera and users can see the video of the device by using a PC test tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_video_virtual_camera_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_video_virtual_camera_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_video_virtual_camera_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_video_virtual_camera/freertos/cm33" doc="readme.txt">
      <description>The USB video virtual camera application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a camera and users can see the video of the device by using a PC test tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_video_virtual_camera_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="dev_video_virtual_camera_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_video_virtual_camera_lite_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_device_video_virtual_camera_lite/bm/cm33" doc="readme.txt">
      <description>The USB video virtual camera application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a camera and users can see the video of the device by using a PC test tool.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_video_virtual_camera_lite_bm_cm33.uvprojx"/>
        <environment name="csolution" load="dev_video_virtual_camera_lite_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_audio_recorder_freertos" folder="boards/evkmimxrt1180/usb_examples/usb_host_audio_recorder/freertos/cm33" doc="readme.txt">
      <description>The Host Audio example supports the audio recorder device. @n The application prints the audio recorder information when the USB recorder device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_audio_recorder_freertos.uvprojx"/>
        <environment name="csolution" load="host_audio_recorder_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_audio_speaker_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_audio_speaker/bm/cm33" doc="readme.txt">
      <description>The Host Audio example supports the audio speaker device. @n The application prints the audio speaker information when the USB speaker device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_audio_speaker_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_audio_speaker_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_audio_speaker_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_audio_speaker/freertos/cm33" doc="readme.txt">
      <description>The Host Audio example supports the audio speaker device. @n The application prints the audio speaker information when the USB speaker device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_audio_speaker_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_audio_speaker_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_cdc_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_cdc/bm/cm33" doc="readme.txt">
      <description>The host CDC project is a simple demonstration program based on the MCUXpresso SDK. It enumerates a COM port and echoes back the data from the UART.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_cdc_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_cdc_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_cdc_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_cdc/freertos/cm33" doc="readme.txt">
      <description>The host CDC project is a simple demonstration program based on the MCUXpresso SDK. It enumerates a COM port and echoes back the data from the UART .</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_cdc_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_cdc_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_generic_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_generic/bm/cm33" doc="readme.txt">
      <description>This application implements a simple HID interrupt in-and-out endpoint bi-directional communication.The application sends one test string to the device. The device receives and sends back the string. The application receives the string and prints it.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_generic_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_hid_generic_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_generic_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_generic/freertos/cm33" doc="readme.txt">
      <description>This application implements a simple HID interrupt in-and-out endpoint bi-directional communication.The application sends one test string to the device. The device receives and sends back the string. The application receives the string and prints it.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_generic_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_hid_generic_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_mouse_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_mouse/bm/cm33" doc="readme.txt">
      <description>The application supports the mouse device. It prints the mouse operation when the mouse device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_mouse_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_hid_mouse_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_mouse_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_mouse/freertos/cm33" doc="readme.txt">
      <description>The application supports the mouse device. It prints the mouse operation when the mouse device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_mouse_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_hid_mouse_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_mouse_keyboard_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_mouse_keyboard/bm/cm33" doc="readme.txt">
      <description>This example supports the mouse device and the keyboard device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_mouse_keyboard_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_hid_mouse_keyboard_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_mouse_keyboard_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_mouse_keyboard/freertos/cm33" doc="readme.txt">
      <description>This example supports the mouse device and the keyboard device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_mouse_keyboard_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_hid_mouse_keyboard_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_msd_command_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_msd_command/bm/cm33" doc="readme.txt">
      <description>This Host MSD example supports the UFI and SCSI U-disk device. The application prints the attached device information when the U-disk device is attached.The application executes UFI commands to test the attached device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_msd_command_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_msd_command_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_msd_command_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_msd_command/freertos/cm33" doc="readme.txt">
      <description>This Host MSD example supports the UFI and SCSI U-disk device.  The application prints the attached device information when the U-disk device is attached.The application executes UFI commands to test the attached device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_msd_command_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_msd_command_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_msd_fatfs_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_msd_fatfs/bm/cm33" doc="readme.txt">
      <description>This Host FatFs example supports UFI and SCSI U-disk device. The application prints the attached device information when U-disk device is attached.The application executes some FatFs APIs to test the attached device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_msd_fatfs_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_msd_fatfs_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_msd_fatfs_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_msd_fatfs/freertos/cm33" doc="readme.txt">
      <description>This Host FatFs example supports UFI and SCSI U-disk device. The application prints the attached device information when U-disk device is attached.The application executes some FatFs APIs to test the attached device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_msd_fatfs_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_msd_fatfs_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_phdc_manager_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_phdc_manager/bm/cm33" doc="readme.txt">
      <description>The Host PHDC Manager Example is a simple demonstration program based on the MCUXpresso SDK.The application supports the USB weight scale device. It prints out the body mass and body mass index information when the USB weight scale device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_phdc_manager_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_phdc_manager_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_phdc_manager_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_phdc_manager/freertos/cm33" doc="readme.txt">
      <description>The Host PHDC Manager Example is a simple demonstration program based on the MCUXpresso SDK.The application supports the USB weight scale device. It prints out the body mass and body mass index information when the USB weight scale device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_phdc_manager_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_phdc_manager_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_printer_plain_text_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_printer_plain_text/bm/cm33" doc="readme.txt">
      <description>The host printer example demonstrates how to get the status of the printer deviceand how to print a certain test string.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_printer_plain_text_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_printer_plain_text_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_printer_plain_text_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_printer_plain_text/freertos/cm33" doc="readme.txt">
      <description>The host printer example demonstrates how to get the status of the printer deviceand how to print a certain test string..</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_printer_plain_text_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_printer_plain_text_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_suspend_resume_hid_mouse_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_suspend_resume_host_hid_mouse/bm/cm33" doc="readme.txt">
      <description>This is one example support suspend/resume.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_suspend_resume_hid_mouse_bm_cm33.uvprojx"/>
        <environment name="csolution" load="host_suspend_resume_hid_mouse_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_suspend_resume_hid_mouse_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_suspend_resume_host_hid_mouse/freertos/cm33" doc="readme.txt">
      <description>This is one example support suspend/resume.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_suspend_resume_hid_mouse_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_suspend_resume_hid_mouse_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_video_camera_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_host_video_camera/freertos/cm33" doc="readme.txt">
      <description>The application supports to get JPEG image from camera function.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_video_camera_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="host_video_camera_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="keyboard2mouse_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_keyboard2mouse/bm/cm33" doc="readme.txt">
      <description>This example implements the host and the device, where the one controller works as a host and the other controller works as a device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/keyboard2mouse_bm_cm33.uvprojx"/>
        <environment name="csolution" load="keyboard2mouse_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="keyboard2mouse_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_keyboard2mouse/freertos/cm33" doc="readme.txt">
      <description>This example implements the host and the device, where the one controller works as a host and the other controller works as a device.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/keyboard2mouse_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="keyboard2mouse_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pin_detect_hid_mouse_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_pin_detect_hid_mouse/bm/cm33" doc="readme.pdf">
      <description>This pin detect HID example supports the mouse device. The application prints the operation information when the mouse device is attached or plugged in to the PC host.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pin_detect_hid_mouse_bm_cm33.uvprojx"/>
        <environment name="csolution" load="pin_detect_hid_mouse_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pin_detect_hid_mouse_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_pin_detect_hid_mouse/freertos/cm33" doc="readme.pdf">
      <description>This pin detect HID example supports the mouse device. The application prints the operation information when the mouse device is attached or plugged in to the PC host.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pin_detect_hid_mouse_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="pin_detect_hid_mouse_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pin_detect_hid_msd_bm_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_pin_detect_hid_msd/bm/cm33" doc="readme.pdf">
      <description>This pin detect HID MSD example can become a HID mouse device or a MSD host that supports U-disk. The application prints the operation information when the U-disk is attached or is plugged into the PC host.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pin_detect_hid_msd_bm_cm33.uvprojx"/>
        <environment name="csolution" load="pin_detect_hid_msd_bm_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="pin_detect_hid_msd_freertos_cm33" folder="boards/evkmimxrt1180/usb_examples/usb_pin_detect_hid_msd/freertos/cm33" doc="readme.pdf">
      <description>This pin detect HID MSD example can become a HID mouse device or a MSD host that supports U-disk. The application prints the operation information when the U-disk is attached or is plugged into the PC host.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pin_detect_hid_msd_freertos_cm33.uvprojx"/>
        <environment name="csolution" load="pin_detect_hid_msd_freertos_cm33.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_bm_cm7" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_mouse/bm/cm7" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_bm_cm7.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_freertos_cm7" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_mouse/freertos/cm7" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="dev_hid_mouse_lite_bm_cm7" folder="boards/evkmimxrt1180/usb_examples/usb_device_hid_mouse_lite/bm/cm7" doc="readme.txt">
      <description>The USB HID mouse application is a simple demonstration program based on the MCUXpresso SDK.It is enumerated as a mouse. Users can see the mouse arrow moving on the PC screen according in a rectangular fashion.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dev_hid_mouse_lite_bm_cm7.uvprojx"/>
        <environment name="csolution" load="dev_hid_mouse_lite_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_mouse_bm_cm7" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_mouse/bm/cm7" doc="readme.txt">
      <description>The application supports the mouse device. It prints the mouse operation when the mouse device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_mouse_bm_cm7.uvprojx"/>
        <environment name="csolution" load="host_hid_mouse_bm_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="host_hid_mouse_freertos_cm7" folder="boards/evkmimxrt1180/usb_examples/usb_host_hid_mouse/freertos/cm7" doc="readme.txt">
      <description>The application supports the mouse device. It prints the mouse operation when the mouse device is attached.</description>
      <board name="MIMXRT1180-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/host_hid_mouse_freertos_cm7.uvprojx"/>
        <environment name="csolution" load="host_hid_mouse_freertos_cm7.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
