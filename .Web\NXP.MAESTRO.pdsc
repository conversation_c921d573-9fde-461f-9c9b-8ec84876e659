<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MAESTRO</name>
  <vendor>NXP</vendor>
  <description>Software Pack for maestro_framework</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SWP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Software Pack</keyword>
  </keywords>
  <requirements>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <conditions>
    <condition id="middleware.maestro_framework.condition_id">
      <require condition="allOf.middleware.maestro_framework.doc, component.osa_free_rtos, middleware.fatfs, middleware.maestro_framework.template.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.maestro_framework.doc, component.osa_free_rtos, middleware.fatfs, middleware.maestro_framework.template.internal_condition">
      <require Cclass="Audio" Cgroup="Framework" Csub="doc"/>
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_free_rtos"/>
      <require Cclass="File System" Cgroup="FAT File System" Csub="fatfs"/>
      <require Cclass="Audio" Cgroup="Framework" Csub="template"/>
    </condition>
    <condition id="middleware.maestro_framework.codecs.condition_id">
      <require condition="allOf.middleware.maestro_framework.internal_condition"/>
    </condition>
    <condition id="allOf.middleware.maestro_framework.internal_condition">
      <require Cclass="Audio" Cgroup="Framework" Csub="maestro_framework"/>
    </condition>
    <condition id="allOf.cores=cm7f, toolchains=armgcc.condition_id">
      <require condition="cores.cm7f.internal_condition"/>
      <require condition="toolchains.armgcc.internal_condition"/>
    </condition>
    <condition id="cores.cm7f.internal_condition">
      <accept Dcore="Cortex-M7"/>
    </condition>
    <condition id="toolchains.armgcc.internal_condition">
      <accept Tcompiler="GCC"/>
    </condition>
    <condition id="allOf.cores=cm33, toolchains=armgcc.condition_id">
      <require condition="cores.cm33.internal_condition"/>
      <require condition="toolchains.armgcc.internal_condition"/>
    </condition>
    <condition id="cores.cm33.internal_condition">
      <accept Dcore="Cortex-M33"/>
    </condition>
    <condition id="allOf.cores=cm7f, toolchains=iar.condition_id">
      <require condition="cores.cm7f.internal_condition"/>
      <require condition="toolchains.iar.internal_condition"/>
    </condition>
    <condition id="toolchains.iar.internal_condition">
      <accept Tcompiler="IAR"/>
    </condition>
    <condition id="allOf.cores=cm33, toolchains=iar.condition_id">
      <require condition="cores.cm33.internal_condition"/>
      <require condition="toolchains.iar.internal_condition"/>
    </condition>
    <condition id="middleware.maestro_framework.ogg.condition_id">
      <require condition="allOf.component.osa_free_rtos, middleware.maestro_framework.opus.internal_condition"/>
    </condition>
    <condition id="allOf.component.osa_free_rtos, middleware.maestro_framework.opus.internal_condition">
      <require Cclass="NXP Component" Cgroup="OS Abstraction Layer" Csub="osa_free_rtos"/>
      <require Cclass="Audio" Cgroup="Framework" Csub="opus"/>
    </condition>
    <condition id="middleware.maestro_framework.opus.condition_id">
      <require condition="allOf.middleware.maestro_framework.internal_condition"/>
    </condition>
    <condition id="middleware.maestro_framework.opusfile.condition_id">
      <require condition="allOf.component.osa_free_rtos, middleware.maestro_framework.opus.internal_condition"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Audio" Cgroup="Framework" Csub="maestro_framework" Cversion="1.7.0" condition="middleware.maestro_framework.condition_id">
      <description>MCU Maestro Audio Framework Streamer Core</description>
      <Pre_Include_Global_h>
#ifndef CASCFG_PLATFORM_FREERTOS
#define CASCFG_PLATFORM_FREERTOS 
#endif
#ifndef FSL_OS_SELECTED
#define FSL_OS_SELECTED SDK_OS_FREERTOS
#endif
#ifndef FSL_OSA_TASK_ENABLE
#define FSL_OSA_TASK_ENABLE 1
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/maestro/inc/error.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/file_utils.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/general_utils.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/maestro_logging.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/ringbuffer.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/streamer_api.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/streamer_element_properties.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/inc/streamer_message.h" projectpath="maestro/inc"/>
        <file category="header" name="middleware/maestro/config/audio_cfg.h" projectpath="maestro/config"/>
        <file category="header" name="middleware/maestro/streamer/cci/include/LVC_Types.h" projectpath="maestro/streamer/cci/include"/>
        <file category="header" name="middleware/maestro/streamer/cci/include/cci_metadata.h" projectpath="maestro/streamer/cci/include"/>
        <file category="header" name="middleware/maestro/streamer/cci/include/codec_interface.h" projectpath="maestro/streamer/cci/include"/>
        <file category="header" name="middleware/maestro/streamer/cci/include/codec_interface_public_api.h" projectpath="maestro/streamer/cci/include"/>
        <file category="header" name="middleware/maestro/streamer/cci/include/errordefs.h" projectpath="maestro/streamer/cci/include"/>
        <file category="header" name="middleware/maestro/streamer/cci/include/ssrc_head.h" projectpath="maestro/streamer/cci/include"/>
        <file category="header" name="middleware/maestro/streamer/core/pad.h" projectpath="maestro/streamer/core"/>
        <file category="header" name="middleware/maestro/streamer/core/pipeline.h" projectpath="maestro/streamer/core"/>
        <file category="header" name="middleware/maestro/streamer/core/streamer.h" projectpath="maestro/streamer/core"/>
        <file category="header" name="middleware/maestro/streamer/core/streamer_element.h" projectpath="maestro/streamer/core"/>
        <file category="header" name="middleware/maestro/streamer/decoders/cci_dec/ccidec.h" projectpath="maestro/streamer/decoders/cci_dec"/>
        <file category="header" name="middleware/maestro/streamer/devices/audio_sink_pcmrtos.h" projectpath="maestro/streamer/devices"/>
        <file category="header" name="middleware/maestro/streamer/devices/audio_src_pcmrtos.h" projectpath="maestro/streamer/devices"/>
        <file category="header" name="middleware/maestro/streamer/elements/audio_proc.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/audio_sink.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/audio_src.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/decoder.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/decoder_pads.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/encoder.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/file_sink.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/file_src_freertos.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/mem_sink.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/mem_src.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/netbuf_src.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/elements/vit_sink.h" projectpath="maestro/streamer/elements"/>
        <file category="header" name="middleware/maestro/streamer/encoders/cei/cei.h" projectpath="maestro/streamer/encoders/cei"/>
        <file category="header" name="middleware/maestro/streamer/encoders/cei/cei_enctypes.h" projectpath="maestro/streamer/encoders/cei"/>
        <file category="header" name="middleware/maestro/streamer/encoders/cei/cei_table.h" projectpath="maestro/streamer/encoders/cei"/>
        <file category="header" name="middleware/maestro/streamer/encoders/opus/opusenc_cei.h" projectpath="maestro/streamer/encoders/opus"/>
        <file category="sourceC" name="middleware/maestro/streamer/core/pad.c" projectpath="maestro/streamer/core"/>
        <file category="sourceC" name="middleware/maestro/streamer/core/pipeline.c" projectpath="maestro/streamer/core"/>
        <file category="sourceC" name="middleware/maestro/streamer/core/streamer.c" projectpath="maestro/streamer/core"/>
        <file category="sourceC" name="middleware/maestro/streamer/core/streamer_element.c" projectpath="maestro/streamer/core"/>
        <file category="sourceC" name="middleware/maestro/streamer/core/streamer_msg.c" projectpath="maestro/streamer/core"/>
        <file category="sourceC" name="middleware/maestro/streamer/decoders/cci_dec/ccidec.c" projectpath="maestro/streamer/decoders/cci_dec"/>
        <file category="sourceC" name="middleware/maestro/streamer/decoders/cci_dec/codec_interface.c" projectpath="maestro/streamer/decoders/cci_dec"/>
        <file category="sourceC" name="middleware/maestro/streamer/decoders/cci_dec/codecextractmetadata.c" projectpath="maestro/streamer/decoders/cci_dec"/>
        <file category="sourceC" name="middleware/maestro/streamer/devices/audio_sink_pcmrtos.c" projectpath="maestro/streamer/devices"/>
        <file category="sourceC" name="middleware/maestro/streamer/devices/audio_src_pcmrtos.c" projectpath="maestro/streamer/devices"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/audio_proc.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/audio_sink.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/audio_src.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/decoder.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/decoder_pads.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/file_sink.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/file_src_freertos.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/mem_sink.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/vit_sink.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/mem_src.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/netbuf_src.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/parsers/cci/cci_codec_type_conversion.c" projectpath="maestro/streamer/parsers/cci"/>
        <file category="sourceC" name="middleware/maestro/streamer/elements/encoder.c" projectpath="maestro/streamer/elements"/>
        <file category="sourceC" name="middleware/maestro/streamer/encoders/opus/opusenc_cei.c" projectpath="maestro/streamer/encoders/opus"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/mp3/mp3_extractmetadata.c" projectpath="maestro/streamer/cci/metadata/src/mp3"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/mp3/mp3_id3v2.c" projectpath="maestro/streamer/cci/metadata/src/mp3"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/mp3/mp3_vbr.c" projectpath="maestro/streamer/cci/metadata/src/mp3"/>
        <file category="header" name="middleware/maestro/streamer/cci/metadata/src/mp3/mp3_id3v2.h" projectpath="maestro/streamer/cci/metadata/src/mp3"/>
        <file category="header" name="middleware/maestro/streamer/cci/metadata/src/mp3/mp3_id3v2_internal.h" projectpath="maestro/streamer/cci/metadata/src/mp3"/>
        <file category="header" name="middleware/maestro/streamer/cci/metadata/src/mp3/mp3_metadata.h" projectpath="maestro/streamer/cci/metadata/src/mp3"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/adpcm/adpcm_extractmetadata.c" projectpath="maestro/streamer/cci/metadata/src/adpcm"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/adpcm/wav_common.c" projectpath="maestro/streamer/cci/metadata/src/adpcm"/>
        <file category="header" name="middleware/maestro/streamer/cci/metadata/src/adpcm/wav_metadata.h" projectpath="maestro/streamer/cci/metadata/src/adpcm"/>
        <file category="header" name="middleware/maestro/streamer/cci/metadata/src/adpcm/wavedata.h" projectpath="maestro/streamer/cci/metadata/src/adpcm"/>
        <file category="header" name="middleware/maestro/streamer/cci/metadata/src/adpcm/wavedefs.h" projectpath="maestro/streamer/cci/metadata/src/adpcm"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/opus/opus_extractmetadata.c" projectpath="maestro/streamer/cci/metadata/src/opus"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/aac/aac_extractmetadata.c" projectpath="maestro/streamer/cci/metadata/src/aac"/>
        <file category="sourceC" name="middleware/maestro/streamer/cci/metadata/src/flac/flac_extractmetadata.c" projectpath="maestro/streamer/cci/metadata/src/flac"/>
        <file category="sourceC" name="middleware/maestro/streamer/utils/file_utils.c" projectpath="maestro/streamer/utils"/>
        <file category="sourceC" name="middleware/maestro/streamer/utils/general_utils.c" projectpath="maestro/streamer/utils"/>
        <file category="sourceC" name="middleware/maestro/streamer/utils/maestro_logging.c" projectpath="maestro/streamer/utils"/>
        <file category="sourceC" name="middleware/maestro/streamer/utils/ringbuffer.c" projectpath="maestro/streamer/utils"/>
        <file category="include" name="middleware/maestro/config/"/>
        <file category="include" name="middleware/maestro/inc/"/>
        <file category="include" name="middleware/maestro/streamer/cci/include/"/>
        <file category="include" name="middleware/maestro/streamer/core/"/>
        <file category="include" name="middleware/maestro/streamer/decoders/cci_dec/"/>
        <file category="include" name="middleware/maestro/streamer/devices/"/>
        <file category="include" name="middleware/maestro/streamer/elements/"/>
        <file category="include" name="middleware/maestro/streamer/encoders/opus/"/>
        <file category="include" name="middleware/maestro/streamer/encoders/cei/"/>
      </files>
    </component>
    <component Cclass="Audio" Cgroup="Framework" Csub="codecs" Cversion="1.7.0" condition="middleware.maestro_framework.codecs.condition_id">
      <description>MCU Maestro Audio Framework Codecs</description>
      <files>
        <file condition="allOf.cores=cm7f, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm7f/armgcc/release/libmp3.a" projectpath="maestro/libs/cm7f/armgcc/release"/>
        <file condition="allOf.cores=cm7f, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm7f/armgcc/release/libwav.a" projectpath="maestro/libs/cm7f/armgcc/release"/>
        <file condition="allOf.cores=cm7f, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm7f/armgcc/release/libaac.a" projectpath="maestro/libs/cm7f/armgcc/release"/>
        <file condition="allOf.cores=cm7f, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm7f/armgcc/release/libflac.a" projectpath="maestro/libs/cm7f/armgcc/release"/>
        <file condition="allOf.cores=cm7f, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm7f/armgcc/release/libssrc.a" projectpath="maestro/libs/cm7f/armgcc/release"/>
        <file condition="allOf.cores=cm33, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm33f/armgcc/release/libmp3.a" projectpath="maestro/libs/cm33f/armgcc/release"/>
        <file condition="allOf.cores=cm33, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm33f/armgcc/release/libwav.a" projectpath="maestro/libs/cm33f/armgcc/release"/>
        <file condition="allOf.cores=cm33, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm33f/armgcc/release/libaac.a" projectpath="maestro/libs/cm33f/armgcc/release"/>
        <file condition="allOf.cores=cm33, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm33f/armgcc/release/libflac.a" projectpath="maestro/libs/cm33f/armgcc/release"/>
        <file condition="allOf.cores=cm33, toolchains=armgcc.condition_id" category="library" name="middleware/maestro/libs/cm33f/armgcc/release/libssrc.a" projectpath="maestro/libs/cm33f/armgcc/release"/>
        <file condition="allOf.cores=cm7f, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm7f/iar/release/libmp3.a" projectpath="maestro/libs/cm7f/iar/release"/>
        <file condition="allOf.cores=cm7f, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm7f/iar/release/libwav.a" projectpath="maestro/libs/cm7f/iar/release"/>
        <file condition="allOf.cores=cm7f, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm7f/iar/release/libflac.a" projectpath="maestro/libs/cm7f/iar/release"/>
        <file condition="allOf.cores=cm7f, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm7f/iar/release/libssrc.a" projectpath="maestro/libs/cm7f/iar/release"/>
        <file condition="allOf.cores=cm33, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm33f/iar/release/libmp3.a" projectpath="maestro/libs/cm33f/iar/release"/>
        <file condition="allOf.cores=cm33, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm33f/iar/release/libwav.a" projectpath="maestro/libs/cm33f/iar/release"/>
        <file condition="allOf.cores=cm33, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm33f/iar/release/libflac.a" projectpath="maestro/libs/cm33f/iar/release"/>
        <file condition="allOf.cores=cm33, toolchains=iar.condition_id" category="library" name="middleware/maestro/libs/cm33f/iar/release/libssrc.a" projectpath="maestro/libs/cm33f/iar/release"/>
      </files>
    </component>
    <component Cclass="Audio" Cgroup="Framework" Csub="doc" Cversion="1.7.0">
      <description>MCU Maestro Audio Framework Doc</description>
      <files>
        <file category="doc" name="middleware/maestro/ChangeLogKSDK.txt" projectpath="maestro"/>
        <file category="doc" name="middleware/maestro/docs/index.html" projectpath="maestro/docs"/>
        <file category="doc" name="middleware/maestro/docs/src/LVC__Types_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/LVC__Types_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/aacdec_start.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/annotated.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/annotated_dup.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__proc_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__proc_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__proc_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__proc_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink__pcmrtos_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink__pcmrtos_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink__pcmrtos_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__sink__pcmrtos_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src__pcmrtos_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src__pcmrtos_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src__pcmrtos_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/audio__src__pcmrtos_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/bc_s.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/bc_sd.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cci__codec__type__conversion_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cci__codec__type__conversion_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cci__metadata_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cci__metadata_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ccidec_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ccidec_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ccidec_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ccidec_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cei_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cei_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cei__enctypes_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cei__enctypes_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cei__table_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cei__table_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/classes.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/clipboard.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/closed.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec__interface_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec__interface_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec__interface_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec__interface_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec__interface__public__api_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec__interface__public__api_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codec_flow.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codecextractmetadata_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/codecextractmetadata_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/communication.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/cookie.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/customdoxygen.css" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder__pads_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder__pads_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder__pads_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/decoder__pads_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_09c368f94a4ceb2a0ab51c686fa3f34a.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_09c368f94a4ceb2a0ab51c686fa3f34a.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_27572bb7135b2aed50a12b695ac102f7.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_3011f417f47a2f1757a119bb2b6678f7.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_31b2f1d6653e3410a2de348edc054f4b.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_6343cdd224fb18bab339c25e250c0ab5.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_6343cdd224fb18bab339c25e250c0ab5.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_72ccead873c9c44a43b71eb584d84baf.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_7d4bffc30d6bb72c004c672b0b15a03f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_7d4bffc30d6bb72c004c672b0b15a03f.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_7f27e557e2b5ccf3e7af2c250dae6925.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_8da1243e135ad2073cfde7cf1f60b0f9.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_8da1243e135ad2073cfde7cf1f60b0f9.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_a0ca4d96eaffbbc747c3e40a45a7dda3.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_a0ca4d96eaffbbc747c3e40a45a7dda3.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_ac63662da0d688fa8bd4f66397a3bf04.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_ac63662da0d688fa8bd4f66397a3bf04.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_b80bfe80509f7be30c69d6b8307c8ce5.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_b80bfe80509f7be30c69d6b8307c8ce5.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_bb14acadcc9c6e0b0efab3b339a130cb.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_bb14acadcc9c6e0b0efab3b339a130cb.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_be7de2811e7b2903a1c63c530e2329f8.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_be7de2811e7b2903a1c63c530e2329f8.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_bf14883e86cd277860239a84004d696e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_bf14883e86cd277860239a84004d696e.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_bfccd401955b95cf8c75461437045ac0.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_bfccd401955b95cf8c75461437045ac0.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_c10f813485221bef8c5a72414f9e2bee.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_cdad1f4fa813539d4a2cca747b2f86bd.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_cdad1f4fa813539d4a2cca747b2f86bd.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_e4fd4aec6719dce5076d59c812fc0a62.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_e4fd4aec6719dce5076d59c812fc0a62.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_f5879de724d6c7af5db22be3d4d2d409.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dir_f5879de724d6c7af5db22be3d4d2d409.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/doc.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/docd.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/doxygen.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/doxygen_crawl.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/dynsections.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/elements.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/error_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/error_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/errordefs_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/errordefs_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__sink_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__sink_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__sink_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__sink_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__src__freertos_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__src__freertos_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__src__freertos_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__src__freertos_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__utils_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__utils_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__utils_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/file__utils_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/files.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/files_dup.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/flacdec_start.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/folderclosed.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/folderclosedd.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/folderopen.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/folderopend.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/fs_logo.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_b.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_d.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_dup.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_func.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_g.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_i.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_k.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_l.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_m.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_n.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_o.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_p.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_q.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_r.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_s.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_u.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_v.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_b.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_d.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_g.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_i.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_k.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_l.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_m.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_n.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_o.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_p.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_q.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_r.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_s.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_u.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_v.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_w.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_vars_y.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_w.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/functions_y.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/general__utils_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/general__utils_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/general__utils_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/general__utils_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_a.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_b.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_d.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_a.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_b.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_d.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_g.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_i.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_l.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_m.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_n.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_p.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_q.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_r.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_s.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_v.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_w.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_x.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_defs_y.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_dup.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_enum.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_b.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_d.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_i.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_l.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_m.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_p.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_s.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_eval_v.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_a.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_d.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_e.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_f.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_g.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_i.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_l.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_m.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_n.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_o.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_p.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_q.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_r.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_s.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_u.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_func_v.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_g.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_i.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_l.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_m.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_n.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_o.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_p.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_q.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_r.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_s.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_type.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_u.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_v.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_vars.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_w.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_x.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/globals_y.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/group__asi__dffi.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/group__asi__dffi.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/index.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/index.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/jquery.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/maestro.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/maestroApp.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/maestro__logging_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/maestro__logging_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_CeiEncoder.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_ProgrammersGuide.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_maestro__playback.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_maestro__record.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_maestro__sync.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_maestro__usb__mic.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_maestro__usb__speaker.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_processing__time.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_supported__features.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/md_wavdec.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__sink_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__sink_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__sink_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__sink_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__src_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__src_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__src_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mem__src_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/menu.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/menudata.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mic_speaker_1ch_elements_without_fileread.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mic_speaker_2ch_elements_without_fileread.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mic_vit_elements.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mic_vs_vit_elements.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/minus.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/minusd.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mp3_44kHz_with_fileread.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mp3_44kHz_without_fileread.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mp3_48kHz_with_fileread.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mp3_48kHz_without_fileread.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/mp3dec_start.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/nav_f.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/nav_fd.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/nav_g.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/nav_h.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/nav_hd.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtree.css" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtree.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreedata.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex0.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex1.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex2.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex3.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex4.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex5.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex6.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex7.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/navtreeindex8.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/netbuf__src_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/netbuf__src_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/netbuf__src_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/netbuf__src_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/new_stylesheet.css" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/open.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pad_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pad_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pad_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pad_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pages.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pipeline_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pipeline_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pipeline_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/pipeline_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/playback.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/playback_streamer2.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/plus.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/plusd.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/record.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/record_streamer2.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/resize.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ringbuffer_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ringbuffer_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ringbuffer_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/ringbuffer_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/splitbar.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/splitbard.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__api_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__api_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__element_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__element_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__element_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__element_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__element__properties_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__element__properties_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__message_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__message_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__msg_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/streamer__msg_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAUDSRC__SET__NAME__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAUDSRC__SET__NAME__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAlbumArt.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAlbumArt.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioPacketHeader.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioPacketHeader.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioRefData__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioRefData__t.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioSinkPadDeviceIOFuncType.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioSinkPadDeviceIOFuncType.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structAudioSrcBufferQueue__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structBufferInfo.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structBufferInfo.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCCIDecInfo.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCCIDecInfo.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCCI__Ctx.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCCI__Ctx.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCeiBitstreamInfo.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCeiBitstreamInfo.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCeiEncoderFunctions.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCeiEncoderFunctions.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCeiOpusConfig.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structCeiOpusConfig.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structDecoderHandlersType.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structDecoderHandlersType.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structELEMENT__PROPERTY__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structELEMENT__PROPERTY__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structEXT__AUDIOELEMENT__DESC__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structEXT__AUDIOELEMENT__DESC__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structEXT__PROCESS__DESC__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structEXT__PROCESS__DESC__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structEXT__PROCESS__REFDAT__DESC__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structEXT__PROCESS__REFDAT__DESC__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementEncoder.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementEncoder.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementInit.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementInit.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementNetbufSrc.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementNetbufSrc.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementPropertyLookup.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementPropertyLookup.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementTypeLookup.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structElementTypeLookup.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structFileDump.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structFileDump.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structFile__Fs__Rec.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structFile__Fs__Rec.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__BPMModuleStats__st.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__BPMModuleStats__st.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__ContextTable__st.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__ContextTable__st.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__MemoryRegion__st.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__MemoryRegion__st.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__MemoryTable__st.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structLVM__MemoryTable__st.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structMEMSRC__SET__BUFFER__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structMEMSRC__SET__BUFFER__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPCMRtosDeviceInfo.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPCMRtosDeviceInfo.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPCMSinkDeviceInfo.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPCMSinkDeviceInfo.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPacketHeader.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPacketHeader.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structPipelineElements.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structQUEUE__SET__BUFFER__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structQUEUE__SET__BUFFER__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structRawPacketHeader.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structRawPacketHeader.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSET__BUFFER__DESC__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSET__BUFFER__DESC__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSSRC__Instance__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSSRC__Params__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__CREATE__PARAM.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__CREATE__PARAM.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__MSG__HANDLER__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__MSG__HANDLER__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__MSG__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__MSG__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTREAMER__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTRING__DESC__T.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structSTRING__DESC__T.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamBuffer.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamBuffer.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamEvent.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamEvent.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamImageType.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamImageType.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamMessage.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamMessage.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamQuery.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamQuery.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamStringType.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamStringType.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamerFileSrcConfig.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structStreamerFileSrcConfig.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structTrackInfo.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structTrackInfo.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementAudioProc.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementAudioProc.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementAudioSink.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementAudioSink.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementAudioSrc.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementAudioSrc.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementDecoder.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementDecoder.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementFileSink.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementFileSink.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementFileSrc.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementFileSrc.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementMemSink.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementMemSink.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementMemSrc.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementMemSrc.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementVitSink.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__ElementVitSink.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__PadSink.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__PadSink.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__PadSrc.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__PadSrc.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__Pipeline.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__Pipeline.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__StreamElement.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__StreamElement.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__StreamPad.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/struct__StreamPad.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structcodec__interface__function__table__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structcodec__interface__function__table__t.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structdecoder__callback__FunctionTable__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structdecoder__callback__FunctionTable__t.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structext__proc__args.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structext__proc__args.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structfile__meta__data__t.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structfile__meta__data__t.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structringbuf.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/structringbuf.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/sync_off.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/sync_on.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_a.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_ad.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_b.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_bd.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_h.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_hd.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_s.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tab_sd.png" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/tabs.css" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/test_48khz_16bit_2ch.wav" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/topics.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/topics.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/unionStreamData.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/unionStreamData.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/vit__sink_8c.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/vit__sink_8c.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/vit__sink_8h.html" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/vit__sink_8h.js" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/wav_44kHz.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/wav_48kHz.svg" projectpath="maestro/docs/src"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_10.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_11.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_12.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_13.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_14.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_15.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_16.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_17.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_18.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_d.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_e.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/all_f.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/classes_d.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/close.svg" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_10.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_11.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_12.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_13.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_14.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_d.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_e.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/defines_f.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enums_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/enumvalues_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/files_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_10.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_11.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_d.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_e.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/functions_f.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/groups_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/mag.svg" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/mag_d.svg" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/mag_sel.svg" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/mag_seld.svg" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/pages_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/search.css" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/search.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/searchdata.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/typedefs_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_0.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_1.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_10.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_11.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_12.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_13.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_14.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_15.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_16.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_2.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_3.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_4.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_5.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_6.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_7.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_8.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_9.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_a.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_b.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_c.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_d.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_e.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/docs/src/search/variables_f.js" projectpath="maestro/docs/src/search"/>
        <file category="doc" name="middleware/maestro/mcu-audio/ssrc/doc/Synchronous_Sample_Rate_Converter_User_Guide.pdf" projectpath="maestro/mcu-audio/ssrc/doc"/>
        <file category="doc" name="middleware/maestro/mcu-audio/asrc/doc/UG10087_Asynchronous Sample Rate Converter_UG.pdf" projectpath="maestro/mcu-audio/asrc/doc"/>
      </files>
    </component>
    <component Cclass="Audio" Cgroup="Framework" Csub="ogg" Cversion="1.0.0" condition="middleware.maestro_framework.ogg.condition_id">
      <description>MCU Maestro Audio Framework Ogg</description>
      <Pre_Include_Global_h>
#ifndef HAVE_CONFIG_H
#define HAVE_CONFIG_H 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="header" name="middleware/maestro/mcu-audio/ogg/include/ogg/memory.h" projectpath="maestro/ogg/include/ogg" path="middleware/maestro/mcu-audio/ogg/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/ogg/include/ogg/ogg.h" projectpath="maestro/ogg/include/ogg" path="middleware/maestro/mcu-audio/ogg/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/ogg/include/ogg/os_types.h" projectpath="maestro/ogg/include/ogg" path="middleware/maestro/mcu-audio/ogg/include"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/ogg/src/framing.c" projectpath="maestro/ogg/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/ogg/src/memory.c" projectpath="maestro/ogg/src"/>
        <file category="header" name="middleware/maestro/mcu-audio/ogg/src/crctable.h" projectpath="maestro/ogg/src"/>
        <file category="include" name="middleware/maestro/mcu-audio/ogg/include/"/>
        <file category="include" name="middleware/maestro/mcu-audio/ogg/src/"/>
      </files>
    </component>
    <component Cclass="Audio" Cgroup="Framework" Csub="opus" Cversion="1.0.0" condition="middleware.maestro_framework.opus.condition_id">
      <description>MCU Maestro Audio Framework Opus</description>
      <Pre_Include_Global_h>
#ifndef HAVE_CONFIG_H
#define HAVE_CONFIG_H 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/bands.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/celt.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/celt_decoder.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/celt_encoder.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/celt_lpc.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/cwrs.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/entcode.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/entdec.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/entenc.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/kiss_fft.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/laplace.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/mathops.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/mdct.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/modes.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/pitch.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/quant_bands.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/rate.c" projectpath="maestro/opus/celt"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/celt/vq.c" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/_kiss_fft_guts.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/arch.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/bands.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/celt.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/celt_lpc.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/cpu_support.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/cwrs.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/ecintrin.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/entcode.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/entdec.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/entenc.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/fixed_c5x.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/fixed_c6x.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/fixed_debug.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/fixed_generic.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/float_cast.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/kiss_fft.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/laplace.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/mathops.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/mdct.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/mfrngcod.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/modes.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/os_support.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/pitch.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/quant_bands.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/rate.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/stack_alloc.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/static_modes_fixed.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/static_modes_float.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/celt/vq.h" projectpath="maestro/opus/celt"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/config.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_cci.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_cci_raw.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_custom.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_defines.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_multistream.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_projection.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/opus_types.h" projectpath="maestro/opus/include"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/include/test_opus_common.h" projectpath="maestro/opus/include"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/A2NLSF.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/CNG.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/HP_variable_cutoff.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/LPC_analysis_filter.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/LPC_fit.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/LPC_inv_pred_gain.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/LP_variable_cutoff.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF2A.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_VQ.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_VQ_weights_laroia.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_decode.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_del_dec_quant.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_encode.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_stabilize.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NLSF_unpack.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NSQ.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/NSQ_del_dec.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/PLC.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/VAD.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/VQ_WMat_EC.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/ana_filt_bank_1.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/biquad_alt.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/bwexpander.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/bwexpander_32.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/check_control_input.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/code_signs.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/control_SNR.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/control_audio_bandwidth.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/control_codec.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/debug.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/dec_API.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decode_core.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decode_frame.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decode_indices.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decode_parameters.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decode_pitch.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decode_pulses.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/decoder_set_fs.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/enc_API.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/encode_indices.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/encode_pulses.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/gain_quant.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/init_decoder.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/init_encoder.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/inner_prod_aligned.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/interpolate.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/lin2log.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/log2lin.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/pitch_est_tables.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/process_NLSFs.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/quant_LTP_gains.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_down2.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_down2_3.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_private_AR2.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_private_IIR_FIR.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_private_down_FIR.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_private_up2_HQ.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/resampler_rom.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/shell_coder.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/sigm_Q15.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/sort.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/stereo_LR_to_MS.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/stereo_MS_to_LR.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/stereo_decode_pred.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/stereo_encode_pred.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/stereo_find_predictor.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/stereo_quant_pred.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/sum_sqr_shift.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/table_LSF_cos.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_LTP.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_NLSF_CB_NB_MB.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_NLSF_CB_WB.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_gain.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_other.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_pitch_lag.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/tables_pulses_per_block.c" projectpath="maestro/opus/silk"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/LTP_analysis_filter_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/LTP_scale_ctrl_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/apply_sine_window_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/autocorr_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/burg_modified_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/corrMatrix_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/encode_frame_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/find_LPC_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/find_LTP_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/find_pitch_lags_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/find_pred_coefs_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/k2a_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/k2a_Q16_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/noise_shape_analysis_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/pitch_analysis_core_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/process_gains_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/regularize_correlations_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/residual_energy16_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/residual_energy_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/schur64_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/schur_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/vector_ops_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/silk/fixed/warped_autocorrelation_FIX.c" projectpath="maestro/opus/silk/fixed"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/API.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/Inlines.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/MacroCount.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/MacroDebug.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/NSQ.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/PLC.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/SigProc_FIX.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/control.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/debug.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/define.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/errors.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/macros.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/main.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/pitch_est_defines.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/resampler_private.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/resampler_rom.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/resampler_structs.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/structs.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/tables.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/tuning_parameters.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/typedef.h" projectpath="maestro/opus/silk"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/fixed/main_FIX.h" projectpath="maestro/opus/silk/fixed"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/silk/fixed/structs_FIX.h" projectpath="maestro/opus/silk/fixed"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_cci.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_cci_raw.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_decoder.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_encoder.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_multistream.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_multistream_decoder.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/opus_multistream_encoder.c" projectpath="maestro/opus/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opus/src/repacketizer.c" projectpath="maestro/opus/src"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/src/analysis.h" projectpath="maestro/opus/src"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/src/mlp.h" projectpath="maestro/opus/src"/>
        <file category="header" name="middleware/maestro/mcu-audio/opus/src/opus_private.h" projectpath="maestro/opus/src"/>
        <file category="include" name="middleware/maestro/mcu-audio/opus/celt/"/>
        <file category="include" name="middleware/maestro/mcu-audio/opus/include/"/>
        <file category="include" name="middleware/maestro/mcu-audio/opus/silk/"/>
        <file category="include" name="middleware/maestro/mcu-audio/opus/silk/fixed/"/>
        <file category="include" name="middleware/maestro/mcu-audio/opus/src/"/>
      </files>
    </component>
    <component Cclass="Audio" Cgroup="Framework" Csub="opusfile" Cversion="1.0.0" condition="middleware.maestro_framework.opusfile.condition_id">
      <description>MCU Maestro Audio Framework Opusfile</description>
      <Pre_Include_Global_h>
#ifndef HAVE_CONFIG_H
#define HAVE_CONFIG_H 
#endif
</Pre_Include_Global_h>
      <files>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opusfile/src/info.c" projectpath="maestro/opusfile/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opusfile/src/internal.c" projectpath="maestro/opusfile/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opusfile/src/opusfile.c" projectpath="maestro/opusfile/src"/>
        <file category="sourceC" name="middleware/maestro/mcu-audio/opusfile/src/stream.c" projectpath="maestro/opusfile/src"/>
        <file category="header" name="middleware/maestro/mcu-audio/opusfile/src/internal.h" projectpath="maestro/opusfile/src"/>
        <file category="header" name="middleware/maestro/mcu-audio/opusfile/include/opusfile.h" projectpath="maestro/opusfile/include"/>
        <file category="include" name="middleware/maestro/mcu-audio/opusfile/include/"/>
        <file category="include" name="middleware/maestro/mcu-audio/opusfile/src/"/>
      </files>
    </component>
    <component Cclass="Audio" Cgroup="Framework" Csub="template" Cversion="1.0.0">
      <description>maestro_framework template</description>
      <files>
        <file category="sourceC" attr="config" name="middleware/maestro/template/streamer_pcm.c" version="1.0.0" projectpath="maestro/template"/>
        <file category="header" attr="config" name="middleware/maestro/template/streamer_pcm.h" version="1.0.0" projectpath="maestro/template"/>
      </files>
    </component>
  </components>
</package>
