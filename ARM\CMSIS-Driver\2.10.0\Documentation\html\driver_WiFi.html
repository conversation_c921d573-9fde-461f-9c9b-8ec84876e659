<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: WiFi</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('driver_WiFi.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">WiFi </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="driver_wifi_devices"></a>
Driver Implementations</h1>
<p >The <a class="el" href="index.html#driver_pack_content">Pack Content</a> provides implementations of <a href="https://arm-software.github.io/CMSIS_6/latest/Driver/group__wifi__interface__gr.html" target="_blank"><b>CMSIS-WiFi drivers</b></a> for the following devices: </p><table class="cmtable" summary="WiFi Drivers">
<tr>
<th>Driver </th><th>Description  </th></tr>
<tr>
<td><a class="el" href="driver_WiFi.html#driver_DA16200">DA16200</a>  </td><td>WiFi Driver for the Renesas DA16200.   </td></tr>
<tr>
<td><a class="el" href="driver_WiFi.html#driver_ESP32">ESP32</a> </td><td>WiFi Driver for the Espressif ESP32.   </td></tr>
<tr>
<td><a class="el" href="driver_WiFi.html#driver_ESP8266">ESP8266</a> </td><td>WiFi Driver for the Espressif ESP8266.   </td></tr>
<tr>
<td><a class="el" href="driver_WiFi.html#driver_ISM43362">ISM43362</a> </td><td>WiFi Driver for the Inventek ISM43362.   </td></tr>
<tr>
<td><a class="el" href="driver_WiFi.html#driver_WizFi360">WizFi360</a> </td><td>WiFi Driver for the WizNet WizFi360.  </td></tr>
</table>
<h2><a class="anchor" id="driver_DA16200"></a>
DA16200</h2>
<p >The documentation for the Renesas DA16200 can be found here: <a href="https://www.renesas.com/eu/en/products/interface-connectivity/wireless-communications/wi-fi/da16200-ultra-low-power-wi-fi-soc-battery-powered-iot-devices">https://www.renesas.com/eu/en/products/interface-connectivity/wireless-communications/wi-fi/da16200-ultra-low-power-wi-fi-soc-battery-powered-iot-devices</a></p>
<p >DA16200 FreeRTOS SDK Firmware Image v3.2.3.0 (or newer) is required. Firmware image and programming instructions "DA16200 DA16600 FreeRTOS Getting Started Guide" are also available on the above web site.</p>
<h2><a class="anchor" id="driver_ESP32"></a>
ESP32</h2>
<p >The documentation for the Espressif ESP32 can be found here: <a href="https://www.espressif.com/en/products/hardware/esp-wroom-32/overview">https://www.espressif.com/en/products/hardware/esp-wroom-32/overview</a></p>
<h2><a class="anchor" id="driver_ESP8266"></a>
ESP8266</h2>
<p >The documentation for the Espressif ESP8266 can be found here: <a href="https://www.espressif.com/en/products/hardware/esp8266ex/overview/">https://www.espressif.com/en/products/hardware/esp8266ex/overview/</a></p>
<h2><a class="anchor" id="driver_ISM43362"></a>
ISM43362</h2>
<p >The documentation for the Inventek ISM43362 can be found here: <a href="https://www.inventeksys.com/ism4336-m3g-l44-e-embedded-serial-to-wifi-module/">https://www.inventeksys.com/ism4336-m3g-l44-e-embedded-serial-to-wifi-module/</a></p>
<h3><a class="anchor" id="ismart43362_e_firmware_download"></a>
Flashing a different firmware to ISMART43362-E Shield</h3>
<p >The ISMART43362-E Shield has two options to communicate with the underlying target hardware: UART and SPI. By default, the module is flashed with the firmware for UART communication. To enable SPI communication, follow these steps to flash the module:</p>
<p ><b>Prepare</b> <b>the</b> <b>hardware</b> </p><ul>
<li>Set the power supply jumper on ISMART43362-E to connect 5V_USB and 5V_BOARD.</li>
<li>Set the SW3 switch to position 1 (factory default position).</li>
</ul>
<p ><b>Prepare</b> <b>the</b> <b>software</b> </p><ul>
<li>Download <a href="https://www.inventeksys.com/iwin/wp-content/uploads/ISM43362_M3G_L44_SPI_C6.2.1.7.zip">ISM43362_M3G_L44_SPI_C6.2.1.7.zip</a> and extract it locally.</li>
<li>Download <a href="https://www.inventeksys.com/iwin/wp-content/uploads/eS-WIFi_Demo.zip">eS-WIFi_Demo</a></li>
<li>Unzip the file and install the application on your PC.</li>
<li>Start es-WiFi Demo and select <b>Menu - Install Drivers</b> and install both, CYW9WCD1EVAL1 and BCM9WCD1EVAL1: <div class="image">
<img src="ISM43362_install_drivers.png" alt=""/>
</div>
</li>
<li>Connect the ISMART43362-E to the PC using the Mini-USB connector. In Device Manager, you should see WICED USB Serial Port (COMx).</li>
<li>If not already done, assign a low COM port number.</li>
<li>In the es-WiFi Demo application, execute:<ul>
<li>Go to <b>Setup - Serial Port - Configure/Open</b>: <div class="image">
<img src="ISM43362_set_COM_port_conf.png" alt=""/>
</div>
</li>
<li>Check the following settings (Serial Port being the one added just now): <div class="image">
<img src="ISM43362_set_COM_port.png" alt=""/>
</div>
</li>
<li>Go to <b>Menu - Firmware - Update</b>: <div class="image">
<img src="ISM43362_update_firmware.png" alt=""/>
</div>
</li>
<li>Select the binary image file extracted in the first step (ISM43362_M3G_L44_SPI_C6.2.1.7.bin). Make sure it is an SPI firmware variant: <div class="image">
<img src="ISM43362_module_detected.png" alt=""/>
</div>
</li>
</ul>
</li>
<li><p class="startli">After the firmware is updated, the console will show the message "Resetting...": </p><div class="image">
<img src="ISM43362_update_firmware_resetting.png" alt=""/>
</div>
<p> You can now disconnect the module from the PC as it contains the new SPI firmware.</p>
<p class="startli">If you want to flash a different firmware later, please visit <a href="https://www.inventeksys.com/iwin/firmware/">Inventek's Firmware page</a> for the latest binary files.</p>
</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>Firmware version ISM43362_M3G_L44_SPI_C6.2.1.8 is not supported!</dd></dl>
<h2><a class="anchor" id="driver_WizFi360"></a>
WizFi360</h2>
<p >The documentation for the WIZnet WizFi360 can be found here: <a href="https://docs.wiznet.io/Product/Wi-Fi-Module/WizFi360">https://docs.wiznet.io/Product/Wi-Fi-Module/WizFi360</a></p>
<p >The latest firmware images are available on GitHub: <a href="https://github.com/wizfi/Release">https://github.com/wizfi/Release</a>. Instructions on how to flash the firmware onto the device can be found on the WIZnet Documents page: <a href="https://docs.wiznet.io/Product/Wi-Fi-Module/WizFi360/documents#firmware-update-guide">https://docs.wiznet.io/Product/Wi-Fi-Module/WizFi360/documents#firmware-update-guide</a>. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
