<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MCX-N5XX-EVK_MAESTRO_FRAMEWORK_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware maestro_framework Examples Pack for MCX-N5XX-EVK</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MCX-N5XX-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="MCXN547_DFP" vendor="NXP" version="19.0.0"/>
      <package name="SDMMC" vendor="NXP" version="3.0.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
      <package name="MAESTRO" vendor="NXP" version="2.0.0"/>
      <package name="FATFS" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="VIT" vendor="NXP" version="2.0.0"/>
      <package name="USB" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <examples>
    <example name="maestro_playback" folder="boards/mcxn5xxevk/audio_examples/maestro_playback/cm33_core0" doc="readme.md">
      <description>Audio maestro framework demo example.</description>
      <board name="MCX-N5XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="maestro_playback.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="maestro_record" folder="boards/mcxn5xxevk/audio_examples/maestro_record/cm33_core0" doc="readme.md">
      <description>Audio maestro framework demo example.</description>
      <board name="MCX-N5XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="maestro_record.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="maestro_usb_mic" folder="boards/mcxn5xxevk/audio_examples/maestro_usb_mic/cm33_core0" doc="readme.md">
      <description>Audio maestro framework USB microphone example.</description>
      <board name="MCX-N5XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="maestro_usb_mic.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="maestro_usb_speaker" folder="boards/mcxn5xxevk/audio_examples/maestro_usb_speaker/cm33_core0" doc="readme.md">
      <description>Audio maestro framework USB speaker example.</description>
      <board name="MCX-N5XX-EVK" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="csolution" load="maestro_usb_speaker.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
