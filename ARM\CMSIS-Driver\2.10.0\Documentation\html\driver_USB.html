<!-- HTML header for doxygen 1.9.2-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>CMSIS-Driver Implementations: USB</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="footer.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(document).ready(function() { init_search(); });
/* @license-end */
</script>
<link href="extra_stylesheet.css" rel="stylesheet" type="text/css"/>
<link href="version.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../version.js"></script>
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 55px;">
  <td id="projectlogo" style="padding: 1.5em;"><img alt="Logo" src="cmsis_logo_white_small.png"/></td>
  <td style="padding-left: 1em; padding-bottom: 1em;padding-top: 1em;">
   <div id="projectname">CMSIS-Driver Implementations
&#160;<span id="projectnumber"><script type="text/javascript">
     <!--
     writeHeader.call(this);
     writeVersionDropdown(this);
     //-->
    </script>
   </span>
   </div>
   <div id="projectbrief">MCU independent CMSIS-Driver implementations</div>
  </td>
  <!--END !PROJECT_NAME-->
   <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.svg"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('driver_USB.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">USB </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="driver_usb"></a>
Driver Implementations</h1>
<p >The <a class="el" href="index.html#driver_pack_content">Pack Content</a> provides implementations of <a href="https://arm-software.github.io/CMSIS_6/latest/Driver/group__usb__interface__gr.html" target="_blank"><b>CMSIS-USB drivers</b></a> for the following controllers: </p><table class="cmtable" summary="USB Drivers">
<tr>
<th>Driver </th><th>Description  </th></tr>
<tr>
<td><a class="el" href="driver_USB.html#driver_EHCI">EHCI</a>  </td><td>USB Host Driver for the EHCI with Transaction Translator (TT) host controller.   </td></tr>
<tr>
<td><a class="el" href="driver_USB.html#driver_OHCI">OHCI</a> </td><td>USB Host Driver for the OHCI host controller.   </td></tr>
</table>
<h2><a class="anchor" id="driver_EHCI"></a>
EHCI</h2>
<p >Enhanced Host Controller Interface (EHCI) with TT is EHCI controller with integrated Transaction Translator that supports high/full/low-speed devices.</p>
<p >It is usually used in embedded devices to remove the requirement of having additional host controller (OHCI) for handling full/low-speed devices separately.</p>
<p >This driver exports up to 2 driver instances thus it can support 2 EHCI with TT host controllers.</p>
<p >It requires hardware-specific functions implementation that are available in the template module <b>USBH_EHCI_HW.c</b>.</p>
<p >It is configured via define values in the <b>USBH_EHCI_Config.h</b> configuration file.</p>
<p ><b>Configuration</b></p>
<ul>
<li><b>USB Host Controller 0</b>:<ul>
<li><b>Export control block Driver_USBH#</b>: Specifies the exported driver control block number.</li>
<li><b>EHCI Registers base address</b>: Specifies the absolute address at which EHCI controller registers are located.</li>
<li><b>Locate EHCI Communication Area</b>: Specifies if the communication area is located in a specific memory (via the linker script):<ul>
<li><b>Section name</b>: Specifies the section name for the EHCI communication area (for positioning via the linker script).</li>
</ul>
</li>
</ul>
</li>
<li><b>USB Host Controller 1</b> (can be enabled/disabled):<ul>
<li><b>Export control block Driver_USBH#</b>: Specifies the exported driver control block number.</li>
<li><b>EHCI Registers base address</b>: Specifies the absolute address at which EHCI controller registers are located.</li>
<li><b>Locate EHCI Communication Area</b>: Specifies if the communication area is located in a specific memory (via the linker script):<ul>
<li><b>Section name</b>: Specifies the section name for the EHCI communication area (for positioning via the linker script).</li>
</ul>
</li>
</ul>
</li>
<li><b>Maximum number of Pipes (per controller)</b>: Specifies the maximum number of pipes that the driver will support (per controller).</li>
</ul>
<h2><a class="anchor" id="driver_OHCI"></a>
OHCI</h2>
<p >Open Host Controller Interface (OHCI) is a host controller interface that supports full/low-speed devices.</p>
<p >This driver exports up to 2 driver instances thus it can support 2 OHCI host controllers.</p>
<p >It requires hardware-specific functions implementation that are available in the template module <b>USBH_OHCI_HW.c</b>.</p>
<p >It is configured via define values in the <b>USBH_OHCI_Config.h</b> configuration file.</p>
<p ><b>Configuration</b></p>
<ul>
<li><b>USB Host Controller 0</b>:<ul>
<li><b>Export control block Driver_USBH#</b>: Specifies the exported driver control block number.</li>
<li><b>OHCI Registers base address</b>: Specifies the absolute address at which OHCI controller registers are located.</li>
<li><b>Locate OHCI Communication Area (HCCA)</b>: Specifies if the communication area is located in a specific memory (via the linker script):<ul>
<li><b>Section name</b>: Specifies the section name for the OHCI communication area (for positioning via the linker script).</li>
</ul>
</li>
</ul>
</li>
<li><b>USB Host Controller 1</b> (can be enabled/disabled):<ul>
<li><b>Export control block Driver_USBH#</b>: Specifies the exported driver control block number.</li>
<li><b>OHCI Registers base address</b>: Specifies the absolute address at which OHCI controller registers are located.</li>
<li><b>Locate OHCI Communication Area (HCCA)</b>: Specifies if the communication area is located in a specific memory (via the linker script):<ul>
<li><b>Section name</b>: Specifies the section name for the OHCI communication area (for positioning via the linker script).</li>
</ul>
</li>
</ul>
</li>
<li><b>Maximum number of Pipes (per controller)</b>: Specifies the maximum number of pipes that the driver will support (per controller). </li>
</ul>
</div></div><!-- contents -->
</div><!-- PageDoc -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">
      <script type="text/javascript">
        <!--
        writeFooter.call(this);
        //-->
      </script>    
    </li>
  </ul>
</div>
</body>
</html>
