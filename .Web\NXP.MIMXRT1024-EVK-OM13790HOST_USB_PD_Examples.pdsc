<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1024-EVK-OM13790HOST_USB_PD_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware usb_pd Examples Pack for MIMXRT1024-EVK-OM13790HOST</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="2.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
    <release version="1.0.0" date="2024-01-14">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.15.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="MIMXRT1024_DFP" vendor="NXP" version="19.0.0"/>
      <package name="USB_PD" vendor="NXP" version="3.0.0"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="3.0.0"/>
      <package name="MIMXRT1024-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="3.0.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT1024-EVK-OM13790HOST">
      <description>i.MX RT1024 Evaluation Kit with USB Type-C Host Shield Board Gen 2 with Display Port Alt Mode</description>
      <image small="boards/evkmimxrt1024_om13790host/evkmimxrt1024_om13790host.png"/>
      <book category="overview" name="https://www.nxp.com/pip/OM13790" title="i.MX RT1024 Evaluation Kit with USB Type-C Host Shield Board Gen 2 with Display Port Alt Mode" public="true"/>
      <mountedDevice Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device_id.MIMXRT1024xxxxx.internal_condition">
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="device.MIMXRT1024.internal_condition">
      <accept Dname="MIMXRT1024CAG4A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024CAG4B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5A" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1024DAG5B" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.evkmimxrt1024_om13790host.condition_id">
      <require condition="allOf.component.lpuart_adapter, component.serial_manager, component.serial_manager_uart, device_id=MIMXRT1024xxxxx, device.MIMXRT1024_startup, driver.clock, driver.common, driver.igpio, driver.iomuxc, driver.lpuart, driver.xip_board.evkmimxrt1024, driver.xip_device, kit=evkmimxrt1024_om13790host, utility.debug_console.internal_condition"/>
    </condition>
    <condition id="allOf.component.lpuart_adapter, component.serial_manager, component.serial_manager_uart, device_id=MIMXRT1024xxxxx, device.MIMXRT1024_startup, driver.clock, driver.common, driver.igpio, driver.iomuxc, driver.lpuart, driver.xip_board.evkmimxrt1024, driver.xip_device, kit=evkmimxrt1024_om13790host, utility.debug_console.internal_condition">
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require condition="device_id.MIMXRT1024xxxxx.internal_condition"/>
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iomuxc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Board Support" Cgroup="SDK Drivers" Csub="evkmimxrt1024 xip"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xip_device"/>
      <require condition="kit.evkmimxrt1024_om13790host.internal_condition"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="kit.evkmimxrt1024_om13790host.internal_condition">
      <accept condition="device.MIMXRT1024.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="usb_pd_bm" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd/bm" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the board keys and debug console to test the PD functions. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_charger_battery_bm" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd_charger_battery/bm" doc="readme.txt">
      <description>the application simulate battery product , it prints the battery percent continually. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_charger_battery_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_charger_battery_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_charger_battery_freertos" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd_charger_battery/freertos" doc="readme.txt">
      <description>The application simulate battery product, it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_charger_battery_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_charger_battery_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_freertos" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd/freertos" doc="readme.txt">
      <description>This PD example is a simple demonstration based on the MCUXpresso SDK PD stack. The application use the board keys and debug console to test the PD functions. The demo works as DRP. When connect, the board can be source or sink.</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_sink_battery_bm" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd_sink_battery/bm" doc="readme.txt">
      <description>The application simulate battery product , it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_sink_battery_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_sink_battery_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_sink_battery_freertos" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd_sink_battery/freertos" doc="readme.txt">
      <description>The application simulate battery product, it prints the battery percent continually. The demo works as sink and get power from partner port</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_sink_battery_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_sink_battery_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_source_charger_bm" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd_source_charger/bm" doc="readme.txt">
      <description>The application simulate charger product. The demo only works as source and is external powered</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_source_charger_bm.uvprojx"/>
        <environment name="csolution" load="usb_pd_source_charger_bm.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="usb_pd_source_charger_freertos" folder="boards/evkmimxrt1024_om13790host/usb_examples/usb_pd_source_charger/freertos" doc="readme.txt">
      <description>The application simulate charger product. The demo only works as source and is external powered</description>
      <board name="MIMXRT1024-EVK-OM13790HOST" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/usb_pd_source_charger_freertos.uvprojx"/>
        <environment name="csolution" load="usb_pd_source_charger_freertos.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="USB" Cgroup="USB PD Project Template" Csub="evkmimxrt1024_om13790host" Cvariant="evkmimxrt1024_om13790host" Cversion="1.0.0" condition="BOARD_Project_Template.evkmimxrt1024_om13790host.condition_id">
      <description>BOARD_Project_Template evkmimxrt1024_om13790host</description>
      <RTE_Components_h>
#ifndef XIP_EXTERNAL_FLASH
#define XIP_EXTERNAL_FLASH 1
#endif
#ifndef XIP_BOOT_HEADER_ENABLE
#define XIP_BOOT_HEADER_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/board.c" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/board.h" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/clock_config.c" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/clock_config.h" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/dcd.c" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/dcd.h" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/pin_mux.c" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/pin_mux.h" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/peripherals.c" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1024_om13790host/project_template/peripherals.h" version="1.0.0" projectpath="evkmimxrt1024_om13790host/project_template"/>
        <file category="include" name="boards/evkmimxrt1024_om13790host/project_template/"/>
      </files>
    </component>
  </components>
</package>
