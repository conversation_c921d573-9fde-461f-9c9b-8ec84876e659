<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>EZR32HG_DFP</name>
  <description>Silicon Labs EZR32HG EZR Happy Gecko Series Device Support.</description>

  <releases>
    <release version="5.8.10" date="2019-11-12" deprecated="2019-11-12" replacement="SiliconLabs.GeckoPlatform_EZR32HG_DFP">
      Deprecating this pack. Now maintained in SiliconLabs.GeckoPlatform_EZR32HG_DFP.
    </release>
    <release version="5.8.2" date="2019-08-26">
      Added new Zen Gecko family EFR32ZG13P.
      New device in Zen Gecko ZGM13 module family.
      Updates in Blue Gecko BGM21 and Mighty Gecko MGM21 module families.
    </release>
    <release version="5.8.0" date="2019-05-20">
      New FLM flashloader for EFM32GG12 device family.
      Added IAR flashloader support for flashing bootloader pages.
      Removed discontinued device families.
      Added new devices in BGM210, MGM210, EFM32GG12B and EFR32BG12P families.
    </release>
    <release version="5.7.0" date="2018-12-12">
      Added Mighty Gecko MGM1, MGM12, MGM13, MGM21 and ARTIKMG1 modules.
      Added Blue Gecko BGM1, BGM13, BGM21 and ARTIKBG1 modules.
      Added Mighty Gecko EFR32MG13P731 and EFR32MG14P731 parts.
      Added Mighty Gecko EFR32MG21 family.
      Added Blue Gecko EFR32BG21 family.
      Added Zen Gecko EFR32ZG14P family.
      Added Zen Gecko ZGM13 module.
      Added Giant Gecko EFM32GG12B family.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EZR32HG</keyword>
    <keyword>EZR32</keyword>
    <keyword>EZR Happy Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EZR32HG Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M0+" DcoreVersion="r0p1" Dfpu="0" Dmpu="0" Dendian="Little-endian"/>
      <processor Dclock="24000000"/>
      <book name="Documents/cortex_m0p_dgug.pdf"      title="Cortex-M0+ Generic User Guide"/>
      <book name="Documents/EZR32HG-RM.pdf"  title="EZR32HG Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M0+ processor running at up to 25 MHz&#xD;&#xA;- Up to 64 kB Flash and 8 kB RAM memory&#xD;&#xA;- Energy efficient and autonomous peripherals&#xD;&#xA;- Ultra low power Energy Modes with sub-uA operation&#xD;&#xA;- Fast wake-up time of only 2 us&#xD;&#xA;&#xD;&#xA;The EZR32HG microcontroller series revolutionizes the 8- to 32-bit market with acombination of unmatched performance and ultra low power consumption in bothactive- and sleep modes. EZR32HG devices consume as little as 114 uA/MHz in runmode.
      </description>

      <subFamily DsubFamily="EZR32HG220">
        <book         name="Documents/ezr32hg220-datasheet.pdf"      title="EZR32HG220 Data Sheet"/>
        <book         name="Documents/ezr32hg-errata.pdf"         title="EZR32HG220 Errata"/>
        <!-- *************************  Device 'EZR32HG220F32R55'  ***************************** -->
        <device Dname="EZR32HG220F32R55">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R55"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F32R60'  ***************************** -->
        <device Dname="EZR32HG220F32R60">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R60"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F32R61'  ***************************** -->
        <device Dname="EZR32HG220F32R61">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R61"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F32R63'  ***************************** -->
        <device Dname="EZR32HG220F32R63">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R63"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F32R67'  ***************************** -->
        <device Dname="EZR32HG220F32R67">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R67"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F32R68'  ***************************** -->
        <device Dname="EZR32HG220F32R68">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R68"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F32R69'  ***************************** -->
        <device Dname="EZR32HG220F32R69">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F32R69"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F32R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R55'  ***************************** -->
        <device Dname="EZR32HG220F64R55">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R55"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R60'  ***************************** -->
        <device Dname="EZR32HG220F64R60">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R60"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R61'  ***************************** -->
        <device Dname="EZR32HG220F64R61">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R61"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R63'  ***************************** -->
        <device Dname="EZR32HG220F64R63">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R63"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R67'  ***************************** -->
        <device Dname="EZR32HG220F64R67">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R67"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R68'  ***************************** -->
        <device Dname="EZR32HG220F64R68">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R68"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG220F64R69'  ***************************** -->
        <device Dname="EZR32HG220F64R69">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG220F64R69"/>
          <debug      svd="SVD/EZR32HG/EZR32HG220F64R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EZR32HG320">
        <book         name="Documents/ezr32hg320-datasheet.pdf"      title="EZR32HG320 Data Sheet"/>
        <book         name="Documents/ezr32hg-errata.pdf"         title="EZR32HG320 Errata"/>
        <!-- *************************  Device 'EZR32HG320F32R55'  ***************************** -->
        <device Dname="EZR32HG320F32R55">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R55"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F32R60'  ***************************** -->
        <device Dname="EZR32HG320F32R60">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R60"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F32R61'  ***************************** -->
        <device Dname="EZR32HG320F32R61">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R61"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F32R63'  ***************************** -->
        <device Dname="EZR32HG320F32R63">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R63"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F32R67'  ***************************** -->
        <device Dname="EZR32HG320F32R67">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R67"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F32R68'  ***************************** -->
        <device Dname="EZR32HG320F32R68">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R68"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F32R69'  ***************************** -->
        <device Dname="EZR32HG320F32R69">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F32R69"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F32R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R55'  ***************************** -->
        <device Dname="EZR32HG320F64R55">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R55"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R55.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R60'  ***************************** -->
        <device Dname="EZR32HG320F64R60">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R60"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R60.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R61'  ***************************** -->
        <device Dname="EZR32HG320F64R61">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R61"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R61.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R63'  ***************************** -->
        <device Dname="EZR32HG320F64R63">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R63"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R63.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R67'  ***************************** -->
        <device Dname="EZR32HG320F64R67">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R67"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R67.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R68'  ***************************** -->
        <device Dname="EZR32HG320F64R68">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R68"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R68.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EZR32HG320F64R69'  ***************************** -->
        <device Dname="EZR32HG320F64R69">
          <compile header="Device/SiliconLabs/EZR32HG/Include/em_device.h"  define="EZR32HG320F64R69"/>
          <debug      svd="SVD/EZR32HG/EZR32HG320F64R69.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00010000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00002000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32M0P.FLM"  start="0x00000000"  size="0x00010000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00010000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EZR32HG">
      <description>Silicon Labs EZR32HG device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EZR32HG*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="5.8.2" condition="EZR32HG">
      <description>System Startup for Silicon Labs EZR32HG device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EZR32HG/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EZR32HG/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EZR32HG/Source/ARM/startup_ezr32hg.s" condition="Compiler ARMCC" attr="config" version="5.8.2"/>
        <file category="source"  name="Device/SiliconLabs/EZR32HG/Source/GCC/startup_ezr32hg.S" condition="Compiler GCC"   attr="config" version="5.8.2"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EZR32HG/Source/GCC/ezr32hg.ld" condition="Compiler GCC" attr="config" version="5.8.2"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EZR32HG/Source/system_ezr32hg.c" attr="config" version="5.8.2"/>
      </files>
    </component>
  </components>
</package>
