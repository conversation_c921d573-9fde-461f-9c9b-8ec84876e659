CMSIS-Driver WiFi Test Report   Jul  2 2020   07:45:21

TEST 01: WIFI_GetVersion
  DV_WIFI.c (305): [INFO] Driver API version 1.1, Driver version 1.4
                                          PASSED
TEST 02: WIFI_GetCapabilities             PASSED
TEST 03: WIFI_Initialize_Uninitialize     PASSED
TEST 04: WIFI_PowerControl
  DV_WIFI.c (424): [WARNING] PowerControl (ARM_POWER_LOW) is not supported
                                          PASSED
TEST 05: WIFI_GetModuleInfo
  DV_WIFI.c (473): [INFO] Module Info is AT version:1.6.2.0(Apr 13 2018 11:10:59)
SDK version:2.2.1(6ab97e9)
compile time:Jun  7 2018 19:34:26

                                          PASSED
TEST 06: WIFI_SetOption_GetOption
  DV_WIFI.c (557): [WARNING] SetOption ARM_WIFI_BSSID for Access Point is not supported
  DV_WIFI.c (613): [WARNING] GetOption ARM_WIFI_BSSID for Access Point is not supported
  DV_WIFI.c (890): [WARNING] SetOption ARM_WIFI_DTIM for Station is not supported
  DV_WIFI.c (904): [WARNING] SetOption ARM_WIFI_DTIM for Access Point is not supported
  DV_WIFI.c (932): [WARNING] GetOption ARM_WIFI_DTIM for Station is not supported
  DV_WIFI.c (952): [WARNING] GetOption ARM_WIFI_DTIM for Access Point is not supported
  DV_WIFI.c (1018): [WARNING] SetOption ARM_WIFI_BEACON for Access Point is not supported
  DV_WIFI.c (1050): [WARNING] GetOption ARM_WIFI_BEACON for Access Point is not supported
                                          PASSED
TEST 07: WIFI_Scan                        PASSED
TEST 08: WIFI_Activate_Deactivate         PASSED
TEST 09: WIFI_IsConnected                 PASSED
TEST 10: WIFI_GetNetInfo                  PASSED
TEST 11: WIFI_SocketCreate                PASSED
TEST 12: WIFI_SocketBind                  PASSED
TEST 13: WIFI_SocketListen                PASSED
TEST 14: WIFI_SocketAccept                PASSED
TEST 15: WIFI_SocketConnect
  DV_WIFI.c (4370): [WARNING] Non BSD-strict, connect to non-existent port (result ARM_SOCKET_ETIMEDOUT, expected ARM_SOCKET_ECONNREFUSED)
                                          PASSED
TEST 16: WIFI_SocketRecv                  PASSED
TEST 17: WIFI_SocketRecvFrom              PASSED
TEST 18: WIFI_SocketSend                  PASSED
TEST 19: WIFI_SocketSendTo                PASSED
TEST 20: WIFI_SocketGetSockName           PASSED
TEST 21: WIFI_SocketGetPeerName           PASSED
TEST 22: WIFI_SocketGetOpt                PASSED
TEST 23: WIFI_SocketSetOpt                PASSED
TEST 24: WIFI_SocketClose                 PASSED
TEST 25: WIFI_SocketGetHostByName         PASSED
TEST 26: WIFI_Ping                        PASSED
TEST 27: WIFI_Transfer_Fixed              PASSED
TEST 28: WIFI_Transfer_Incremental        PASSED
TEST 29: WIFI_Send_Fragmented             PASSED
TEST 30: WIFI_Recv_Fragmented             PASSED
TEST 31: WIFI_Test_Speed                  PASSED
TEST 32: WIFI_Concurrent_Socket           PASSED
TEST 33: WIFI_Downstream_Rate
  DV_WIFI.c (8342): [INFO] Speed 78 KB/s
                                          PASSED
TEST 34: WIFI_Upstream_Rate
  DV_WIFI.c (8410): [INFO] Speed 23 KB/s
                                          PASSED

Test Summary: 34 Tests, 34 Passed, 0 Failed.
Test Result: PASSED
