<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPCXpresso54102_BSP</name>
  <vendor>NXP</vendor>
  <description>Board Support Pack for LPCXPRESSO54102</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.0.1" date="2019-09-23">NXP CMSIS packs based on MCUXpresso SDK 2.6.1</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="LPC54102_DFP" vendor="NXP"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="LPCXpresso54102">
      <description>The LPCXpresso family of boards provides a powerful and flexible development system for NXP®'s Cortex-M MCUs</description>
      <mountedDevice Dname="LPC54102J512" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.LPC54102_AND_component.serial_manager_uart_AND_component.vusart_adapter_AND_device.LPC54102_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_AND_driver.power_AND_driver.vusart_AND_utility.debug_console">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="vusart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="Startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
    </condition>
  </conditions>
  <examples>
    <example name="low_power_cm0plus" folder="multicore_examples/low_power/cm0plus" doc="readme.txt">
      <description>The multicore Low Power demo application demonstrates how to leverage the auxiliary core for periodicdata collection from sensors and passing the data to the primary core for further processing.The data from sensors is read with frequency of about 50Hz (every 20ms) by secondary core and storedinto a buffer. When the buffer contains 50 samples from sensors, the secondary core sends an interruptto the primary core to process the data. The primary core computes average values from temperature andpressure and then prints them with last values from other sensors to the console.The reading of data from sensors takes about 3ms, the rest of the time the cores are in deep sleepmode to reduce power consumption. In order to switch to deep sleep mode, the Power APIPOWER_EnterDeepSleep() is used and to wake it up the interrupt from uTick timer is used.When both cores can't go to the deep sleep mode (the primary core processes data or the secondary corereads data from the sensors), the core which has nothing to do goes to sleep mode by WFI. Also, whenthe primary core is switched to the sleep mode, the flash memory is powered down by Power APIPOWER_PowerDownFlash() and before waking up the primary core it is powered on by POWER_PowerUpFlash().The demo is based on:A. Fuks, "Sensor-hub sweet-spot analysis for ultra-low-power always-on operation," 2015 Symposium on VLSI Circuits (VLSI Circuits), Kyoto, 2015, pp. C154-C155.doi: 10.1109/VLSIC.2015.7231247URL: http://ieeexplore.ieee.org/stamp/stamp.jsp?tp=&amp;arnumber=7231247&amp;isnumber=7231231</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/low_power_cm0plus.uvprojx"/>
        <environment name="iar" load="iar/low_power_cm0plus.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_interrupt_cm0plus" folder="driver_examples/mailbox/interrupt/cm0plus" doc="readme.txt">
      <description>The mailbox_interrupt example shows how to use mailbox to exchange message.In this example:The core 0(CM4) writes value to mailbox for Core 1(CM0+), it causes mailbox interrupton CM0+ side. CM0+ reads value from mailbox increments and writes it to mailbox registerfor CM4, it causes mailbox interrupt on CM4 side. CM4 reads value from mailbox incrementsand writes it to mailbox register for CM0 again.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_interrupt_cm0plus.uvprojx"/>
        <environment name="iar" load="iar/mailbox_interrupt_cm0plus.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_mutex_cm0plus" folder="driver_examples/mailbox/mutex/cm0plus" doc="readme.txt">
      <description>The mailbox_mutex example shows how to use mailbox mutex.In this example:The core 0 sends address of shared variable to core 1 by mailbox.Both cores trying to get mutex in while loop, after that updates shared variableand sets mutex, to allow access other core to shared variable.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_mutex_cm0plus.uvprojx"/>
        <environment name="iar" load="iar/mailbox_mutex_cm0plus.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_i2c_dma_b2b_transfer_master" folder="cmsis_driver_examples/i2c/dma_b2b_transfer/master/cm4" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_i2c_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_i2c_dma_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/dma_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_i2c_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_i2c_int_b2b_transfer_master" folder="cmsis_driver_examples/i2c/int_b2b_transfer/master/cm4" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_i2c_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_i2c_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_i2c_int_b2b_transfer_slave" folder="cmsis_driver_examples/i2c/int_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The i2c_interrupt_b2b_transfer_master example shows how to use CMSIS i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_i2c_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_i2c_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_spi_dma_b2b_transfer_master" folder="cmsis_driver_examples/spi/dma_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The cmsis_spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'cmsis_spi_dma_b2b_transfer_master.c' includes the SPI master code.1. SPI master send/received data to/from SPI slave in edma way. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_spi_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_spi_dma_b2b_transfer_slave" folder="cmsis_driver_examples/spi/dma_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The cmsis_spi_dma_b2b_transfer example shows how to use SPI CMSIS driver in dma way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'cmsis_spi_dma_b2b_transfer_slave.c' includes the SPI slave code.1. SPI master send/received data to/from SPI slave in dma way. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_spi_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_spi_int_b2b_transfer_master" folder="cmsis_driver_examples/spi/int_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The cmsis_spi_int_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'cmsis_spi_int_b2b_transfer_master.c' includes the SPI master code.1. SPI master send/received data to/from SPI slave in interrupt way. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_spi_int_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_spi_int_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_spi_int_b2b_transfer_slave" folder="cmsis_driver_examples/spi/int_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The cmsis_spi_int_b2b_transfer example shows how to use SPI CMSIS driver in interrupt way:In this example , we need two boards, one board used as SPI master and another board used as SPI slave.The file 'cmsis_spi_int_b2b_transfer_slave.c' includes the SPI slave code.1. SPI master send/received data to/from SPI slave in interrupt . </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_spi_int_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_spi_int_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_usart_dma_transfer" folder="cmsis_driver_examples/usart/dma_transfer/cm4" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_edma_transfer example shows how to use uart cmsis  driver with EDMA:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_usart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_usart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="cmsis_lpc_usart_interrupt_transfer" folder="cmsis_driver_examples/usart/interrupt_transfer/cm4" doc="readme.txt">
      <description>CMSIS-Driver defines generic peripheral driver interfaces for middleware making it reusable across a wide range of supported microcontroller devices. The API connects microcontroller peripherals with middleware that implements for example communication stacks, file systems, or graphic user interfaces. More information and usage methord please refer to http://www.keil.com/pack/doc/cmsis/Driver/html/index.html.The cmsis_uart_interrupt_transfer example shows how to use uart cmsis driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/cmsis_lpc_usart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/cmsis_lpc_usart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="crc" folder="driver_examples/crc/cm4" doc="readme.txt">
      <description>The CRC Example project is a demonstration program that uses the KSDK software to generate checksumsfor an ASCII string. Several CRC protocols are implemented using the CRC driver API.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/crc.uvprojx"/>
        <environment name="iar" load="iar/crc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_example" folder="driver_examples/ctimer/simple_match/cm4" doc="readme.txt">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, the match feature of the CTimer is used to toggle the output level.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_match_interrupt_example" folder="driver_examples/ctimer/simple_match_interrupt/cm4" doc="readme.txt">
      <description>The Simple Match Interrupt project is to demonstrate usage of the SDK CTimer driver with interrupt callback functionsIn this example the upon match and IO pin connected to the LED is toggled and the timer is reset, so it would generate a square wave.With an interrupt callback the match value is changed frequently in such a way that the frequency of the output square wave is increased gradually.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_match_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_match_interrupt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_example" folder="driver_examples/ctimer/simple_pwm/cm4" doc="readme.txt">
      <description>The CTimer Example project is to demonstrate usage of the KSDK ctimer driver.In this example, CTimer is used to generate a PWM signal.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="ctimer_pwm_interrupt_example" folder="driver_examples/ctimer/simple_pwm_interrupt/cm4" doc="readme.txt">
      <description>The Simple PWM Interrupt project is to demonstrate usage of the SDK CTimer driver as a PWM with interrupt callback functionsIn this example an IO pin connected to the LED is used as a PWM output line to generate a PWM signal.With an interrupt callback the PWM duty cycle is changed frequently in such a way that the LED brightness can be varied.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/ctimer_pwm_interrupt_example.uvprojx"/>
        <environment name="iar" load="iar/ctimer_pwm_interrupt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_channel_chain" folder="driver_examples/dma/channel_chain/cm4" doc="readme.txt">
      <description>The DMA channel chain example is a simple demonstration program that uses the SDK software.The purpose of this example is to show how to use the DMA channel chain feature.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_channel_chain.uvprojx"/>
        <environment name="iar" load="iar/dma_channel_chain.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_interleave_transfer" folder="driver_examples/dma/interleave_transfer/cm4" doc="readme.txt">
      <description>The DMA interleave transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA interleave feature.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_interleave_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_interleave_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_linked_transfer" folder="driver_examples/dma/linked_transfer/cm4" doc="readme.txt">
      <description>The DMA linked transfer example is a simple demonstration program that uses the SDK software.It executes a linked transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a linked trnasfer example.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_linked_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_linked_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_m2m_polling" folder="driver_examples/dma/m2m_polling/cm4" doc="readme.txt">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot polling transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example for debugging and further development.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_m2m_polling.uvprojx"/>
        <environment name="iar" load="iar/dma_m2m_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_memory_to_memory" folder="driver_examples/dma/memory_to_memory/cm4" doc="readme.txt">
      <description>The DMA memory to memory example is a simple demonstration program that uses the SDK software.It executes one shot transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA and to provide a simple example fordebugging and further development.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_memory_to_memory.uvprojx"/>
        <environment name="iar" load="iar/dma_memory_to_memory.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="dma_wrap_transfer" folder="driver_examples/dma/wrap_transfer/cm4" doc="readme.txt">
      <description>The DMA wrap transfer example is a simple demonstration program that uses the SDK software.It executes a wrap transfer from source buffer to destination buffer using the SDK DMA drivers.The purpose of this example is to show how to use the DMA wrap feature.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/dma_wrap_transfer.uvprojx"/>
        <environment name="iar" load="iar/dma_wrap_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="flashiap" folder="driver_examples/flashiap/cm4" doc="readme.txt">
      <description>The FLASIAP project is a simple demonstration program of the SDK FLASIAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/flashiap.uvprojx"/>
        <environment name="iar" load="iar/flashiap.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="fmeas" folder="driver_examples/fmeas/cm4" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Frequency Measure feature of SYSCON module on LPC devices.It shows how to measure a target frequency using a (faster) reference frequency. The example uses the internal main clock as the reference frequency to measure the frequencies of the RTC, watchdog oscillator, and internal RC oscillator.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fmeas.uvprojx"/>
        <environment name="iar" load="iar/fmeas.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gint" folder="driver_examples/gint/cm4" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Group GPIO input interrupt peripheral.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gint.uvprojx"/>
        <environment name="iar" load="iar/gint.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="gpio_led_output" folder="driver_examples/gpio/led_output/cm4" doc="readme.txt">
      <description>The GPIO Example project is a demonstration program that uses the KSDK software to manipulate the general-purposeoutputs. The example use LEDs and buttons to demonstrates GPIO API for port and pin manipulation (init, set,clear, and toggle).</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/gpio_led_output.uvprojx"/>
        <environment name="iar" load="iar/gpio_led_output.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="hello_world" folder="demo_apps/hello_world/cm4" doc="readme.txt">
      <description>The Hello World demo application provides a sanity check for the new SDK build environments and board bring up. The HelloWorld demo prints the "Hello World" string to the terminal using the SDK UART drivers. The purpose of this demo is toshow how to use the UART, and to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/hello_world.uvprojx"/>
        <environment name="iar" load="iar/hello_world.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="iap_flash" folder="driver_examples/iap/iap_flash/cm4" doc="readme.txt">
      <description>The IAP Flash project is a simple demonstration program of the SDK IAP driver. It erases and programs a portion of on-chip flash memory. A message a printed on the UART terminal as various operations on flash memory are performed.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/iap_flash.uvprojx"/>
        <environment name="iar" load="iar/iap_flash.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="led_blinky" folder="demo_apps/led_blinky/cm4" doc="readme.txt">
      <description>The LED Blinky demo application provides a sanity check for the new SDK build environments and board bring up. The LED Blinky demo uses the systick interrupt to realize the function of timing delay. The example takes turns to shine the LED. The purpose of this demo is to provide a simple project for debugging and further development.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/led_blinky.uvprojx"/>
        <environment name="iar" load="iar/led_blinky.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="low_power_cm4" folder="multicore_examples/low_power/cm4" doc="readme.txt">
      <description>The multicore Low Power demo application demonstrates how to leverage the auxiliary core for periodicdata collection from sensors and passing the data to the primary core for further processing.The data from sensors is read with frequency of about 50Hz (every 20ms) by secondary core and storedinto a buffer. When the buffer contains 50 samples from sensors, the secondary core sends an interruptto the primary core to process the data. The primary core computes average values from temperature andpressure and then prints them with last values from other sensors to the console.The reading of data from sensors takes about 3ms, the rest of the time the cores are in deep sleepmode to reduce power consumption. In order to switch to deep sleep mode, the Power APIPOWER_EnterDeepSleep() is used and to wake it up the interrupt from uTick timer is used.When both cores can't go to the deep sleep mode (the primary core processes data or the secondary corereads data from the sensors), the core which has nothing to do goes to sleep mode by WFI. Also, whenthe primary core is switched to the sleep mode, the flash memory is powered down by Power APIPOWER_PowerDownFlash() and before waking up the primary core it is powered on by POWER_PowerUpFlash().The demo is based on:A. Fuks, "Sensor-hub sweet-spot analysis for ultra-low-power always-on operation," 2015 Symposium on VLSI Circuits (VLSI Circuits), Kyoto, 2015, pp. C154-C155.doi: 10.1109/VLSIC.2015.7231247URL: http://ieeexplore.ieee.org/stamp/stamp.jsp?tp=&amp;arnumber=7231247&amp;isnumber=7231231</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/low_power_cm4.uvprojx"/>
        <environment name="iar" load="iar/low_power_cm4.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_basic" folder="driver_examples/adc/lpc_adc_basic/cm4" doc="readme.txt">
      <description>The lpc_adc_basic example shows how to use LPC ADC driver in the simplest way.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software trigger API is called to start the conversion. Then it polls the conversion sequence A's flag till the conversion is completed. When the conversion is completed, just print the conversion result to terminal.Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above. (In this example, we use this way.)      2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored.  Program Flow1.This example demonstrates how to configure the A sequences with polling, assigning one channel with software  trigger, you can configure channel via "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.    3.After ADC channels are assigned to each of the sequences, the software trigger is chosen. Setting   SEQA_CTRL_START to '1' will trigger sequence A.  4.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, software trigger will start. 5.Read the corresponding DATAVALID field with polling to judge whether the conversion completes and the result is ready.  If the result is ready, the example will printf result information to terminal.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_basic.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_basic.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_burst" folder="driver_examples/adc/lpc_adc_burst/cm4" doc="readme.txt">
      <description>The lpc_adc_burst example shows how to use LPC ADC driver with the burst mode.In this example, the internal temperature sensor is used to created the input analog signal.When user type in any key from the keyboard, the burst mode is enabled. Then the conversion sequence A would be started automatically, till the burst would be disabled in conversion completed ISR. Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above.       2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored. (In this example, we use this way.)         3. What kinds of interrupt do ADC have?   There are four interrupts that can be generated by the ADC:     • Conversion-Complete or Sequence-Complete interrupts for sequences A and B     • Threshold-Compare Out-of-Range Interrupt     • Data Overrun Interrupt   Any of these interrupt requests may be individually enabled or disabled in the INTEN register.  Program Flow1.This example demonstrates how to configure the A sequences with burst mode, you can configure channel via   "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.  3.Enable the Conversion-Complete or Sequence-Complete interrupt for sequences A.  4.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, burst mode will start.    5.When the first conversion completes, the interrupt would be triggered. The ISR will stop the burst mode and print conversion result   to terminal.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_burst.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_burst.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_dma" folder="driver_examples/adc/lpc_adc_dma/cm4" doc="readme.txt">
      <description>The lpc_adc_dma example shows how to use LPC ADC driver with DMA.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software trigger API is called to start the conversion. When the ADC conversion is completed, it would trigger the DMA to move the ADC conversion result from ADC conversion data register to user indicated memory. Then the main loop waits for the transfer to be done and print the result to terminal.Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above. (In this example, we use this way.)      2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored.          3. How to use DMA to work with ADC?   The sequence-A or sequence-B conversion/sequence-complete interrupts may also be   used to generate a DMA trigger. To trigger a DMA transfer, the same conditions must be   met as the conditions for generating an interrupt.   Remark: If the DMA is used, the ADC interrupt must be disabled in the NVIC.   Program Flow1.This example demonstrates how to configure the A sequences with interrupt, assigning one channel with software  trigger, you can configure channel via "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.  3.Configure the DMA and DMAMUX to work with ADC sequences.4.Enable the Conversion-Complete or Sequence-Complete DMA for sequences A.  5.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, software trigger will start.    6.When the conversion completes, the DMA would be requested.7.When the DMA transfer completes, DMA will trigger a interrupt. ISR would set the "bDmaTransferDone" to 'true'. Then main function will   print conversion result to terminal.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_dma.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_dma.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_adc_interrupt" folder="driver_examples/adc/lpc_adc_interrupt/cm4" doc="readme.txt">
      <description>The lpc_adc_interrupt example shows how to use interrupt with LPC ADC driver.In this example, the internal temperature sensor is used to created the input analog signal. When user type in any key from the keyboard, the software trigger API is called to start the conversion. Then it polls the flag variable which would be asserted when the conversion completed ISR is executed. Then just print the conversion result to terminal.Project Information1.what are ADC conversion sequences?  A conversion sequence is a single pass through a series of A/D conversions performed on a selected set of  A/D channels. Software can set-up two independent conversion sequences, either of which can be triggered   by software or by a transition on one of the hardware triggers. Each sequence can be triggered by a different   hardware trigger. One of these conversion sequences is referred to as the A sequence and the other as the B  sequence. It is not necessary to employ both sequences. An optional single-step mode allows advancing through  the channels of a sequence one at a time on each successive occurrence of a trigger. The user can select whether  a trigger on the B sequence can interrupt an already-in-progress A sequence. The B sequence, however, can never be  interrupted by an A trigger.  2. How to use software-triggered conversion?   There are two ways that software can trigger a conversion sequence:      1. Start Bit: The first way to software-trigger an sequence is by setting the START bit in         the corresponding SEQn_CTRL register. The response to this is identical to         occurrence of a hardware trigger on that sequence. Specifically, one cycle of         conversions through that conversion sequence will be immediately triggered except         as indicated above. (In this example, we use this way.)      2. Burst Mode: The other way to initiate conversions is to set the BURST bit in the         SEQn_CTRL register. As long as this bit is 1 the designated conversion sequence will         be continuously and repetitively cycled through. Any new software or hardware trigger         on this sequence will be ignored.          3. What kinds of interrupt do ADC have?   There are four interrupts that can be generated by the ADC:     • Conversion-Complete or Sequence-Complete interrupts for sequences A and B     • Threshold-Compare Out-of-Range Interrupt     • Data Overrun Interrupt   Any of these interrupt requests may be individually enabled or disabled in the INTEN register.  Program Flow1.This example demonstrates how to configure the A sequences with interrupt, assigning one channel with software  trigger, you can configure channel via "DEMO_ADC_SAMPLE_CHANNEL_NUMBER".  2.Before configuration of the ADC begins, the ADC is put through a self-calibration cycle.  3.Enable the Conversion-Complete or Sequence-Complete interrupt for sequences A.  4.After ADC channels are assigned to each of the sequences, if the user enters any key via terminal, software trigger will start.    5.When the conversion completes, the interrupt would be triggered. The ISR will print conversion result to terminal.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_adc_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_adc_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_dma_b2b_transfer_master" folder="driver_examples/i2c/dma_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The i2c_dma_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with DMA:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_dma_b2b_transfer_slave" folder="driver_examples/i2c/dma_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The i2c_dma_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with a DMA master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_master" folder="driver_examples/i2c/interrupt_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_master example shows how to use i2c driver as master to do board to board transfer with interrupt:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_interrupt_b2b_transfer_slave" folder="driver_examples/i2c/interrupt_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The i2c_interrupt_b2b_transfer_slave example shows how to use i2c driver as slave to do board to board transfer with interrupt:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_master" folder="driver_examples/i2c/polling_b2b/master/cm4" doc="readme.txt">
      <description>The i2c_polling_b2b_master example shows how to use i2c driver as master to do board to board transfer using polling method:In this example, one i2c instance as master and another i2c instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_polling_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_i2c_polling_b2b_slave" folder="driver_examples/i2c/polling_b2b/slave/cm4" doc="readme.txt">
      <description>The i2c_polling_b2b_slave example shows how to use i2c driver as slave to do board to board transfer with a polling master:In this example, one i2c instance as slave and another i2c instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_i2c_polling_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_i2c_polling_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_dma_b2b_transfer_master" folder="driver_examples/spi/dma_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The spi_dma_b2b_transfer_master example shows how to use driver API to transfer in DMA way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checksif the data received from slave is correct. This example needs to work with spi_dma_b2b_transfer_slave example.Note: This example will run in master mode, please prepare another board for slave, and the slave       board should be started first.Project Information1.How to set the baudrate of SPI?  The baudrate of SPI is easy to set, just make sure the source clock of SPI.  The formula is below:    baudrate = PCLK_SPIn / DIVVAL  SPIn-&gt;DIV = DIVVAL -1  For example, if the needed baudrate of SPI is 500khz, and the SPI clock is 12MHz  DIVVAL = 12000000/500000  DIVVAL = 24  SPIn-&gt;DIV = DIVVAL -1 = 23Program Flow:Main routine:  1.Initialize the hardware.	Configure pin settings, clock settings by using BOARD_InitHardware();	  2.Send information to terminal by using debug console.	  3.Initialize the SPI with configuration.	  4.Set up DMA configuration for master SPI.    Initialize DMA and DMA channel setting(create handle and set callback) for both    SPI RX and TX, set prioritory for TX channel and RX channel.	  5.Start SPI master transfer in DMA way.    Initialize buffers and start SPI transfer in DMA way.	  6.Check if data from master is all right.    Waiting for transmission complete, print received data and log information to terminal.    7.De-initialize the SPI and DMA.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_dma_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_dma_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_dma_b2b_transfer_slave" folder="driver_examples/spi/dma_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The spi_dma_b2b_transfer_slave example shows how to use driver API to transfer in DMA way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checksif the data received from master is correct. This example needs to work with spi_dma_b2b_transfer_master example.Note: This example will run in slave mode, please prepare another board for master, and the slave       board should be started first.Project InformationProgram Flow:Main routine:  1.Initialize the hardware.	Configure pin settings, clock settings by using BOARD_InitHardware();	  2.Send information to terminal by using debug console.	  3.Initialize the SPI with configuration.	  4.Set up DMA configuration for slave SPI.    Initialize DMA and DMA channel setting(create handle and set callback) for both    SPI RX and TX, set prioritory for TX channel and RX channel.	  5.Start SPI master transfer in DMA way.    Initialize buffers and start SPI transfer in DMA way.	  6.Check if data from master is all right.    Waiting for transmission complete, print received data and log information to terminal.    7.De-initialize the SPI and DMA.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_dma_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_dma_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_half_duplex_dma_master" folder="driver_examples/spi/half_duplex_transfer/dma/master/cm4" doc="readme.txt">
      <description>The lpc_spi_half_duplex_dma_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer data in DMA way. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_half_duplex_dma_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_half_duplex_dma_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_half_duplex_dma_slave" folder="driver_examples/spi/half_duplex_transfer/dma/slave/cm4" doc="readme.txt">
      <description>The lpc_spi_half_duplex_dma transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses dma mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_half_duplex_dma_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_half_duplex_dma_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_half_duplex_int_master" folder="driver_examples/spi/half_duplex_transfer/int/master/cm4" doc="readme.txt">
      <description>The lpc_spi_half_duplex_int_transfer_master example shows how to use driver API to transfer in half-duplex way.  In this example, one spi instance as master and another spi instance on the other board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer data in interrupt way. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_half_duplex_int_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_half_duplex_int_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_half_duplex_int_slave" folder="driver_examples/spi/half_duplex_transfer/int/slave/cm4" doc="readme.txt">
      <description>The lpc_spi_half_duplex_int_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_half_duplex_int_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_half_duplex_int_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_half_duplex_polling_master" folder="driver_examples/spi/half_duplex_transfer/polling/master/cm4" doc="readme.txt">
      <description>The lpc_spi_half_duplex_polling_transfer_master example shows how to use driver API to transfer in half-duplex way.In this example, one spi instance as master and another spi instance on the othere board as slave. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.Besides, master will transfer data in polling way. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_half_duplex_polling_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_half_duplex_polling_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_half_duplex_polling_slave" folder="driver_examples/spi/half_duplex_transfer/polling/slave/cm4" doc="readme.txt">
      <description>The lpc_spi_half_duplex_polling_transfer_slave example shows how to receive and transmit data to master board.Notice: The SPI slave of this example uses interrupt mode. The data transfer size(not buffer size) is twice ofthe master's transfer size. The first half of the transmission will receive data, and the second half will senddata back to master, so the address of the receive buffer is &amp;rxDataPolling[64].In this example, one spi instance as slave and another spi instance on the other board as master. Master sends a piece of data to slave, and receive a piece of data from slave. This example checks if the data received from master is correct. And slave will print what it received.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_half_duplex_polling_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_half_duplex_polling_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_interrupt" folder="driver_examples/spi/interrupt/cm4" doc="readme.txt">
      <description>The spi_interrupt example shows how to use spi functional API to do interrupt transfer:In this example, one spi instance as master and another spi instance as slave. Master sends a piece of data to slave,and check if the data slave received is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_interrupt_b2b_master" folder="driver_examples/spi/interrupt_b2b/master/cm4" doc="readme.txt">
      <description>The spi_interrupt_b2b_master example shows how to use spi functional API to do interrupt transfer as a master:In this example, the spi instance as master. Master sends a piece of data to slave, receive data from slave,and checkif the data master received is correct. This example needs to work with spi_interrupt_b2b_slave example.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_interrupt_b2b_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_interrupt_b2b_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_interrupt_b2b_slave" folder="driver_examples/spi/interrupt_b2b/slave/cm4" doc="readme.txt">
      <description>The spi_interrupt_b2b_slave example shows how to use spi functional API to do interrupt transfer as a slave:In this example, the spi instance as slave. Slave receives data froma master and send a peiece of data to master,and check if the data slave received is correct. This example needs to work with spi_interrupt_b2b_master example.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_interrupt_b2b_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_interrupt_b2b_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_interrupt_b2b_transfer_master" folder="driver_examples/spi/interrupt_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The spi_interrupt_board2board_master example shows how to use spi driver as master to do board to board transfer withinterrupt:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_interrupt_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_interrupt_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_interrupt_b2b_transfer_slave" folder="driver_examples/spi/interrupt_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The spi_interrupt_board2board_slave example shows how to use spi driver as slave to do board to board transfer with interrupt:In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_interrupt_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_interrupt_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_polling_b2b_transfer_master" folder="driver_examples/spi/polling_b2b_transfer/master/cm4" doc="readme.txt">
      <description>The spi_polling_board2board_master example shows how to use spi driver as master to do board to board transfer with polling:In this example, one spi instance as master and another spi instance on othereboard as slave. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from slave is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_polling_b2b_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_polling_b2b_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_spi_polling_b2b_transfer_slave" folder="driver_examples/spi/polling_b2b_transfer/slave/cm4" doc="readme.txt">
      <description>The spi_polling_board2board_slave example shows how to use spi driver as slave to do board to board transfer withpolling:Notice: The SPI slave of this example uses interrupt mode, as there is no polling mode for SPI slave.In this example, one spi instance as slave and another spi instance on other board as master. Master sends a piece ofdata to slave, and receive a piece of data from slave. This example checks if the data received from master is correct.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_spi_polling_b2b_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_spi_polling_b2b_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_dma_double_buffer_transfer" folder="driver_examples/usart/dma_double_buffer_transfer/cm4" doc="readme.txt">
      <description>This example shows how to use the DMA driver to implement a double buffer receive scheme from the USARTThe example shows the double buffer constructed using two descriptors (g_pingpong_desc). These descriptors are cycled from one to the other.Things to note- The descriptors of the ping pong transfer need to be aligned to size 16- The inital transfer will perform the same job as first descriptor of ping pong, so the first linkeage is to go to g_pingpong_desc[1]- g_pingpong_desc[1] then chains the g_pingpong_desc[0] as the next descriptor- The properties are set up such that g_pingpong_desc[0] (and the initial configuration uses INTA to signal back to the callback)- g_pingpong_desc[1] uses INTB to signal to the callback- The scheduled callback uses this information to know which data was last writtenA note on PerformanceThe intent of this example is to illustrate how a double-buffer scheme can be implemented using the dma. The performance of this example will be limited to how quickly the echo printer can read-out the data from the ping pong buffer and display it. This means that the example will work well if characters are entered at a rate where the DMA callback to echo the string can keep up with the input stream. Connecting the USARTRX to a continuous fast speed will cause the DMA to fall behind. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_dma_double_buffer_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_dma_double_buffer_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_dma_rb_transfer" folder="driver_examples/usart/dma_rb_transfer/cm4" doc="readme.txt">
      <description>The usart dma ring buffer example shows how to illustrate a ring buffer with DMA used. In the example, board will send back the received characters, data received will stored in ring buffer automatically, and routine will check the ring buffer, once receive character reach 8, will send them out in DMA mode too. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_dma_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_dma_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_dma_transfer" folder="driver_examples/usart/dma_transfer/cm4" doc="readme.txt">
      <description>The usart_dma example shows how to use usart driver with DMA:In this example, one usart instance connect to PC through usart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_dma_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_dma_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_interrupt" folder="driver_examples/usart/interrupt/cm4" doc="readme.txt">
      <description>The uart_functioncal_interrupt example shows how to use uart driver functionalAPI to receive data with interrupt method:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_interrupt.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_interrupt_rb_transfer" folder="driver_examples/usart/interrupt_rb_transfer/cm4" doc="readme.txt">
      <description>The uart_interrupt_ring_buffer example shows how to use uart driver in interrupt way withRX ring buffer enabled:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_interrupt_rb_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_interrupt_rb_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_interrupt_transfer" folder="driver_examples/usart/interrupt_transfer/cm4" doc="readme.txt">
      <description>The uart_interrupt example shows how to use uart driver in interrupt way:In this example, one uart instance connect to PC through uart, the board willsend back all characters that PC send to the board.Note: The example echo every 8 characters, so input 8 characters every time.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_interrupt_transfer.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_interrupt_transfer.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_polling" folder="driver_examples/usart/polling/cm4" doc="readme.txt">
      <description>The uart_polling example shows how to use uart driver in polling way:In this example, one uart instance connect to PC through uart, the board will send back all characters that PCsend to the board.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_polling.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_polling.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_sync_transfer_master" folder="driver_examples/usart/sync_transfer/master/cm4" doc="readme.txt">
      <description>The usart synchronous transfer example shows how to use usart driver in synchronous way.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_sync_transfer_master.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_sync_transfer_master.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="lpc_usart_sync_transfer_slave" folder="driver_examples/usart/sync_transfer/slave/cm4" doc="readme.txt">
      <description>The usart synchronous transfer example shows how to use usart driver in synchronous way.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/lpc_usart_sync_transfer_slave.uvprojx"/>
        <environment name="iar" load="iar/lpc_usart_sync_transfer_slave.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_interrupt_cm4" folder="driver_examples/mailbox/interrupt/cm4" doc="readme.txt">
      <description>The mailbox_interrupt example shows how to use mailbox to exchange message.In this example:The core 0(CM4) writes value to mailbox for Core 1(CM0+), it causes mailbox interrupton CM0+ side. CM0+ reads value from mailbox increments and writes it to mailbox registerfor CM4, it causes mailbox interrupt on CM4 side. CM4 reads value from mailbox incrementsand writes it to mailbox register for CM0 again.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_interrupt_cm4.uvprojx"/>
        <environment name="iar" load="iar/mailbox_interrupt_cm4.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mailbox_mutex_cm4" folder="driver_examples/mailbox/mutex/cm4" doc="readme.txt">
      <description>The mailbox_mutex example shows how to use mailbox mutex.In this example:The core 0 sends address of shared variable to core 1 by mailbox.Both cores trying to get mutex in while loop, after that updates shared variableand sets mutex, to allow access other core to shared variable.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mailbox_mutex_cm4.uvprojx"/>
        <environment name="iar" load="iar/mailbox_mutex_cm4.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="mrt_example" folder="driver_examples/mrt/cm4" doc="readme.txt">
      <description>The MRT project is a simple demonstration program of the SDK MRT driver. It sets up the MRThardware block to trigger a periodic interrupt after every 250 milliseconds. When the PIT interrupt is triggereda message a printed on the UART terminal and an LED is toggled on the board.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/mrt_example.uvprojx"/>
        <environment name="iar" load="iar/mrt_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pattern_match" folder="driver_examples/pint/pattern_match/cm4" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pattern_match.uvprojx"/>
        <environment name="iar" load="iar/pint_pattern_match.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="pint_pin_interrupt" folder="driver_examples/pint/pin_interrupt/cm4" doc="readme.txt">
      <description>This example shows how to use SDK drivers to use the Pin interrupt &amp; pattern match peripheral.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/pint_pin_interrupt.uvprojx"/>
        <environment name="iar" load="iar/pint_pin_interrupt.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="power_manager_lpc" folder="demo_apps/power_manager_lpc/cm4" doc="readme.txt">
      <description>The power_manager_lpc application shows the usage of normal power mode control APIs for entering the three kinds of low power mode: Sleep mode, Deep Sleep mode and Sleep Power Down mode. When the application runs to each low power mode, the device would cut off the power for specific modules to save energy. The device can be also waken up by prepared wakeup source from external event.
Tips: This demo is to show how the various power mode can switch to each other. However, in actual low power use case, to save energy and reduce the consumption even more, many things can be done including: - Disable the clock for unnecessary module during low power mode. That means, programmer can disable the clocks before entering the low power mode and re-enable them after exiting the low power mode when necessary. - Disable the function for unnecessary part of a module when other part would keep working in low power mode. At the most time, more powerful function means more power consumption. For example, disable the digital function for the unnecessary pin mux, and so on. - Set the proper pin state (direction and logic level) according to the actual application hardware. Otherwise, the pin cirrent would be activied unexpectedly waste some energy. - Other low power consideration based on the actual application hardware. - In order to meet typedef power consumption of DateSheet manual, Please configure MCU under the following conditions. • Configure all pins as GPIO with pull-up resistor disabled in the IOCON block. • Configure GPIO pins as outputs using the GPIO DIR register. • Write 1 to the GPIO CLR register to drive the outputs LOW. • All peripherals disabled.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/power_manager_lpc.uvprojx"/>
        <environment name="iar" load="iar/power_manager_lpc.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rit_example" folder="driver_examples/rit/cm4" doc="readme.txt">
      <description>The RIT project is a simple demonstration program of the SDK RIT driver. It sets up the RIThardware block to trigger a periodic interrupt at 1 second period interval to toggel a specifiedLED on board.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rit_example.uvprojx"/>
        <environment name="iar" load="iar/rit_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="rtc_example" folder="driver_examples/rtc/cm4" doc="readme.txt">
      <description>The RTC project is a simple demonstration program of the SDK RTC driver. It sets up the RTChardware block to trigger an alarm after a user specified time period. The test will set the currentdate and time to a predefined value. The alarm will be set with reference to this predefined dateand time.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/rtc_example.uvprojx"/>
        <environment name="iar" load="iar/rtc_example.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_16bit_counter" folder="driver_examples/sctimer/16bit_counter/cm4" doc="readme.txt">
      <description>The SCTimer 16-bit counter project is a demonstration program of the SDK SCTimer driver operation when using the SCTimer counteras two 16-bit counters.The example toggles an output per counter when a match occurs.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_16bit_counter.uvprojx"/>
        <environment name="iar" load="iar/sctimer_16bit_counter.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_multi_state_pwm" folder="driver_examples/sctimer/multi_state_pwm/cm4" doc="readme.txt">
      <description>The SCTImer multi-state project is a demonstration program of the SCTimer state machine. It shows how to set up events to be triggered in a certain stateand transitioning between states.State 0 has 2 events that generate a PWM signal, it also has an event linked to an input signal to transition to State 1.State 1 has 4 events that generate 2 PWM signals, it also has an event linked to an input signal to transition to State 0.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_multi_state_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_multi_state_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_pwm_with_dutycyle_change" folder="driver_examples/sctimer/pwm_with_dutycyle_change/cm4" doc="readme.txt">
      <description>This SCTIMer project is a demonstration program of the SDK SCTimer driver's PWM generation. It sets up a PWM signaland periodically updates the PWM signals dutycycle.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_pwm_with_dutycyle_change.uvprojx"/>
        <environment name="iar" load="iar/sctimer_pwm_with_dutycyle_change.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="sctimer_simple_pwm" folder="driver_examples/sctimer/simple_pwm/cm4" doc="readme.txt">
      <description>The SCTimer project is a simple demonstration program of the SDK SCTimer's driver capabiltiy to generate PWM signals.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sctimer_simple_pwm.uvprojx"/>
        <environment name="iar" load="iar/sctimer_simple_pwm.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="shell" folder="demo_apps/shell/cm4" doc="readme.txt">
      <description>The Shell Demo application demonstrates to control Leds by commands.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/shell.uvprojx"/>
        <environment name="iar" load="iar/shell.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="utick_wakeup" folder="demo_apps/utick_wakeup/cm4" doc="readme.txt">
      <description>The purpose of this demo is to show wakeup from deep sleep mode using MicroTick timer.The demo sets the MicroTick Timer as a wake up source and puts the device in deep-sleep mode. The MicroTick timer wakes up the device. </description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/utick_wakeup.uvprojx"/>
        <environment name="iar" load="iar/utick_wakeup.ewp"/>
      </project>
      <attributes/>
    </example>
    <example name="wwdt_example" folder="driver_examples/wwdt/cm4" doc="readme.txt">
      <description>The WDOG Example project is to demonstrate usage of the KSDK wdog driver.In this example,quick test is first implemented to test the wdog.And then after 5 times of refreshing the watchdog, a timeout reset is generated.</description>
      <board name="LPCXpresso54102" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/wwdt_example.uvprojx"/>
        <environment name="iar" load="iar/wwdt_example.ewp"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="lpcxpresso54102" Cversion="1.0.0" condition="device.LPC54102_AND_component.serial_manager_uart_AND_component.vusart_adapter_AND_device.LPC54102_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_AND_driver.power_AND_driver.vusart_AND_utility.debug_console">
      <description/>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
