<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>MIMXRT1180-EVK-A8974_ISSDK_Examples</name>
  <vendor>NXP</vendor>
  <description>Middleware issdk Examples Pack for MIMXRT1180-EVK-A8974</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.0" date="2024-07-17">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.16.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="ISSDK" vendor="NXP" version="3.0.0"/>
      <package name="MIMXRT1189_DFP" vendor="NXP" version="19.0.0"/>
      <package name="MIMXRT1180-EVK_BSP" vendor="NXP" version="19.0.0"/>
      <package name="CMSIS" vendor="ARM" version="5.9.0"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="MIMXRT1180-EVK-A8974">
      <description>i.MX RT1180 Evaluation Kit with shield board FRDM-STBI-A8974</description>
      <image small="boards/evkmimxrt1180_a8974/evkmimxrt1180_a8974.png"/>
      <mountedDevice Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1181CVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1181XVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1182CVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1182XVP2B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1187AVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1187CVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1187XVM8B" Dvendor="NXP:11"/>
      <compatibleDevice Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </board>
  </boards>
  <conditions>
    <condition id="device.MIMXRT1189.internal_condition">
      <accept Dname="MIMXRT1189CVM8B" Dvendor="NXP:11"/>
      <accept Dname="MIMXRT1189XVM8B" Dvendor="NXP:11"/>
    </condition>
    <condition id="BOARD_Project_Template.evkmimxrt1180_a8974.condition_id">
      <require condition="allOf.device.MIMXRT1189_startup, driver.lpuart, driver.clock, driver.common, driver.iomuxc, driver.misc, driver.rgpio, driver.xip_device, driver.pmu_1, driver.dcdc_soc, utility.debug_console, component.lpuart_adapter, component.serial_manager_uart, component.serial_manager, device=MIMXRT1189.internal_condition"/>
    </condition>
    <condition id="allOf.device.MIMXRT1189_startup, driver.lpuart, driver.clock, driver.common, driver.iomuxc, driver.misc, driver.rgpio, driver.xip_device, driver.pmu_1, driver.dcdc_soc, utility.debug_console, component.lpuart_adapter, component.serial_manager_uart, component.serial_manager, device=MIMXRT1189.internal_condition">
      <require Cclass="Device" Cgroup="Startup" Csub=""/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iomuxc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="misc"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="xip_device"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="pmu_1"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dcdc_soc"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lpuart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require condition="device.MIMXRT1189.internal_condition"/>
    </condition>
  </conditions>
  <examples>
    <example name="fxls8974cf_interrupt" folder="boards/evkmimxrt1180_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_interrupt/cm33" doc="readme.txt">
      <description>The FXLS8974CF Interrupt example application demonstrates the use of the FXLS8974CF sensor in interrupt Mode using I2C interface.The example demonstrates configuration sensor registers reguired to put the sensor in...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_interrupt.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_interrupt.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxls8974cf_motion_wakeup" folder="boards/evkmimxrt1180_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_motion_wakeup/cm33" doc="readme.txt">
      <description>The FXLS8974CF motion wakeup example application demonstrates motion detection and Auto-Wake/Sleep features of FXLS8974CF sensor.</description>
      <board name="MIMXRT1180-EVK-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_motion_wakeup.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_motion_wakeup.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxls8974cf_poll" folder="boards/evkmimxrt1180_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_poll/cm33" doc="readme.txt">
      <description>The FXLS8974CF POLL example application demonstrates the use of the FXLS8974CF sensor in polling Mode using I2C interface.The example demonstrates configuration sensor registers reguired to put the sensor in Poll...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_poll.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_poll.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="fxls8974cf_spi" folder="boards/evkmimxrt1180_a8974/issdk_examples/sensors/fxls8974cf/fxls8974cf_spi/cm33" doc="readme.txt">
      <description>The FXLS8974CF SPI example application demonstrates the use of the FXLS8974CF sensor in polling Mode using SPI interface.The example demonstrates configuration sensor registers reguired to put the sensor in Poll Mode...See more details in readme document.</description>
      <board name="MIMXRT1180-EVK-A8974" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/fxls8974cf_spi.uvprojx"/>
        <environment name="csolution" load="fxls8974cf_spi.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
  <components>
    <component Cclass="Sensors" Cgroup="ISSDK project template" Csub="evkmimxrt1180_a8974" Cvariant="evkmimxrt1180_a8974" Cversion="1.0.0" condition="BOARD_Project_Template.evkmimxrt1180_a8974.condition_id">
      <description>BOARD_Project_Template evkmimxrt1180_a8974</description>
      <RTE_Components_h>
#ifndef XIP_EXTERNAL_FLASH
#define XIP_EXTERNAL_FLASH 1
#endif
#ifndef XIP_BOOT_HEADER_ENABLE
#define XIP_BOOT_HEADER_ENABLE 1
#endif
</RTE_Components_h>
      <files>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1180_a8974/project_template/board.c" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1180_a8974/project_template/board.h" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1180_a8974/project_template/clock_config.c" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1180_a8974/project_template/clock_config.h" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1180_a8974/project_template/dcd.c" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1180_a8974/project_template/dcd.h" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1180_a8974/project_template/pin_mux.c" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1180_a8974/project_template/pin_mux.h" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="sourceC" attr="config" name="boards/evkmimxrt1180_a8974/project_template/peripherals.c" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="header" attr="config" name="boards/evkmimxrt1180_a8974/project_template/peripherals.h" version="1.0.0" projectpath="evkmimxrt1180_a8974/project_template"/>
        <file category="include" name="boards/evkmimxrt1180_a8974/project_template/"/>
      </files>
    </component>
  </components>
</package>
