<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFM32ZG_DFP</name>
  <description>Silicon Labs EFM32ZG Zero Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="4.4.0" date="2024-02-08">
      New device family EFM32PG28 and new OPNs for EFR32SG28, EFR32MG24, EFR32BG24, MGM21, RM21
    </release>
    <release version="4.3.0" date="2023-06-15">
      New device families EFR32SG23, EFR32SG28 and new OPNs for EFR32MG27, EFR32MG24, EFR32FG25, EFR32BG24, MGM24
    </release>
    <release version="4.2.0" date="2022-11-25">
      New device families EFR32ZG28, EFR32FG28 and EFR32MR21
    </release>
    <release version="4.1.1" date="2022-06-22">
      Add TrustZone support for Series2 Devices
    </release>
    <release version="4.1.0" date="2022-06-03">
      Update CMSIS version to 5.8.0. New device families EFR32BG27, EFR32MG27. Documentation updated for ZGM130S, MGM220P, EFR32PG23. Add Cortex-M33 Generic User Guide
    </release>
    <release version="4.0.2" date="2022-04-22">
      New OPNs for modules MGM24, for SoC EFR32MG24. New device families EFR32FG25. Documentation updated for EFM32PG22, EFR32BG24, EFR32MG24 and WGM160.
    </release>
    <release version="4.0.0" date="2022-01-24">
      New OPNs for modules MGM21 and MGM24, for SoC EFR32MG24. New device families EFR32BG24, BGM24. Documentation updated for EFR32FG23, EFR32ZG23 and EFM32PG23.
    </release>
    <release version="3.2.0" date="2021-06-16">
      New MCU's families efr32mg24, mgm24, wgm160.
    </release>
    <release version="3.1.0" date="2020-12-11">
      New MCU's families efr32pg22, efr32fg23, efr32zg23 and efm32pg23.
    </release>
    <release version="3.0.0" date="2020-09-14">
      Addition of EFR32ZG13L, EFR32ZG13S and removal of EFR32BG14P device families.
    </release>
    <release version="2.7.3" date="2020-03-23">
      The PRORTC timer peripheral has been added to EFR32xG13 and EFR32xG14 device families.
    </release>
    <release version="2.7.0" date="2019-11-12">
      Changed pack names and version number sequence.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFM32ZG</keyword>
    <keyword>EFM32</keyword>
    <keyword>Zero Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFM32ZG Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M0+" DcoreVersion="r0p1" Dfpu="NO_FPU" Dmpu="NO_MPU" Dtz="NO_TZ" Ddsp="NO_DSP" Dendian="Little-endian" Dclock="24000000"/>
      <book name="Documents/cortex_m0p_dgug.pdf"      title="Cortex-M0+ Generic User Guide"/>
      <book name="Documents/EFM32ZG-RM.pdf"  title="EFM32ZG Reference Manual"/>
      <description>
- 32-bit ARM Cortex-M0+ processor running at up to 24 MHz&#xD;&#xA;- Energy efficient and autonomous peripherals&#xD;&#xA;- Ultra low power Energy Modes with sub-uA operation&#xD;&#xA;- Fast wake-up time of only 2 us&#xD;&#xA;&#xD;&#xA;The EFM32ZG microcontroller series revolutionizes the 8- to 32-bit market with a combination of unmatched performance and ultra low power consumption in both active- and sleep modes. EFM32ZG devices consume as little as 114 uA/MHz in run mode.
      </description>

      <subFamily DsubFamily="EFM32ZG108">
        <book         name="Documents/efm32zg-datasheet.pdf"      title="EFM32ZG108 Data Sheet"/>
        <book         name="Documents/efm32zg-errata.pdf"         title="EFM32ZG108 Errata"/>
        <!-- *************************  Device 'EFM32ZG108F16'  ***************************** -->
        <device Dname="EFM32ZG108F16">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG108F16"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG108F16.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00004000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00004000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00004000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG108F32'  ***************************** -->
        <device Dname="EFM32ZG108F32">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG108F32"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG108F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG108F4'  ***************************** -->
        <device Dname="EFM32ZG108F4">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG108F4"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG108F4.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00001000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00001000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00001000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG108F8'  ***************************** -->
        <device Dname="EFM32ZG108F8">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG108F8"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG108F8.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00002000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00002000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00002000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32ZG110">
        <book         name="Documents/efm32zg-datasheet.pdf"      title="EFM32ZG110 Data Sheet"/>
        <book         name="Documents/efm32zg-errata.pdf"         title="EFM32ZG110 Errata"/>
        <!-- *************************  Device 'EFM32ZG110F16'  ***************************** -->
        <device Dname="EFM32ZG110F16">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG110F16"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG110F16.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00004000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00004000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00004000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG110F32'  ***************************** -->
        <device Dname="EFM32ZG110F32">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG110F32"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG110F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG110F4'  ***************************** -->
        <device Dname="EFM32ZG110F4">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG110F4"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG110F4.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00001000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00001000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00001000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG110F8'  ***************************** -->
        <device Dname="EFM32ZG110F8">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG110F8"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG110F8.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00002000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00002000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00002000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32ZG210">
        <book         name="Documents/efm32zg-datasheet.pdf"      title="EFM32ZG210 Data Sheet"/>
        <book         name="Documents/efm32zg-errata.pdf"         title="EFM32ZG210 Errata"/>
        <!-- *************************  Device 'EFM32ZG210F16'  ***************************** -->
        <device Dname="EFM32ZG210F16">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG210F16"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG210F16.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00004000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00004000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00004000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG210F32'  ***************************** -->
        <device Dname="EFM32ZG210F32">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG210F32"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG210F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG210F4'  ***************************** -->
        <device Dname="EFM32ZG210F4">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG210F4"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG210F4.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00001000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00001000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00001000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG210F8'  ***************************** -->
        <device Dname="EFM32ZG210F8">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG210F8"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG210F8.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00002000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00002000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00002000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFM32ZG222">
        <book         name="Documents/efm32zg-datasheet.pdf"      title="EFM32ZG222 Data Sheet"/>
        <book         name="Documents/efm32zg-errata.pdf"         title="EFM32ZG222 Errata"/>
        <!-- *************************  Device 'EFM32ZG222F16'  ***************************** -->
        <device Dname="EFM32ZG222F16">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG222F16"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG222F16.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00004000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00004000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00004000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG222F32'  ***************************** -->
        <device Dname="EFM32ZG222F32">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG222F32"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG222F32.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00008000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00001000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00008000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00008000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG222F4'  ***************************** -->
        <device Dname="EFM32ZG222F4">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG222F4"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG222F4.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00001000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00001000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00001000"  default="0"   style="IAR"/>
        </device>

        <!-- *************************  Device 'EFM32ZG222F8'  ***************************** -->
        <device Dname="EFM32ZG222F8">
          <compile header="Device/SiliconLabs/EFM32ZG/Include/em_device.h"  define="EFM32ZG222F8"/>
          <debug      svd="SVD/EFM32ZG/EFM32ZG222F8.svd"/>
          <memory     id="IROM1"                start="0x00000000"  size="0x00002000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00000800"  init   ="0"   default="1"/>
          <algorithm  name="Flash/EFM32ZG.FLM"  start="0x00000000"  size="0x00002000"  default="1"   style="Keil"/>
          <algorithm  name="Flash/FlashEFM32M0P.flash"  start="0x00000000"  size="0x00002000"  default="0"   style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFM32ZG">
      <description>Silicon Labs EFM32ZG device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFM32ZG*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="4.4.0" condition="EFM32ZG">
      <description>System Startup for Silicon Labs EFM32ZG device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFM32ZG/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFM32ZG/Include/em_device.h"/>
        <!-- startup files -->
        <file category="source"  name="Device/SiliconLabs/EFM32ZG/Source/GCC/startup_efm32zg.S" condition="Compiler GCC"   attr="config" version="4.4.0"/>
        <file category="source"  name="Device/SiliconLabs/EFM32ZG/Source/IAR/startup_efm32zg.s" condition="Compiler IAR"   attr="config" version="4.4.0"/>
        <!-- linker command file -->
        <file category="linkerScript"  name="Device/SiliconLabs/EFM32ZG/Source/GCC/efm32zg.ld" condition="Compiler GCC" attr="config" version="4.4.0"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFM32ZG/Source/system_efm32zg.c" attr="config" version="4.4.0"/>
      </files>
    </component>
  </components>
</package>
