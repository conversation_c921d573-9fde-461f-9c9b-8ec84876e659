<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>LPC54102_DFP</name>
  <vendor>NXP</vendor>
  <description>Device Family Pack for LPC54102</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="12.0.1" date="2019-09-23">NXP CMSIS packs based on MCUXpresso SDK 2.6.1</release>
    <release version="12.0.0" date="2019-06-10">NXP CMSIS packs based on MCUXpresso SDK 2.6.0</release>
    <release version="11.0.1" date="2019-04-26">Removed invalid entries from Software Content Register</release>
    <release version="11.0.0" date="2018-12-19">NXP CMSIS packs based on MCUXpresso SDK 2.5.0</release>
    <release version="10.0.3" date="2018-07-16">A problem with missing components from some example the projects were dependent on was fixed (KEX-4241); Incorrect path to linked libraries were fixed: a pack version was a static part of the path and hadn’t worked for pack versions other than v10.0.0 (KEX-4373); A problem with incorrectly defined XIP_BOOT_HEADER_ENABLE, XIP_BOOT_HEADER_DCD_ENABLE and XIP_EXTERNAL_FLASH preprocessor symbols was fixed (KEX-4553); Missing flash algorithms for MK27, MK28, LPC802 and LPC804 device family packs were added (KEX-4659).</release>
    <release version="10.0.2" date="2018-05-25">NXP CMSIS packs based on MCUXpresso SDK 2.4.0</release>
    <release version="10.0.1" date="2018-04-04">NXP CMSIS packs based on MCUXpresso SDK 2.3.1</release>
    <release version="10.0.0" date="2018-01-19">NXP CMSIS packs with updated Clear BSD license, based on MCUXpresso SDK 2.3.0. NXP CMSIS pack will maintain independent versioning starting with 10.0.0</release>
    <release version="2.3.0" date="2017-11-17">NXP MCUXpresso SDK 2.3.0 CMSIS packs with updated Clear BSD license</release>
  </releases>
  <keywords>
    <keyword>Device Family Pack</keyword>
  </keywords>
  <taxonomy>
    <description Cclass="Board Support" Cgroup="SDK Project Template">NXP MCUXpresso SDK Project Template</description>
    <description Cclass="Device" Cgroup="SDK Project Template">NXP MCUXpresso SDK RTE Device Project Template</description>
    <description Cclass="Device" Cgroup="SDK Drivers">NXP MCUXpresso SDK Peripheral Drivers</description>
    <description Cclass="Device" Cgroup="SDK Utilities">NXP MCUXpresso SDK Utilities</description>
    <description Cclass="Device" Cgroup="Startup">NXP MCUXpresso SDK Start up</description>
    <description Cclass="CMSIS Driver">NXP MCUXpresso SDK Peripheral CMSIS Drivers</description>
  </taxonomy>
  <requirements>
    <packages>
      <package name="CMSIS" vendor="ARM"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <devices>
    <family Dfamily="LPC54102" Dvendor="NXP:11">
      <debugconfig default="swd" clock="5000000" swj="true"/>
      <sequences>
        <sequence name="ResetCatchClear">
          <block>
      // System Control Space (SCS) offset as defined
      // in ARMv6-M/ARMv7-M. Reimplement this sequence
      // if the SCS is located at a different offset.
      __var SCS_Addr   = 0xE000E000;
      __var DEMCR_Addr = SCS_Addr + 0xDFC;
      __var value      = 0;

      // Disable Reset Vector Catch in DEMCR
      value = Read32(DEMCR_Addr);
      Write32(DEMCR_Addr, (value &amp; (~0x00000001)));

      //Clear BP0 and FPB
      Write32(0xE0002008, 0);                         // Clear BP0
      Write32(0xE0002000, 0x00000002);                // Disable FPB
    </block>
        </sequence>
        <sequence name="DebugCodeMemRemap">
          <block>
      Write32 (0x40000000, 2);                        // Setup remap: Interrupt vectors reside in flash
    </block>
        </sequence>
        <sequence name="TraceStart_CM4">
          <block>
      __var traceSWO = (__traceout &amp; 0x1) != 0;   // SWO Trace Selected?
    </block>
          <control if="traceSWO">
            <block>
        Sequence("EnableTraceSWO_CM4");               // Call SWO Trace Setup
      </block>
          </control>
        </sequence>
        <sequence name="EnableTraceSWO_CM4">
          <block>
      Write32 (0x40000220, 0x00002000);               // Enable IOCON peripheral clock
      Write32 (0x40000304, 0x00000000);               // Enable Trace clock divider
    </block>
          <control if="LPC541xx_SWO_Pin == 0">
            <block>
        Write32 (0x4000103C, 2);                      // Configure PIO0_15 IOCON for function 2
      </block>
          </control>
          <control if="LPC541xx_SWO_Pin == 1">
            <block>
        Write32 (0x40001084, 2);                      // Configure PIO1_1 IOCON for function 2
      </block>
          </control>
          <control if="LPC541xx_SWO_Pin == 2">
            <block>
        Write32 (0x40001028, 6);                      // Configure PIO0_10 IOCON for function 6
      </block>
          </control>
          <control if="LPC541xx_SWO_Pin == 3">
            <block>
        Write32 (0x40001020, 4);                      // Configure PIO0_8 IOCON for function 4
      </block>
          </control>
        </sequence>
        <sequence name="ResetCatchSet_LPC5410x_M4">
          <block>
      __var SCS_Addr   = 0xE000E000;
      __var DHCSR_Addr = SCS_Addr + 0xDF0;
      __var DEMCR_Addr = SCS_Addr + 0xDFC;
      __var value      = 0;
      __var FPB_BKPT_H = 0x80000000;
      __var FPB_BKPT_L = 0x40000000;
      __var FPB_COMP_M = 0x1FFFFFFC;
      __var FPB_KEY    = 0x00000002;
      __var FPB_ENABLE = 0x00000001;
      __var masterCPU  = 0x00000000;
      __var CPUCTRL    = 0x40000300;

      // Get MasterCPU
      value = Read32( CPUCTRL);                           //  CPU Control register: CPUCTRL (0x40000800)
      value = 0xC0C40000 | (value &amp; 0x0000FFFF);      //  Bit31:16 must be written as 0xC0C4 for the write to have an effect
      value = value &amp; 0xFFFF807D;                     //  Bit1, Bit7:14 should be 0
      masterCPU = value &amp; 0x00000001;
    </block>
          <control if="Dbg_CR == 0x00000000" info="Stop after bootloader disabled">
            <block>
        // Enable Reset Vector Catch in DEMCR
        value = Read32(DEMCR_Addr);
        Write32(DEMCR_Addr, (value | 0x00000001));
      </block>
          </control>
          <control if="Dbg_CR == 0x00000001" info="Stop after bootloader enabled">
            <block>
        // Run over Bootloader
        Write32(DEMCR_Addr, 0x00000000);                // Disable Reset Vector Catch
        Write32(0x40000000, 0x00000002);                // Map Flash to Vectors
      </block>
            <control if="masterCPU == 1" info="Cortex-M4 is the master CPU">
              <block>
          value = Read32 (0x00000004);                  // Read Reset Vector
        </block>
            </control>
            <control if="masterCPU == 0" info="Cortex-M4 is the slave CPU">
              <block>
          value = Read32 (sCPU_ImageEntry + 4);         // Read Reset Vector
        </block>
            </control>
            <control if="value &lt; 0x20000000" info="Set and enable breakpoint">
              <block>
          value = ((value &amp; 0x02) ? FPB_BKPT_H : FPB_BKPT_L) | (value &amp; FPB_COMP_M) | FPB_ENABLE ;
          Write32(0xE0002008, value);                    // Set BP0 to Reset Vector
          value = FPB_KEY | FPB_ENABLE;
          Write32(0xe0002000, value);                    // Enable FPB
        </block>
            </control>
            <control if="value &gt;= 0x20000000" info="Enable reset vector catch">
              <block>
        // Enable Reset Vector Catch in DEMCR
        value = Read32(DEMCR_Addr);
        Write32(DEMCR_Addr, (value | 0x00000001));
        </block>
            </control>
          </control>
          <block>
      Read32(DHCSR_Addr);                               // Read DHCSR to clear potentially set DHCSR.S_RESET_ST bit
    </block>
        </sequence>
        <sequence name="ResetCatchSet_LPC5410x_M0">
          <block>
      __var SCS_Addr   = 0xE000E000;
      __var DHCSR_Addr = SCS_Addr + 0xDF0;
      __var DEMCR_Addr = SCS_Addr + 0xDFC;
      __var value      = 0;
      __var FPB_BKPT_H = 0x80000000;
      __var FPB_BKPT_L = 0x40000000;
      __var FPB_COMP_M = 0x1FFFFFFC;
      __var FPB_KEY    = 0x00000002;
      __var FPB_ENABLE = 0x00000001;
      __var masterCPU  = 0x00000000;
      __var CPUCTRL    = 0x40000300;

      // Get MasterCPU
      value = Read32( CPUCTRL);                           //  CPU Control register: CPUCTRL (0x40000800)
      value = 0xC0C40000 | (value &amp; 0x0000FFFF);      //  Bit31:16 must be written as 0xC0C4 for the write to have an effect
      value = value &amp; 0xFFFF807D;                     //  Bit1, Bit7:14 should be 0
      masterCPU = value &amp; 0x00000001;
    </block>
          <control if="Dbg_CR == 0x00000000" info="Stop after bootloader disabled">
            <block>
        // Enable Reset Vector Catch in DEMCR
        value = Read32(DEMCR_Addr);
        Write32(DEMCR_Addr, (value | 0x00000001));
      </block>
          </control>
          <control if="Dbg_CR == 0x00000001" info="Stop after bootloader enabled">
            <block>
        // Run over Bootloader
        Write32(DEMCR_Addr, 0x00000000);                // Disable Reset Vector Catch
        Write32(0x40000000, 0x00000002);                // Map Flash to Vectors
      </block>
            <control if="masterCPU == 1" info="Cortex-M0 is the slave CPU">
              <block>
          value = Read32 (sCPU_ImageEntry + 4);         // Read Reset Vector
        </block>
            </control>
            <control if="masterCPU == 0" info="Cortex-M0 is the master CPU">
              <block>
          value = Read32 (0x00000004);                  // Read Reset Vector
        </block>
            </control>
            <control if="value &lt; 0x20000000" info="Set and enable breakpoint">
              <block>
          value = ((value &amp; 0x02) ? FPB_BKPT_H : FPB_BKPT_L) | (value &amp; FPB_COMP_M) | FPB_ENABLE ;
          Write32(0xE0002008, value);                   // Set BP0
          value = FPB_KEY | FPB_ENABLE;                 // Set BP0 to Reset Vector
          Write32(0xe0002000, value);                   // Enable FPB
        </block>
            </control>
            <control if="value &gt;= 0x20000000" info="Enable reset vector catch">
              <block>
        // Enable Reset Vector Catch in DEMCR
        value = Read32(DEMCR_Addr);
        Write32(DEMCR_Addr, (value | 0x00000001));
        </block>
            </control>
          </control>
          <block>
      Read32(DHCSR_Addr);                               // Read DHCSR to clear potentially set DHCSR.S_RESET_ST bit
    </block>
        </sequence>
        <sequence name="ResetProcessor_LPC5410x_M4">
          <block>
      __var value      = 0;
      __var masterCPU  = 0;
      __var resetCPU   = 0;
      __var CPUCTRL    = 0x40000300;
      __var CPBOOT     = 0x40000304;
      __var CPSTACK    = 0x40000308;
      __var slaveBOOT  = 0;
      __var slaveSTACK = 0;

      // Get MasterCPU
      value = Read32( CPUCTRL);                           //  CPU Control register: CPUCTRL (0x40000800)
      value = 0xC0C40000 | (value &amp; 0x0000FFFF);      //  Bit31:16 must be written as 0xC0C4 for the write to have an effect
      value = value &amp; 0xFFFF807D;                     //  Bit1, Bit7:14 should be 0
      masterCPU = value &amp; 0x00000001;
    </block>
          <control if="masterCPU == 1" info="Cortex-M4 is the master CPU">
            <block>
        Write32(0xE000ED0C, 0x05FA0001);                // Reset Cortex-M4, execute VECTRESET via AIRCR
      </block>
          </control>
          <control if="masterCPU == 0" info="Cortex-M4 is the slave CPU">
            <block>
        slaveBOOT = Read32( sCPU_ImageEntry + 4);
        slaveSTACK = Read32( sCPU_ImageEntry);

        __ap = 1;                                       // Access via masterCPU access port
        Write32( CPBOOT, slaveBOOT);                    // Set slave boot address
        Write32( CPSTACK, slaveSTACK);                  // Set slave stack address

        resetCPU = value | 0x00000010;
        Write32( CPUCTRL, resetCPU);                    // Reset Cortex-M0 via CPUCTRL, CM0RSTEN(Bit5)
        Write32( CPUCTRL, value);                       // Release Cortex-M0 from reset
        __ap = 0;
      </block>
          </control>
        </sequence>
        <sequence name="ResetProcessor_LPC5410x_M0">
          <block>
      __var value      = 0;
      __var masterCPU  = 0;
      __var resetCPU   = 0;
      __var CPUCTRL    = 0x40000300;
      __var CPBOOT     = 0x40000304;
      __var CPSTACK    = 0x40000308;
      __var slaveBOOT  = 0;
      __var slaveSTACK = 0;

      // Get MasterCPU
      value = Read32( CPUCTRL);                         //  CPU Control register: CPUCTRL (0x40000800)
      value = 0xC0C40000 | (value &amp; 0x0000FFFF);    //  Bit31:16 must be written as 0xC0C4 for the write to have an effect
      value = value &amp; 0xFFFF807D;                   //  Bit1, Bit7:14 should be 0
      masterCPU = value &amp; 0x00000001;
    </block>
          <control if="masterCPU == 1" info="Cortex-M0 is the slave CPU">
            <block>
        slaveBOOT = Read32( sCPU_ImageEntry + 4);
        slaveSTACK = Read32( sCPU_ImageEntry);

        __ap = 0;                                       // Access via masterCPU access port
        Write32( CPBOOT, slaveBOOT);                    // Set slave boot address
        Write32( CPSTACK, slaveSTACK);                  // Set slave stack address

        resetCPU = value | 0x00000020;
        Write32( CPUCTRL, resetCPU);                    // Reset Cortex-M0 via CPUCTRL, CM0RSTEN(Bit5)
        Write32( CPUCTRL, value);                       // Release Cortex-M0 from reset
        __ap = 1;
      </block>
          </control>
          <control if="masterCPU == 0" info="Cortex-M0 is the master CPU">
            <block>
        //Write32(0xE000ED0C, 0x05FA0001);              // Reset Cortex-M0, execute VECTRESET via AIRCR, !!! THERE IS NO VECTRESET IN AIRCR
                                                        // DOES NOT SUPPORTED
      </block>
          </control>
        </sequence>
      </sequences>
      <debugvars configfile="arm/LPC541xx.dbgconf">
  // Debug Access Variables, can be modified by user via copies of DBGCONF files as created by uVision. Also see sub-family level.
  __var LPC541xx_SWO_Pin      = 0;                    // Serial Wire Output pin: 0 = PIO0_15, 1 = PIO1_1
  __var Dbg_CR                = 0x00000000;           // DBG_CR
  __var sCPU_ImageEntry       = 0x00020000;           // Slave CPU Image Start Entry
</debugvars>
      <description>The LPC5410x are ARM Cortex-M4 based microcontrollers for embedded applications. These devices include an ARM Cortex-M0+ coprocessor, up to 104 KB of on-chip SRAM, up to 512 KB on-chip flash, five general-purpose timers, one SCTimer/PWM, one RTC/alarm timer, one 24-bit Multi-Rate Timer (MRT), a Windowed Watchdog Timer (WWDT), 3 USART, 2 SPI, and 3 I2C interface, and one 12-bit 5.0 Msamples/sec ADC. The ARM Cortex-M4 is a 32-bit core that offers system enhancements such as low power consumption, enhanced debug features, and a high level of support block integration. The ARM Cortex-M4 CPU incorporates a 3-stage pipeline, uses a Harvard architecture with separate local instruction and data buses as well as a third bus for peripherals, and includes an internal prefetch unit that supports speculative branching. The ARM Cortex-M4 supports single-cycle digital signal processing and SIMD instructions. A hardware floating-point unit is integrated in the core. The ARM Cortex-M0+ coprocessor is an energy-efficient and easy-to-use 32-bit core which is code and tool-compatible with the Cortex-M4 core. The Cortex-M0+ coprocessor offers up to 100 MHz performance with a simple instruction set and reduced code size.</description>
      <device Dname="LPC54102J512">
        <processor Pname="cm0plus" Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="150000000"/>
        <processor Pname="cm4" Dcore="Cortex-M4" Dfpu="SP_FPU" Dmpu="MPU" Dendian="Little-endian" Dclock="150000000"/>
        <environment Pname="cm0plus" name="iar">
          <file category="linkerfile" name="iar/LPC54102J512_cm0plus.icf"/>
        </environment>
        <environment Pname="cm4" name="iar">
          <file category="linkerfile" name="iar/LPC54102J512_cm4.icf"/>
        </environment>
        <sequences>
          <sequence name="ResetProcessor" Pname="cm4">
            <block>
      Sequence("ResetProcessor_LPC5410x_M4");
    </block>
          </sequence>
          <sequence name="ResetProcessor" Pname="cm0plus">
            <block>
      Sequence("ResetProcessor_LPC5410x_M0");
    </block>
          </sequence>
          <sequence name="ResetCatchSet" Pname="cm4">
            <block>
      Sequence("ResetCatchSet_LPC5410x_M4");
    </block>
          </sequence>
          <sequence name="ResetCatchSet" Pname="cm0plus">
            <block>
      Sequence("ResetCatchSet_LPC5410x_M0");
    </block>
          </sequence>
          <sequence name="TraceStart" Pname="cm4">
            <block>
      Sequence("TraceStart_CM4");
    </block>
          </sequence>
          <sequence name="TraceStart" Pname="cm0plus">
            <block>

    </block>
          </sequence>
        </sequences>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x080000" access="rx" default="1" startup="1"/>
        <memory name="SRAM0" start="0x02000000" size="0x010000" access="rw" default="1"/>
        <memory name="SRAM1" start="0x02010000" size="0x8000" access="rw" default="1"/>
        <memory name="SRAM2" start="0x03400000" size="0x2000" access="rw" default="1"/>
        <algorithm Pname="cm4" name="arm/LPC5410x_512.FLM" start="0x00000000" size="0x00080000" RAMstart="0x02000000" RAMsize="0x00001000" default="1"/>
        <debug svd="LPC54102_cm0plus.xml" Pname="cm0plus" __dp="0" __ap="1"/>
        <debug svd="LPC54102_cm4.xml" Pname="cm4" __dp="0" __ap="0"/>
        <variant Dvariant="LPC54102J512BD64">
          <compile Pname="cm0plus" header="fsl_device_registers.h" define="CPU_LPC54102J512BD64_cm0plus"/>
          <compile Pname="cm4" header="fsl_device_registers.h" define="CPU_LPC54102J512BD64_cm4"/>
        </variant>
        <variant Dvariant="LPC54102J512UK49">
          <compile Pname="cm0plus" header="fsl_device_registers.h" define="CPU_LPC54102J512UK49_cm0plus"/>
          <compile Pname="cm4" header="fsl_device_registers.h" define="CPU_LPC54102J512UK49_cm4"/>
        </variant>
      </device>
      <device Dname="LPC54102J256">
        <processor Pname="cm0plus" Dcore="Cortex-M0+" Dfpu="NO_FPU" Dmpu="NO_MPU" Dendian="Little-endian" Dclock="150000000"/>
        <processor Pname="cm4" Dcore="Cortex-M4" Dfpu="SP_FPU" Dmpu="MPU" Dendian="Little-endian" Dclock="150000000"/>
        <environment Pname="cm0plus" name="iar">
          <file category="linkerfile" name="iar/LPC54102J256_cm0plus.icf"/>
        </environment>
        <environment Pname="cm4" name="iar">
          <file category="linkerfile" name="iar/LPC54102J256_cm4.icf"/>
        </environment>
        <sequences>
          <sequence name="ResetProcessor" Pname="cm4">
            <block>
      Sequence("ResetProcessor_LPC5410x_M4");
    </block>
          </sequence>
          <sequence name="ResetProcessor" Pname="cm0plus">
            <block>
      Sequence("ResetProcessor_LPC5410x_M0");
    </block>
          </sequence>
          <sequence name="ResetCatchSet" Pname="cm4">
            <block>
      Sequence("ResetCatchSet_LPC5410x_M4");
    </block>
          </sequence>
          <sequence name="ResetCatchSet" Pname="cm0plus">
            <block>
      Sequence("ResetCatchSet_LPC5410x_M0");
    </block>
          </sequence>
          <sequence name="TraceStart" Pname="cm4">
            <block>
      Sequence("TraceStart_CM4");
    </block>
          </sequence>
          <sequence name="TraceStart" Pname="cm0plus">
            <block>

    </block>
          </sequence>
        </sequences>
        <memory name="PROGRAM_FLASH" start="0x00000000" size="0x040000" access="rx" default="1" startup="1"/>
        <memory name="SRAM0" start="0x02000000" size="0x010000" access="rw" default="1"/>
        <memory name="SRAM1" start="0x02010000" size="0x8000" access="rw" default="1"/>
        <memory name="SRAM2" start="0x03400000" size="0x2000" access="rw" default="1"/>
        <algorithm Pname="cm4" name="arm/LPC5410x_256.FLM" start="0x00000000" size="0x00040000" RAMstart="0x02000000" RAMsize="0x00001000" default="1"/>
        <debug svd="LPC54102_cm0plus.xml" Pname="cm0plus" __dp="0" __ap="1"/>
        <debug svd="LPC54102_cm4.xml" Pname="cm4" __dp="0" __ap="0"/>
        <variant Dvariant="LPC54102J256BD64">
          <compile Pname="cm0plus" header="fsl_device_registers.h" define="CPU_LPC54102J256BD64_cm0plus"/>
          <compile Pname="cm4" header="fsl_device_registers.h" define="CPU_LPC54102J256BD64_cm4"/>
        </variant>
        <variant Dvariant="LPC54102J256UK49">
          <compile Pname="cm0plus" header="fsl_device_registers.h" define="CPU_LPC54102J256UK49_cm0plus"/>
          <compile Pname="cm4" header="fsl_device_registers.h" define="CPU_LPC54102J256UK49_cm4"/>
        </variant>
      </device>
    </family>
  </devices>
  <conditions>
    <condition id="device.LPC54102_AND_component.serial_manager_uart_AND_component.vusart_adapter_AND_device.LPC54102_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_AND_driver.power_AND_driver.vusart_AND_utility.debug_console">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="vusart_adapter"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="iocon"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="gpio"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="Startup"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.lpc_dma">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.common_AND_driver.ctimer">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.common">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.LPC54102_AND_component.lists_AND_driver.common">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.vusart">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.common_AND_driver.vusart">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
    </condition>
    <condition id="CMSIS_Include_core_cm0plus_OR_CMSIS_Include_core_cm4">
      <accept Cclass="CMSIS" Cgroup="CORE"/>
      <accept Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="device.LPC54102_AND_CMSIS_Include_core_cm0plus_OR_CMSIS_Include_core_cm4">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require condition="CMSIS_Include_core_cm0plus_OR_CMSIS_Include_core_cm4"/>
    </condition>
    <condition id="core_type.cm0p">
      <require Dcore="Cortex-M0+"/>
    </condition>
    <condition id="core_type.cm4f">
      <require Dcore="Cortex-M4"/>
    </condition>
    <condition id="core_type.cm0p_AND_mdk">
      <require Tcompiler="ARMCC"/>
      <require Dcore="Cortex-M0+"/>
    </condition>
    <condition id="core_type.cm4f_AND_mdk">
      <require Tcompiler="ARMCC"/>
      <require Dcore="Cortex-M4"/>
    </condition>
    <condition id="core_type.cm0p_AND_iar">
      <require Tcompiler="IAR"/>
      <require Dcore="Cortex-M0+"/>
    </condition>
    <condition id="core_type.cm4f_AND_iar">
      <require Tcompiler="IAR"/>
      <require Dcore="Cortex-M4"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.common_AND_driver.power">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="power"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.LPC54102_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.lpc_i2c_dma">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_dma"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
    </condition>
    <condition id="device.LPC54102_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.vspi_AND_driver.vspi_dma">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" Capiversion="2.2.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi_dma"/>
    </condition>
    <condition id="device.LPC54102_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.vusart_AND_driver.vusart_dma">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="CMSIS Driver" Cgroup="USART" Capiversion="2.3.0"/>
      <require Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart_dma"/>
    </condition>
    <condition id="device.LPC54102_AND_device.LPC54102_CMSIS_AND_driver.clock_AND_driver.reset">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="clock"/>
      <require Cclass="Device" Cgroup="CMSIS" Csub="LPC54102_header"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="reset"/>
    </condition>
    <condition id="device.LPC54102_AND_CMSIS_Driver_Include.I2C">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" Capiversion="2.3.0"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.common_AND_driver.inputmux_connections">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="inputmux_connections"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.lpc_dma_AND_driver.lpc_i2c">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="i2c"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.lpc_dma_AND_driver.vspi">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="spi"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.LPC54102_AND_driver.lpc_dma_AND_driver.vusart">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="usart"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="dma"/>
    </condition>
    <condition id="device.LPC54102_AND_utility.debug_console">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
    </condition>
    <condition id="device.LPC54102_AND_component.serial_manager_AND_driver.common">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
    <condition id="device.LPC54102_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512" Dvariant="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J512UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256BD64" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256" Dvariant="LPC54102J256UK49" Dvendor="NXP:11"/>
      <accept Dname="LPC54102J256UK49" Dvendor="NXP:11"/>
      <require Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="lists"/>
      <require Cclass="Device" Cgroup="SDK Drivers" Csub="common"/>
    </condition>
  </conditions>
  <components>
    <component Cclass="Board Support" Cgroup="SDK Project Template" Csub="project_template" Cvariant="LPC54102" Cversion="1.0.0" condition="device.LPC54102_AND_component.serial_manager_uart_AND_component.vusart_adapter_AND_device.LPC54102_startup_AND_driver.clock_AND_driver.common_AND_driver.lpc_gpio_AND_driver.lpc_iocon_AND_driver.power_AND_driver.vusart_AND_utility.debug_console" isDefaultVariant="1">
      <description/>
      <files>
        <file category="sourceC" attr="config" name="project_template/board.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/board.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/clock_config.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/clock_config.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/peripherals.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/peripherals.h" version="1.0.0"/>
        <file category="sourceC" attr="config" name="project_template/pin_mux.c" version="1.0.0"/>
        <file category="header" attr="config" name="project_template/pin_mux.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Project Template" Csub="RTE_Device" Cversion="1.0.0" condition="device.LPC54102_AND_driver.lpc_dma">
      <description/>
      <files>
        <file category="header" attr="config" name="template/RTE_Device.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer_adapter" Cversion="1.0.0" condition="device.LPC54102_AND_driver.common_AND_driver.ctimer">
      <description/>
      <files>
        <file category="sourceC" name="components/timer/ctimer_adapter.c" version="1.0.0"/>
        <file category="header" name="components/timer/timer.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lists" Cversion="1.0.0" condition="device.LPC54102_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/lists/generic_list.c" version="1.0.0"/>
        <file category="header" name="components/lists/generic_list.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager" Cversion="1.0.0" condition="device.LPC54102_AND_component.lists_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_manager.c" version="1.0.0"/>
        <file category="header" name="components/serial_manager/serial_manager.h" version="1.0.0"/>
        <file category="header" name="components/serial_manager/serial_port_internal.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="serial_manager_uart" Cversion="1.0.0" condition="device.LPC54102_AND_driver.vusart">
      <description/>
      <files>
        <file category="sourceC" name="components/serial_manager/serial_port_uart.c" version="1.0.0"/>
        <file category="header" name="components/serial_manager/serial_port_uart.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="vusart_adapter" Cversion="1.0.0" condition="device.LPC54102_AND_driver.common_AND_driver.vusart">
      <description/>
      <files>
        <file category="header" name="components/uart/uart.h" version="1.0.0"/>
        <file category="sourceC" name="components/uart/vusart_adapter.c" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="CMSIS" Csub="LPC54102_header" Cversion="1.0.0" condition="device.LPC54102_AND_CMSIS_Include_core_cm0plus_OR_CMSIS_Include_core_cm4">
      <description/>
      <files>
        <file condition="core_type.cm0p" category="header" name="LPC54102_cm0plus.h" version="1.0.0"/>
        <file condition="core_type.cm0p" category="header" name="LPC54102_cm0plus_features.h" version="1.0.0"/>
        <file condition="core_type.cm4f" category="header" name="LPC54102_cm4.h" version="1.0.0"/>
        <file condition="core_type.cm4f" category="header" name="LPC54102_cm4_features.h" version="1.0.0"/>
        <file category="header" name="fsl_device_registers.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0">
      <description/>
      <files>
        <file condition="core_type.cm0p_AND_mdk" category="sourceAsm" attr="config" name="arm/startup_LPC54102_cm0plus.s" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_mdk" category="sourceAsm" attr="config" name="arm/startup_LPC54102_cm4.s" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_iar" category="sourceAsm" attr="config" name="iar/startup_LPC54102_cm0plus.s" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_iar" category="sourceAsm" attr="config" name="iar/startup_LPC54102_cm4.s" version="1.0.0"/>
        <file condition="core_type.cm0p" category="sourceC" attr="config" name="system_LPC54102_cm0plus.c" version="1.0.0"/>
        <file condition="core_type.cm0p" category="header" attr="config" name="system_LPC54102_cm0plus.h" version="1.0.0"/>
        <file condition="core_type.cm4f" category="sourceC" attr="config" name="system_LPC54102_cm4.c" version="1.0.0"/>
        <file condition="core_type.cm4f" category="header" attr="config" name="system_LPC54102_cm4.h" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J256_cm0plus.scf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J256_cm0plus_ram.scf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J256_cm4.scf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J256_cm4_ram.scf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J512_cm0plus.scf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J512_cm0plus_ram.scf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J512_cm4.scf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_mdk" category="linkerScript" attr="config" name="arm/LPC54102J512_cm4_ram.scf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J256_cm0plus.icf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J256_cm0plus_ram.icf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J256_cm4.icf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J256_cm4_ram.icf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J512_cm0plus.icf" version="1.0.0"/>
        <file condition="core_type.cm0p_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J512_cm0plus_ram.icf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J512_cm4.icf" version="1.0.0"/>
        <file condition="core_type.cm4f_AND_iar" category="linkerScript" attr="config" name="iar/LPC54102J512_cm4_ram.icf" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="clock" Cversion="2.1.0" condition="device.LPC54102_AND_driver.common_AND_driver.power">
      <description>Clock Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_clock.c" version="2.1.0"/>
        <file category="header" name="drivers/fsl_clock.h" version="2.1.0"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C" Csub="lpc_i2c_cmsis" Cversion="2.0.0" condition="device.LPC54102_AND_CMSIS_Driver_Include.I2C_AND_RTE_Device_AND_driver.lpc_i2c_dma">
      <description>I2C CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_i2c_cmsis.c" version="2.0.0"/>
        <file category="header" name="cmsis_drivers/fsl_i2c_cmsis.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI" Csub="vspi_cmsis" Cversion="2.0.0" condition="device.LPC54102_AND_CMSIS_Driver_Include.SPI_AND_RTE_Device_AND_driver.vspi_AND_driver.vspi_dma">
      <description>SPI CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_spi_cmsis.c" version="2.0.0"/>
        <file category="header" name="cmsis_drivers/fsl_spi_cmsis.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART" Csub="vusart_cmsis" Cversion="2.0.0" condition="device.LPC54102_AND_CMSIS_Driver_Include.USART_AND_RTE_Device_AND_driver.vusart_AND_driver.vusart_dma">
      <description>USART CMSIS Driver</description>
      <files>
        <file category="sourceC" name="cmsis_drivers/fsl_usart_cmsis.c" version="2.0.0"/>
        <file category="header" name="cmsis_drivers/fsl_usart_cmsis.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="common" Cversion="2.1.3" condition="device.LPC54102_AND_device.LPC54102_CMSIS_AND_driver.clock_AND_driver.reset">
      <description>COMMON Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_common.c" version="2.1.3"/>
        <file category="header" name="drivers/fsl_common.h" version="2.1.3"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ctimer" Cversion="2.0.2" condition="device.LPC54102_AND_driver.common">
      <description>CTimer Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_ctimer.c" version="2.0.2"/>
        <file category="header" name="drivers/fsl_ctimer.h" version="2.0.2"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="flashiap" Cversion="2.0.0" condition="device.LPC54102_AND_driver.common">
      <description>FLASHIAP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_flashiap.c" version="2.0.0"/>
        <file category="header" name="drivers/fsl_flashiap.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="fmeas" Cversion="2.1.0" condition="device.LPC54102_AND_driver.common">
      <description>FMEAS Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_fmeas.c" version="2.1.0"/>
        <file category="header" name="drivers/fsl_fmeas.h" version="2.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ft5406" Cversion="1.0.0" condition="device.LPC54102_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/ft5406/fsl_ft5406.c" version="1.0.0"/>
        <file category="header" name="components/ft5406/fsl_ft5406.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ft6x06" Cversion="1.0.0" condition="device.LPC54102_AND_CMSIS_Driver_Include.I2C">
      <description/>
      <files>
        <file category="sourceC" name="components/ft6x06/fsl_ft6x06.c" version="1.0.0"/>
        <file category="header" name="components/ft6x06/fsl_ft6x06.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gint" Cversion="2.0.1" condition="device.LPC54102_AND_driver.common">
      <description>GINT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_gint.c" version="2.0.1"/>
        <file category="header" name="drivers/fsl_gint.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="iap" Cversion="2.0.3" condition="device.LPC54102_AND_driver.common">
      <description>IAP Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_iap.c" version="2.0.3"/>
        <file category="header" name="drivers/fsl_iap.h" version="2.0.3"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="ili9341" Cversion="1.0.0" condition="device.LPC54102_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="components/ili9341/fsl_ili9341.c" version="1.0.0"/>
        <file category="header" name="components/ili9341/fsl_ili9341.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="inputmux" Cversion="2.0.1" condition="device.LPC54102_AND_driver.common_AND_driver.inputmux_connections">
      <description>INPUTMUX Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_inputmux.c" version="2.0.1"/>
        <file category="header" name="drivers/fsl_inputmux.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="inputmux_connections" Cversion="2.0.1" condition="device.LPC54102_AND_driver.common">
      <description>Inputmux_connections Driver</description>
      <files>
        <file category="header" name="drivers/fsl_inputmux_connections.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="adc" Cversion="2.3.2" condition="device.LPC54102_AND_driver.common">
      <description>ADC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_adc.c" version="2.3.2"/>
        <file category="header" name="drivers/fsl_adc.h" version="2.3.2"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_crc" Cversion="2.0.1" condition="device.LPC54102_AND_driver.common">
      <description>CRC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_crc.c" version="2.0.1"/>
        <file category="header" name="drivers/fsl_crc.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="dma" Cversion="2.4.0" condition="device.LPC54102_AND_driver.common">
      <description>DMA Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_dma.c" version="2.4.0"/>
        <file category="header" name="drivers/fsl_dma.h" version="2.4.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="gpio" Cversion="2.1.4" condition="device.LPC54102_AND_driver.common">
      <description>GPIO Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_gpio.c" version="2.1.4"/>
        <file category="header" name="drivers/fsl_gpio.h" version="2.1.4"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c" Cversion="2.0.4" condition="device.LPC54102_AND_driver.common">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c.c" version="2.0.4"/>
        <file category="header" name="drivers/fsl_i2c.h" version="2.0.4"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="i2c_dma" Cversion="2.0.4" condition="device.LPC54102_AND_driver.lpc_dma_AND_driver.lpc_i2c">
      <description>I2C Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_i2c_dma.c" version="2.0.4"/>
        <file category="header" name="drivers/fsl_i2c_dma.h" version="2.0.4"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="iocon" Cversion="2.1.1" condition="device.LPC54102_AND_driver.common">
      <description>IOCON Driver</description>
      <files>
        <file category="header" name="drivers/fsl_iocon.h" version="2.1.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="lpc_rtc" Cversion="2.0.0" condition="device.LPC54102_AND_driver.common">
      <description>RTC Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rtc.c" version="2.0.0"/>
        <file category="header" name="drivers/fsl_rtc.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mailbox" Cversion="2.0.0" condition="device.LPC54102_AND_driver.common">
      <description>MAILBOX Driver</description>
      <files>
        <file category="header" name="drivers/fsl_mailbox.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="mrt" Cversion="2.0.1" condition="device.LPC54102_AND_driver.common">
      <description>MRT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_mrt.c" version="2.0.1"/>
        <file category="header" name="drivers/fsl_mrt.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="pint" Cversion="2.1.4" condition="device.LPC54102_AND_driver.common">
      <description>PINT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_pint.c" version="2.1.4"/>
        <file category="header" name="drivers/fsl_pint.h" version="2.1.4"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="power" Cversion="2.0.0" condition="device.LPC54102_AND_driver.common">
      <description>Power driver with Power Lib Hard ABI</description>
      <files>
        <file condition="core_type.cm4f_AND_mdk" category="library" name="arm/keil_lib_power.lib" version="2.0.0"/>
        <file condition="core_type.cm0p_AND_mdk" category="library" name="arm/keil_lib_power_m0.lib" version="2.0.0"/>
        <file category="sourceC" name="drivers/fsl_power.c" version="2.0.0"/>
        <file category="header" name="drivers/fsl_power.h" version="2.0.0"/>
        <file condition="core_type.cm4f_AND_iar" category="library" name="iar/iar_lib_power.a" version="2.0.0"/>
        <file condition="core_type.cm0p_AND_iar" category="library" name="iar/iar_lib_power_m0.a" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="reset" Cversion="2.0.1" condition="device.LPC54102_AND_driver.common">
      <description>Reset Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_reset.c" version="2.0.1"/>
        <file category="header" name="drivers/fsl_reset.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="rit" Cversion="2.1.0" condition="device.LPC54102_AND_driver.common">
      <description>RIT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_rit.c" version="2.1.0"/>
        <file category="header" name="drivers/fsl_rit.h" version="2.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="sctimer" Cversion="2.1.1" condition="device.LPC54102_AND_driver.common">
      <description>SCT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_sctimer.c" version="2.1.1"/>
        <file category="header" name="drivers/fsl_sctimer.h" version="2.1.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="utick" Cversion="2.0.2" condition="device.LPC54102_AND_driver.common">
      <description>UTICK Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_utick.c" version="2.0.2"/>
        <file category="header" name="drivers/fsl_utick.h" version="2.0.2"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi" Cversion="2.0.2" condition="device.LPC54102_AND_driver.common">
      <description>SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_spi.c" version="2.0.2"/>
        <file category="header" name="drivers/fsl_spi.h" version="2.0.2"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="spi_dma" Cversion="2.0.1" condition="device.LPC54102_AND_driver.lpc_dma_AND_driver.vspi">
      <description>SPI Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_spi_dma.c" version="2.0.1"/>
        <file category="header" name="drivers/fsl_spi_dma.h" version="2.0.1"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="usart" Cversion="2.1.0" condition="device.LPC54102_AND_driver.common">
      <description>USART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_usart.c" version="2.1.0"/>
        <file category="header" name="drivers/fsl_usart.h" version="2.1.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="usart_dma" Cversion="2.0.0" condition="device.LPC54102_AND_driver.lpc_dma_AND_driver.vusart">
      <description>USART Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_usart_dma.c" version="2.0.0"/>
        <file category="header" name="drivers/fsl_usart_dma.h" version="2.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Drivers" Csub="wwdt" Cversion="2.1.3" condition="device.LPC54102_AND_driver.common">
      <description>WWDT Driver</description>
      <files>
        <file category="sourceC" name="drivers/fsl_wwdt.c" version="2.1.3"/>
        <file category="header" name="drivers/fsl_wwdt.h" version="2.1.3"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="assert" Cversion="1.0.0" condition="device.LPC54102_AND_utility.debug_console">
      <description/>
      <files>
        <file category="sourceC" name="utilities/fsl_assert.c" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="debug_console" Cversion="1.0.0" condition="device.LPC54102_AND_component.serial_manager_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="utilities/debug_console/fsl_debug_console.c" version="1.0.0"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console.h" version="1.0.0"/>
        <file category="header" name="utilities/debug_console/fsl_debug_console_conf.h" version="1.0.0"/>
        <file category="sourceC" name="utilities/str/fsl_str.c" version="1.0.0"/>
        <file category="header" name="utilities/str/fsl_str.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="notifier" Cversion="1.0.0" condition="device.LPC54102_AND_driver.common">
      <description/>
      <files>
        <file category="sourceC" name="utilities/fsl_notifier.c" version="1.0.0"/>
        <file category="header" name="utilities/fsl_notifier.h" version="1.0.0"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="SDK Utilities" Csub="shell" Cversion="1.0.0" condition="device.LPC54102_AND_component.lists_AND_driver.common_AND_utility.debug_console">
      <description/>
      <files>
        <file category="sourceC" name="utilities/fsl_shell.c" version="1.0.0"/>
        <file category="header" name="utilities/fsl_shell.h" version="1.0.0"/>
      </files>
    </component>
  </components>
</package>
