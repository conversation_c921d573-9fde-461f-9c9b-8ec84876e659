<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.7.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>SiliconLabs</vendor>
  <url>https://www.silabs.com/documents/public/cmsis-packs/</url>
  <name>GeckoPlatform_EFR32MG29_DFP</name>
  <description>Silicon Labs EFR32MG29 Mighty Gecko Series Device Support.</description>
  <license>License/license.md</license>

  <releases>
    <release version="2025.6.0" date="2025-07-25">
      New device families for EFR32BG29, EFR32MG29 and new modules for BGM26, MGM27. New OPNs for Socs EFR32BG22, EFR32BG24, EFR32FG25.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Silicon Labs</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Silicon Labs</keyword>
    <keyword>EFR32MG29</keyword>
    <keyword>EFR32</keyword>
    <keyword>Mighty Gecko</keyword>
    <keyword>Gecko</keyword>
  </keywords>

  <devices>
    <family Dfamily="EFR32MG29 Series" Dvendor="Silicon Labs:21">
      <processor Dcore="Cortex-M33" DcoreVersion="r0p4" Dfpu="FPU" Dmpu="MPU" Dtz="TZ" Ddsp="DSP" Dendian="Little-endian" Dclock="38400000"/>
      <book name="Documents/cortex_m33_dgug.pdf"      title="Cortex-M33 Generic User Guide"/>
      <description>
- 32-bit ARM Cortex M33 core with up to 76.8 MHz maximum operating frequency&#xD;&#xA;- Scalable Memory and Radio configuration options available in QFN and WLCSP packaging&#xD;&#xA;- Energy-efficient radio core with low active and sleep currents&#xD;&#xA;- Secure Vault (RTSL)&#xD;&#xA;- Integrated PA with up to 8 dBm transmit power&#xD;&#xA;- DC-DC supporting buck (1.8-3.8 V) or boost (0.8 - 1.7 V) operation&#xD;&#xA;
      </description>

      <subFamily DsubFamily="EFR32MG29B140">
        <!-- *************************  Device 'EFR32MG29B140F1024IM40'  ***************************** -->
        <device Dname="EFR32MG29B140F1024IM40">
          <compile header="Device/SiliconLabs/EFR32MG29/Include/em_device.h"  define="EFR32MG29B140F1024IM40"/>
          <debug      svd="SVD/EFR32MG29/EFR32MG29B140F1024IM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

      <subFamily DsubFamily="EFR32MG29B230">
        <!-- *************************  Device 'EFR32MG29B230F1024CM40'  ***************************** -->
        <device Dname="EFR32MG29B230F1024CM40">
          <compile header="Device/SiliconLabs/EFR32MG29/Include/em_device.h"  define="EFR32MG29B230F1024CM40"/>
          <debug      svd="SVD/EFR32MG29/EFR32MG29B230F1024CM40.svd"/>
          <memory     id="IROM1"                start="0x08000000"  size="0x00100000"  startup="1"   default="1"/>
          <memory     id="IRAM1"                start="0x20000000"  size="0x00040000"  init   ="0"   default="1"/>
          <algorithm  name="Flash/GECKOS2C3.FLM"  start="0x08000000"  size="0x00100000"  RAMstart="0x20000000" RAMsize="0x80000" default="1" style="Keil"/>
          <algorithm  name="Flash/FlashGECKOS2C3.flash"  start="0x08000000"  size="0x00100000"  default="0"  style="IAR"/>
        </device>

      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler GCC">
      <require Tcompiler="GCC"/>
    </condition>
    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device Conditions -->
    <condition id="EFR32MG29">
      <description>Silicon Labs EFR32MG29 device series</description>
      <require Dvendor="Silicon Labs:21" Dname="EFR32MG29*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="2025.6.0" condition="EFR32MG29">
      <description>System Startup for Silicon Labs EFR32MG29 device series</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/SiliconLabs/EFR32MG29/Include/"/>
        <file category="header"  name="Device/SiliconLabs/EFR32MG29/Include/em_device.h"/>
        <!-- system file -->
        <file category="source"  name="Device/SiliconLabs/EFR32MG29/Source/system_efr32mg29.c" attr="config" version="2025.6.0"/>
      </files>
    </component>
  </components>
</package>
