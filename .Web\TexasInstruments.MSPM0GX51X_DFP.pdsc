<?xml version="1.0" encoding="utf-8"?>

<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
    <vendor>TexasInstruments</vendor>
    <name>MSPM0GX51X_DFP</name>
    <description>Device Family Pack for Texas Instruments MSPM0GX51X Series</description>
    <url>https://software-dl.ti.com/msp430/esd/MSPM0-CMSIS/MSPM0GX51X/latest/exports/</url>
    <supportContact>https://e2e.ti.com/support/microcontrollers/</supportContact>
    <license>01_Pack/license.txt</license>

    <releases>
        <release version="1.0.0" date="2024-08-26">
        Initial release of MSPM0GX51X series device familiy pack.
        New device support:
            * MSPM0G351X
            * MSPM0G151X
        </release>
    </releases>

    <keywords>
        <!-- keywords for indexing -->
        <keyword>Texas Instruments</keyword>
        <keyword>MSPM0</keyword>
        <keyword>MSPM0G</keyword>
        <keyword>MSPM0GXX</keyword>
        <keyword>MSPM0G351X</keyword>
        <keyword>MSPM0G151X</keyword>
        <keyword>Device Support</keyword>
        <keyword>Device Family Package Texas Instruments</keyword>
      </keywords>
  
    <!-- devices section (mandatory for Device Family Packs) -->
    <devices>
        <family Dfamily="MSPM0GX51X Series" Dvendor="Texas Instruments:16">
            <processor Dcore="Cortex-M0+" DcoreVersion="r0p1" Dfpu="NO_FPU" Dclock="80000000" Dmpu="MPU" Dendian="Little-endian"/>
            <debugconfig default="swd" clock="5000000" swj="1"/>
            <book name="https://developer.arm.com/documentation/dui0662/latest/" title="Cortex-M0+ Generic User Guide"/>
            <description>
The MSPM0G3xxx and MSPM0G1xxx microcontrollers (MCUs) are part of MSP's
highly-integrated, low-power 32-bit MCU family based on the enhanced Arm®
Cortex®-M0+ 32-bit core platform operating at up to 80-MHz frequency.

These cost-optimized MCUs offer high-performance analog peripheral integration,
support extended temperature ranges from -40°C to 125°C, and operate with supply
voltages ranging from 1.62V to 3.6V.

The MSPM0G351X devices provide up to 128 KB embedded Flash program memory
with built-in error correction code (ECC) and up to 32 KB SRAM with
hardware parity option. They also incorporate a memory protection unit,
7-channel DMA, math accelerator, and a variety of high-performance analog
peripherals such as two 12-bit 4MSPS ADCs, configurable internal shared
voltage reference, one 12-bit 1MSPS DAC, three high speed comparators
with built-in reference DACs, two zero-drift, zero-crossover op-amps
with programmable gain, and one general-purpose amplifier.

These devices also offer intelligent digital peripherals suchas three
16-bit advanced control timers, three 16-bit general purpose timers,
one 24-bit high resolution timer, two windowed-watchdog timers,
and one RTC with alarm and calendar mode.

These devices provide
data integrity and encryption peripherals (AES, CRC, TRNG) and enhanced
communication interfaces (four UART, two I2C, two SPI, CAN 2.0/FD).
            </description>
            <debug>
                <!-- Patched ROM Table for a Cortex-M0+ -->
                <datapatch  type="AP" __dp="0" __ap="0" address="0xF8" value="0xF0000003" info="AP BASE Register, ROM Table at 0xF0000002"/>
            </debug>
            <!-- debug sequences -->  
            <sequences>
                <sequence name="DebugPortSetup">
                    <block>
                        __var isSWJ      = ((__protocol &amp; 0x00010000) != 0);
                        __var hasDormant = __protocol &amp; 0x00020000;
                        __var protType   = __protocol &amp; 0x0000FFFF;
                    </block>
                    <!-- JTAG Protocol -->
                    <control if="protType == 1">
                        <control if="isSWJ">
                            <control if="hasDormant">
                                <block atomic="1">
                                    // Ensure current debug interface is in reset state
                                    DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);
                                    // Select Dormant State (from SWD)
                                    DAP_SWJ_Sequence(16, 0xE3BC);                            
                                    // At least 8 cycles SWDIO/TMS HIGH
                                    DAP_SWJ_Sequence(8, 0xFF);                            
                                    // Alert Sequence Bits  0.. 63
                                    DAP_SWJ_Sequence(64, 0x86852D956209F392);                            
                                    // Alert Sequence Bits 64..127
                                    DAP_SWJ_Sequence(64, 0x19BC0EA2E3DDAFE9);
                                    // 4 cycles SWDIO/TMS LOW + 8-Bit JTAG Activation Code (0x0A)            
                                    DAP_SWJ_Sequence(12, 0x0A0);
                                    // Ensure JTAG interface is reset
                                    DAP_SWJ_Sequence(6, 0x3F);
                                </block>
                            </control>
                            <control if="!hasDormant">                    
                                <block atomic="1">
                                // Ensure current debug interface is in reset state
                                DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);                            
                                // Execute SWJ-DP Switch Sequence SWD to JTAG (0xE73C)
                                // Change if SWJ-DP uses deprecated switch code (0xAEAE)
                                DAP_SWJ_Sequence(16, 0xE73C);                            
                                // Ensure JTAG interface is reset
                                DAP_SWJ_Sequence(6, 0x3F);
                                </block>                    
                            </control>                    
                        </control>
                            <block atomic="1">
                                // JTAG "Soft" Reset
                                DAP_JTAG_Sequence(6, 1, 0x3F);
                                DAP_JTAG_Sequence(1, 0, 0x01);
                            </block>
                    </control>

                    <!-- SWD Protocol -->
                    <control if="protType == 2">
                        <control if="isSWJ">
                            <control if="hasDormant">
                                <block atomic="1">
                                    // Ensure current debug interface is in reset state
                                    DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);                            
                                    // Select Dormant State (from JTAG)
                                    DAP_SWJ_Sequence(31, 0x33BBBBBA);                            
                                    // At least 8 cycles SWDIO/TMS HIGH
                                    DAP_SWJ_Sequence(8, 0xFF);                            
                                    // Alert Sequence Bits  0.. 63
                                    DAP_SWJ_Sequence(64, 0x86852D956209F392);                            
                                    // Alert Sequence Bits 64..127
                                    DAP_SWJ_Sequence(64, 0x19BC0EA2E3DDAFE9);
                                    // 4 cycles SWDIO/TMS LOW + 8-Bit SWD Activation Code (0x1A)            
                                    DAP_SWJ_Sequence(12, 0x1A0);
                                    // Enter SWD Line Reset State
                                    DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  // &gt; 50 cycles SWDIO/TMS High
                                    DAP_SWJ_Sequence(3,  0x00);                // At least 2 idle cycles (SWDIO/TMS Low)
                                </block>
                            </control>
                            <control if="!hasDormant">
                                <block>
                                    // Ensure current debug interface is in reset state
                                    DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //lets put the JTAG line in a reset state
                                    DAP_SWJ_Sequence(16,0xE73C); //SWD to JTAG, this is to ensure the state machine is initialized
                                    DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF); //Put it back in reset
                                    DAP_SWJ_Sequence(16, 0xE79E); //JTAG to SWD switch
                                    DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  //Ensure the switch is successful 
                                    DAP_SWJ_Sequence(7,  0x00); //Give the line some time to initialize
                                </block>
                            </control>
                        </control>
                        <control if="!isSWJ">
                            <control if="hasDormant">
                                <block atomic="1">
                                // Ensure current debug interface is in reset state
                                DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);
                                
                                // At least 8 cycles SWDIO/TMS HIGH
                                DAP_SWJ_Sequence(8, 0xFF);
                                
                                // Alert Sequence Bits  0.. 63
                                DAP_SWJ_Sequence(64, 0x86852D956209F392);
                                
                                // Alert Sequence Bits 64..127
                                DAP_SWJ_Sequence(64, 0x19BC0EA2E3DDAFE9);
                        
                                // 4 cycles SWDIO/TMS LOW + 8-Bit SWD Activation Code (0x1A)            
                                DAP_SWJ_Sequence(12, 0x1A0);
                                
                                // Enter SWD Line Reset State
                                DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  // &gt; 50 cycles SWDIO/TMS High
                                DAP_SWJ_Sequence(3,  0x00);                // At least 2 idle cycles (SWDIO/TMS Low)
                                </block>
                            </control>
                            <control if="!hasDormant">
                                <block atomic="1">
                                // Enter SWD Line Reset State
                                DAP_SWJ_Sequence(51, 0x0007FFFFFFFFFFFF);  // &gt; 50 cycles SWDIO/TMS High
                                DAP_SWJ_Sequence(3,  0x00);                // At least 2 idle cycles (SWDIO/TMS Low)
                                </block>
                            </control>
                        </control>
                        <block>
                            // Read DPIDR to enable SWD interface (SW-DPv1 and SW-DPv2)
                            ReadDP(0x0);
                        </block>
                    </control>
                </sequence>
                <sequence name="DebugPortStart">

                    <block>
                        __var SW_DP_ABORT       = 0x0;
                        __var DP_CTRL_STAT      = 0x4;
                        __var DP_SELECT         = 0x8;
                        __var powered_down      = 0;

                        //Beyond here do not modify
                        // Switch to DP Register Bank 0
                        WriteDP(DP_SELECT, 0x00000000);

                        // Read DP CTRL/STAT Register and check if CSYSPWRUPACK and CDBGPWRUPACK bits are set
                        powered_down = ((ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000);
                    </block>
                    <control if="powered_down">
                        <block>
                            // Request Debug/System Power-Up
                            Message(0, "Debug/System power-up request sent");
                            WriteDP(DP_CTRL_STAT, 0x50000000);
                        </block>
                        
                        <!-- Wait for Power-Up Request to be acknowledged -->
                        <control while="(ReadDP(DP_CTRL_STAT) &amp; 0xA0000000) != 0xA0000000" timeout="1000000"/>
                        
                        <!-- JTAG Specific Part of sequence -->
                        <control if="(__protocol &amp; 0xFFFF) == 1">
                        
                            <block>
                                // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                                // Additionally clear STICKYORUN, STICKYCMP, and STICKYERR bits by writing '1'
                                WriteDP(DP_CTRL_STAT, 0x50000F32);
                            </block>
                        
                        </control>
                        
                        <!-- SWD Specific Part of sequence -->
                        <control if="(__protocol &amp; 0xFFFF) == 2">
                            <block>
                                Message(0, "executing SWD power up");
                                // Init AP Transfer Mode, Transaction Counter, and Lane Mask (Normal Transfer Mode, Include all Byte Lanes)
                                WriteDP(DP_CTRL_STAT, 0x50000F00);
                                
                                // Clear WDATAERR, STICKYORUN, STICKYCMP, and STICKYERR bits of CTRL/STAT Register by write to ABORT register
                                WriteDP(SW_DP_ABORT, 0x0000001E);
                            </block>
                        </control>
                    </control>
                    <block>
                        __var DEBUG_PORT_VAL    = 0;
                        __var ACCESS_POINT_VAL  = 0;

                        __ap = 1; 
                        WriteAP(0x0C, 0x04); //lets use the mini pwr-ap as backup
                        
                        __ap = 4;
                        ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
                        Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
                    </block>            
                        <block>
                        __var nReset = 0x80;
                        __var canReadPins = 0;
                        // De-assert nRESET line to activate the hardware reset
                        canReadPins = (DAP_SWJ_Pins(0x00, nReset, 0) != 0xFFFFFFFF);
                        </block>
                        <!-- Keep reset active for 50 ms -->
                        <control while="1" timeout="200"/>
                        <control if="canReadPins">
                            <!-- Assert nRESET line and wait max. 1s for recovery -->
                            <control while="(DAP_SWJ_Pins(nReset, nReset, 0) &amp; nReset) == 0" timeout="1000000"/>
                        </control>
                        <control if="!canReadPins">
                            <block>
                            // Assert nRESET line
                            DAP_SWJ_Pins(nReset, nReset, 0);
                            </block>
                            <!-- Wait 100ms for recovery if nRESET not readable -->
                            <control while="1" timeout="100000"/>
                        </control>
                    <control if = "(ACCESS_POINT_VAL &amp; 0x00E00000) == 0">
                        <block>
                            WriteAP(0x00, 0x190008);
                            WriteAP(0xF0, 0x01);
                        </block> 
                    </control>
                    <control if = "(ACCESS_POINT_VAL &amp; 0x00E00000) != 0">
                        <block>
                            WriteAP(0xF0, 0x01);
                            WriteAP(0x00, 0xF90008);
                            WriteAP(0xF0, 0x01);
                        </block>
                    </control>
                    <block>
                        ACCESS_POINT_VAL = ReadAP(0x00);//Reading current state of access point
                        Message(0, "Current state of access point is: %x",ACCESS_POINT_VAL);
                        __ap = 0; //lets make sure we reset the access point selection
                    </block>
                </sequence>
                <sequence name="DebugDeviceUnlock">
                    <block>
                        __ap = 1;
                        __var deviceID = 0;
                        __var version = 0;
                        __var partNum = 0;
                        __var manuf = 0;
                        __var isMSPM0GX51X = 0;
                        __var isProduction = 0;
                        __var continueId = 0;
                        deviceID =   ReadAP(0x00);
                        version = deviceID >> 28;
                        partNum = (deviceID &amp; 0x0FFFF000) >> 12;
                        manuf = (deviceID &amp; 0x00000FFE) >> 1;
                        isMSPM0GX51X = (partNum == 0xBBA9) &amp;&amp; (manuf == 0x17);
                        isProduction = (version &gt; 0);
                    </block>
                    <!-- Check if device ID is correct -->
                    <control if="!isMSPM0GX51X">
                        <block>
                            continueId = Query(1, "Incorrect ID. This support package is for MSPM0GX51X devices. Continue?", 4);
                        </block>
                    </control>
                    <control if="continueId == 4">
                        <block>
                            Message(2, "Invalid ID");
                        </block>
                    </control>
                </sequence>
            </sequences>
            <!-- features common for the whole family -->
            <feature type="NVIC" n="32" name="Nested Vectored Interrupt Controller (NVIC)"/>
            <feature type="DMA" n="12" name="Direct Memory Access (DMA)"/>
            <feature type="MemoryOther" name="Up to 512KB Flash memory with built-in error correction code (ECC)"/>
            <feature type="MemoryOther" name="Up to 128KB SRAM with hardware parity"/>
            <feature type="XTAL" n="4000000" m="48000000" name="High Frequency crystal (HFXT)"/>
            <feature type="PLL" n="1" name="Internal PLL"/>
            <feature type="RTC" n="32768" name="Internal RTC with alarm and calendar modes"/>
            <feature type="ClockOther" name="Internal 4-32MHz oscillator with +-1% accuracy (SYSOSC)"/>
            <feature type="ClockOther" name="Internal 32kHz oscillator (LFOSC)"/>
            <feature type="ClockOther" name="Low Frequency 32kHz crystal (LFXT)"/>
            <feature type="PowerMode" n="12" name="RUN0, RUN1, RUN2, SLEEP0, SLEEP1, SLEEP2, STOP0, STOP1, STOP2, STANDBY0, STANDBY1, SHUTDOWN"/>
            <feature type="VCC" n="1.62" m="3.6"/>
            <feature type="Temp" n="-40" m="125" name="Extended Operating Temperature Range"/>
            <feature type="DAC" n="1" m="12" name="1MSPS digital-to-analog converter with integrated output buffer (DAC)"/>
            <feature type="AnalogOther" n="1" name="General-purpose amplifier (GPAMP)"/>
            <feature type="AnalogOther" n="3" name="High-speed comparator (COMP) with 8-bit reference DACs"/>
            <feature type="AnalogOther" n="1" name="Configurable 1.4V or 2.5V internal shared voltage reference (VREF)"/>
            <feature type="Timer" n="26" m="16" name="Nine 16-bit advanced timers with deadband supporting up to 26 PWM channels"/>
            <feature type="TimerOther" n="3" name="16-bit general purpose timer"/>
            <feature type="TimerOther" n="2" name="16-bit general purpose timer supporting QEI"/>
            <feature type="TimerOther" n="1" name="16-bit general purpose timer supporting low power operation in STANDBY mode"/>
            <feature type="TimerOther" n="1" name="32-bit high resolution timer"/>
            <feature type="TimerOther" n="2" name="16-bit advanced timers with deadband support up to 12 PWM channels"/>
            <feature type="WDT" n="3" name="Two Window-watchdog timer (WWDT), one independent watchdog timer (IWDT)"/>
            <feature type="I2C" n="3" name="I2C interface supporting up to FM+ (1Mbps), SMBus/PMBus, and wakeup from STOP mode"/>
            <feature type="SPI" n="3" m="32000000" name="SPI interface"/>
            <feature type="UART" n="7" name="UART inteface, two supporting LIN, IrDA, DALI, Smart Card, Manchester, and five supporting low-power operation in STANDBY mode"/>
            <feature type="Other" n="1" name="Math accelerator supporting DIV, SQRT, and TRIG computations"/>
            <feature type="Crypto" n="128.256" name="HW accelerated AES Encryption/Decryption Engine"/>
            <feature type="RNG" n="1" name="True Random Number generator (TRNG)"/>
            <feature type="ADC" n="27" m="12" name="Two simultaneous 4MSPS analog-to-digital converters (ADC)"/>
            <feature type="IOs" n="93" name="General purpose I/Os, including two 5-V tolerant, two high-drive with 20mA driver strength"/>
<!-- ************************  Subfamily MSPM0G351X  **************************** -->
            <subFamily DsubFamily="MSPM0G351X">
                <feature type="CAN" n="2" name="Controller Area Network (CAN) interface supporting CAN 2.0 A or B and CAN-FD"/>
                <!-- *************************  Device MSPM0G3519  ***************************** -->
                <device Dname="MSPM0G3519">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00080000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00080000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00010000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00020000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000800" default="1"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000200" default="1"/>
                    <memory     name="Data" access="r"  start="0x41D00000" size="0x00004000" default="1"/>
                    <compile    define="__MSPM0G3519__"/>
                    <debug      svd="03_SVD/MSPM0G351X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_MAIN_512KB.FLM" start="0x00000000" size="0x00080000" RAMstart="0x20200000" RAMsize="0x00020000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_NONMAIN.FLM" start="0x41C00000" size="0x00000800" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_DATA_16KB.FLM" start="0x41D00000" size="0x00004000" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G3518  ***************************** -->
                <device Dname="MSPM0G3518">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00040000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00040000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00010000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00020000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000800" default="1"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000200" default="1"/>
                    <memory     name="Data" access="r"  start="0x41D00000" size="0x00004000" default="1"/>
                    <compile    define="__MSPM0G3518__"/>
                    <debug      svd="03_SVD/MSPM0G351X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_MAIN_256KB.FLM" start="0x00000000" size="0x00040000" RAMstart="0x20200000" RAMsize="0x00020000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_NONMAIN.FLM" start="0x41C00000" size="0x00000800" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_DATA_16KB.FLM" start="0x41D00000" size="0x00004000" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                </device>
            </subFamily>
<!-- ************************  Subfamily MSPM0G151X  **************************** -->
            <subFamily DsubFamily="MSPM0G151X">
                <!-- *************************  Device MSPM0G1519  ***************************** -->
                <device Dname="MSPM0G1519">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00080000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00080000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00010000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00020000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000800" default="1"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000200" default="1"/>
                    <memory     name="Data" access="r"  start="0x41D00000" size="0x00004000" default="1"/>
                    <compile    define="__MSPM0G1519__"/>
                    <debug      svd="03_SVD/MSPM0G151X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_MAIN_512KB.FLM" start="0x00000000" size="0x00080000" RAMstart="0x20200000" RAMsize="0x00020000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_NONMAIN.FLM" start="0x41C00000" size="0x00000800" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_DATA_16KB.FLM" start="0x41D00000" size="0x00004000" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G1518  ***************************** -->
                <device Dname="MSPM0G1518">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00040000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00040000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00010000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00020000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00010000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000800" default="1"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000200" default="1"/>
                    <memory     name="Data" access="r"  start="0x41D00000" size="0x00004000" default="1"/>
                    <compile    define="__MSPM0G1518__"/>
                    <debug      svd="03_SVD/MSPM0G151X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_MAIN_256KB.FLM" start="0x00000000" size="0x00040000" RAMstart="0x20200000" RAMsize="0x00020000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_NONMAIN.FLM" start="0x41C00000" size="0x00000800" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0GX51X_DATA_16KB.FLM" start="0x41D00000" size="0x00004000" RAMstart="0x20200000" RAMsize="0x00020000" default="0"/>
                </device>
            </subFamily>
        </family>
    </devices>

    <boards>
        <board vendor="TexasInstruments" name="LP-MSPM0G3519" salesContact="http://www.ti.com/general/docs/contact.tsp">
            <description>MSPM0G3519 LaunchPad</description>
            <mountedDevice    deviceIndex="0" Dvendor="Texas Instruments:16" Dname="MSPM0G3519"/>
            <compatibleDevice deviceIndex="0" Dvendor="Texas Instruments:16" Dfamily="MSPM0GX51X Series" DsubFamily="MSPM0G351X"/> 
            <debugInterface adapter="XDS110-ET" connector="XDS110-ET Onboard Emulator"/>  
            <debugInterface adapter="SWD" connector="10-pin Cortex Debug Connector (0.05 inch connector)"/>
            <feature type="USB" n="1" name="USB Device,  Micro-B receptacle"/>
            <feature type="Button" n="3" name="reset and two user push-buttons"/>
            <feature type="LED" n="4" name="LEDs for user interaction, including 1 RGB LED"/> 
            <feature type="ConnOther" n="4" name="2x40 pin BoosterPack Connector and support for 20 pin BoosterPacks"/>
            <feature type="TempSens" n="1" name="Temperature sensor circuit"/>
            <feature type="LightSens" n="1" name="Light sensor circuit"/>
            <feature type="SensOther" n="1" name="External OPA2365 for 4MSPS ADC evaluation"/>
            <feature type="XTAL" n="48000000"/>
            <feature type="Other" name="EnergyTrace technology available for ultra-low-power debugging"/>
            <feature type="Other" name="32kHz crystal"/>
        </board>
    </boards>

</package>
