<?xml version="1.0" encoding="UTF-8"?>
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd" schemaVersion="1.4">
  <name>FRDM-K32L3A6_SDMMC_BSP</name>
  <vendor>NXP</vendor>
  <description>Middleware sdmmc Board Support Pack for FRDM-K32L3A6</description>
  <url>https://mcuxpresso.nxp.com/cmsis_pack/repo/</url>
  <license>Licenses.txt</license>
  <releases>
    <release version="1.0.1" date="2023-10-25" deprecated="2023-10-25" replacement="NXP.FRDM-K32L3A6_SDMMC_Examples">This pack is no longer maintained. The new pack has been released with "_Examples" suffix instead of "_BSP".</release>
    <release version="1.0.0" date="2023-07-27">NXP CMSIS SBSP Packs based on MCUXpresso SDK 2.14.0</release>
  </releases>
  <keywords>
    <keyword>Middleware Board Support Pack</keyword>
  </keywords>
  <requirements>
    <packages>
      <package name="K32L3A60_DFP" vendor="NXP" version="18.0.0"/>
      <package name="SDMMC" vendor="NXP" version="1.0.1"/>
      <package name="Component_OS_Abstraction_Layer" vendor="NXP" version="1.0.1"/>
      <package name="CMSIS" vendor="ARM" version="5.8.0"/>
      <package name="FATFS" vendor="NXP" version="1.0.1"/>
      <package name="FREERTOS-KERNEL" vendor="NXP" version="1.0.1"/>
    </packages>
    <languages>
      <language name="C" version="99"/>
    </languages>
  </requirements>
  <boards>
    <board vendor="NXP" name="FRDM-K32L3A6">
      <description>Freedom Development Platform for K32 L3 MCUs</description>
      <image small="boards/frdmk32l3a6/frdmk32l3a6.png"/>
      <mountedDevice Dname="K32L3A60xxx" Dvendor="NXP:11"/>
    </board>
  </boards>
  <examples>
    <example name="sdcard_polling_cm0plus" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_polling/cm0plus" doc="readme.txt">
      <description>The SDCARD Polling project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use polling based transfer API in SDHC driver in SDK software to access SD card.Note: If DATA3 is used as the card detect PIN, please make sure DATA3 is pull down, no matter internal or external, at the same time, make sure the card can pull DATA3 up, then host can detect card through DATA3.And SDHC do not support detect card through CD by host, card can be detected through DATA3 or GPIO.No matter detect card through host or gpio, make sure the pinmux configuration is correct.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_polling_cm0plus.uvprojx"/>
        <environment name="csolution" load="sdcard_polling_cm0plus.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_interrupt_cm0plus" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_interrupt/cm0plus" doc="readme.txt">
      <description>The SDCARD Interrupt project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use interrupt based transfer API in SDHC driver in SDK software to access SD card.Note: If DATA3 is used as the card detect PIN, please make sure DATA3 is pull down, no matter internal or external, at the same time, make sure the card can pull DATA3 up, then host can detect card through DATA3.And SDHC do not support detect card through CD by host, card can be detected through DATA3 or GPIO.No matter detect card through host or gpio, make sure the pinmux configuration is correct.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_interrupt_cm0plus.uvprojx"/>
        <environment name="csolution" load="sdcard_interrupt_cm0plus.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_freertos_cm0plus" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_fatfs_freertos/cm0plus" doc="readme.txt">
      <description>The SDCARD FATFS FREERTOS project is a demonstration program that uses the SDK software. The purpose of this example is to show how to use SDCARD with fatfs and freertos.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_freertos_cm0plus.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_freertos_cm0plus.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_cm0plus" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_fatfs/cm0plus" doc="readme.txt">
      <description>The SDCARD FATFS project is a demonstration program that uses the SDK software. Tt mounts a file system based on a SD card then does "creat directory/read directory/create file/write file/read file"operation. The file sdhc_config.h has default SDHC configuration which can be adjusted to let carddriver has different performance. The purpose of this example is to show how to use SDCARD driver based FATFS disk in SDK software.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_cm0plus.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_cm0plus.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_freertos_cm0plus" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_freertos/cm0plus" doc="readme.txt">
      <description>The SDCARD FreeRTOS project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver with FreeRTOS in SDK software to access SD card.Note: If DATA3 is used as the card detect PIN, please make sure DATA3 is pull down, no matter internal or external, at the same time, make sure the card can pull DATA3 up, then host can detect card through DATA3.And SDHC do not support detect card through CD by host, card can be detected through DATA3 or GPIO.No matter detect card through host or gpio, make sure the pinmux configuration is correct.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_freertos_cm0plus.uvprojx"/>
        <environment name="csolution" load="sdcard_freertos_cm0plus.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_polling_cm4" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_polling/cm4" doc="readme.txt">
      <description>The SDCARD Polling project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use polling based transfer API in SDHC driver in SDK software to access SD card.Note: If DATA3 is used as the card detect PIN, please make sure DATA3 is pull down, no matter internal or external, at the same time, make sure the card can pull DATA3 up, then host can detect card through DATA3.And SDHC do not support detect card through CD by host, card can be detected through DATA3 or GPIO.No matter detect card through host or gpio, make sure the pinmux configuration is correct.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_polling_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_polling_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_interrupt_cm4" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_interrupt/cm4" doc="readme.txt">
      <description>The SDCARD Interrupt project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver andshow how to use interrupt based transfer API in SDHC driver in SDK software to access SD card.Note: If DATA3 is used as the card detect PIN, please make sure DATA3 is pull down, no matter internal or external, at the same time, make sure the card can pull DATA3 up, then host can detect card through DATA3.And SDHC do not support detect card through CD by host, card can be detected through DATA3 or GPIO.No matter detect card through host or gpio, make sure the pinmux configuration is correct.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_interrupt_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_interrupt_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_freertos_cm4" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_fatfs_freertos/cm4" doc="readme.txt">
      <description>The SDCARD FATFS FREERTOS project is a demonstration program that uses the SDK software. The purpose of this example is to show how to use SDCARD with fatfs and freertos.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_fatfs_cm4" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_fatfs/cm4" doc="readme.txt">
      <description>The SDCARD FATFS project is a demonstration program that uses the SDK software. Tt mounts a file system based on a SD card then does "creat directory/read directory/create file/write file/read file"operation. The file sdhc_config.h has default SDHC configuration which can be adjusted to let carddriver has different performance. The purpose of this example is to show how to use SDCARD driver based FATFS disk in SDK software.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_fatfs_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_fatfs_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
    <example name="sdcard_freertos_cm4" folder="boards/frdmk32l3a6/sdmmc_examples/sdcard_freertos/cm4" doc="readme.txt">
      <description>The SDCARD FreeRTOS project is a demonstration program that uses the SDK software. It reads/writes/erases the SD card continusly. The purpose of this example is to show how to use SDCARD driver with FreeRTOS in SDK software to access SD card.Note: If DATA3 is used as the card detect PIN, please make sure DATA3 is pull down, no matter internal or external, at the same time, make sure the card can pull DATA3 up, then host can detect card through DATA3.And SDHC do not support detect card through CD by host, card can be detected through DATA3 or GPIO.No matter detect card through host or gpio, make sure the pinmux configuration is correct.</description>
      <board name="FRDM-K32L3A6" vendor="NXP" Dvendor="NXP:11"/>
      <project>
        <environment name="uv" load="mdk/sdcard_freertos_cm4.uvprojx"/>
        <environment name="csolution" load="sdcard_freertos_cm4.csolution.yml"/>
      </project>
      <attributes/>
    </example>
  </examples>
</package>
