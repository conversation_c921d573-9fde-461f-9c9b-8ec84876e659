<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.6" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>tensorflow</vendor>
  <name>flatbuffers</name>
  <description>FlatBuffers: Memory Efficient Serialization Library</description>
  <!-- web download link -->
  <url>https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.25.2/</url>
  <license>LICENSE.txt</license>
  <releases>
    <release version="1.25.2" date="2025-05-09">
      Flatbuffers for TensorFlow 1.25.2
    </release>
    <release version="1.25.2-rc4" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.25.2-rc4/tensorflow.flatbuffers.1.25.2-rc4.pack" date="2025-03-26">Based on the latest release from : https://gitlab.arm.com/artificial-intelligence/ethos-u/ethos-u

Introduces Reference Applications</release>
<release version="1.24.11" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.24.11/tensorflow.flatbuffers.1.24.11.pack" date="2025-01-09">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/tags/24.11/24.11.json</release>
<release version="1.24.8" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.24.8/tensorflow.flatbuffers.1.24.8.pack" date="2024-10-01">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/tags/24.08/24.08.json
</release>
<release version="1.24.2-rc6" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.24.2-rc6/tensorflow.flatbuffers.1.24.2-rc6.pack" date="2024-05-08">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u-core-software/+/refs/tags/24.02</release>
<release version="1.23.2" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.23.2/tensorflow.flatbuffers.1.23.2.pack" date="2023-06-15">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u-core-software/+/refs/tags/23.02</release>
<release version="1.22.8" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.8/tensorflow.flatbuffers.1.22.8.pack" date="2022-12-07">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u-core-software/+/refs/tags/22.08</release>
<release version="1.22.5" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.5/tensorflow.flatbuffers.1.22.5.pack" date="2022-10-18">Release based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/heads/master/22.05.json</release>
<release version="1.22.5-rc4" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.5-rc4/tensorflow.flatbuffers.1.22.5-rc4.pack" date="2022-06-13">Release candidate for pack based on versions from: https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/+/refs/heads/master/22.05.json
</release>
<release version="1.22.2" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/1.22.2/tensorflow.flatbuffers.1.22.2.pack" date="2022-06-07">First release synchronized with released versions from https://review.mlplatform.org/plugins/gitiles/ml/ethos-u/ethos-u/ (rev 22.02)</release>
<release version="0.4.0" url="https://github.com/MDK-Packs/tensorflow-pack/releases/download/0.4.0/tensorflow.flatbuffers.0.4.0.pack" date="2021-09-30">First release listed on pack repository</release>

  </releases>
  <taxonomy>
    <description Cclass="Data Exchange">Software Components for Data Exchange</description>
    <description Cclass="Data Exchange" Cgroup="Serialization">Data Serializer Components</description>
  </taxonomy>
  <components>
    <component Cclass="Data Exchange" Cgroup="Serialization" Csub="flatbuffers" Cvariant="tensorflow" Cversion="1.25.2" isDefaultVariant="true" >
      <description>Flatbuffers</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DataExchange_Serialization_flatbuffers     /* flatbuffers */
      </RTE_Components_h>
      <files>
        <file category="include" name="src/include/"/>
      </files>
    </component>
  </components>
</package>
