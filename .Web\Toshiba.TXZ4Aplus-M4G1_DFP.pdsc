<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <license>License/SOFTWARE_LICENSE_AGREEMENT.txt</license> 
  <vendor>Toshiba</vendor>
  <url>https://toshiba.semicon-storage.com/content/dam/toshiba-ss/shared/docs/product/micro/device-family-Pack/</url>
  <name>TXZ4Aplus-M4G1_DFP</name>
  <description>Toshiba TXZ4A+ Series TMPM4G(1) Group Device Support</description>

  <releases>
    <release version="1.0.1" date="2021-07-26">
      Correction for SOFTWARE_LICENSE_AGREEMENT.
      First Release version of TXZ4A+ Series TMPM4G(1) Group Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>Toshiba</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package Toshiba</keyword>
    <keyword>TMPM4G</keyword>
    <keyword>TXZ4A+</keyword>
  </keywords>

  <devices>
    <family Dfamily="TXZ4A+ Series" Dvendor="Toshiba:92">
      <processor Dcore="Cortex-M4" DcoreVersion="r0p1" Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
The TXZ4A+ microcontroller series embeds an ARM Cortex-M4 core, which provides high code density and fast interrupt response times required for real-time applications.
      </description>
      <!-- ************************  Subfamily 'TMPM4Gx'  **************************** -->
      <subFamily DsubFamily="M4G(1)">

        <!-- ***********************  Device 'TMPM4GRF20x'  ************************* -->
        <device Dname="TMPM4GRF20FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00200000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_2048.FLM" start="0x00000000" size="0x00200000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"   RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4GRF20XBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00200000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_2048.FLM" start="0x00000000" size="0x00200000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"   RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>

        <!-- ***********************  Device 'TMPM4GRF15x'  ************************* -->
        <device Dname="TMPM4GRF15FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4GRF15XBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>

        <!-- ***********************  Device 'TMPM4GRF10x'  ************************* -->
        <device Dname="TMPM4GRF10FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4GRF10XBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>

        <!-- ***********************  Device 'TMPM4GRFDx'  ************************* -->
        <device Dname="TMPM4GRFDFG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="176" name="LQFP 176 20x20x0.4"/>
        </device>
        <device Dname="TMPM4GRFDXBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GR.h" define="TMPM4GR"/>
          <debug svd="SVD/M4GR.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="155"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="177" name="VFBGA 177 13x13x0.8"/>
        </device>

      <!-- ************************  Subfamily 'TMPM4GQx'  **************************** -->

        <!-- ***********************  Device 'TMPM4GQF20x'  ************************* -->
        <device Dname="TMPM4GQF20FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00200000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_2048.FLM" start="0x00000000" size="0x00200000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"   RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4GQF20XBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00200000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_2048.FLM" start="0x00000000" size="0x00200000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"   RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4GQF15x'  ************************* -->
        <device Dname="TMPM4GQF15FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4GQF15XBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4GQF10x'  ************************* -->
        <device Dname="TMPM4GQF10FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4GQF10XBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        <!-- ***********************  Device 'TMPM4GQFDx'  ************************* -->
        <device Dname="TMPM4GQFDFG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP" n="144" name="LQFP 144 20x20x0.5"/>
        </device>
        <device Dname="TMPM4GQFDXBG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GQ.h" define="TMPM4GQ"/>
          <debug svd="SVD/M4GQ.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="5"                           name="I2C"/>
          <feature type="I2C"           n="5"                           name="EI2C"/>
          <feature type="ADC"           n="24"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="16"/>
          <feature type="IOs"           n="127"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="BGA" n="145" name="VFBGA 145 12x12x0.8"/>
        </device>
        
      <!-- ************************  Subfamily 'TMPM4GNx'  **************************** -->

        <!-- ***********************  Device 'TMPM4GNF20x'  ************************* -->
        <device Dname="TMPM4GNF20FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GN.h" define="TMPM4GN"/>
          <debug svd="SVD/M4GN.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00200000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_2048.FLM" start="0x00000000" size="0x00200000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"   RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="3"                           name="I2C"/>
          <feature type="I2C"           n="3"                           name="EI2C"/>
          <feature type="ADC"           n="16"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="12"/>
          <feature type="IOs"           n="91"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
        <!-- ***********************  Device 'TMPM4GNF15x'  ************************* -->
        <device Dname="TMPM4GNF15FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GN.h" define="TMPM4GN"/>
          <debug svd="SVD/M4GN.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00180000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1536.FLM" start="0x00000000" size="0x00180000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="3"                           name="I2C"/>
          <feature type="I2C"           n="3"                           name="EI2C"/>
          <feature type="ADC"           n="16"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="12"/>
          <feature type="IOs"           n="91"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
        <!-- ***********************  Device 'TMPM4GNF10x'  ************************* -->
        <device Dname="TMPM4GNF10FG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GN.h" define="TMPM4GN"/>
          <debug svd="SVD/M4GN.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00040000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_1024.FLM" start="0x00000000" size="0x00100000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="3"                           name="I2C"/>
          <feature type="I2C"           n="3"                           name="EI2C"/>
          <feature type="ADC"           n="16"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="12"/>
          <feature type="IOs"           n="91"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
        <!-- ***********************  Device 'TMPM4GNFDx'  ************************* -->
        <device Dname="TMPM4GNFDFG">
          <processor Dclock="200000000"/>
          <compile header="Device/Include/TMPM4GN.h" define="TMPM4GN"/>
          <debug svd="SVD/M4GN.svd"/>
          <memory id="IROM1"                           start="0x00000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                           start="0x20000000" size="0x00030000" init   ="0" default="1"/>
          <algorithm name="Flash/TMPM4Gx_code_512.FLM" start="0x00000000" size="0x00080000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>
          <algorithm name="Flash/TMPM4Gx_data_32.FLM"  start="0x30000000" size="0x00008000"  RAMstart="0x20000000"  RAMsize="0x2000"  default="1"/>

          <!--book name=""/-->

          <feature type="WDT"           n="1"/>
          <feature type="Timer"         n="16"      m="32"/>
          <feature type="DMA"           n="3"                           name="DMA"/>
          <feature type="UART"          n="6"/>
          <feature type="I2C"           n="3"                           name="I2C"/>
          <feature type="I2C"           n="3"                           name="EI2C"/>
          <feature type="ADC"           n="16"     m="12"               name="AD converter"/>
          <feature type="ExtInt"        n="12"/>
          <feature type="IOs"           n="91"/>
          <feature type="VCC"           n="2.70"    m="3.60"/>
          <feature type="Temp"          n="-40"     m="85"/>
          <feature type="I2S"           n="2" />
          <feature type="QFP"           n="100" name="LQFP 100 14x14x0.5"/>
        </device>
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="TMPM4Gx Compiler">
      <accept condition="Compiler ARMCC"/>
      <accept condition="Compiler IAR"/>   
    </condition>

    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

    <condition id="Compiler IAR">
      <require Tcompiler="IAR"/>
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="TMPM4GRx CMSIS">
      <description>Toshiba TMPM4GRx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4GR*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4GQx CMSIS">
      <description>Toshiba TMPM4GQx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4GQ*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>
    <condition id="TMPM4GNx CMSIS">
      <description>Toshiba TMPM4GNx Devices and CMSIS-CORE</description>
      <require Dvendor="Toshiba:92" Dname="TMPM4GN*"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>

  </conditions>

  <components>

    <!-- Startup TMPM4GRx -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4GRx CMSIS">
      <description>System Startup for Toshiba TMPM4GRx Devices</description>
      <RTE_Components_h>
        <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_DEVICE_STARTUP_TMPM4GRx      /* Device Startup for TMPM4GRx */
      </RTE_Components_h>

      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4GR.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4GR.s" attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="1.0.0" condition="TMPM4Gx Compiler"/>
      </files>
    </component>
    <!-- Startup TMPM4GQx -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4GQx CMSIS">
      <description>System Startup for Toshiba TMPM4GQx Devices</description>
      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4GQ.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4GQ.s" attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="1.0.0" condition="TMPM4Gx Compiler"/>
      </files>
    </component>

    <!-- Startup TMPM4GNx -->
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="TMPM4GNx CMSIS">
      <description>System Startup for Toshiba TMPM4GNx Devices</description>
      <files>
        <file category="include" name="Device/Include/" condition="TMPM4Gx Compiler"/>
        <file category="source"  name="Device/Source/ARM/startup_TMPM4GN.s" attr="config" version="1.0.0" condition="Compiler ARMCC"/>
        <file category="source"  name="Device/Source/IAR/startup_TMPM4GN.s" attr="config" version="1.0.0" condition="Compiler IAR"/>
        <file category="source"  name="Device/Source/system_TMPM4Gx.c"      attr="config" version="1.0.0" condition="TMPM4Gx Compiler"/>
      </files>
    </component>

  </components>

</package>
