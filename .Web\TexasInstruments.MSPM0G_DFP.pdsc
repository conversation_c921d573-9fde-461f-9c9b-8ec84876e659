<?xml version="1.0" encoding="utf-8"?>

<package schemaVersion="1.4" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
    <vendor>TexasInstruments</vendor>
    <name>MSPM0G_DFP</name>
    <description>Device Family Pack for Texas Instruments MSPM0G Series</description>
    <url>http://software-dl.ti.com/msp430/esd/MSPM0-CMSIS/MSPM0G/latest/exports/</url>
    <supportContact>https://e2e.ti.com/support/microcontrollers/</supportContact>
    <license>01_Pack\license.txt</license>

    <releases>
        <release version="1.2.1" date="2023-10-12" deprecated="2023-10-04" replacement="TexasInstruments.MSPM0G1X0X_G3X0X_DFP">
        * Fix installation error with previous release
        </release>
        <release version="1.2.0" date="2023-10-04" deprecated="2023-10-04" replacement="TexasInstruments.MSPM0G1X0X_G3X0X_DFP">
        * This Software Pack is no longer maintained. Replaced by TexasInstruments.MSPM0G1X0X_G3X0X_DFP
        </release>
        <release version="1.1.0" date="2023-05-15">
        * Disable cache in flash loader
        * Use address 0x2020_0000 for M0G Flash loader
        * Limit programming range using OPN information
        * Update DriverLib and header files
        </release>
        <release version="1.0.0" date="2023-03-14">
        Initial release of MSPM0G series device familiy pack.
        New device support:
            * MSPM0G350X
            * MSPM0G310X
            * MSPM0G110X
            * MSPM0G150X
        </release>
    </releases>

    <keywords>
        <!-- keywords for indexing -->
        <keyword>Texas Instruments</keyword>
        <keyword>MSPM0</keyword>
        <keyword>MSPM0G</keyword>
        <keyword>MSPM0GXX</keyword>
        <keyword>MSPM0G350X</keyword>
        <keyword>MSPM0G150X</keyword>
        <keyword>MSPM0G310X</keyword>
        <keyword>MSPM0G110X</keyword>
        <keyword>Device Support</keyword>
        <keyword>Device Family Package Texas Instruments</keyword>
      </keywords>
  
    <!-- devices section (mandatory for Device Family Packs) -->
    <devices>
        <family Dfamily="MSPM0G Series" Dvendor="Texas Instruments:16">
            <processor Dcore="Cortex-M0+" DcoreVersion="r0p1" Dfpu="NO_FPU" Dclock="80000000" Dmpu="MPU" Dendian="Little-endian"/>
            <debugconfig default="swd" clock="5000000" swj="1"/>
            <book name="https://developer.arm.com/documentation/dui0662/latest/" title="Cortex-M0+ Generic User Guide"/>
            <description>
The MSPM0G3xxx and MSPM0G1xxx microcontrollers (MCUs) are part of MSP's
highly-integrated, low-power 32-bit MCU family based on the enhanced Arm®
Cortex®-M0+ 32-bit core platform operating at up to 80-MHz frequency.

These cost-optimized MCUs offer high-performance analog peripheral integration,
support extended temperature ranges from -40°C to 125°C, and operate with supply
voltages ranging from 1.62V to 3.6V.

The MSPM0G350X devices provide up to 128 KB embedded Flash program memory
with built-in error correction code (ECC) and up to 32 KB SRAM with
hardware parity option. They also incorporate a memory protection unit,
7-channel DMA, math accelerator, and a variety of high-performance analog
peripherals such as two 12-bit 4MSPS ADCs, configurable internal shared
voltage reference, one 12-bit 1MSPS DAC, three high speed comparators
with built-in reference DACs, two zero-drift, zero-crossover op-amps
with programmable gain, and one general-purpose amplifier.

These devices also offer intelligent digital peripherals suchas three
16-bit advanced control timers, three 16-bit general purpose timers,
one 24-bit high resolution timer, two windowed-watchdog timers,
and one RTC with alarm and calendar mode.

These devices provide
data integrity and encryption peripherals (AES, CRC, TRNG) and enhanced
communication interfaces (four UART, two I2C, two SPI, CAN 2.0/FD).
            </description>
            <debug>
                <!-- Patched ROM Table for a Cortex-M0+ -->
                <datapatch  type="AP" __dp="0" __ap="0" address="0xF8" value="0xF0000003" info="AP BASE Register, ROM Table at 0xF0000002"/>
            </debug>
            <!-- debug sequences -->  
            <sequences>
                <sequence name="DebugDeviceUnlock">
                    <block>
                        __var deviceID = 0;
                        __var version = 0;
                        __var partNum = 0;
                        __var manuf = 0;
                        __var isMSPM0G = 0;
                        __var isProduction = 0;
                        __var continueId = 0;
                        deviceID =   Read32(0x41C40004);
                        version = deviceID >> 28;
                        partNum = (deviceID &amp; 0x0FFFF000) >> 12;
                        manuf = (deviceID &amp; 0x00000FFE) >> 1;
                        isMSPM0G = (partNum == 0xBB88) &amp;&amp; (manuf == 0x17);
                        isProduction = (version &gt; 0);
                    </block>
                    <!-- Check if device ID is correct -->
                    <control if="!isMSPM0G">
                        <block>
                            continueId = Query(1, "Incorrect ID. This support package is for MSPM0G devices. Continue?", 4);
                        </block>
                    </control>
                    <control if="continueId == 4">
                        <block>
                            Message(2, "Invalid ID");
                        </block>
                    </control>
                    <!-- Check if the device is early sample material -->
                    <control if="!isProduction">
                        <block>
                            Query(0, "This support package doesn't support MSPM0 early samples. We recommend moving to production-quality MSPM0G silicon by ordering samples at www.ti.com/mspm0.", 0);
                            Message(2, "Invalid device revision");
                        </block>
                    </control>
                </sequence>
            </sequences>
            <!-- features common for the whole family -->
            <feature type="NVIC" n="32" name="Nested Vectored Interrupt Controller (NVIC)"/>
            <feature type="DMA" n="7" name="Direct Memory Access (DMA)"/>
            <feature type="MemoryOther" name="Up to 128KB Flash memory with built-in error correction code (ECC)"/>
            <feature type="MemoryOther" name="Up to 32KB SRAM with hardware parity"/>
            <feature type="XTAL" n="4000000" m="48000000" name="Low Frequency crystal (LFXT)"/>
            <feature type="PLL" n="1" name="Internal PLL"/>
            <feature type="RTC" n="32768" name="Internal RTC with alarm and calendar modes"/>
            <feature type="ClockOther" name="Internal 4-32MHz oscillator with +-1% accuracy (SYSOSC)"/>
            <feature type="ClockOther" name="Internal 32kHz oscillator (LFOSC)"/>
            <feature type="ClockOther" name="Low Frequency 32kHz crystal (LFXT)"/>
            <feature type="PowerMode" n="12" name="RUN0, RUN1, RUN2, SLEEP0, SLEEP1, SLEEP2, STOP0, STOP1, STOP2, STANDBY0, STANDBY1, SHUTDOWN"/>
            <feature type="VCC" n="1.62" m="3.6"/>
            <feature type="Temp" n="-40" m="125" name="Extended Operating Temperature Range"/>
<!-- ************************  Subfamily MSPM0G350X  **************************** -->
            <subFamily DsubFamily="MSPM0G350X">
                <feature type="DAC" n="1" m="12" name="1MSPS digital-to-analog converter with integrated output buffer (DAC)"/>
                <feature type="AnalogOther" n="2" name="Zero-drift, zero-crossover chipper op-amp (OPA)"/>
                <feature type="AnalogOther" n="1" name="General-purpose amplifier (GPAMP)"/>
                <feature type="AnalogOther" n="3" name="High-speed comparator (COMP) with 8-bit reference DACs"/>
                <feature type="AnalogOther" n="1" name="Configurable 1.4V or 2.5V internal shared voltage reference (VREF)"/>
                <feature type="Timer" n="12" m="16" name="Two 16-bit advanced timers with deadband supporting up to 12 PWM channels"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer supporting QEI"/>
                <feature type="TimerOther" n="2" name="16-bit general purpose timer supporting low power operation in STANDBY mode"/>
                <feature type="TimerOther" n="1" name="32-bit high resolution timer"/>
                <feature type="WDT" n="2" name="Window-watchdog timer"/>
                <feature type="CAN" n="1" name="COntroller Area Network (CAN) interface supporting CAN 2.0 A or B and CAN-FD"/>
                <feature type="I2C" n="2" name="I2C interface supporting up to FM+ (1Mbps), SMBus/PMBus, and wakeup from STOP mode"/>
                <feature type="SPI" n="2" m="32000000" name="SPI interface"/>
                <feature type="UART" n="4" name="UART inteface, one supporting LIN, IrDA, DALI, Smart Card, Manchester, and three supporting low-power operation in STANDBY mode"/>
                <feature type="Other" n="1" name="Math accelerator supporting DIV, SQRT, and TRIG computations"/>
                <feature type="Crypto" n="128.256" name="HW accelerated AES Encryption/Decryption Engine"/>
                <feature type="RNG" n="1" name="True Random Number generator (TRNG)"/>
                <feature type="ADC" n="32" m="12" name="Two simultaneous 4MSPS analog-to-digital converters with up to 16-ch each (ADC)"/>
                <feature type="IOs" n="60" name="General purpose I/Os, including some 5-V tolerant, some high-drive with 20mA driver strength"/>
                <!-- *************************  Device MSPM0G3507  ***************************** -->
                <device Dname="MSPM0G3507">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00020000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00020000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G3507__"/>
                    <debug      svd="03_SVD/MSPM0G350X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_128KB.FLM" start="0x00000000" size="0x00020000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G3506  ***************************** -->
                <device Dname="MSPM0G3506">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00010000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00010000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G3506__"/>
                    <debug      svd="03_SVD/MSPM0G350X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_64KB.FLM" start="0x00000000" size="0x00010000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G3505  ***************************** -->
                <device Dname="MSPM0G3505">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00008000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00008000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00004000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00004000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G3505__"/>
                    <debug      svd="03_SVD/MSPM0G350X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_32KB.FLM" start="0x00000000" size="0x00008000" RAMstart="0x20200000" RAMsize="0x00004000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00004000" default="0"/>
                </device>
            </subFamily>
<!-- ************************  Subfamily MSPM0G310X  **************************** -->
            <subFamily DsubFamily="MSPM0G310X">
                <feature type="AnalogOther" n="1" name="General-purpose amplifier (GPAMP)"/>
                <feature type="AnalogOther" n="1" name="Configurable 1.4V or 2.5V internal shared voltage reference (VREF)"/>
                <feature type="Timer" n="3" m="16" name="Advanced control timer supporting dead time insertion and fault handling"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer supporting QEI"/>
                <feature type="TimerOther" n="2" name="16-bit general purpose timer supporting low power operation in STANDBY mode"/>
                <feature type="TimerOther" n="1" name="24-bit high resolution timer"/>
                <feature type="WDT" n="2" name="Window-watchdog timer"/>
                <feature type="CAN" n="1" name="COntroller Area Network (CAN) interface supporting CAN 2.0 A or B and CAN-FD"/>
                <feature type="I2C" n="2" name="I2C interface supporting up to FM+ (1Mbps), SMBus/PMBus, and wakeup from STOP mode"/>
                <feature type="SPI" n="2" m="32000000" name="SPI interface"/>
                <feature type="UART" n="4" name="UART inteface, one supporting LIN, IrDA, DALI, Smart Card, Manchester, and three supporting low-power operation in STANDBY mode"/>
                <feature type="Crypto" n="128.256" name="HW accelerated AES Encryption/Decryption Engine"/>
                <feature type="RNG" n="1" name="True Random Number generator (TRNG)"/>
                <feature type="ADC" n="32" m="12" name="Two simultaneous 4MSPS analog-to-digital converters with up to 16-ch each (ADC)"/>
                <feature type="IOs" n="60" name="General purpose I/Os, including some 5-V tolerant, some high-drive with 20mA driver strength"/>
                <!-- *************************  Device MSPM0G3107  ***************************** -->
                <device Dname="MSPM0G3107">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00020000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00020000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G3107__"/>
                    <debug      svd="03_SVD/MSPM0G310X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_128KB.FLM" start="0x00000000" size="0x00020000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G3106  ***************************** -->
                <device Dname="MSPM0G3106">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00010000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00010000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G3106__"/>
                    <debug      svd="03_SVD/MSPM0G310X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_64KB.FLM" start="0x00000000" size="0x00010000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G3105  ***************************** -->
                <device Dname="MSPM0G3105">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00008000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00008000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00004000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00004000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G3105__"/>
                    <debug      svd="03_SVD/MSPM0G310X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_32KB.FLM" start="0x00000000" size="0x00008000" RAMstart="0x20200000" RAMsize="0x00004000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00004000" default="0"/>
                </device>
            </subFamily>
<!-- ************************  Subfamily MSPM0G150X  **************************** -->
            <subFamily DsubFamily="MSPM0G150X">
                <feature type="DAC" n="1" m="12" name="1MSPS digital-to-analog converter with integrated output buffer (DAC)"/>
                <feature type="AnalogOther" n="2" name="Zero-drift, zero-crossover chipper op-amp (OPA)"/>
                <feature type="AnalogOther" n="1" name="General-purpose amplifier (GPAMP)"/>
                <feature type="AnalogOther" n="3" name="High-speed comparator (COMP) with 8-bit reference DACs"/>
                <feature type="AnalogOther" n="1" name="Configurable 1.4V or 2.5V internal shared voltage reference (VREF)"/>
                <feature type="Timer" n="12" m="16" name="Two 16-bit advanced timers with deadband supporting up to 12 PWM channels"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer supporting QEI"/>
                <feature type="TimerOther" n="2" name="16-bit general purpose timer supporting low power operation in STANDBY mode"/>
                <feature type="TimerOther" n="1" name="32-bit high resolution timer"/>
                <feature type="WDT" n="2" name="Window-watchdog timer"/>
                <feature type="I2C" n="2" name="I2C interface supporting up to FM+ (1Mbps), SMBus/PMBus, and wakeup from STOP mode"/>
                <feature type="SPI" n="2" m="32000000" name="SPI interface"/>
                <feature type="UART" n="4" name="UART inteface, one supporting LIN, IrDA, DALI, Smart Card, Manchester, and three supporting low-power operation in STANDBY mode"/>
                <feature type="Other" n="1" name="Math accelerator supporting DIV, SQRT, and TRIG computations"/>
                <feature type="Crypto" n="128.256" name="HW accelerated AES Encryption/Decryption Engine"/>
                <feature type="RNG" n="1" name="True Random Number generator (TRNG)"/>
                <feature type="ADC" n="32" m="12" name="Two simultaneous 4MSPS analog-to-digital converters with up to 16-ch each (ADC)"/>
                <feature type="IOs" n="60" name="General purpose I/Os, including some 5-V tolerant, some high-drive with 20mA driver strength"/>
                <!-- *************************  Device MSPM0G1507  ***************************** -->
                <device Dname="MSPM0G1507">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00020000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00020000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G1507__"/>
                    <debug      svd="03_SVD/MSPM0G150X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_128KB.FLM" start="0x00000000" size="0x00020000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G1506  ***************************** -->
                <device Dname="MSPM0G1506">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00010000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00010000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G1506__"/>
                    <debug      svd="03_SVD/MSPM0G150X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_64KB.FLM" start="0x00000000" size="0x00010000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G1505  ***************************** -->
                <device Dname="MSPM0G1505">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00008000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00008000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00004000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00004000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G1505__"/>
                    <debug      svd="03_SVD/MSPM0G150X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_32KB.FLM" start="0x00000000" size="0x00008000" RAMstart="0x20200000" RAMsize="0x00004000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00004000" default="0"/>
                </device>
            </subFamily>
<!-- ************************  Subfamily MSPM0G110X  **************************** -->
            <subFamily DsubFamily="MSPM0G110X">
                <feature type="AnalogOther" n="1" name="General-purpose amplifier (GPAMP)"/>
                <feature type="AnalogOther" n="1" name="Configurable 1.4V or 2.5V internal shared voltage reference (VREF)"/>
                <feature type="Timer" n="12" m="16" name="Two 16-bit advanced timers with deadband supporting up to 12 PWM channels"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer"/>
                <feature type="TimerOther" n="1" name="16-bit general purpose timer supporting QEI"/>
                <feature type="TimerOther" n="2" name="16-bit general purpose timer supporting low power operation in STANDBY mode"/>
                <feature type="TimerOther" n="1" name="32-bit high resolution timer"/>
                <feature type="WDT" n="2" name="Window-watchdog timer"/>
                <feature type="I2C" n="2" name="I2C interface supporting up to FM+ (1Mbps), SMBus/PMBus, and wakeup from STOP mode"/>
                <feature type="SPI" n="2" m="32000000" name="SPI interface"/>
                <feature type="UART" n="4" name="UART inteface, one supporting LIN, IrDA, DALI, Smart Card, Manchester, and three supporting low-power operation in STANDBY mode"/>
                <feature type="ADC" n="32" m="12" name="Two simultaneous 4MSPS analog-to-digital converters with up to 16-ch each (ADC)"/>
                <feature type="IOs" n="60" name="General purpose I/Os, including some 5-V tolerant, some high-drive with 20mA driver strength"/>
                <!-- *************************  Device MSPM0G1107  ***************************** -->
                <device Dname="MSPM0G1107">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00020000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00020000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G1107__"/>
                    <debug      svd="03_SVD/MSPM0G110X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_128KB.FLM" start="0x00000000" size="0x00020000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G1106  ***************************** -->
                <device Dname="MSPM0G1106">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00010000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00010000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00008000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00008000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00008000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G1106__"/>
                    <debug      svd="03_SVD/MSPM0G110X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_64KB.FLM" start="0x00000000" size="0x00010000" RAMstart="0x20200000" RAMsize="0x00008000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00008000" default="0"/>
                </device>
                <!-- *************************  Device MSPM0G1105  ***************************** -->
                <device Dname="MSPM0G1105">
                    <memory     name="IROM1" access="rx"  start="0x00000000" size="0x00008000" startup="1" default="1"/>
                    <memory     name="IROM2" access="rx"  start="0x00400000" size="0x00008000" default="0" alias="IROM1"/>
                    <memory     name="IRAM1" access="rwx" start="0x20000000" size="0x00004000" default="0"/>
                    <memory     name="IRAM_Parity" access="rwx" start="0x20100000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="IRAM_No_Parity" access="rwx" start="0x20200000" size="0x00004000" default="1" alias="IRAM1"/>
                    <memory     name="IRAM_Parity_Code" access="rwx" start="0x20300000" size="0x00004000" default="0" alias="IRAM1"/>
                    <memory     name="NonMain_ECC" access="r"  start="0x41C00000" size="0x00000200" default="1"/>
                    <memory     name="NonMain_noECC" access="r"  start="0x41C10000" size="0x00000200" default="0" alias="NonMain_ECC"/>
                    <memory     name="Factory_ECC" access="r"  start="0x41C40000" size="0x00000080" default="1"/>
                    <memory     name="Factory_noECC" access="r"  start="0x41C50000" size="0x00000080" default="0" alias="Factory_ECC"/>
                    <compile    define="__MSPM0G1105__"/>
                    <debug      svd="03_SVD/MSPM0G110X.svd"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_MAIN_32KB.FLM" start="0x00000000" size="0x00008000" RAMstart="0x20200000" RAMsize="0x00004000" default="1"/>
                    <algorithm  name="02_Flash_Programming/FlashARM/MSPM0G_NONMAIN.FLM" start="0x41C00000" size="0x00000200" RAMstart="0x20200000" RAMsize="0x00004000" default="0"/>
                </device>
            </subFamily>
        </family>
    </devices>

    <boards>
        <board vendor="TexasInstruments" name="LP-MSPM0G3507" salesContact="http://www.ti.com/general/docs/contact.tsp">
            <description>MSPM0G3507 LaunchPad</description>
            <mountedDevice    deviceIndex="0" Dvendor="Texas Instruments:16" Dname="MSPM0G3507"/>
            <compatibleDevice deviceIndex="0" Dvendor="Texas Instruments:16" Dfamily="MSPM0G Series" DsubFamily="MSPM0G350X"/> 
            <debugInterface adapter="XDS110-ET" connector="XDS110-ET Onboard Emulator"/>  
            <debugInterface adapter="SWD" connector="10-pin Cortex Debug Connector (0.05 inch connector)"/>
            <feature type="USB" n="1" name="USB Device,  Micro-B receptacle"/>
            <feature type="Button" n="3" name="reset and two user push-buttons"/>
            <feature type="LED" n="4" name="LEDs for user interaction, including 1 RGB LED"/> 
            <feature type="ConnOther" n="2" name="40 pin BoosterPack Connector and support for 20 pin BoosterPacks"/>
            <feature type="TempSens" n="1" name="Temperature sensor circuit"/>
            <feature type="LightSens" n="1" name="Light sensor circuit"/>
            <feature type="SensOther" n="1" name="External OPA2365 for 4MSPS ADC evaluation"/>
            <feature type="XTAL" n="48000000"/>
            <feature type="Other" name="EnergyTrace technology available for ultra-low-power debugging"/>
            <feature type="Other" name="32kHz crystal"/>
        </board>
    </boards>

</package>
